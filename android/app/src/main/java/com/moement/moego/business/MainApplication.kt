package com.moement.moego.business

// ---------------- Manual added ----------------
import android.app.Application
import android.content.res.Configuration
import com.facebook.react.PackageList
import com.facebook.react.ReactApplication
import com.facebook.react.ReactHost
import com.facebook.react.ReactNativeHost
import com.facebook.react.ReactPackage
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.load
import com.facebook.react.defaults.DefaultReactNativeHost
import com.facebook.soloader.SoLoader
import com.intercom.reactnative.IntercomModule
import com.squareup.sdk.reader.ReaderSdk
import com.twiliovoicereactnative.VoiceApplicationProxy
import com.twiliovoicereactnative.VoiceApplicationProxy.VoiceReactNativeHost
import expo.modules.ApplicationLifecycleDispatcher
import expo.modules.ReactNativeHostWrapper
import com.tencent.mmkv.MMKV

// ---------------- Manual added ----------------

class MainApplication : Application(), ReactApplication {

  override var reactNativeHost: ReactNativeHost
  private var voiceApplicationProxy: VoiceApplicationProxy? = null

  init {
    val mVoiceReactNativeHost = MainReactNativeHost(this)
    reactNativeHost = ReactNativeHostWrapper(this, mVoiceReactNativeHost)
    voiceApplicationProxy = VoiceApplicationProxy(mVoiceReactNativeHost)
  }


  override val reactHost: ReactHost
    get() = ReactNativeHostWrapper.createReactHost(applicationContext, reactNativeHost)

  override fun onCreate() {
    super.onCreate()

    // ---------------- Manual added ----------------
    // 1. 初始化基础sdk
    MMKV.initialize(this)
    SoLoader.init(this, false)

    // 2. 初始化业务sdk
    voiceApplicationProxy!!.onCreate()
    ReaderSdk.initialize(this)
    IntercomModule.initialize(this, "android_sdk-f3e399e7548e930ec9813f5bb31abbb9a8198680", "oh5g31xm")
    // ---------------- Manual added ----------------
    

    if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      // If you opted-in for the New Architecture, we load the native entry point for this app.
      load()
    }
    ApplicationLifecycleDispatcher.onApplicationCreate(this)
}

  override fun onConfigurationChanged(newConfig: Configuration) {
    super.onConfigurationChanged(newConfig)
    ApplicationLifecycleDispatcher.onConfigurationChanged(this, newConfig)
  }
}

# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx512m -XX:MaxMetaspaceSize=256m
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:+UseG1GC -XX:MaxGCPauseMillis=200
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true

# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true

# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=true

# Enable AAPT2 PNG crunching
android.enablePngCrunchInReleaseBuilds=true

# Use this property to specify which architecture you want to build.
# You can also override it from the CLI using
# ./gradlew <task> -PreactNativeArchitectures=x86_64
reactNativeArchitectures=armeabi-v7a,arm64-v8a,x86,x86_64

# Use this property to enable support to the new architecture.
# This will allow you to use TurboModules and the Fabric render in
# your application. You should enable this flag either if you want
# to write custom TurboModules/Fabric components OR use libraries that
# are providing them.
newArchEnabled=false

# Use this property to enable or disable the Hermes JS engine.
# If set to false, you will be using JSC instead.
hermesEnabled=true

# Enable GIF support in React Native images (~200 B increase)
expo.gif.enabled=true
# Enable webp support in React Native images (~85 KB increase)
expo.webp.enabled=true
# Enable animated webp support (~3.4 MB increase)
# Disabled by default because iOS doesn't support animated webp
expo.webp.animated=false

# Enable network inspector
EX_DEV_CLIENT_NETWORK_INSPECTOR=true

# Use legacy packaging to compress native libraries in the resulting APK.
expo.useLegacyPackaging=false

# ####################### MANUAL ADDED ########################
# # staging
# SQUARE_READER_SDK_APPLICATION_ID=sq0idp-nZSpet2hDgW2nXvem_vXyg
# SQUARE_READER_SDK_REPOSITORY_PASSWORD=xlnx4bjscmfpir4jbugrmxwos5n665avo3ry6bfuu2fcxxn3fhiq

# # online
## The Application ID displayed on the Credentials tab in the Square Application Control Panel.
SQUARE_READER_SDK_APPLICATION_ID=sq0idp-ojCjvDy12EX07-dcKOVf9g
## The Application Secret from the OAuth tab in the Square Application Control Panel.
SQUARE_READER_SDK_REPOSITORY_PASSWORD=swy6kn5vynjazs6zcr7k2jbkhu6civ6xthgun2oaglfsz4v4mr3q

# ####################### DO NOT MODIFY THIS CONTENT ########################
versionName=2.16.2
versionCode=130
# ####################### DO NOT MODIFY THIS CONTENT ########################

android.minSdkVersion=26
android.compileSdkVersion=34
android.targetSdkVersion=34
android.extraMavenRepos=[]

# 正确的依赖锁定配置应类似这样
dependencyLocking.enabled=true
dependencyLocking.ignoredDependencies=[] 
dependencyLocking.configurations=[:releaseRuntimeClasspath, :debugRuntimeClasspath, :releaseCompileClasspath]

# fix error
# > Could not resolve all files for configuration ':app:releaseRuntimeClasspath'.
# > Failed to transform jackson-core-2.16.1.jar (com.fasterxml.jackson.core:jackson-core:2.16.1) to match attributes {artifactType=processed-jar, org.gradle.category=library, org.gradle.dependency.bundling=external, org.gradle.libraryelements=jar, org.gradle.status=release, org.gradle.usage=java-runtime}.
android.jetifier.ignorelist=jackson-core

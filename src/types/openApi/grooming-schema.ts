export namespace grooming {
  export interface paths {
    'GET/grooming/2021/summary': operations['getBusiness2021Summary'];
    'GET/grooming/2021/thanksgiving/petCount': operations['getPetCountUtilChristmas'];
    'GET/grooming/2023/questionnaire': operations['searchQuestionnaire'];
    'POST/grooming/2023/questionnaire': operations['submitQuestionnaire'];
    'GET/grooming/2023/summary': operations['getBusiness2023Summary'];
    'GET/grooming/abandoned-schedule-message-setting': operations['getAbandonedScheduleMessageSetting'];
    'PUT/grooming/abandoned-schedule-message-setting': operations['updateAbandonedScheduleMessageSetting'];
    'GET/grooming/applicable/service': operations['queryApplicableService'];
    'GET/grooming/applicable/sorted/service': operations['queryApplicableServiceV2'];
    'GET/grooming/appointment': operations['getAppointmentWithPetDetails'];
    'PUT/grooming/appointment': operations['modifyGroomingAppointment'];
    'POST/grooming/appointment': operations['addGroomingAppointment'];
    'DELETE/grooming/appointment': operations['deleteAppointment'];
    'GET/grooming/appointment/actionLog': operations['getActionLog'];
    'PUT/grooming/appointment/alertNotes': operations['updateAlertNotes'];
    /** 支持alertnotes 基于repeat批量修改 */
    'PUT/grooming/appointment/alertNotes/repeat': operations['updateAlertNotesWithRepeat'];
    'POST/grooming/appointment/amount/validate': operations['validateAppointmentEdit'];
    'POST/grooming/appointment/applyCustomService': operations['applyCustomService'];
    'PUT/grooming/appointment/block': operations['modifyAppointmentBlock'];
    'POST/grooming/appointment/block': operations['addAppointmentBlock'];
    'DELETE/grooming/appointment/block': operations['deleteAppointmentBlock'];
    'PUT/grooming/appointment/block/repeat': operations['modifyAppointmentBlockRepeat'];
    'POST/grooming/appointment/block/repeat': operations['addAppointmentBlockRepeat'];
    'POST/grooming/appointment/business/upcoming': operations['queryBusinessUpComingAppoint'];
    'PUT/grooming/appointment/cancel': operations['cancel'];
    /** @deprecated */
    'PUT/grooming/appointment/checkin': operations['checkInGroomingAppointment'];
    /** @deprecated */
    'PUT/grooming/appointment/checkout': operations['checkOutGroomingAppointment'];
    'PUT/grooming/appointment/color': operations['changeColor'];
    'PUT/grooming/appointment/color/repeat': operations['modifyAppointmentColorRepeat'];
    'PUT/grooming/appointment/comments': operations['updateComments'];
    'GET/grooming/appointment/comments/history': operations['queryCustomerHistoryComments'];
    'PUT/grooming/appointment/confirm': operations['confirm'];
    'PUT/grooming/appointment/conflict/calendar': operations['checkCalendarConflict'];
    'PUT/grooming/appointment/conflict/check': operations['checkConflict'];
    'POST/grooming/appointment/conflict/check/batch': operations['batchCheckConflict'];
    'GET/grooming/appointment/customer/upcoming': operations['queryCustomerUpComingAppoint'];
    'GET/grooming/appointment/customer/upcoming/url': operations['getCustomerUpComingUrl'];
    'GET/grooming/appointment/detail': operations['queryTicketDetailWithWindow'];
    'GET/grooming/appointment/detail/pup': operations['queryTicketDetail'];
    'GET/grooming/appointment/driving/info': operations['getDrivingInfo'];
    'POST/grooming/appointment/driving/info/batch': operations['getDrivingInfoBatch'];
    'PUT/grooming/appointment/finish': operations['finish'];
    'POST/grooming/appointment/multiple': operations['addAppointmentMultiple'];
    'PUT/grooming/appointment/notification': operations['pushNotification'];
    /** @deprecated */
    'GET/grooming/appointment/operation/num': operations['queryStaffUpComingOperationCount'];
    'PUT/grooming/appointment/pay': operations['paid'];
    /** @deprecated */
    'PUT/grooming/appointment/reopen': operations['apptReopen'];
    'PUT/grooming/appointment/repeat': operations['modifyAppointmentRepeat'];
    'POST/grooming/appointment/repeat': operations['addAppointmentRepeat'];
    'PUT/grooming/appointment/reverse/checkin': operations['reverseCheckIn'];
    'POST/grooming/appointment/route/optimization': operations['routeOptimization'];
    'GET/grooming/appointment/route/optimization/available/times': operations['getRouteOptimizationAvailableTimesForSoloPlan'];
    /** @deprecated */
    'GET/grooming/appointment/service/num': operations['queryServiceUpComingAppointCount'];
    /** @deprecated */
    'POST/grooming/appointment/smartSchedule': operations['smartSchedule_1'];
    'PUT/grooming/appointment/special': operations['modifySpecialAppointment'];
    'GET/grooming/appointment/staff/num': operations['queryStaffUpComingAppointCount'];
    'PUT/grooming/appointment/status': operations['updateAppointmentStatus'];
    'PUT/grooming/appointment/status/revert': operations['revertAppointmentStatus'];
    /** @deprecated */
    'GET/grooming/appointment/ticketComment': operations['getTicketComment'];
    'GET/grooming/appointment/ticketComment/v2': operations['getTicketCommentV2'];
    'POST/grooming/appointment/transfer': operations['transferAppointment_1'];
    'PUT/grooming/appointment/unconfirm': operations['unconfirm'];
    'PUT/grooming/appointment/waiting': operations['toWaiting'];
    'POST/grooming/appointment/waiting': operations['addGroomingAppointmentWaiting'];
    'GET/grooming/appointment/waiting/list': operations['queryAppointmentWaitingList'];
    'PUT/grooming/appointment/waiting/still': operations['modifyGroomingAppointmentWaiting'];
    'GET/grooming/bookOnline/appointment': operations['queryGroomingBookOnlineAppointment'];
    'GET/grooming/bookOnline/appointment/v2': operations['queryGroomingBookOnlineAppointmentV2'];
    'POST/grooming/bookOnline/checkAvailableDist': operations['checkAvailableDist'];
    'GET/grooming/bookOnline/client/business/preference': operations['getBusinessPreference_1'];
    /** 获取商家对应公司下所有的location信息 */
    'GET/grooming/bookOnline/client/company/locations': operations['getRelevantBiz_1'];
    'GET/grooming/bookOnline/client/gallery': operations['getGallery_1'];
    'GET/grooming/bookOnline/client/info': operations['getInfo_1'];
    'POST/grooming/bookOnline/client/preauth/amount': operations['showServicePreAuth'];
    'POST/grooming/bookOnline/client/prepay/amount': operations['calculateServiceDeposit'];
    'GET/grooming/bookOnline/client/prepay/detail': operations['getOBDepositDetail'];
    'GET/grooming/bookOnline/client/profile': operations['getProfile_2'];
    'GET/grooming/bookOnline/client/question': operations['getQuestionForClient_1'];
    'POST/grooming/bookOnline/client/services': operations['queryBookOnlinePetServicesForClient_1'];
    'POST/grooming/bookOnline/client/submit/check': operations['checkSubmitInfo'];
    'POST/grooming/bookOnline/customer/lastAppt': operations['getCustomerLastAppointmentForBookOnline'];
    'GET/grooming/bookOnline/gallery': operations['getGallery'];
    'POST/grooming/bookOnline/gallery': operations['addGalleryImage'];
    'DELETE/grooming/bookOnline/gallery': operations['deleteGalleryImage'];
    'PUT/grooming/bookOnline/gallery/star': operations['batchStarGalleryImage'];
    'PUT/grooming/bookOnline/payment/group/setting': operations['updatePaymentGroupSetting'];
    'DELETE/grooming/bookOnline/payment/group/setting': operations['deletePaymentGroupSetting'];
    'GET/grooming/bookOnline/pet/limit': operations['getPetLimit'];
    'POST/grooming/bookOnline/pet/limit': operations['savePetLimit'];
    'GET/grooming/bookOnline/profile': operations['getProfile_1'];
    'PUT/grooming/bookOnline/profile': operations['updateProfile'];
    'GET/grooming/bookOnline/question': operations['getQuestion'];
    'PUT/grooming/bookOnline/question': operations['updateQuestion'];
    'POST/grooming/bookOnline/question': operations['createQuestion'];
    'DELETE/grooming/bookOnline/question': operations['deleteQuestion'];
    'PUT/grooming/bookOnline/question/sort': operations['updateQuestionSort'];
    'GET/grooming/bookOnline/questionList': operations['getQuestionList'];
    'PUT/grooming/bookOnline/questionList': operations['updateQuestionList'];
    'GET/grooming/bookOnline/setting/info': operations['getSettingInfo'];
    'PUT/grooming/bookOnline/setting/info': operations['updateSettingInfo'];
    'PUT/grooming/bookOnline/setting/mobile': operations['updateMobileGroomingSetting'];
    'PUT/grooming/bookOnline/setting/notification': operations['updateNotificationSetting'];
    'PUT/grooming/bookOnline/setting/service': operations['updateServiceSetting'];
    'GET/grooming/bookOnline/staff/availability': operations['getStaffOnlineAvailability'];
    'PUT/grooming/bookOnline/staff/availability': operations['modifyStaffOnlineAvailability'];
    'POST/grooming/bookOnline/staff/availability/batch': operations['modifyStaffOnlineAvailabilityBatch'];
    'GET/grooming/bookOnline/staff/availability/sync': operations['getSyncWithWorkingHourStatus'];
    'POST/grooming/bookOnline/staff/availability/sync': operations['modifySyncWithWorkingHourStatus'];
    'GET/grooming/bookOnline/staff/time': operations['getAvailableStaffTime'];
    'POST/grooming/bookOnline/staff/time': operations['saveAvailableStaffTime'];
    'POST/grooming/bookOnline/v2/client/availableTime': operations['getStaffAvailableTimeV2_1'];
    'GET/grooming/bookOnline/zipcode': operations['selectByZipCodes'];
    'GET/grooming/bookOnline/zipcode/details': operations['selectByZipCodes_1'];
    'GET/grooming/bookOnline/zipcode/search': operations['searchByPrefix'];
    'GET/grooming/calendar/card/day/list': operations['getDayList'];
    'GET/grooming/calendar/card/monthly/list': operations['getMonthlyList'];
    'PUT/grooming/calendar/card/reschedule': operations['reschedule_1'];
    'GET/grooming/client/appt': operations['getApptDetail'];
    'POST/grooming/client/appt': operations['rescheduleAppt'];
    'DELETE/grooming/client/appt': operations['cancelAppt'];
    'POST/grooming/client/appt-v2': operations['rescheduleApptV2'];
    'GET/grooming/client/appt/coming-next': operations['getComingNextAppt'];
    'GET/grooming/client/appt/last-finished': operations['getLastFinishedAppt'];
    'POST/grooming/client/appts': operations['getApptList'];
    'PUT/grooming/company/batch/serviceCategory': operations['updateServiceCategoryBatch'];
    'PUT/grooming/company/service': operations['updateUpdateService_1'];
    'POST/grooming/company/service': operations['companyCreateService'];
    'POST/grooming/company/service/page': operations['getCompanyServiceByPage'];
    'GET/grooming/customer/appointment/count': operations['appointmentCount'];
    /** @deprecated */
    'POST/grooming/customer/appointment/list': operations['queryGroomingCustomerAppointment'];
    'POST/grooming/customer/appointment/list/v2': operations['queryGroomingCustomerAppointmentV2'];
    'PUT/grooming/customer/service': operations['modifyCustomerService'];
    'DELETE/grooming/customer/service': operations['deleteCustomerService'];
    'GET/grooming/customer/service/info': operations['queryCustomerService'];
    /** @deprecated */
    'GET/grooming/data/rule/info': operations['queryDataRuleInfo'];
    'GET/grooming/debugging/distanceMatrixUsageSummary': operations['getDistanceMatrixUsageSummary'];
    'GET/grooming/deposit/client/detail': operations['getDepositAndInvoiceDetailByGUID'];
    /** 生成depositGuid， 用于pay online 支付定金; 如果已存在，就更新amount */
    'POST/grooming/deposit/create': operations['checkInvoiceGuid'];
    'GET/grooming/deposit/detail': operations['getDepositAndInvoiceDetailByInvoiceId'];
    'GET/grooming/distance': operations['checkAvailableDist_2'];
    'GET/grooming/exchange/rate': operations['getExchangeRate'];
    'POST/grooming/google/calendar/event/webhook': operations['watchEventWebHook'];
    'POST/grooming/google/calendar/oauth/save': operations['saveOauthResult'];
    'GET/grooming/google/calendar/setting': operations['queryGoogleCalendarSetting'];
    'PUT/grooming/google/calendar/setting': operations['updateGooGleCalendarSetting'];
    'POST/grooming/google/calendar/setting': operations['createGooGleCalendarSetting'];
    'POST/grooming/google/map/driving-info/cal': operations['drivingInfoCalculation'];
    'GET/grooming/google/map/place-id': operations['getPlace'];
    'DELETE/grooming/grooming-note/{id}': operations['deleteById'];
    'GET/grooming/grooming-report/client/info': operations['getGroomingReportInfoForClient'];
    'POST/grooming/grooming-report/client/view/upload': operations['addUpGroomingReportOpenedCount'];
    'GET/grooming/grooming-report/info': operations['getGroomingReportInfo'];
    'POST/grooming/grooming-report/info': operations['saveGroomingReportInfo'];
    'POST/grooming/grooming-report/init': operations['initGroomingReport'];
    'GET/grooming/grooming-report/preview/data': operations['getPreviewData'];
    'POST/grooming/grooming-report/preview/url': operations['getPreviewUrl'];
    'GET/grooming/grooming-report/records': operations['getGroomingReportRecords'];
    'GET/grooming/grooming-report/setting': operations['getGroomingReportSetting'];
    'PUT/grooming/grooming-report/setting': operations['saveGroomingReportSetting'];
    'GET/grooming/grooming-report/template': operations['getGroomingReportTemplate'];
    'PUT/grooming/grooming-report/template': operations['saveGroomingReportTemplate'];
    'GET/grooming/grooming-report/theme/config': operations['getGroomingReportThemeConfigList'];
    'POST/grooming/invoice/apply-package': operations['applyPackage'];
    /** @deprecated */
    'DELETE/grooming/invoice/apply-package': operations['removeApplyPackageService'];
    /** pay online for client 数据详情下发 */
    'GET/grooming/invoice/client/detail': operations['queryInvoiceByInvoiceGuid'];
    'GET/grooming/invoice/client/url': operations['getInvoiceGuid'];
    'POST/grooming/invoice/complete': operations['setCompleted'];
    'POST/grooming/invoice/confirm-payment': operations['confirmPayment'];
    'GET/grooming/invoice/detail': operations['getById'];
    'GET/grooming/invoice/detail/encode': operations['queryInvoiceByInvoiceIdEncode'];
    /** @deprecated */
    'GET/grooming/invoice/detailWithPayment': operations['getWithPaymentById'];
    'POST/grooming/invoice/noshow': operations['createNoShowInvoice'];
    /** @deprecated */
    'GET/grooming/invoice/order/detail': operations['getOrderDetail'];
    'GET/grooming/invoice/packageAvailable': operations['hasAvailablePackage'];
    'POST/grooming/invoice/place-order': operations['save'];
    'POST/grooming/invoice/preauth/noshow': operations['createNoShowInvoiceAndPreAuth'];
    'PUT/grooming/invoice/revert': operations['revertInvoice'];
    'GET/grooming/invoice/send/url': operations['getInvoiceSendUrl'];
    'POST/grooming/invoice/set-discount': operations['setDiscount'];
    /** @deprecated */
    'POST/grooming/invoice/set-tips': operations['setTips'];
    'DELETE/grooming/invoice/v2/apply-package': operations['removeApplyPackageServiceV2'];
    'GET/grooming/invoice/v2/order/detail': operations['getOrderDetailV2'];
    'GET/grooming/ob/v2/abandoned-client-filters': operations['findAll'];
    'POST/grooming/ob/v2/abandoned-client/bulk-add-client': operations['bulkAddClient'];
    'POST/grooming/ob/v2/abandoned-client/bulk-delete-record': operations['bulkDeleteAbandonedRecords'];
    'GET/grooming/ob/v2/abandoned-client/export': operations['export'];
    'POST/grooming/ob/v2/abandoned-client/search': operations['search'];
    'GET/grooming/ob/v2/business/abandon-client/record': operations['getAbandonClientRecord'];
    'DELETE/grooming/ob/v2/business/abandon-client/record': operations['deleteAbandonClientRecord'];
    'PUT/grooming/ob/v2/business/abandon-client/record-pets': operations['updateAbandonClientRecordAddPets'];
    'GET/grooming/ob/v2/business/brief/profile/list': operations['getBriefOBProfileList'];
    'GET/grooming/ob/v2/business/config': operations['getLandingPageConfig'];
    'POST/grooming/ob/v2/business/config': operations['updateLandingPageConfig'];
    'POST/grooming/ob/v2/business/metrics': operations['listMetrics'];
    'GET/grooming/ob/v2/business/migration': operations['getLandingPageMergeProfile'];
    'PUT/grooming/ob/v2/business/migration': operations['updateLandingPageMergeProfile'];
    'PUT/grooming/ob/v2/client-pets': operations['updateClientPetsDetail'];
    'POST/grooming/ob/v2/client-pets-diff': operations['getClientPetsDetail'];
    'DELETE/grooming/ob/v2/client-pets-diff': operations['deleteClientPetsRequest'];
    'POST/grooming/ob/v2/client/abandon': operations['collectAbandonInfo'];
    'GET/grooming/ob/v2/client/appt': operations['getCustomerAppointmentForBookOnline_1'];
    'GET/grooming/ob/v2/client/appt/last': operations['getCustomerLastAppointmentForBookOnline_1'];
    'GET/grooming/ob/v2/client/business/config': operations['getBusinessOBProfileAndConfig'];
    'GET/grooming/ob/v2/client/business/info': operations['getInfo'];
    'GET/grooming/ob/v2/client/business/location': operations['getRelevantBiz'];
    'GET/grooming/ob/v2/client/business/preference': operations['getBusinessPreference'];
    'GET/grooming/ob/v2/client/business/profile': operations['getProfile'];
    'GET/grooming/ob/v2/client/business/services': operations['getBusinessOBServices'];
    'POST/grooming/ob/v2/client/business/staff': operations['getOBAvailableStaffListByService'];
    'GET/grooming/ob/v2/client/distance': operations['checkAvailableDist_1'];
    'GET/grooming/ob/v2/client/grooming-report/appt': operations['getCustomerAppointmentForBookOnline'];
    'GET/grooming/ob/v2/client/question': operations['getQuestionForClient'];
    'POST/grooming/ob/v2/client/service': operations['queryBookOnlinePetServicesForClient'];
    /** @deprecated */
    'POST/grooming/ob/v2/client/submit': operations['bookOnlineSubmit'];
    'POST/grooming/ob/v2/client/timeslot': operations['getStaffAvailableTimeV2'];
    'GET/grooming/ob/v2/ob-request': operations['getOBRequestDetail'];
    'POST/grooming/ob/v3/client/submit': operations['submitBookingRequest'];
    'GET/grooming/order/tipSplit/detail': operations['getTipSplitDetail'];
    'POST/grooming/order/tipSplit/preview': operations['previewTipSplitDetail'];
    'GET/grooming/order/whitelist/check': operations['isInWhitelist'];
    'DELETE/grooming/package': operations['deleteCustomerPackage'];
    'GET/grooming/package/info': operations['queryCustomerPackageInfo'];
    'GET/grooming/package/list': operations['queryCustomerPackageList'];
    'PUT/grooming/package/used': operations['usePackageForGrooming'];
    /** @deprecated */
    'GET/grooming/packages': operations['queryCustomerPackageByServiceId'];
    'GET/grooming/pet/count/within': operations['getRemainPetCountWithin'];
    'GET/grooming/pet/detail/day/list': operations['queryPetDetailList'];
    'GET/grooming/pet/detail/monthly/list': operations['queryPetDetailListMonthly'];
    'PUT/grooming/pet/detail/time': operations['editServiceTime'];
    'GET/grooming/pet/last/service': operations['getPetLastService'];
    'GET/grooming/printcard/activity': operations['getActivityCardInfoByDate'];
    'GET/grooming/printcard/list': operations['getPrintCardInfoByDate'];
    'GET/grooming/printcard/one': operations['getPrintCardInfoByGroomingId'];
    'GET/grooming/printcard/stay/list': operations['getStayCardInfoByDate'];
    'GET/grooming/printcard/stay/one': operations['getStayCardInfoByGroomingId'];
    /**
     * 获取商家当前关联的qb账号，有那些bank类型的account(账本)
     * @deprecated
     */
    'GET/grooming/qb/account': operations['getBusinessConnectAccount'];
    /**
     * quickbooks回调后调用，保存code等信息
     * @description 这里返回的connectId，一会需要回传
     */
    'POST/grooming/qb/connect/create': operations['createQbSetting'];
    /**
     * qb oauth链接
     * @description 获取qb oauth链接
     */
    'POST/grooming/qb/connect/oauth/url': operations['getConnectOAuthUrl'];
    /** 获取invoice状态 */
    'GET/grooming/qb/invoice/status': operations['getInvoiceSyncStatus'];
    /**
     * 获取商家设置信息
     * @deprecated
     * @description 用于设置界面和qb oauth回调界面的配置信息获取
     */
    'GET/grooming/qb/setting': operations['getQbConnectSetting'];
    /**
     * setting更新接口
     * @description 所有参数都可以不传，建议不更新的参数不要传递
     */
    'PUT/grooming/qb/setting': operations['createBusinessQbProfile'];
    /**
     * 获取指定商家设置信息
     * @description 用于设置界面和qb oauth回调界面的配置信息获取
     */
    'GET/grooming/qb/setting/business': operations['getQbConnectSettingByBusinessId'];
    /**
     * 获取company下所有business设置信息
     * @description 用于设置界面和qb oauth回调界面的配置信息获取
     */
    'GET/grooming/qb/setting/company': operations['getQbConnectSettingByCompanyId'];
    /**
     * 基于connectId，创建商家设置信息
     * @deprecated
     * @description quickbooks回调界面，商家点击确定协议后，最后调用此接口
     */
    'POST/grooming/qb/setting/create': operations['qbCreateSetting'];
    /**
     * 基于connectId，创建商家设置信息
     * @description quickbooks回调界面，商家点击确定协议后，最后调用此接口
     */
    'POST/grooming/qb/setUp': operations['qbSetup'];
    /** 删除 business 所有 invoice */
    'DELETE/grooming/qb/sync/all/invoice': operations['deleteAllInvoice'];
    /** 删除指定biz下所有的qb sync数据 */
    'POST/grooming/qb/sync/deleted/all': operations['syncDeletedCustomerTask'];
    /** 迁移数据, from qb 1.0 to qb 2.0 */
    'POST/grooming/qb/sync/migration': operations['migrationQBData'];
    /** 同步单个预约 */
    'POST/grooming/qb/sync/one': operations['syncGroomingAppointment'];
    /** 同步单个 invoice */
    'POST/grooming/qb/sync/one/invoice': operations['syncInvoice'];
    /** 删除单个 invoice */
    'DELETE/grooming/qb/sync/one/invoice': operations['deleteInvoice'];
    /** 刷新metadata */
    'POST/grooming/qb/sync/refresh/metadata': operations['refreshMetadata'];
    /** 触发定时任务 */
    'POST/grooming/qb/sync/task/test': operations['syncTask'];
    'GET/grooming/repeat/appointment': operations['getRepeatAppointment'];
    'PUT/grooming/repeat/conflict': operations['checkConflictDay'];
    'POST/grooming/repeat/preview': operations['previewRepeatDay_1'];
    'GET/grooming/repeat/rule': operations['queryRepeatRule_1'];
    'PUT/grooming/repeat/rule': operations['modifyRepeatRule_1'];
    'POST/grooming/repeat/rule': operations['addRepeatRule_1'];
    'GET/grooming/repeat/rule/all': operations['queryAllBusinessRepeatRule'];
    'PUT/grooming/repeat/rule/full': operations['modifyFullRepeatRule'];
    'GET/grooming/repeat/ss/available': operations['getRepeatWithSSAvailability'];
    'POST/grooming/repeat/ss/preview': operations['previewRepeatDayWithSS'];
    'GET/grooming/repeat/v2/appointment/list': operations['getRepeatAppointmentList'];
    'PUT/grooming/repeat/v2/appointment/list': operations['saveRepeatAppointmentList'];
    'POST/grooming/repeat/v2/conflict/check': operations['checkConflict_1'];
    'POST/grooming/repeat/v2/preview': operations['previewRepeatDay'];
    'GET/grooming/repeat/v2/rule': operations['queryRepeatRule'];
    'PUT/grooming/repeat/v2/rule': operations['modifyRepeatRule'];
    'POST/grooming/repeat/v2/rule': operations['addRepeatRule'];
    'GET/grooming/report/mobile/appt': operations['getReportAppointments'];
    'GET/grooming/report/mobile/apptNum': operations['getReportApptsNumber'];
    /** @deprecated */
    'GET/grooming/report/mobile/payment': operations['getPaymentMethodReport'];
    'GET/grooming/report/mobile/summary': operations['getMobileDashboardSummary'];
    'PUT/grooming/service': operations['updateService'];
    'POST/grooming/service': operations['createService'];
    'DELETE/grooming/service': operations['deleteService'];
    'GET/grooming/service-area-pic-cache': operations['getFromClient'];
    'POST/grooming/service-area-pic-cache': operations['createOrUpdateFromClient'];
    'DELETE/grooming/service-area-pic-cache': operations['delete'];
    /** 根据 serviceId 查询 service 信息，包括被删除、inactive的 */
    'POST/grooming/service/info': operations['getServicesByIds'];
    'PUT/grooming/service/location/ob/setting': operations['updateUpdateService'];
    'POST/grooming/service/page': operations['getServiceByPage'];
    'PUT/grooming/service/sort': operations['serviceSort'];
    'PUT/grooming/service/tax': operations['updateServiceForApptInvoice'];
    'GET/grooming/serviceCategory': operations['getEditServiceCategory'];
    'PUT/grooming/serviceCategory': operations['updateServiceCategory'];
    'POST/grooming/serviceCategory': operations['createServiceCategory'];
    'DELETE/grooming/serviceCategory': operations['deleteServiceCategory'];
    'GET/grooming/serviceCategory/service': operations['getEditServiceWithCategory'];
    'PUT/grooming/serviceCategory/sort': operations['serviceCategorySort'];
    'GET/grooming/services': operations['queryPetServices'];
    'GET/grooming/summary': operations['getBusinessSummary'];
    'POST/grooming/upcoming/preview/list': operations['queryCustomerUpComingAppointForClientShare'];
    'PUT/grooming/v2/appointment/action-time': operations['updateActionTime'];
    'PUT/grooming/v2/appointment/color-code': operations['updateColorCode'];
    'PUT/grooming/v2/appointment/multi-pets/start-time': operations['setMultiPetsStartTime'];
    'GET/grooming/v2/appointment/operation/list': operations['getOperationList'];
    'PUT/grooming/v2/appointment/pet': operations['updatePet'];
    'DELETE/grooming/v2/appointment/pet': operations['deletePet'];
    'PUT/grooming/v2/appointment/petsAndServices': operations['updatePetsAndServices'];
    'POST/grooming/v2/appointment/quick-add': operations['quickAddAppointment'];
    'PUT/grooming/v2/appointment/repeat': operations['modifyGroomingAppointmentRepeatV2'];
    'PUT/grooming/v2/appointment/rescheduleFromWaitList': operations['reschedule'];
    'POST/grooming/v2/appointment/smartSchedule': operations['smartSchedule2'];
    'POST/grooming/v2/appointment/staff/upcoming/count': operations['getStaffUpcomingCount'];
    'POST/grooming/v2/appointment/staff/upcoming/operation/count': operations['getStaffUpcomingOperationCount'];
    'POST/grooming/v2/appointment/transfer': operations['transferAppointment'];
    'PUT/grooming/wait-list': operations['updateWaitList'];
    'POST/grooming/wait-list': operations['addWaitList'];
    'DELETE/grooming/wait-list': operations['deleteWaitList'];
    'POST/grooming/wait-list/appointment': operations['addWaitListFromAppointment'];
    'GET/grooming/wait-list/availableInfoOnApptCancel': operations['getAvailableOnApptCancel'];
    'POST/grooming/wait-list/calendarView': operations['calendarView'];
    'GET/grooming/wait-list/detail': operations['waitListDetail'];
    'POST/grooming/wait-list/list': operations['getWaitList'];
    'POST/grooming/wait-list/smartSchedule': operations['smartSchedule'];
    'POST/grooming/wait-list/transferNewWaitList': operations['transferNewWaitList'];
    'GET/grooming/website_summary': operations['getSummary'];
  }
  export interface operations {
    getBusiness2021Summary: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.web.dto.Business2021SummaryDto'];
    };
    getPetCountUtilChristmas: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.web.dto.ThanksgivingResultDto'];
    };
    searchQuestionnaire: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.QuestionnaireVO'];
    };
    submitQuestionnaire: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.SubmitQuestionnaireParams']>;
      Res: {};
    };
    getBusiness2023Summary: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.Business2023SummaryVo'];
    };
    getAbandonedScheduleMessageSetting: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.AbandonedScheduleMessageSettingVO'];
    };
    updateAbandonedScheduleMessageSetting: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.web.AbandonedScheduleMessageSettingController$UpdateAbandonedScheduleMessageSettingParam']
      >;
      Res: {};
    };
    queryApplicableService: {
      Req: Partial<
        {
          customerId: number;
        } & {
          petId: number;
        } & {
          type?: number;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.PetServiceDTO'][];
    };
    queryApplicableServiceV2: {
      Req: Partial<
        {
          customerId: number;
        } & {
          petId: number;
        } & {
          type?: number;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.ApplicableServiceByCategoryDTO'][];
    };
    getAppointmentWithPetDetails: {
      Req: Partial<{
        appointmentId: number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.dto.AppointmentWithPetDetailsDto'];
    };
    modifyGroomingAppointment: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.AppointmentParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    addGroomingAppointment: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.AppointmentParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.AddResultDTO'];
    };
    deleteAppointment: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.DeleteAppointmentParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    getActionLog: {
      Req: Partial<{
        appointmentId: number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.dto.appointment.history.ActionHistoryDTO'];
    };
    updateAlertNotes: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.EditCommentsParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    updateAlertNotesWithRepeat: {
      Req: Partial<
        {
          type: number;
        } & components['schemas']['com.moego.server.grooming.params.EditContextParams']
      >;
      Res: {};
    };
    validateAppointmentEdit: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.ApptEditSaveCheckParams']>;
      Res: components['schemas']['com.moego.server.payment.dto.RefundChannelDTO'];
    };
    applyCustomService: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.AppointmentParams']>;
      Res: string;
    };
    modifyAppointmentBlock: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.AppointmentBlockParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    addAppointmentBlock: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.AppointmentBlockParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.AddResultDTO'];
    };
    deleteAppointmentBlock: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.IdParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    modifyAppointmentBlockRepeat: {
      Req: Partial<
        {
          type: number;
        } & components['schemas']['com.moego.server.grooming.params.AppointmentBlockParams']
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    addAppointmentBlockRepeat: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.BlockRepeatParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    queryBusinessUpComingAppoint: {
      Req: Partial<{
        date: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.BusinessUpcomingDTO'];
    };
    cancel: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.CancelParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    checkInGroomingAppointment: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.CheckParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    checkOutGroomingAppointment: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.CheckParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    changeColor: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.ColorEditParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    modifyAppointmentColorRepeat: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.ColorEditParams']>;
      Res: {};
    };
    updateComments: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.EditCommentsParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    queryCustomerHistoryComments: {
      Req: Partial<
        {
          customerId: number;
        } & {
          petId?: string | number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.HistoryCommentsDTO'];
    };
    confirm: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.ConfirmParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    checkCalendarConflict: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.CalendarCheckParams']>;
      Res: components['schemas']['com.moego.server.grooming.dto.CalendarConflictDTO'];
    };
    checkConflict: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.AppointmentCheckParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.ConflictCheckDTO'];
    };
    batchCheckConflict: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.AppointmentBatchCheckParams']>;
      Res: components['schemas']['com.moego.server.grooming.dto.ConflictCheckDTO'][];
    };
    queryCustomerUpComingAppoint: {
      Req: Partial<{
        id: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.CustomerUpcomingBusinessDTO'];
    };
    getCustomerUpComingUrl: {
      Req: Partial<{
        customerId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    queryTicketDetailWithWindow: {
      Req: Partial<{
        id: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.GroomingTicketWindowDetailDTO'];
    };
    queryTicketDetail: {
      Req: Partial<
        {
          /** @description false only filters grooming, true may include */
          filterGroomingOnlyService?: boolean;
        } & {
          id: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.GroomingTicketDetailDTO'];
    };
    getDrivingInfo: {
      Req: Partial<
        {
          date: string;
        } & {
          staffId: number;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.service.dto.DrivingDisplayInfo'][];
    };
    getDrivingInfoBatch: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.BatchDrivingInfoParams']>;
      Res: components['schemas']['com.moego.server.grooming.service.dto.BatchDrivingDisplayInfo'];
    };
    finish: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.EditIdParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    addAppointmentMultiple: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.AppointmentRepeatParams'][]>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.AddResultDTO'];
    };
    pushNotification: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.EditIdParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    queryStaffUpComingOperationCount: {
      Req: Partial<
        {
          sourceStaffId: number;
        } & {
          targetStaffId: number;
        }
      >;
      Res: number;
    };
    paid: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.EditIdParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    apptReopen: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.ApptReopenParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    modifyAppointmentRepeat: {
      Req: Partial<
        {
          type: number;
        } & components['schemas']['com.moego.server.grooming.params.AppointmentParams']
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    addAppointmentRepeat: {
      Req: Partial<
        {
          appointmentId?: number;
        } & components['schemas']['com.moego.server.grooming.params.AppointmentRepeatParams'][]
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.AddResultDTO'];
    };
    reverseCheckIn: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.EditIdParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    routeOptimization: {
      Req: Partial<components['schemas']['com.moego.server.grooming.service.dto.RouteOptimizationRequest']>;
      Res: components['schemas']['com.moego.server.grooming.service.dto.RouteOptimizationResponse'];
    };
    getRouteOptimizationAvailableTimesForSoloPlan: {
      Req: Partial<{}>;
      Res: number;
    };
    queryServiceUpComingAppointCount: {
      Req: Partial<{
        serviceId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.service.dto.GroomingServiceUpcomingAppointmentCountDto'];
    };
    smartSchedule_1: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.ss.SmartScheduleRequest']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.ss.SmartScheduleVO'];
    };
    modifySpecialAppointment: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.MoeGroomingAppointmentSpecialParam']>;
      Res: {};
    };
    queryStaffUpComingAppointCount: {
      Req: Partial<{
        staffId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.service.dto.GroomingStaffUpcomingAppointmentCountDto'];
    };
    updateAppointmentStatus: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.status.StatusUpdateParams']>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.UpdateStatusResultVO'];
    };
    revertAppointmentStatus: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.status.StatusRevertParams']>;
      Res: boolean;
    };
    getTicketComment: {
      Req: Partial<
        {
          appointmentId: number;
        } & {
          petId?: string | number;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.appointment.comment.TicketCommentsDTO'];
    };
    getTicketCommentV2: {
      Req: Partial<
        {
          appointmentId?: number;
        } & {
          customerId: number;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.appointment.comment.TicketCommentsDTO'];
    };
    transferAppointment_1: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.TransferAppointmentParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    unconfirm: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.EditIdParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    toWaiting: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.EditIdParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    addGroomingAppointmentWaiting: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.AppointmentParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.AddResultDTO'];
    };
    queryAppointmentWaitingList: {
      Req: Partial<
        {
          endTime?: string;
        } & {
          startTime?: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.GroomingAppointmentWaitingListDTO'];
    };
    modifyGroomingAppointmentWaiting: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.AppointmentParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    queryGroomingBookOnlineAppointment: {
      Req: Partial<
        {
          orderBy?: string;
        } & {
          orderType?: string;
        } & {
          pageNum?: number;
        } & {
          pageSize?: number;
        } & {
          type?: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.github.pagehelper.PageInfoCom.moego.server.grooming.dto.GroomingBookingDTO'];
    };
    queryGroomingBookOnlineAppointmentV2: {
      Req: Partial<
        {
          orderBy?: string;
        } & {
          orderType?: string;
        } & {
          pageNum?: number;
        } & {
          pageSize?: number;
        } & {
          type?: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.github.pagehelper.PageInfoCom.moego.server.grooming.dto.GroomingBookingDTO'];
    };
    checkAvailableDist: {
      Req: Partial<
        {
          businessName: string;
        } & {
          lat: string;
        } & {
          lng: string;
        } & {
          zipcode: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.ob.IsAvailableDto'];
    };
    getBusinessPreference_1: {
      Req: Partial<{
        businessName: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.business.dto.MoeBusinessDto'];
    };
    getRelevantBiz_1: {
      Req: Partial<{
        businessName: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.BookOnlineLocationDTO'];
    };
    getGallery_1: {
      Req: Partial<{
        businessName: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.ob.GalleryListDto'];
    };
    getInfo_1: {
      Req: Partial<{
        businessName: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.ob.InfoDto'];
    };
    showServicePreAuth: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] &
          components['schemas']['com.moego.server.grooming.params.PreAuthAmountParams']
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.PreAuthAmountDTO'];
    };
    calculateServiceDeposit: {
      Req: Partial<
        (components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] & {
          /** @deprecated */
          businessName?: string;
        }) &
          components['schemas']['com.moego.server.grooming.params.PrepayAmountParams']
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.PrepayAmountDTO'];
    };
    getOBDepositDetail: {
      Req: Partial<{
        guid: string;
      }>;
      Res: components['schemas']['com.moego.server.grooming.dto.BookOnlineDepositForClientDTO'];
    };
    getProfile_2: {
      Req: Partial<{
        businessName: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.ob.ProfileDto'];
    };
    getQuestionForClient_1: {
      Req: Partial<
        {
          businessName: string;
        } & {
          type: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.ob.QuestionListDto'];
    };
    queryBookOnlinePetServicesForClient_1: {
      Req: Partial<
        {
          businessName: string;
        } & components['schemas']['com.moego.server.grooming.params.QueryServiceByPetIdsParams']
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.ob.OBServiceListDto'];
    };
    checkSubmitInfo: {
      Req: Partial<
        (components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] & {
          /** @deprecated */
          businessName?: string;
        }) &
          components['schemas']['com.moego.server.grooming.params.BookOnlineSubmitParams']
      >;
      Res: boolean;
    };
    getCustomerLastAppointmentForBookOnline: {
      Req: Partial<
        {
          businessName: string;
        } & {
          customerId: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.ob.LastApptDto'];
    };
    getGallery: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.mapperbean.MoeBookOnlineGallery'];
    };
    addGalleryImage: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.MoeBookOnlineGalleryParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.AddResultDTO'];
    };
    deleteGalleryImage: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.MoeBookOnlineGalleryBatchParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    batchStarGalleryImage: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.MoeBookOnlineGalleryBatchParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    updatePaymentGroupSetting: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.ob.BusinessBookOnlinePaymentParams']>;
      Res: {};
    };
    deletePaymentGroupSetting: {
      Req: Partial<{}>;
      Res: {};
    };
    getPetLimit: {
      Req: Partial<{
        limitIdList: (string | number)[];
      }>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.BookOnlinePetLimitResponse'];
    };
    savePetLimit: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.BookOnlinePetLimitRequest']>;
      Res: string | number;
    };
    getProfile_1: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.mapperbean.MoeBookOnlineProfile'];
    };
    updateProfile: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.MoeBookOnlineProfileParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    getQuestion: {
      Req: Partial<{
        type: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.ob.QuestionListDto'];
    };
    updateQuestion: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.BookOnlineQuestionParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    createQuestion: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.BookOnlineQuestionParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.AddResultDTO'];
    };
    deleteQuestion: {
      Req: Partial<{
        questionId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    updateQuestionSort: {
      Req: Partial<components['schemas']['com.moego.common.params.SortIdListParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    getQuestionList: {
      Req: Partial<{
        type: number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.web.dto.ob.QuestionListDto'];
    };
    updateQuestionList: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.BookOnlineQuestionListParams']>;
      Res: {};
    };
    getSettingInfo: {
      Req: Partial<{
        withoutClientNotification?: boolean;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.ob.SettingInfoDto'];
    };
    updateSettingInfo: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.BusinessBookOnlineParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    updateMobileGroomingSetting: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.ob.MobileGroomingParams']>;
      Res: number;
    };
    updateNotificationSetting: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.ob.NotificationParams']>;
      Res: number;
    };
    updateServiceSetting: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.ob.ServiceParams']>;
      Res: number;
    };
    getStaffOnlineAvailability: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.dto.BookOnlineStaffAvailabilityDTO'][];
    };
    modifyStaffOnlineAvailability: {
      Req: Partial<components['schemas']['com.moego.server.grooming.dto.BookOnlineStaffAvailabilityDTO']>;
      Res: boolean;
    };
    modifyStaffOnlineAvailabilityBatch: {
      Req: Partial<components['schemas']['com.moego.server.grooming.dto.BookOnlineStaffAvailabilityDTO'][]>;
      Res: boolean;
    };
    getSyncWithWorkingHourStatus: {
      Req: Partial<{}>;
      Res: boolean;
    };
    modifySyncWithWorkingHourStatus: {
      Req: Partial<{}>;
      Res: {};
    };
    getAvailableStaffTime: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.ob.AvailableStaffTimeListDto'];
    };
    saveAvailableStaffTime: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.MoeBookOnlineStaffTimeParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.CommonResultDto'];
    };
    getStaffAvailableTimeV2_1: {
      Req: Partial<
        {
          businessName: string;
        } & components['schemas']['com.moego.server.grooming.params.ObAvailableTimeRequest']
      >;
      Res: {
        [key: string]: components['schemas']['com.moego.server.grooming.service.dto.OBAvailableTimeDto'] | undefined;
      };
    };
    selectByZipCodes: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] & {
          zipCodes: string[];
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.mapperbean.MoeZipcode'][];
    };
    selectByZipCodes_1: {
      Req: Partial<{
        zipCodes: string[];
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.mapperbean.MoeZipcode'];
    };
    searchByPrefix: {
      Req: Partial<{
        prefix: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.mapperbean.MoeZipcode'];
    };
    getDayList: {
      Req: Partial<
        {
          endDate?: string;
        } & {
          filterNoStaff?: boolean;
        } & {
          filterNoStartTime?: boolean;
        } & {
          isWaitingList?: boolean;
        } & {
          startDate: string;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.calendarcard.CalendarCardDTO'][];
    };
    getMonthlyList: {
      Req: Partial<
        {
          endDate?: string;
        } & {
          filterNoStaff?: boolean;
        } & {
          filterNoStartTime?: boolean;
        } & {
          isWaitingList?: boolean;
        } & {
          startDate: string;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.calendarcard.MonthlyViewDTO'][];
    };
    reschedule_1: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.calendar.CardRescheduleParams']>;
      Res: boolean;
    };
    getApptDetail: {
      Req: Partial<{
        bookingId: string;
      }>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.client.ClientApptDetailVO'];
    };
    rescheduleAppt: {
      Req: Partial<components['schemas']['com.moego.server.grooming.service.dto.client.ClientUpdateApptDTO']>;
      Res: boolean;
    };
    cancelAppt: {
      Req: Partial<{
        bookingId: string;
      }>;
      Res: boolean;
    };
    rescheduleApptV2: {
      Req: Partial<components['schemas']['com.moego.server.grooming.service.dto.client.ClientUpdateApptDTO']>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.client.UpdateApptVO'];
    };
    getComingNextAppt: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.client.ClientApptDetailVO'];
    };
    getLastFinishedAppt: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.client.ClientApptDetailVO'];
    };
    getApptList: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.query.ClientApptQuery']>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.client.ClientApptListVO'];
    };
    updateServiceCategoryBatch: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.ServiceCategoryBatchUpdateParams']>;
      Res: components['schemas']['com.moego.server.grooming.service.dto.ServiceCategoryListDto'][];
    };
    updateUpdateService_1: {
      Req: Partial<components['schemas']['com.moego.server.grooming.service.dto.CompanyServiceUpdateDto']>;
      Res: {};
    };
    companyCreateService: {
      Req: Partial<components['schemas']['com.moego.server.grooming.service.dto.ServiceSaveDto']>;
      Res: components['schemas']['com.moego.server.grooming.web.dto.service.AddServiceResultDto'];
    };
    getCompanyServiceByPage: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.PetServicePageParams']>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.PetServicePageVO'];
    };
    appointmentCount: {
      Req: Partial<{
        customerId: number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.dto.CustomerAppointmentNumInfoDTO'];
    };
    queryGroomingCustomerAppointment: {
      Req: Partial<
        {
          appointmentTime: string;
        } & {
          customerId: number;
        } & {
          /** @description false only filters grooming, true may include */
          filterGroomingOnlyService?: boolean;
        } & {
          orderType?: number;
        } & {
          pageNum?: number;
        } & {
          pageSize?: number;
        } & {
          skipCancel: boolean;
        } & {
          time: number;
        } & {
          type: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.service.dto.CustomerAppointmentListDTO'];
    };
    queryGroomingCustomerAppointmentV2: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.GroomingCustomerQueryVO']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.service.dto.CustomerAppointmentListDTO'];
    };
    modifyCustomerService: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.CustomerSaveServiceVo']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    deleteCustomerService: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.CustomerDeleteSaveServiceVo']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    queryCustomerService: {
      Req: Partial<
        {
          customerId: number;
        } & {
          petId: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.service.dto.MoeGroomingServiceDto'];
    };
    queryDataRuleInfo: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.mapperbean.MoeGroomingDataRule'];
    };
    getDistanceMatrixUsageSummary: {
      Req: Partial<
        {
          cleanData?: boolean;
        } & {
          dumpFile?: boolean;
        } & {
          fillData?: boolean;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.web.vo.DistanceMatrixUsageSummaryVo'];
    };
    getDepositAndInvoiceDetailByGUID: {
      Req: Partial<{
        depositGuid: string;
      }>;
      Res: components['schemas']['com.moego.server.grooming.dto.DepositDto'];
    };
    checkInvoiceGuid: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.DepositVo']>;
      Res: components['schemas']['com.moego.common.dto.GuidDto'];
    };
    getDepositAndInvoiceDetailByInvoiceId: {
      Req: Partial<{
        invoiceId: number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.dto.DepositDto'];
    };
    checkAvailableDist_2: {
      Req: Partial<
        {
          lat: string;
        } & {
          lng: string;
        } & {
          zipcode: string;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.web.dto.ob.IsAvailableDto'];
    };
    getExchangeRate: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.service.dto.ExchangeRateDto'];
    };
    watchEventWebHook: {
      Req: Partial<{}>;
      Res: {};
    };
    saveOauthResult: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.GoogleCalendarOauthVo']>;
      Res: components['schemas']['com.moego.server.grooming.service.dto.GoogleCalendarAuthInfoDto'];
    };
    queryGoogleCalendarSetting: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.service.dto.GoogleCalendarSettingDto'];
    };
    updateGooGleCalendarSetting: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.UpdateCalendarSyncSettingVo']>;
      Res: boolean;
    };
    createGooGleCalendarSetting: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.CreateCalendarSyncSettingVo']>;
      Res: components['schemas']['com.moego.server.grooming.service.dto.GoogleCalendarSettingDto'];
    };
    drivingInfoCalculation: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.DrivingInfoCalculationParams']>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.DrivingInfoCalculationVo'];
    };
    getPlace: {
      Req: Partial<
        {
          lat: string;
        } & {
          lng: string;
        }
      >;
      Res: string;
    };
    deleteById: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.DeleteResult'];
    };
    getGroomingReportInfoForClient: {
      Req: Partial<
        {
          reportId?: number;
        } & {
          uuid: string;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO'];
    };
    addUpGroomingReportOpenedCount: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportViewCountParams']
      >;
      Res: boolean;
    };
    getGroomingReportInfo: {
      Req: Partial<
        {
          groomingId: number;
        } & {
          petId: number;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportInfoForInputDTO'];
    };
    saveGroomingReportInfo: {
      Req: Partial<
        {
          /** @description modify type: 1-save as draft, 2-submit */
          type: number;
        } & components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportInfoParams']
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportInfoForInputDTO'];
    };
    initGroomingReport: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportSettingDTO'];
    };
    getPreviewData: {
      Req: Partial<
        {
          reportId?: number;
        } & {
          themeCode?: string;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportPreviewDataDTO'];
    };
    getPreviewUrl: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportPreviewParams']
      >;
      Res: string;
    };
    getGroomingReportRecords: {
      Req: Partial<{
        groomingId: number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportRecordDTO'][];
    };
    getGroomingReportSetting: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportSettingDTO'];
    };
    saveGroomingReportSetting: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportSettingParams']
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportSettingDTO'];
    };
    getGroomingReportTemplate: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportTemplateDTO'];
    };
    saveGroomingReportTemplate: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportTemplateParams']
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportTemplateDTO'];
    };
    getGroomingReportThemeConfigList: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportThemeConfigDTO'][];
    };
    applyPackage: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.InvoiceIdVo']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.InvoiceSummaryDTO'];
    };
    removeApplyPackageService: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.ApplyPackageServiceVo']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.InvoiceSummaryDTO'];
    };
    queryInvoiceByInvoiceGuid: {
      Req: Partial<{
        guid: string;
      }>;
      Res: components['schemas']['com.moego.server.grooming.dto.InvoicePayOnlineDTO'];
    };
    getInvoiceGuid: {
      Req: Partial<
        {
          invoiceId: number;
        } & {
          requiredProcessingFee?: boolean;
        }
      >;
      Res: components['schemas']['com.moego.common.dto.GuidDto'];
    };
    setCompleted: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.InvoiceIdVo']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.InvoiceSummaryDTO'];
    };
    confirmPayment: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.InvoiceIdVo']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.InvoiceSummaryDTO'];
    };
    getById: {
      Req: Partial<{
        invoiceId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.InvoiceSummaryDTO'];
    };
    queryInvoiceByInvoiceIdEncode: {
      Req: Partial<{
        id: string;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.InvoicePayOnlineDTO'];
    };
    getWithPaymentById: {
      Req: Partial<{
        invoiceId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.InvoiceSummaryDTO'];
    };
    createNoShowInvoice: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.CreateNoShowInvoiceVo']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.InvoiceSummaryDTO'];
    };
    getOrderDetail: {
      Req: Partial<{
        orderId: number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.dto.OrderInvoiceSummaryDTO'];
    };
    hasAvailablePackage: {
      Req: Partial<{
        invoiceId: number;
      }>;
      Res: boolean;
    };
    save: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.InvoiceIdVo']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.InvoiceSummaryDTO'];
    };
    createNoShowInvoiceAndPreAuth: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.CreateNoShowInvoiceVo']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.InvoiceSummaryDTO'];
    };
    revertInvoice: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.ApptReopenParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    getInvoiceSendUrl: {
      Req: Partial<{
        invoiceId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.service.dto.GroomingInvoiceSendUrlDto'];
    };
    setDiscount: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.InvoiceAmountVo']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.InvoiceSummaryDTO'];
    };
    setTips: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.InvoiceAmountVo']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.InvoiceSummaryDTO'];
    };
    removeApplyPackageServiceV2: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.DeleteApplyPackageServiceVo']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.InvoiceSummaryDTO'];
    };
    getOrderDetailV2: {
      Req: Partial<{
        orderId: number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.dto.OrderInvoiceSummaryDTO'];
    };
    findAll: {
      Req: Partial<
        {
          endTimeSec?: string | number;
        } & {
          startTimeSec?: string | number;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.web.vo.ob.AbandonedClientFilterVO'][];
    };
    bulkAddClient: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.SearchAbandonedClientParam']>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.ob.BulkAddClientVO'];
    };
    bulkDeleteAbandonedRecords: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.SearchAbandonedClientParam']>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.ob.BulkDeleteAbandonedVO'];
    };
    export: {
      Req: Partial<
        {
          endTimeSec?: string | number;
        } & {
          fileName?: string;
        } & {
          sheetName?: string;
        } & {
          startTimeSec?: string | number;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.web.vo.ExportVO'];
    };
    search: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.SearchAbandonedClientParam']>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.AbandonedClientPageDTOCom.moego.server.grooming.web.vo.ob.AbandonedClientVO'];
    };
    getAbandonClientRecord: {
      Req: Partial<
        {
          action?: string;
        } & {
          id: string;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO'];
    };
    deleteAbandonClientRecord: {
      Req: Partial<{
        id: string;
      }>;
      Res: {};
    };
    updateAbandonClientRecordAddPets: {
      Req: Partial<{
        id: string;
      }>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO'];
    };
    getBriefOBProfileList: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.web.result.ob.BriefOBProfileResult$BriefOBProfileDTO'][];
    };
    getLandingPageConfig: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO'];
    };
    updateLandingPageConfig: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.OBLandingPageConfigParams']>;
      Res: {};
    };
    listMetrics: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.OBMetricsParams']>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.ob.OBMetricVO'][];
    };
    getLandingPageMergeProfile: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.ob.OBLandingPageMergeVO'];
    };
    updateLandingPageMergeProfile: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.OBLandingPageMergeParams']>;
      Res: {};
    };
    updateClientPetsDetail: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.ob.CustomerPetsUpdateParams']>;
      Res: {};
    };
    getClientPetsDetail: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.ob.CustomerPetsParams']>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.ob.OBClientPetsDetailVO'];
    };
    deleteClientPetsRequest: {
      Req: Partial<{
        customerId: number;
      }>;
      Res: boolean;
    };
    collectAbandonInfo: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] &
          components['schemas']['com.moego.server.grooming.web.params.OBAbandonParams']
      >;
      Res: {};
    };
    getCustomerAppointmentForBookOnline_1: {
      Req: Partial<
        {
          bookingId: string;
        } & components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams']
      >;
      Res: components['schemas']['com.moego.server.grooming.service.dto.ob.OBClientApptDTO'];
    };
    getCustomerLastAppointmentForBookOnline_1: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] & {
          businessId: number;
        } & {
          businessName: string;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.service.dto.ob.OBLastApptVO'];
    };
    getBusinessOBProfileAndConfig: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams']>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.ob.OBLandingPageConfigClientVO'];
    };
    getInfo: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] & {
          businessName: string;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.web.dto.ob.InfoDto'];
    };
    getRelevantBiz: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] & {
          businessId: number;
        } & {
          businessName: string;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.web.vo.ob.OBBusinessLocationVO'][];
    };
    getBusinessPreference: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] & {
          businessId: number;
        } & {
          businessName: string;
        }
      >;
      Res: components['schemas']['com.moego.server.business.dto.OBBusinessInfoDTO'];
    };
    getProfile: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] & {
          businessId: number;
        } & {
          businessName: string;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.web.dto.ob.ProfileDto'];
    };
    getBusinessOBServices: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] & {
          type: number;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.web.vo.ob.OBLandingPageServiceCategoryVO'][];
    };
    getOBAvailableStaffListByService: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] &
          components['schemas']['com.moego.server.grooming.web.params.OBBusinessStaffParams']
      >;
      Res: components['schemas']['com.moego.server.grooming.web.vo.ob.OBBusinessStaffVO'][];
    };
    checkAvailableDist_1: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] & {
          businessId: number;
        } & {
          businessName: string;
        } & {
          lat: string;
        } & {
          lng: string;
        } & {
          zipcode: string;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.web.dto.ob.IsAvailableDto'];
    };
    getCustomerAppointmentForBookOnline: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] & {
          reportId: number;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.service.dto.ob.OBClientApptDTO'];
    };
    getQuestionForClient: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] & {
          businessId: number;
        } & {
          businessName: string;
        } & {
          type: number;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.web.dto.ob.QuestionListDto'];
    };
    queryBookOnlinePetServicesForClient: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] &
          components['schemas']['com.moego.server.grooming.web.params.OBServiceParams']
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.ob.OBServiceListDto'];
    };
    bookOnlineSubmit: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] &
          components['schemas']['com.moego.server.grooming.web.params.OBClientInfoParams']
      >;
      Res: components['schemas']['com.moego.server.grooming.web.dto.ob.OBSubmitResultDTO'];
    };
    getStaffAvailableTimeV2: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] &
          components['schemas']['com.moego.server.grooming.web.params.OBTimeSlotParams']
      >;
      Res: {
        [key: string]: components['schemas']['com.moego.server.grooming.service.dto.OBAvailableTimeDto'] | undefined;
      };
    };
    getOBRequestDetail: {
      Req: Partial<{
        id: number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.ob.OBRequestDetailVO'];
    };
    submitBookingRequest: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] &
          components['schemas']['com.moego.server.grooming.web.params.SubmitBookingRequestParams']
      >;
      Res: components['schemas']['com.moego.server.grooming.web.vo.SubmitBookingRequestResult'];
    };
    getTipSplitDetail: {
      Req: Partial<{
        orderId: string | number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.dto.TipSplitDetailDTO'];
    };
    previewTipSplitDetail: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.PreviewSplitTipParams']>;
      Res: components['schemas']['com.moego.server.grooming.dto.TipSplitDetailDTO'];
    };
    isInWhitelist: {
      Req: Partial<{
        businessId: number;
      }>;
      Res: boolean;
    };
    deleteCustomerPackage: {
      Req: Partial<{
        id: number;
      }>;
      Res: boolean;
    };
    queryCustomerPackageInfo: {
      Req: Partial<components['schemas']['com.moego.server.grooming.service.params.QueryCustomerPackageInfoParam']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.GroomingPackageInfoDTO'];
    };
    queryCustomerPackageList: {
      Req: Partial<{
        customerId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.GroomingPackageDTO'];
    };
    usePackageForGrooming: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.PackageUsedParams'][]>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    queryCustomerPackageByServiceId: {
      Req: Partial<
        {
          customerId: number;
        } & {
          serviceId: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.GroomingPackageServiceInfoDTO'];
    };
    getRemainPetCountWithin: {
      Req: Partial<
        {
          endDate: string;
        } & {
          queryAll?: boolean;
        } & {
          startDate?: string;
        }
      >;
      Res: number;
    };
    queryPetDetailList: {
      Req: Partial<
        {
          appointmentTime?: string;
        } & {
          endTime?: string;
        } & {
          /** @description false only filters grooming, true may include */
          filterGroomingOnlyService?: boolean;
        } & {
          filterNoStaff?: boolean;
        } & {
          filterNoStartTime?: boolean;
        } & {
          isWaitingList: number;
        } & {
          startTime?: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.GroomingPetServiceListInfoDTO'];
    };
    queryPetDetailListMonthly: {
      Req: Partial<
        {
          endTime: string;
        } & {
          /** @description false only filters grooming, true may include */
          filterGroomingOnlyService?: boolean;
        } & {
          filterNoStaff?: boolean;
        } & {
          filterNoStartTime?: boolean;
        } & {
          isWaitingList: number;
        } & {
          startTime: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.GroomingPetServiceListInfoDTO'];
    };
    editServiceTime: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.EditPetDetailParams'][]>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    getPetLastService: {
      Req: Partial<{
        petId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.PetLastServiceDTO'];
    };
    getActivityCardInfoByDate: {
      Req: Partial<
        {
          date: string;
        } & {
          isPetsCheckedInOnly?: boolean;
        } & {
          serviceItemTypes?: number[];
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.printcard.ActivityCardDetail'];
    };
    getPrintCardInfoByDate: {
      Req: Partial<{
        date: string;
      }>;
      Res: components['schemas']['com.moego.server.grooming.dto.printcard.PrintCardDetail'][];
    };
    getPrintCardInfoByGroomingId: {
      Req: Partial<{
        groomingId: string | number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.dto.printcard.PrintCardDetail'][];
    };
    getStayCardInfoByDate: {
      Req: Partial<
        {
          date: string;
        } & {
          isPetsCheckedInOnly?: boolean;
        } & {
          serviceItemTypes?: number[];
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.printcard.StayCardDetail'][];
    };
    getStayCardInfoByGroomingId: {
      Req: Partial<{
        groomingId: string | number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.dto.printcard.StayCardDetail'][];
    };
    getBusinessConnectAccount: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.service.dto.QBAccountReturnDto'];
    };
    createQbSetting: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.QBCreateConnectVo']>;
      Res: components['schemas']['com.moego.server.grooming.service.dto.QBConnectReturnDto'];
    };
    getConnectOAuthUrl: {
      Req: Partial<number[]>;
      Res: string;
    };
    getInvoiceSyncStatus: {
      Req: Partial<{
        invoiceId: number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.service.dto.QbInvoiceStatusDto'];
    };
    getQbConnectSetting: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.service.dto.QBBusinessSettingDto'];
    };
    createBusinessQbProfile: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.QBSettingUpdateVo']>;
      Res: components['schemas']['com.moego.common.dto.CommonResultDto'];
    };
    getQbConnectSettingByBusinessId: {
      Req: Partial<{
        businessId: number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.service.dto.QBBusinessSettingDto'];
    };
    getQbConnectSettingByCompanyId: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.service.dto.QBBusinessSettingDto'][];
    };
    qbCreateSetting: {
      Req: Partial<{
        connectId: number;
      }>;
      Res: components['schemas']['com.moego.common.dto.CommonResultDto'];
    };
    qbSetup: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.QbSetUpVo']>;
      Res: string;
    };
    deleteAllInvoice: {
      Req: Partial<{
        businessId: number;
      }>;
      Res: string;
    };
    syncDeletedCustomerTask: {
      Req: Partial<
        {
          businessId: number;
        } & {
          endTime: string | number;
        } & {
          startTime: string | number;
        }
      >;
      Res: string;
    };
    migrationQBData: {
      Req: Partial<
        {
          businessId: number;
        } & {
          endDate: string;
        }
      >;
      Res: string;
    };
    syncGroomingAppointment: {
      Req: Partial<{
        groomingId: number;
      }>;
      Res: boolean;
    };
    syncInvoice: {
      Req: Partial<{
        invoiceId: number;
      }>;
      Res: string;
    };
    deleteInvoice: {
      Req: Partial<{
        invoiceId: number;
      }>;
      Res: string;
    };
    refreshMetadata: {
      Req: Partial<{
        mode?: number;
      }>;
      Res: string;
    };
    syncTask: {
      Req: Partial<{}>;
      Res: string;
    };
    getRepeatAppointment: {
      Req: Partial<{
        repeatId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.MoeRepeatInfoDTO'];
    };
    checkConflictDay: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.AppointmentCheckParams'][]>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.ConflictDayInfoDTO'];
    };
    previewRepeatDay_1: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.PreviewRepeatParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.ConflictDayInfoDTO'];
    };
    queryRepeatRule_1: {
      Req: Partial<{
        repeatId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.mapperbean.MoeGroomingRepeat'];
    };
    modifyRepeatRule_1: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.EditRepeatParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    addRepeatRule_1: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.AddRepeatParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.AddResultDTO'];
    };
    queryAllBusinessRepeatRule: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.mapperbean.MoeGroomingRepeat'][];
    };
    modifyFullRepeatRule: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.MoeGroomingRepeatParam']>;
      Res: {};
    };
    getRepeatWithSSAvailability: {
      Req: Partial<{}>;
      Res: number;
    };
    previewRepeatDayWithSS: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.RepeatWithSSParams']>;
      Res: components['schemas']['com.moego.server.grooming.dto.ConflictDayInfoDTO'][];
    };
    getRepeatAppointmentList: {
      Req: Partial<
        {
          includeFinish?: boolean;
        } & {
          repeatId: number;
        } & {
          type: number;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.RepeatAppointmentDto'][];
    };
    saveRepeatAppointmentList: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.SaveRepeatAppointmentListParams']>;
      Res: components['schemas']['com.moego.server.grooming.dto.SaveRepeatAppointmentListResultDTO'];
    };
    checkConflict_1: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.appointment.conflict.BatchConflictCheckParams']
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.ConflictInfoDTO'][];
    };
    previewRepeatDay: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.PreviewRepeatParams']>;
      Res: components['schemas']['com.moego.server.grooming.dto.RepeatPreviewSummaryDTO'];
    };
    queryRepeatRule: {
      Req: Partial<{
        repeatId: number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.dto.MoeRepeatInfoDTO'];
    };
    modifyRepeatRule: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.SaveRepeatParams']>;
      Res: boolean;
    };
    addRepeatRule: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.SaveRepeatParams']>;
      Res: components['schemas']['com.moego.server.grooming.dto.AddResultDTO'];
    };
    getReportAppointments: {
      Req: Partial<
        {
          endDate: string;
        } & {
          startDate: string;
        } & {
          type: PathsGroomingReportMobileApptGetParametersQueryType;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.report.ReportAppointmentResponse'];
    };
    getReportApptsNumber: {
      Req: Partial<
        {
          endDate: string;
        } & {
          startDate: string;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.report.ReportApptsNumberDTO'];
    };
    getPaymentMethodReport: {
      Req: Partial<
        {
          endDate: string;
        } & {
          staffId: number;
        } & {
          startDate: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.report.ReportPaymentSummaryDto'];
    };
    getMobileDashboardSummary: {
      Req: Partial<
        {
          endDate: string;
        } & {
          startDate: string;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.report.MobileSummaryDTO'];
    };
    updateService: {
      Req: Partial<components['schemas']['com.moego.server.grooming.service.dto.ServiceUpdateDto']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    createService: {
      Req: Partial<components['schemas']['com.moego.server.grooming.service.dto.ServiceSaveDto']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.service.AddServiceResultDto'];
    };
    deleteService: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.DeleteIdVo']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    getFromClient: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams']>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.ServiceAreaPicCacheVO'];
    };
    createOrUpdateFromClient: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] &
          components['schemas']['com.moego.server.grooming.service.params.ServiceAreaPicCacheOperationParams']
      >;
      Res: components['schemas']['com.moego.server.grooming.web.vo.ServiceAreaPicCacheVO'];
    };
    delete: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.DeleteResult'];
    };
    getServicesByIds: {
      Req: Partial<number[]>;
      Res: components['schemas']['com.moego.server.grooming.dto.MoeGroomingServiceDTO'][];
    };
    updateUpdateService: {
      Req: Partial<components['schemas']['com.moego.server.grooming.service.dto.CompanyServiceOBSettingUpdateDto']>;
      Res: {};
    };
    getServiceByPage: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.PetServicePageParams']>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.PetServicePageVO'];
    };
    serviceSort: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.SortIdListVo']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    updateServiceForApptInvoice: {
      Req: Partial<components['schemas']['com.moego.server.grooming.service.dto.ServiceTaxUpdateForApptDto']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    getEditServiceCategory: {
      Req: Partial<
        {
          serviceItemType?: number;
        } & {
          type: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.service.ServiceCategoryListResultDto'];
    };
    updateServiceCategory: {
      Req: Partial<components['schemas']['com.moego.server.grooming.service.dto.ServiceCategoryUpdateDto']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    createServiceCategory: {
      Req: Partial<components['schemas']['com.moego.server.grooming.service.dto.ServiceCategorySaveDto']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.service.AddServiceCategoryResultDto'];
    };
    deleteServiceCategory: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.DeleteIdVo']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    getEditServiceWithCategory: {
      Req: Partial<
        {
          inactive?: number;
        } & {
          serviceItemType?: number;
        } & {
          type: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.service.ServiceCategoryGroupedResultDto'];
    };
    serviceCategorySort: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.SortIdListVo']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    queryPetServices: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.QueryServiceParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.ServiceCategoryDTO'];
    };
    getBusinessSummary: {
      Req: Partial<
        {
          endDate: string;
        } & {
          startDate: string;
        }
      >;
      Res: components['schemas']['com.moego.server.grooming.web.vo.SummaryVo'];
    };
    queryCustomerUpComingAppointForClientShare: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.vo.UpcomingPreviewVo']>;
      Res: components['schemas']['com.moego.server.grooming.service.dto.CustomerAppointmentListDTO'];
    };
    updateActionTime: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.appointment.UpdateActionTimeParams']>;
      Res: {};
    };
    updateColorCode: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.appointment.EditAppointmentColorCodeParams']
      >;
      Res: {};
    };
    setMultiPetsStartTime: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.appointment.SetMultiPetsStartTime']>;
      Res: {};
    };
    getOperationList: {
      Req: Partial<{
        petDetailId: number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.result.ServiceOperationListResult'];
    };
    updatePet: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.appointment.EditPetParams']>;
      Res: {};
    };
    deletePet: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.appointment.DeletePetParams']>;
      Res: {};
    };
    updatePetsAndServices: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.appointment.EditAppointmentPetDetailsParams']
      >;
      Res: {};
    };
    quickAddAppointment: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.appointment.QuickAddAppointmentParam']>;
      Res: string | number;
    };
    modifyGroomingAppointmentRepeatV2: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.AppointmentRepeatModifyParams']>;
      Res: {};
    };
    reschedule: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.appointment.WaitListRescheduleParams']>;
      Res: string | number;
    };
    smartSchedule2: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.ss.SmartScheduleRequest']>;
      Res: components['schemas']['com.moego.server.grooming.dto.ss.SmartScheduleResultDto'];
    };
    getStaffUpcomingCount: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.appointment.StaffUpcomingAppointmentCountParams']
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.appointment.StaffUpcomingAppointmentCountDTO'];
    };
    getStaffUpcomingOperationCount: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.appointment.StaffUpcomingOperationCountParams']
      >;
      Res: components['schemas']['com.moego.server.grooming.dto.appointment.StaffUpcomingOperationCountDTO'];
    };
    transferAppointment: {
      Req: Partial<components['schemas']['com.moego.server.grooming.params.appointment.TransferAppointmentParamsV2']>;
      Res: boolean;
    };
    updateWaitList: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.waitlist.UpdateWaitListParam']>;
      Res: {};
    };
    addWaitList: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.waitlist.AddWaitListParam']>;
      Res: {};
    };
    deleteWaitList: {
      Req: Partial<{
        waitListId: string | number;
      }>;
      Res: {};
    };
    addWaitListFromAppointment: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.waitlist.FromAppointmentParam']>;
      Res: {};
    };
    getAvailableOnApptCancel: {
      Req: Partial<{
        appointmentId: string | number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.waitlist.AvailableInfoVO'];
    };
    calendarView: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.waitlist.CalendarViewParam']>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.waitlist.CalendarViewVO'][];
    };
    waitListDetail: {
      Req: Partial<{
        waitListId: string | number;
      }>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.waitlist.WaitListDetailVO'];
    };
    getWaitList: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.waitlist.GetWaitListParam']>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.waitlist.WaitListListVO'];
    };
    smartSchedule: {
      Req: Partial<components['schemas']['com.moego.server.grooming.web.params.waitlist.SmartScheduleParam']>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.waitlist.SmartScheduleListVO'];
    };
    transferNewWaitList: {
      Req: Partial<{}>;
      Res: number;
    };
    getSummary: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.grooming.web.vo.WebsiteSummaryVO'];
    };
  }
  export interface components {
    schemas: {
      'com.github.pagehelper.PageInfoCom.moego.server.grooming.dto.GroomingBookingDTO': {
        /** Format: int64 */
        endRow: string | number;
        hasNextPage: boolean;
        hasPreviousPage: boolean;
        isFirstPage: boolean;
        isLastPage: boolean;
        list: components['schemas']['com.moego.server.grooming.dto.GroomingBookingDTO'][];
        /** Format: int32 */
        navigateFirstPage: number;
        /** Format: int32 */
        navigateLastPage: number;
        navigatepageNums: number[];
        /** Format: int32 */
        navigatePages: number;
        /** Format: int32 */
        nextPage: number;
        /** Format: int32 */
        pageNum: number;
        /** Format: int32 */
        pages: number;
        /** Format: int32 */
        pageSize: number;
        /** Format: int32 */
        prePage: number;
        /** Format: int32 */
        size: number;
        /** Format: int64 */
        startRow: string | number;
        /** Format: int64 */
        total: string | number;
      };
      'com.moego.common.dto.BusinessPreferenceDto': {
        /** Format: int32 */
        autoReplyStatus: number;
        calendarFormat: string;
        /** Format: int32 */
        calendarFormatType: number;
        country: string;
        currencyCode: string;
        currencySymbol: string;
        dateFormat: string;
        /** Format: int32 */
        dateFormatType: number;
        /** Format: int32 */
        messageSendBy: number;
        needSendCode: boolean;
        /** Format: int32 */
        notificationSoundEnable: number;
        numberFormat: string;
        /** Format: int32 */
        numberFormatType: number;
        timeFormat: string;
        /** Format: int32 */
        timeFormatType: number;
        timezoneName: string;
        /** Format: int32 */
        timezoneSeconds: number;
        unitOfDistance: string;
        /** Format: int32 */
        unitOfDistanceType: number;
        unitOfWeight: string;
        /** Format: int32 */
        unitOfWeightType: number;
      };
      'com.moego.common.dto.CommonResultDto': {
        result: boolean;
      };
      'com.moego.common.dto.GuidDto': {
        guid: string;
        /** @description 是否需要更新 invoice 信息 */
        needRefreshInvoice: boolean;
      };
      /** @description Appointment basic info */
      'com.moego.common.dto.PageDTOCom.moego.server.grooming.web.vo.client.ClientApptVO': {
        dataList: components['schemas']['com.moego.server.grooming.web.vo.client.ClientApptVO'][];
        end: boolean;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        /** Format: int64 */
        total: string | number;
      };
      'com.moego.common.dto.PaymentSummary': {
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        invoiceId: number;
        module: string;
        paidAmount: number;
        payments: components['schemas']['com.moego.common.dto.PaymentSummary$PaymentDto'][];
        processingFee: number;
        refundedAmount: number;
        refunds: components['schemas']['com.moego.common.dto.PaymentSummary$PaymentRefundDto'][];
      };
      'com.moego.common.dto.PaymentSummary$PaymentDto': {
        amount: number;
        cardFunding: string;
        cardNumber: string;
        cardType: string;
        checkNumber: string;
        /** Format: int64 */
        createTime: string | number;
        description: string;
        expMonth: string;
        expYear: string;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        isDeposit: number;
        isOnline: boolean;
        isPrepay: boolean;
        merchant: string;
        method: string;
        /** Format: int64 */
        orderPaymentId: string | number;
        paidBy: string;
        processingFee: number;
        signature: string;
        /** Format: int32 */
        squarePaymentMethod: number;
        status: string;
        stripeAccountId: string;
        stripeClientSecret: string;
        stripeCustomerId: string;
        stripeIntentId: string;
        /** Format: int32 */
        stripePaymentMethod: number;
        stripeStatus: string;
        /** Format: int64 */
        updateTime: string | number;
        /** @description values: Stripe, Square or empty string */
        vendor: string;
      };
      'com.moego.common.dto.PaymentSummary$PaymentRefundDto': {
        amount: number;
        /** Format: int64 */
        createTime: string | number;
        error: string;
        /** Format: int32 */
        id: number;
        method: string;
        /** Format: int32 */
        methodId: number;
        module: string;
        /** Format: int32 */
        originPaymentId: number;
        reason: string;
        /** Format: int32 */
        refundId: number;
        /** Format: int64 */
        refundOrderPaymentId: string | number;
        status: string;
        stripeRefundId: string;
        /** Format: int64 */
        updateTime: string | number;
      };
      /** @description Sort by multiple field */
      'com.moego.common.params.PageQuery$SortQuery': {
        /**
         * @description Order type, asc, desc
         * @enum {string}
         */
        order: ComMoegoCommonParamsPageQuery$SortQueryOrder;
        /** @description Sort by field */
        sortBy: string;
      };
      'com.moego.common.params.SortIdListParams': {
        idList: number[];
      };
      'com.moego.common.params.VaccineParams': {
        /** @description Document url array */
        documentUrls?: string[];
        /** @description Expiration date, format: yyyy-MM-dd */
        expirationDate?: string;
        /**
         * Format: int32
         * @description Existing vaccine binding id
         */
        vaccineBindingId?: number;
        /** @description Document url json format, default [] */
        vaccineDocument?: string;
        /** Format: int32 */
        vaccineId: number;
      };
      'com.moego.common.response.ResponseResultCom.github.pagehelper.PageInfoCom.moego.server.grooming.dto.GroomingBookingDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.github.pagehelper.PageInfoCom.moego.server.grooming.dto.GroomingBookingDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.common.dto.CommonResultDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.common.dto.CommonResultDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.business.dto.MoeBusinessDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.business.dto.MoeBusinessDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.AddResultDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.AddResultDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.BusinessUpcomingDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.BusinessUpcomingDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.ConflictCheckDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.ConflictCheckDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.CustomerUpcomingBusinessDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.CustomerUpcomingBusinessDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.GroomingPackageInfoDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.GroomingPackageInfoDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.GroomingTicketDetailDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.GroomingTicketDetailDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.GroomingTicketWindowDetailDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.GroomingTicketWindowDetailDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.InvoicePayOnlineDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.InvoicePayOnlineDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.InvoiceSummaryDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.InvoiceSummaryDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.MoeRepeatInfoDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.MoeRepeatInfoDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.ob.OBServiceListDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.ob.OBServiceListDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.PetLastServiceDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.PetLastServiceDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.report.ReportAppointmentResponse': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.report.ReportAppointmentResponse'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.dto.ss.SmartScheduleVO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.ss.SmartScheduleVO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.mapperbean.MoeBookOnlineProfile': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.mapperbean.MoeBookOnlineProfile'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.mapperbean.MoeGroomingDataRule': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.mapperbean.MoeGroomingDataRule'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.mapperbean.MoeGroomingRepeat': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.mapperbean.MoeGroomingRepeat'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.service.dto.CustomerAppointmentListDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.service.dto.CustomerAppointmentListDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.service.dto.ExchangeRateDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.service.dto.ExchangeRateDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.service.dto.GroomingInvoiceSendUrlDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.service.dto.GroomingInvoiceSendUrlDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.service.dto.GroomingServiceUpcomingAppointmentCountDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.service.dto.GroomingServiceUpcomingAppointmentCountDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.service.dto.GroomingStaffUpcomingAppointmentCountDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.service.dto.GroomingStaffUpcomingAppointmentCountDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.ob.AvailableStaffTimeListDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.web.dto.ob.AvailableStaffTimeListDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.ob.GalleryListDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.web.dto.ob.GalleryListDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.ob.InfoDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.web.dto.ob.InfoDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.ob.IsAvailableDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.web.dto.ob.IsAvailableDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.ob.LastApptDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.web.dto.ob.LastApptDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.ob.ProfileDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.web.dto.ob.ProfileDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.ob.QuestionListDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.web.dto.ob.QuestionListDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.ob.SettingInfoDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.web.dto.ob.SettingInfoDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.service.AddServiceCategoryResultDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.web.dto.service.AddServiceCategoryResultDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.service.AddServiceResultDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.web.dto.service.AddServiceResultDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.service.ServiceCategoryGroupedResultDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.web.dto.service.ServiceCategoryGroupedResultDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.grooming.web.dto.service.ServiceCategoryListResultDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.web.dto.service.ServiceCategoryListResultDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.lang.Boolean': {
        /** Format: int32 */
        code: number;
        data: boolean;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.lang.Integer': {
        /** Format: int32 */
        code: number;
        /** Format: int32 */
        data: number;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.lang.String': {
        /** Format: int32 */
        code: number;
        data: string;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.BookOnlineLocationDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.BookOnlineLocationDTO'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.ConflictDayInfoDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.ConflictDayInfoDTO'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.GroomingAppointmentWaitingListDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.GroomingAppointmentWaitingListDTO'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.GroomingPackageDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.GroomingPackageDTO'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.GroomingPackageServiceInfoDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.GroomingPackageServiceInfoDTO'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.GroomingPetServiceListInfoDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.GroomingPetServiceListInfoDTO'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.HistoryCommentsDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.HistoryCommentsDTO'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.report.ReportPaymentSummaryDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.report.ReportPaymentSummaryDto'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.dto.ServiceCategoryDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.dto.ServiceCategoryDTO'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.mapperbean.MoeBookOnlineGallery': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.mapperbean.MoeBookOnlineGallery'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.mapperbean.MoeZipcode': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.mapperbean.MoeZipcode'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.grooming.service.dto.MoeGroomingServiceDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.grooming.service.dto.MoeGroomingServiceDto'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.utils.Pagination': {
        /** Format: int32 */
        pageNum: number;
        /** Format: int32 */
        pageSize: number;
        /** Format: int32 */
        total: number;
      };
      /** @description Update biz settings working hours params */
      'com.moego.server.business.dto.BusinessWorkingHourDayDetailDTO': {
        friday: components['schemas']['com.moego.server.business.dto.TimeRangeDto'][];
        monday: components['schemas']['com.moego.server.business.dto.TimeRangeDto'][];
        saturday: components['schemas']['com.moego.server.business.dto.TimeRangeDto'][];
        sunday: components['schemas']['com.moego.server.business.dto.TimeRangeDto'][];
        thursday: components['schemas']['com.moego.server.business.dto.TimeRangeDto'][];
        tuesday: components['schemas']['com.moego.server.business.dto.TimeRangeDto'][];
        wednesday: components['schemas']['com.moego.server.business.dto.TimeRangeDto'][];
      };
      'com.moego.server.business.dto.CertainAreaDTO': {
        /** Format: int64 */
        areaId: string | number;
        areaName: string;
        colorCode: string;
      };
      'com.moego.server.business.dto.MoeBusinessDto': {
        address: string;
        address1: string;
        address2: string;
        addressCity: string;
        addressCountry: string;
        addressLat: string;
        addressLng: string;
        addressState: string;
        addressZipcode: string;
        /** Format: int32 */
        appType: number;
        avatarPath: string;
        /** @deprecated */
        bookOnlineName: string;
        businessName: string;
        calendarFormat: string;
        /** Format: int32 */
        calendarFormatType: number;
        /** Format: int32 */
        clockInOutEnable: number;
        /** Format: int32 */
        clockInOutNotify: number;
        /** Format: int32 */
        companyId: number;
        contactEmail: string;
        country: string;
        countryAlpha2Code: string;
        /** @deprecated */
        countryCode: string;
        /** Format: int64 */
        createTime: string | number;
        currencyCode: string;
        currencySymbol: string;
        dateFormat: string;
        /** Format: int32 */
        dateFormatType: number;
        facebook: string;
        google: string;
        /** Format: int32 */
        id: number;
        instagram: string;
        /** Format: int32 */
        isEnableAccessCode: number;
        knowFrom: string;
        /** Format: int32 */
        locations: number;
        /** Format: int32 */
        moveFrom: number;
        needSendCode: boolean;
        numberFormat: string;
        /** Format: int32 */
        numberFormatType: number;
        ownerEmail: string;
        phoneNumber: string;
        /** Format: int32 */
        primaryPayType: number;
        /** Format: int32 */
        retailEnable: number;
        /** Format: int32 */
        serviceAreaEnable: number;
        smartScheduleEndAddr: string;
        smartScheduleEndLat: string;
        smartScheduleEndLng: string;
        /** Format: int32 */
        smartScheduleMaxDist: number;
        /** Format: int32 */
        smartScheduleMaxTime: number;
        /** Format: int32 */
        smartScheduleServiceRange: number;
        smartScheduleStartAddr: string;
        smartScheduleStartLat: string;
        smartScheduleStartLng: string;
        /** Format: int32 */
        source: number;
        /** Format: int32 */
        staffMembers: number;
        timeFormat: string;
        /** Format: int32 */
        timeFormatType: number;
        timezoneName: string;
        /** Format: int32 */
        timezoneSeconds: number;
        unitOfDistance: string;
        /** Format: int32 */
        unitOfDistanceType: number;
        unitOfWeight: string;
        /** Format: int32 */
        unitOfWeightType: number;
        /** Format: int64 */
        updateTime: string | number;
        website: string;
        yelp: string;
      };
      'com.moego.server.business.dto.OBBusinessInfoDTO': {
        address: string;
        address1: string;
        address2: string;
        addressCity: string;
        addressCountry: string;
        addressLat: string;
        addressLng: string;
        addressState: string;
        addressZipcode: string;
        /** Format: int32 */
        appType: number;
        avatarPath: string;
        /** @deprecated */
        bookOnlineName: string;
        /** Format: int32 */
        businessMode: number;
        businessName: string;
        calendarFormat: string;
        /** Format: int32 */
        calendarFormatType: number;
        /** Format: int32 */
        clockInOutEnable: number;
        /** Format: int32 */
        clockInOutNotify: number;
        /** Format: int32 */
        companyId: number;
        country: string;
        countryAlpha2Code: string;
        /** @deprecated */
        countryCode: string;
        /** Format: int64 */
        createTime: string | number;
        currencyCode: string;
        currencySymbol: string;
        dateFormat: string;
        /** Format: int32 */
        dateFormatType: number;
        facebook: string;
        google: string;
        /** Format: int32 */
        id: number;
        instagram: string;
        /** Format: int32 */
        isEnableAccessCode: number;
        knowFrom: string;
        /** Format: int32 */
        locations: number;
        needSendCode: boolean;
        numberFormat: string;
        /** Format: int32 */
        numberFormatType: number;
        ownerEmail: string;
        phoneNumber: string;
        /** Format: int32 */
        primaryPayType: number;
        smartScheduleEndAddr: string;
        smartScheduleEndLat: string;
        smartScheduleEndLng: string;
        /** Format: int32 */
        smartScheduleMaxDist: number;
        /** Format: int32 */
        smartScheduleMaxTime: number;
        /** Format: int32 */
        smartScheduleServiceRange: number;
        smartScheduleStartAddr: string;
        smartScheduleStartLat: string;
        smartScheduleStartLng: string;
        /** Format: int32 */
        source: number;
        /** Format: int32 */
        staffMembers: number;
        tiktok: string;
        timeFormat: string;
        /** Format: int32 */
        timeFormatType: number;
        timezoneName: string;
        /** Format: int32 */
        timezoneSeconds: number;
        /** Format: int32 */
        unitOfDistanceType: number;
        unitOfWeight: string;
        /** Format: int32 */
        unitOfWeightType: number;
        /** Format: int64 */
        updateTime: string | number;
        website: string;
        yelp: string;
      };
      'com.moego.server.business.dto.PetSizeDTO': {
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        id: string | number;
        name: string;
        /** Format: int32 */
        weightHigh: number;
        /** Format: int32 */
        weightLow: number;
      };
      /** @description 偏好的时间段 */
      'com.moego.server.business.dto.TimeRangeDto': {
        /** Format: int32 */
        endTime: number;
        /** Format: int32 */
        startTime: number;
      };
      'com.moego.server.customer.dto.CustomerAddressDto': {
        address1: string;
        address2: string;
        city: string;
        country: string;
        /** Format: int32 */
        customerAddressId: number;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        isPrimary: number;
        isProfileRequestAddress: boolean;
        lat: string;
        lng: string;
        state: string;
        zipcode: string;
      };
      'com.moego.server.customer.dto.CustomerPetDetailDTO': {
        avatarPath: string;
        behavior: string;
        birthday: string;
        breed: string;
        /** Format: int32 */
        breedMix: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        customerId: number;
        deactivateReason: string;
        deleted: boolean;
        emergencyContactName: string;
        emergencyContactPhone: string;
        /** Format: int32 */
        evaluationStatus: number;
        /** Format: int32 */
        expiryNotification: number;
        fixed: string;
        /** Format: int32 */
        gender: number;
        hairLength: string;
        healthIssues: string;
        /** Format: int32 */
        lifeStatus: number;
        passedAway: boolean;
        petAppearanceColor: string;
        petAppearanceNotes: string;
        petBreed: string;
        /** Format: int32 */
        petId: number;
        petName: string;
        petPhotoImage: string;
        /** Format: int32 */
        petTypeId: number;
        /** Format: int64 */
        platformPetId: string | number;
        /** Format: int64 */
        playgroupId: string | number;
        /** Format: int32 */
        status: number;
        typeName: string;
        vaccineList: components['schemas']['com.moego.server.customer.dto.VaccineBindingRecordDto'][];
        vetAddress: string;
        vetName: string;
        vetPhone: string;
        weight: string;
      };
      'com.moego.server.customer.dto.MoePetBreedDTO': {
        /** Format: int32 */
        id: number;
        name: string;
        /** Format: int32 */
        petTypeId: number;
      };
      'com.moego.server.customer.dto.MoePetCodeInfoDTO': {
        codeNumber: string;
        color: string;
        description: string;
        /** Format: int32 */
        petCodeId: number;
        /** Format: int32 */
        petId: number;
      };
      /** @description preferred tips setting  */
      'com.moego.server.customer.dto.PreferredTipDto': {
        /** @description by amount时，设置tips金额 */
        amount: number;
        /** Format: int32 */
        enable: number;
        /**
         * Format: int32
         * @description by percentage时，设置百分比取值[1，100]
         */
        percentage: number;
        /** Format: int32 */
        tipType: number;
      };
      'com.moego.server.customer.dto.VaccineBindingRecordDto': {
        documentUrls: string[];
        expirationDate: string;
        /** Format: int32 */
        vaccineBindingId: number;
        /** Format: int32 */
        vaccineId: number;
        vaccineName: string;
      };
      'com.moego.server.customer.params.CustomerContactUpdateVo': {
        /** Format: int32 */
        customerContactId: number;
        email: string;
        firstName: string;
        /** Format: int32 */
        isPrimary: number;
        lastName: string;
        phoneNumber: string;
        title: string;
      };
      /** @description Customer pet update params */
      'com.moego.server.customer.params.CustomerPetUpdateParams': {
        avatarPath?: string;
        behavior?: string;
        birthday?: string;
        breed?: string;
        /** Format: int32 */
        breedMix?: number;
        emergencyContactName?: string;
        emergencyContactPhone?: string;
        /** Format: int32 */
        evaluationStatus?: number;
        /** Format: int32 */
        expiryNotification?: number;
        fixed?: string;
        /** Format: int32 */
        gender?: number;
        hairLength?: string;
        healthIssues?: string;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        lifeStatus?: number;
        petAppearanceColor?: string;
        petAppearanceNotes?: string;
        petCodeIdList?: number[];
        petName?: string;
        /** Format: int32 */
        petTypeId?: number;
        /** Format: int64 */
        playgroupId?: string | number;
        /** Format: int64 */
        rawCreateTime?: string | number;
        rawId?: string;
        /** Format: int64 */
        rawUpdateTime?: string | number;
        /** Format: int32 */
        status?: number;
        vaccineList?: components['schemas']['com.moego.common.params.VaccineParams'][];
        vetAddress?: string;
        vetName?: string;
        vetPhone?: string;
        weight?: string;
      };
      'com.moego.server.customer.params.EmergencyContact': {
        firstName: string;
        lastName: string;
        phone: string;
      };
      /** @description Customer profile update params */
      'com.moego.server.customer.params.UpdateCustomerInfoParams': {
        addPrimaryAddressFlag?: boolean;
        address1?: string;
        address2?: string;
        /** Format: int32 */
        addressId?: number;
        /** @description 对应复选框选项： 1-Message 2-email  3-phone call */
        apptReminderByList?: number[];
        avatarPath?: string;
        /** Format: date-time */
        birthday?: string;
        city?: string;
        clientColor?: string;
        contactList?: components['schemas']['com.moego.server.customer.params.CustomerContactUpdateVo'][];
        country?: string;
        /** Format: int32 */
        customerId: number;
        customerTagIdList?: number[];
        email?: string;
        firstName?: string;
        googleAdsStr?: string;
        /** Format: int32 */
        inactive?: number;
        /** Format: int32 */
        isBlockMessage?: number;
        /** Format: int32 */
        isBlockOnlineBooking?: number;
        /** Format: int32 */
        isUnsubscribed?: number;
        lastName?: string;
        lat?: string;
        lng?: string;
        phoneNumber?: string;
        /** @description customer prefer weekday, 0-Sunday, 1-6:Monday-Saturday */
        preferredDay?: number[];
        /** Format: int32 */
        preferredFrequencyDay?: number;
        /** Format: int32 */
        preferredFrequencyType?: number;
        /** Format: int32 */
        preferredGroomerId?: number;
        /** @description customer prefer time, [600, 900] */
        preferredTime?: number[];
        /** Format: int32 */
        primaryContactId?: number;
        referralSourceDesc?: string;
        /** Format: int32 */
        referralSourceId?: number;
        /** Format: int32 */
        sendAppAutoMessage?: number;
        /** Format: int32 */
        sendAutoEmail?: number;
        /** Format: int32 */
        sendAutoMessage?: number;
        /** @description 当share_range_type为3时，记录的所有apptIds，仅shareRangeType为3时生效 */
        shareApptIds?: number[];
        /** Format: int32 */
        shareApptStatus?: number;
        /** Format: int32 */
        shareRangeType?: number;
        /**
         * Format: int32
         * @description 不同type时的value
         */
        shareRangeValue?: number;
        source?: string;
        state?: string;
        /**
         * Format: int32
         * @deprecated
         */
        unconfirmedReminderBy?: number;
        zipcode?: string;
      };
      'com.moego.server.grooming.dto.AddResultDTO': {
        /** Format: int32 */
        id: number;
        result: boolean;
      };
      /** @description 不同的Split method对应的tips金额 */
      'com.moego.server.grooming.dto.AmountPercentagePair': {
        amount: number;
        /** Format: int32 */
        percentage: number;
      };
      'com.moego.server.grooming.dto.ApplicableServiceByCategoryDTO': {
        /** Format: int32 */
        categoryId: number;
        categoryName: string;
        serviceList: components['schemas']['com.moego.server.grooming.dto.PetServiceDTO'][];
      };
      'com.moego.server.grooming.dto.appointment.AppointmentOperationDTO': {
        /** Format: int64 */
        operationId: string | number;
        /** Format: int32 */
        staffId: number;
      };
      'com.moego.server.grooming.dto.appointment.comment.TicketComment': {
        appointmentDate: string;
        appointmentEndDate: string;
        /** Format: int64 */
        businessId: string | number;
        /** Format: int32 */
        createBy: number;
        /** Format: int64 */
        createTime: string | number;
        /** Format: int64 */
        id: string | number;
        note: string;
        operatorAvatar: string;
        operatorColorCode: string;
        operatorFirstName: string;
        operatorLastName: string;
        /** Format: int32 */
        updateBy: number;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.grooming.dto.appointment.comment.TicketCommentsDTO': {
        clientCommentForThisTicket: components['schemas']['com.moego.server.grooming.dto.appointment.comment.TicketComment'];
        commentForThisTicket: components['schemas']['com.moego.server.grooming.dto.appointment.comment.TicketComment'];
        historyComments: components['schemas']['com.moego.server.grooming.dto.appointment.comment.TicketComment'][];
      };
      'com.moego.server.grooming.dto.appointment.history.ActionHistoryDTO': {
        actions: components['schemas']['com.moego.server.grooming.dto.appointment.history.AppointmentActionHistory'][];
      };
      'com.moego.server.grooming.dto.appointment.history.AppointmentActionHistory': {
        /**
         * @description 操作类型
         * @enum {string}
         */
        actionType: ComMoegoServerGroomingDtoAppointmentHistoryAppointmentActionHistoryActionType;
        autoRolloverLog: components['schemas']['com.moego.server.grooming.dto.appointment.history.AutoRolloverLogDTO'];
        cancelLogDTO: components['schemas']['com.moego.server.grooming.dto.appointment.history.CancelLogDTO'];
        /** @description 取消的原因，仅针对 actionType 为 CANCEL 的情况有意义 */
        cancelReason: string;
        changeTimeLog: components['schemas']['com.moego.server.grooming.dto.appointment.history.ChangeTimeLogDTO'];
        createLogDTO: components['schemas']['com.moego.server.grooming.dto.appointment.history.CreateLogDTO'];
        customerReplyLogDTO: components['schemas']['com.moego.server.grooming.dto.appointment.history.CustomerReplyLogDTO'];
        notificationUpdateDTO: components['schemas']['com.moego.server.grooming.dto.appointment.history.NotificationUpdateDTO'];
        /** Format: int64 */
        operateTime: string | number;
        /** Format: int32 */
        operatorId: number;
        sendNotificationLog: components['schemas']['com.moego.server.grooming.dto.appointment.history.SendNotificationLogDTO'];
        updateStatusLog: components['schemas']['com.moego.server.grooming.dto.appointment.history.UpdateStatusLogDTO'];
      };
      'com.moego.server.grooming.dto.appointment.history.AutoRolloverLogDTO': {
        source: components['schemas']['com.moego.server.grooming.dto.appointment.history.AutoRolloverLogDTO.Service'];
        target: components['schemas']['com.moego.server.grooming.dto.appointment.history.AutoRolloverLogDTO.Service'];
      };
      'com.moego.server.grooming.dto.appointment.history.AutoRolloverLogDTO.Service': {
        /** Format: int64 */
        id: string | number;
        name: string;
      };
      'com.moego.server.grooming.dto.appointment.history.CancelLogDTO': {
        /** @enum {string} */
        canceledByType: ComMoegoServerGroomingDtoAppointmentHistoryCancelLogDTOCanceledByType;
        cancelReason: string;
      };
      'com.moego.server.grooming.dto.appointment.history.ChangeTimeLogDTO': {
        /** @enum {string} */
        changedByType: ComMoegoServerGroomingDtoAppointmentHistoryChangeTimeLogDTOChangedByType;
        /** Format: int64 */
        newTime: string | number;
        /** Format: int64 */
        oldTime: string | number;
      };
      'com.moego.server.grooming.dto.appointment.history.CreateLogDTO': {
        autoAssign: components['schemas']['com.moego.server.grooming.dto.AutoAssignDTO'];
        isAutoAccept: boolean;
        isOnlineBooking: boolean;
      };
      'com.moego.server.grooming.dto.appointment.history.CustomerReplyLogDTO': {
        /** @enum {string} */
        clientReplyTypeEnum: ComMoegoServerGroomingDtoAppointmentHistoryCustomerReplyLogDTOClientReplyTypeEnum;
        /**
         * Format: int32
         * @description message method
         */
        messageMethodType: number;
        statusUpdated: boolean;
      };
      'com.moego.server.grooming.dto.appointment.history.NotificationUpdateDTO': {
        failedReason: string;
        /** @enum {string} */
        notificationTypeEnum: ComMoegoServerGroomingDtoAppointmentHistoryNotificationUpdateDTONotificationTypeEnum;
        success: boolean;
      };
      'com.moego.server.grooming.dto.appointment.history.SendNotificationLogDTO': {
        /**
         * Format: int32
         * @description message method
         */
        messageMethodType: number;
        /** @enum {string} */
        notificationType: ComMoegoServerGroomingDtoAppointmentHistorySendNotificationLogDTONotificationType;
        sendFailedReason: string;
        sendSuccess: boolean;
      };
      'com.moego.server.grooming.dto.appointment.history.UpdateStatusLogDTO': {
        /**
         * Format: int32
         * @description 预约状态
         */
        newStatus: number;
        /**
         * Format: int32
         * @description 预约状态
         */
        oldStatus: number;
      };
      'com.moego.server.grooming.dto.appointment.StaffUpcomingAppointmentCountDTO': {
        locationUpcomingCountList: components['schemas']['com.moego.server.grooming.dto.appointment.StaffUpcomingAppointmentCountDTO$LocationUpcomingAppointmentCount'][];
        /** Format: int64 */
        staffId: string | number;
      };
      'com.moego.server.grooming.dto.appointment.StaffUpcomingAppointmentCountDTO$LocationUpcomingAppointmentCount': {
        /** Format: int64 */
        businessId: string | number;
        /** Format: int32 */
        upcomingAppointmentCount: number;
      };
      'com.moego.server.grooming.dto.appointment.StaffUpcomingOperationCountDTO': {
        /** Format: int32 */
        upcomingOperationCount: number;
      };
      'com.moego.server.grooming.dto.AppointmentServiceInfo': {
        /** Format: int32 */
        petDetailId: number;
        /** Format: int32 */
        petId: number;
        /** Format: int32 */
        serviceId: number;
        /** Format: int32 */
        serviceItemType: number;
        serviceName: string;
        servicePrice: number;
        /** Format: int32 */
        serviceTime: number;
        /** Format: int32 */
        serviceType: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        startTime: number;
      };
      'com.moego.server.grooming.dto.AppointmentTrackingViewDTO': {
        /** Format: int64 */
        appointmentId: string | number;
        customerAddress: components['schemas']['com.moego.server.grooming.dto.AppointmentTrackingViewDTO$Address'];
        /** Format: int32 */
        delayedStatus: number;
        /** Format: int32 */
        delayedStatusFromLastInTransit: number;
        /** Format: int64 */
        estimatedTravelSeconds: string | number;
        /** Format: int64 */
        estimatedTravelSecondsFromLastInTransit: string | number;
        /** Format: int64 */
        fromLastInTransitLastEstimateAt: string | number;
        /** Format: int64 */
        lastEstimateAt: string | number;
        /** Format: int64 */
        lastInTransitAppointmentId: string | number;
        /** Format: int64 */
        lastInTransitAt: string | number;
        locationSharingDeviceId: string;
        /** Format: int64 */
        locationSharingStaffId: string | number;
        staffAddress: components['schemas']['com.moego.server.grooming.dto.AppointmentTrackingViewDTO$Address'];
        /** Format: int32 */
        staffLocationStatus: number;
        /** Format: int64 */
        travelDistance: string | number;
        /** Format: int64 */
        travelDistanceFromLastInTransit: string | number;
      };
      'com.moego.server.grooming.dto.AppointmentTrackingViewDTO$Address': {
        address1: string;
        address2: string;
        city: string;
        coordinate: components['schemas']['com.moego.server.grooming.dto.AppointmentTrackingViewDTO$LatLng'];
        country: string;
        state: string;
        zipcode: string;
      };
      'com.moego.server.grooming.dto.AppointmentTrackingViewDTO$LatLng': {
        /** Format: double */
        latitude: number;
        /** Format: double */
        longitude: number;
      };
      'com.moego.server.grooming.dto.AppointmentWithPetDetailsDto': {
        appointmentDate: string;
        /** Format: int32 */
        appointmentEndTime: number;
        /** Format: int32 */
        appointmentId: number;
        /** Format: int32 */
        appointmentStartTime: number;
        /** Format: int32 */
        arrivalAfterStartTime: number;
        /** Format: int32 */
        arrivalBeforeStartTime: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        customerId: number;
        endDate: string;
        services: components['schemas']['com.moego.server.grooming.dto.AppointmentServiceInfo'][];
        /** Format: int32 */
        source: number;
        /** Format: int32 */
        status: number;
      };
      'com.moego.server.grooming.dto.AutoAssignDTO': {
        /** Format: int32 */
        appointmentId: number;
        /** Format: int32 */
        appointmentTime: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        staffId: number;
      };
      'com.moego.server.grooming.dto.BookOnlineDepositForClientDTO': {
        amount: number;
        /** Format: int32 */
        groomingId: number;
        guid: string;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        paymentId: number;
        /** Format: int32 */
        status: number;
      };
      'com.moego.server.grooming.dto.BookOnlineDTO$PaymentOption': {
        /**
         * Format: int32
         * @description see moego.models.online_booking.v1.PaymentType
         */
        paymentType: number;
        prePay: components['schemas']['com.moego.server.grooming.dto.BookOnlineDTO$PaymentOption$PrePay'];
      };
      /** @description 当 paymentType 为 PREPAY 时，才会使用到 prePay */
      'com.moego.server.grooming.dto.BookOnlineDTO$PaymentOption$PrePay': {
        depositAmount: number;
        /** Format: int32 */
        depositPercentage: number;
        /**
         * Format: int32
         * @description see moego.models.online_booking.v1.PrepayDepositType
         */
        depositType: number;
        /**
         * Format: int32
         * @description see moego.models.online_booking.v1.PrepayType
         */
        prepayType: number;
      };
      'com.moego.server.grooming.dto.BookOnlineLocationDTO': {
        address: string;
        avatarPath: string;
        bookOnlineName: string;
        businessName: string;
        isAvailable: boolean;
        /** Format: int32 */
        isEnable: number;
        phoneNumber: string;
      };
      'com.moego.server.grooming.dto.BookOnlinePetLimitDTO': {
        allBreed: boolean;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        id: string | number;
        /** Format: int32 */
        maxNumber: number;
        petBreedDTOList: components['schemas']['com.moego.server.customer.dto.MoePetBreedDTO'][];
        petSizeDTO: components['schemas']['com.moego.server.business.dto.PetSizeDTO'];
        /** Format: int32 */
        petTypeId: number;
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.grooming.dto.BookOnlineStaffAvailabilityDTO': {
        /** Format: int32 */
        bySlotEnable?: number;
        /** Format: int32 */
        byWorkingHourEnable?: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        syncWithWorkingHour?: number;
      };
      'com.moego.server.grooming.dto.BusinessUpcomingDTO': {
        /** Format: int32 */
        petNum: number;
        /** Format: int32 */
        totalAppointmentNum: number;
        totalPrice: number;
        upComingAppoints: components['schemas']['com.moego.server.grooming.dto.CustomerUpComingAppointDTO'][];
      };
      'com.moego.server.grooming.dto.calendarcard.CalendarCardDTO': {
        alertNotes: string;
        /** Format: int64 */
        appointmentId: string | number;
        /**
         * Format: int32
         * @description 预约状态
         */
        appointmentStatus: number;
        autoAssign: components['schemas']['com.moego.server.grooming.dto.AutoAssignDTO'];
        /**
         * Format: int32
         * @description booking type
         */
        bookingType: number;
        /** @description card unique id, 唯一标识一张卡片，用于前端操作 */
        cardId: string;
        /**
         * Format: int32
         * @description card type
         */
        cardType: number;
        clientInfo: components['schemas']['com.moego.server.grooming.dto.calendarcard.ClientDTO'];
        colorCode: string;
        date: string;
        draggableInfo: components['schemas']['com.moego.server.grooming.dto.calendarcard.DraggableInfoDTO'];
        endDate: string;
        /** Format: int64 */
        endTime: string | number;
        estimatedTotalPrice: number;
        /**
         * Format: int64
         * @description card id, 针对不同的 card Type，id 表示不同的实体主键
         */
        id: string | number;
        isAutoAccept: boolean;
        paidAmount: number;
        /**
         * Format: int32
         * @description payment status
         */
        paymentStatus: number;
        petDetailIds: number[];
        petList: components['schemas']['com.moego.server.grooming.dto.calendarcard.PetInfoDTO'][];
        preAuthInfo: components['schemas']['com.moego.server.payment.dto.PreAuthDTO'];
        prepaidAmount: number;
        /** Format: int32 */
        prepayStatus: number;
        /** Format: int64 */
        repeatId: string | number;
        requiredSign: boolean;
        /** Format: int64 */
        staffId: string | number;
        /** Format: int64 */
        startTime: string | number;
        ticketComments: string;
      };
      'com.moego.server.grooming.dto.calendarcard.ClientDTO': {
        address1: string;
        address2: string;
        areas: components['schemas']['com.moego.server.business.dto.CertainAreaDTO'][];
        city: string;
        clientColor: string;
        /** Format: int64 */
        clientId: string | number;
        country: string;
        customerFirstName: string;
        customerLastName: string;
        fullAddress: string;
        isNewClient: boolean;
        lat: string;
        lng: string;
        state: string;
        zipcode: string;
      };
      'com.moego.server.grooming.dto.calendarcard.DraggableInfoDTO': {
        draggable: boolean;
        earliestDate: string;
        latestDate: string;
        unavailableStaffList: (string | number)[];
      };
      'com.moego.server.grooming.dto.calendarcard.MonthlyViewDTO': {
        appointmentDate: string;
        /** Format: int64 */
        appointmentId: string | number;
        /**
         * Format: int32
         * @description 预约状态
         */
        appointmentStatus: number;
        autoAssign: components['schemas']['com.moego.server.grooming.dto.AutoAssignDTO'];
        /**
         * Format: int32
         * @description booking type
         */
        bookingType: number;
        /** @description card unique id, 唯一标识一张卡片，用于前端操作 */
        cardId: string;
        /**
         * Format: int32
         * @description card type
         */
        cardType: number;
        colorCode: string;
        customerColor: string;
        customerFirstName: string;
        /** Format: int32 */
        customerId: number;
        customerLastName: string;
        /** @description 描述信息， 仅对 block 类型有效 */
        desc: string;
        /** Format: int64 */
        endTime: string | number;
        /** Format: int64 */
        id: string | number;
        isAutoAccept: boolean;
        isBlock: boolean;
        noStartTime: boolean;
        /** Format: int32 */
        petDetailId: number;
        petDetailsIds: number[];
        /** Format: int32 */
        repeatId: number;
        serviceColorCode: string;
        /** Format: int32 */
        staffId: number;
        /** Format: int64 */
        startTime: string | number;
      };
      'com.moego.server.grooming.dto.calendarcard.PetInfoDTO': {
        petBreedName: string;
        petCodeList: components['schemas']['com.moego.server.customer.dto.MoePetCodeInfoDTO'][];
        /** Format: int32 */
        petId: number;
        petName: string;
        serviceList: components['schemas']['com.moego.server.grooming.dto.calendarcard.ServiceInfoDTO'][];
        vaccineAlerts: components['schemas']['com.moego.server.grooming.dto.calendarcard.VaccineAlertDTO'][];
      };
      'com.moego.server.grooming.dto.calendarcard.ServiceInfoDTO': {
        colorCode: string;
        /** Format: int64 */
        serviceId: string | number;
        serviceName: string;
        servicePrice: number;
        /** Format: int32 */
        serviceTime: number;
      };
      'com.moego.server.grooming.dto.calendarcard.VaccineAlertDTO': {
        expirationDate: string;
        /** Format: int32 */
        vaccineId: number;
        vaccineName: string;
      };
      'com.moego.server.grooming.dto.CalendarConflictDTO': {
        /** Format: int32 */
        conflictType: number;
        isConflict: boolean;
      };
      'com.moego.server.grooming.dto.ConflictCheckDTO': {
        appointmentDate: string;
        isNotConflict: boolean;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        startTime: number;
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.grooming.dto.ConflictDayInfoDTO': {
        /** Format: int32 */
        conflictType: number;
        date: string;
        /** Format: int32 */
        duration: number;
        /** @description 是否是 history appt */
        isHistory: boolean;
        isNotConflict: boolean;
        /**
         * Format: int32
         * @description schedule类型，1-重复规则计算出来的不冲突的时间，2-经过smart schedule后的时间
         */
        scheduleType: number;
        /** Format: int32 */
        staffId: number;
        /**
         * Format: int32
         * @description repeat appt的时间，经过smart schedule后，这个时间和原来的appt start time可能不一样
         */
        startTime: number;
      };
      'com.moego.server.grooming.dto.ConflictInfoDTO': {
        /**
         * Format: int32
         * @description 已存在预约的 id
         */
        appointmentId: number;
        /** @description appointment date */
        date: string;
        /**
         * Format: int32
         * @description 入参传的 serviceTime，和老接口保持一致
         */
        duration: number;
        /**
         * Format: int32
         * @description appointment 的结束时间
         */
        endTime: number;
        /** @description 是否冲突: true-不冲突, false-冲突 */
        isNotConflict: boolean;
        /** @description 预约内每个 staff 的 conflict info */
        staffConflictInfoList: components['schemas']['com.moego.server.grooming.dto.StaffConflictInfoDTO'][];
        /** @description 预约内的 staff id list */
        staffIdList: number[];
        /**
         * Format: int32
         * @description appointment 的开始时间
         */
        startTime: number;
      };
      'com.moego.server.grooming.dto.CustomerAppointmentNumInfoDTO': {
        /** Format: int32 */
        cancelled: number;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        finished: number;
        /** Format: int32 */
        noShow: number;
        /** Format: int32 */
        totalApptAndRequestsCount: number;
        /** Format: int32 */
        totalAppts: number;
        /** Format: int32 */
        upcoming: number;
      };
      'com.moego.server.grooming.dto.CustomerGrooming': {
        appointmentDate: string;
        appointmentEndDate: string;
        /** Format: int32 */
        appointmentEndTime: number;
        /** Format: int64 */
        appointmentOrderId: string | number;
        /** Format: int32 */
        appointmentStartTime: number;
        /**
         * Format: int32
         * @description 预约状态
         */
        appointmentStatus: number;
        /** Format: int32 */
        bookOnlineStatus: number;
        /** @description business id */
        businessId: string;
        /** Format: int32 */
        cancelByType: number;
        /** Format: int64 */
        canceledTime: string | number;
        cancellationFee: number;
        cancelReason: string;
        /** Format: int64 */
        checkInTime: string | number;
        /** Format: int64 */
        checkOutTime: string | number;
        colorCode: string;
        /** Format: int64 */
        confirmedTime: string | number;
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        customerAddressId: number;
        /** Format: int32 */
        customerId: number;
        evaluationServiceList: components['schemas']['com.moego.server.grooming.dto.GroomingEvaluationServiceDetailDTO'][];
        /** Format: int32 */
        groomingCount: number;
        hasExtraOrder: boolean;
        /** Format: int32 */
        id: number;
        /**
         * Format: int32
         * @deprecated
         */
        invoiceId: number;
        /**
         * Format: int32
         * @deprecated
         */
        isPaid: number;
        /** Format: int32 */
        noShow: number;
        noShowFee: number;
        /** Format: int64 */
        noShowOrderId: string | number;
        noShowPaymentStatus: string;
        /** Format: int32 */
        noShowStatus: number;
        orderId: string;
        /** @description 已支付金额 */
        paidAmount: number;
        paymentStatus: string;
        petServiceList: components['schemas']['com.moego.server.grooming.dto.GroomingCustomerPetdetailDTO'][];
        /** @description 预先支付的金额 */
        prepaidAmount: number;
        /**
         * Format: double
         * @description 预支付金额占总金额的比例
         */
        prepayRate: number;
        /** Format: int32 */
        prepayStatus: number;
        /** @description 退款金额 */
        refundAmount: number;
        /** @description 当前 appt 包含的 service 类型列表 */
        serviceItems: number[];
        /**
         * Format: int32
         * @description 当前 appt 包含的 service 类型 bitmap 值
         */
        serviceTypeInclude: number;
        /**
         * Format: int32
         * @deprecated
         */
        status: number;
        /** @description 总金额 */
        subTotalAmount: number;
        waitListDTO: components['schemas']['com.moego.server.grooming.dto.waitlist.WaitListCompatibleDTO'];
      };
      'com.moego.server.grooming.dto.CustomerGroomingAppointmentDTO': {
        appointmentDate: string;
        /** Format: int32 */
        appointmentEndTime: number;
        /** Format: int32 */
        appointmentStartTime: number;
        /** @description business id */
        businessId: string;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        id: number;
        orderId: string;
        petDetails: components['schemas']['com.moego.server.grooming.dto.CustomerGroomingAppointmentPetDetailDTO'][];
      };
      'com.moego.server.grooming.dto.CustomerGroomingAppointmentPetDetailDTO': {
        /** Format: int32 */
        groomingId: number;
        /** Format: int32 */
        lifeStatus: number;
        operationList: components['schemas']['com.moego.server.grooming.dto.GroomingServiceOperationDTO'][];
        petBreed: string;
        /** Format: int32 */
        petDetailId: number;
        /** Format: int32 */
        petId: number;
        /** Format: int32 */
        petIsDelete: number;
        petName: string;
        /** Format: int32 */
        scopeTypePrice: number;
        /** Format: int32 */
        scopeTypeTime: number;
        /** Format: int32 */
        serviceAvailableForBookingOnline: number;
        /** Format: int32 */
        serviceId: number;
        /** Format: int32 */
        serviceInactive: number;
        /** Format: int32 */
        serviceIsDelete: number;
        serviceName: string;
        servicePrice: number;
        /** Format: int32 */
        serviceTime: number;
        /** Format: int32 */
        serviceType: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        workMode: number;
      };
      'com.moego.server.grooming.dto.CustomerUpComingAppointDTO': {
        address1: string;
        address2: string;
        alertNotes: string;
        appointmentDate: string;
        appointmentEndDate: string;
        /** Format: int32 */
        appointmentEndTime: number;
        /** Format: int32 */
        appointmentStartTime: number;
        /** Format: int32 */
        arrivalAfterStartTime: number;
        /** Format: int32 */
        arrivalBeforeStartTime: number;
        businessAvatarPath: string;
        /** Format: int32 */
        businessId: number;
        businessName: string;
        city: string;
        clientFullAddress: string;
        clientPhoneNumber: string;
        country: string;
        /** Format: int32 */
        createBy: number;
        createByFirstName: string;
        createByLastName: string;
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        customerAddressId: number;
        customerFirstName: string;
        /** Format: int32 */
        customerId: number;
        customerLastName: string;
        email: string;
        estimatePrice: number;
        /** Format: int32 */
        id: number;
        orderId: string;
        petDetails: components['schemas']['com.moego.server.grooming.dto.CustomerUpcomingPetDetailDTO'][];
        state: string;
        ticketComments: string;
        /** Format: int64 */
        updateTime: string | number;
        zipcode: string;
      };
      'com.moego.server.grooming.dto.CustomerUpcomingBusinessDTO': {
        /** Format: int32 */
        appType: number;
        businessAddress: string;
        businessEmail: string;
        /** @deprecated */
        businessHeadImg: string;
        businessName: string;
        /** @deprecated */
        businessSymbol: string;
        companyName: string;
        currencySymbol: string;
        /** Format: int32 */
        customerId: number;
        isShowServicePrice: boolean;
        /** @deprecated */
        timezoneName: string;
        /**
         * Format: int32
         * @deprecated
         */
        timezoneSeconds: number;
        upComingAppoint: components['schemas']['com.moego.server.grooming.dto.CustomerUpComingAppointDTO'][];
        useCompanyInfo: boolean;
      };
      'com.moego.server.grooming.dto.CustomerUpcomingPetDetailDTO': {
        petBreed: string;
        /** Format: int32 */
        petDetailId: number;
        /** Format: int32 */
        petId: number;
        petName: string;
        /** Format: int32 */
        serviceId: number;
        serviceName: string;
        servicePrice: number;
        /** Format: int32 */
        serviceType: number;
        staffFirstName: string;
        /** Format: int32 */
        staffId: number;
        staffLastName: string;
      };
      /** @description for deposit */
      'com.moego.server.grooming.dto.DepositDto': {
        amount: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        deGuid: string;
        desc: string;
        /** Format: int32 */
        id: number;
        /** @description 提前计算的processingFee，paymentSetting.processingFeePayBy = 1 (client) */
        initProcessingFee: number;
        /** Format: int32 */
        invoiceId: number;
        /** @description 本次支付是否需要添加processingFee，需满足：1.首选支付方式为Stripe 2.全局开关打开 3.单次开关打开 */
        requiredProcessingFee: boolean;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        status: number;
        /** Format: date-time */
        updateTime: string;
      };
      'com.moego.server.grooming.dto.DiscountCodeDTO': {
        discountCodeName: string;
      };
      'com.moego.server.grooming.dto.EvaluationServiceDetailDTO': {
        /** Format: int64 */
        groomingId: string | number;
        /** Format: int64 */
        id: string | number;
        /** Format: int64 */
        petId: string | number;
        /** Format: int64 */
        serviceId: string | number;
        serviceName: string;
        servicePrice: number;
        /** Format: int64 */
        staffId: string | number;
      };
      'com.moego.server.grooming.dto.EvaluationServiceDTO': {
        /** Format: int64 */
        id: string | number;
        name: string;
      };
      'com.moego.server.grooming.dto.GroomingAppointmentWaitingListDTO': {
        address1: string;
        address2: string;
        appointmentDate: string;
        /** Format: int32 */
        appointmentStartTime: number;
        city: string;
        clientFullAddress: string;
        clientPhoneNumber: string;
        country: string;
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        customerAddressId: number;
        customerFirstName: string;
        /** Format: int32 */
        customerId: number;
        customerLastName: string;
        state: string;
        /** Format: int32 */
        ticketId: number;
        /** Format: int64 */
        updateTime: string | number;
        waitingListPetDetails: components['schemas']['com.moego.server.grooming.dto.WaitingListPetDetailDTO'][];
        zipcode: string;
      };
      'com.moego.server.grooming.dto.GroomingBookingDTO': {
        appointmentDate: string;
        /** Format: int32 */
        appointmentEndTime: number;
        /** Format: int32 */
        appointmentStartTime: number;
        avatarPath: string;
        /** Format: int32 */
        bookOnlineStatus: number;
        /** Format: int64 */
        businessId: string | number;
        colorCode: string;
        /** Format: int64 */
        createTime: string | number;
        customerFirstName: string;
        /** Format: int32 */
        customerId: number;
        customerLastName: string;
        enablePreAuth: boolean;
        /** Format: int32 */
        groomingCount: number;
        /** Format: int32 */
        groomingId: number;
        /** @description has customer profile request update */
        hasRequestUpdate: boolean;
        isNewCustomer: boolean;
        /** Format: int32 */
        isPaid: number;
        noStartTime: boolean;
        /** Format: int32 */
        outOfArea: number;
        /** @description 已支付金额 */
        paidAmount: number;
        petList: components['schemas']['com.moego.server.grooming.dto.GroomingCustomerPetdetailDTO'][];
        phoneNumber: string;
        /** @description 预先支付的金额 */
        prepaidAmount: number;
        /**
         * Format: double
         * @description 预支付金额占总金额的比例
         */
        prepayRate: number;
        /** Format: int32 */
        prepayStatus: number;
        /** @description 退款金额 */
        refundAmount: number;
        staffFirstName: string;
        /** Format: int32 */
        staffId: number;
        staffLastName: string;
        /** Format: int32 */
        status: number;
        waitListDTO: components['schemas']['com.moego.server.grooming.dto.waitlist.WaitListCompatibleDTO'];
      };
      'com.moego.server.grooming.dto.GroomingCustomerInfoDTO': {
        clientColor: string;
        clientFullAddress: string;
        /** Format: int32 */
        customerId: number;
        email: string;
        firstName: string;
        formJson: string;
        hasPetParentAppAccount: boolean;
        isDeleted: boolean;
        isNewCustomer: boolean;
        lastName: string;
        ownerPhoneNumber: string;
        phoneNumber: string;
        preferredDay: number[];
        /** Format: int32 */
        preferredFrequencyDay: number;
        /** Format: int32 */
        preferredFrequencyType: number;
        /** Format: int32 */
        preferredGroomerId: number;
        preferredTime: number[];
        questionJson: string;
        referralSourceDesc: string;
        /** Format: int32 */
        referralSourceId: number;
        /** Format: int32 */
        sendAutoEmail: number;
        /** Format: int32 */
        sendAutoMessage: number;
        tagList: string[];
      };
      'com.moego.server.grooming.dto.GroomingCustomerPetdetailDTO': {
        /** Format: int64 */
        endTime: string | number;
        /** @deprecated */
        petBreed: string;
        /** Format: int32 */
        petDetailId: number;
        /** Format: int32 */
        petId: number;
        /** @deprecated */
        petName: string;
        /** Format: int32 */
        serviceId: number;
        serviceName: string;
        servicePrice: number;
        /** Format: int32 */
        serviceTime: number;
        /**
         * Format: int32
         * @deprecated
         */
        staffId: number;
        staffIds: (string | number)[];
        /** Format: int64 */
        startTime: string | number;
      };
      'com.moego.server.grooming.dto.GroomingEvaluationServiceDetailDTO': {
        /** Format: int64 */
        endTime: string | number;
        /** Format: int64 */
        evaluationServiceDetailId: string | number;
        /** Format: int64 */
        petId: string | number;
        /** Format: int64 */
        serviceId: string | number;
        servicePrice: number;
        /** Format: int32 */
        serviceTime: number;
        /** Format: int64 */
        startTime: string | number;
      };
      'com.moego.server.grooming.dto.GroomingPackageDTO': {
        applied: boolean;
        /** @description business id */
        businessId: string;
        /** Format: int32 */
        cartPackageId: number;
        confirmationId: string;
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        customerId: number;
        /** Format: int64 */
        endTime: string | number;
        /** @description format: yyyy-MM-dd, 9999-01-01 means never expired */
        expirationDate: string;
        /** Format: int32 */
        id: number;
        packageDesc: string;
        packageName: string;
        packagePrice: number;
        /** Format: int64 */
        purchaseTime: string | number;
        /** Format: int32 */
        retailInvoiceItemId: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int64 */
        startTime: string | number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        totalRemainingQuantity: number;
        /** Format: int64 */
        updateTime: string | number;
        used: boolean;
      };
      /** @description package history 分页结果列表 */
      'com.moego.server.grooming.dto.GroomingPackageHistoryDTO': {
        /**
         * Format: int32
         * @description package activity
         */
        activityType: number;
        afterExtendExpireDate: string;
        appointmentDate: string;
        confirmationId: string;
        /** Format: int64 */
        endTime: string | number;
        /** Format: int32 */
        groomingId: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        invoiceId: number;
        packageDesc: string;
        /** Format: int32 */
        packageId: number;
        packageName: string;
        packagePrice: number;
        /** Format: int32 */
        packageServiceId: number;
        /** Format: int64 */
        purchaseTime: string | number;
        /** Format: int32 */
        quantity: number;
        /** Format: int32 */
        retailInvoiceItemId: number;
        /** Format: int32 */
        serviceId: number;
        serviceName: string;
        /** Format: int32 */
        staffId: number;
        /** Format: int64 */
        startTime: string | number;
        /** Format: int32 */
        status: number;
        /** Format: int64 */
        useTime: string | number;
      };
      'com.moego.server.grooming.dto.GroomingPackageInfoDTO': {
        /** @description package history 分页结果列表 */
        packageHistories: components['schemas']['com.moego.server.grooming.dto.GroomingPackageHistoryDTO'][];
        /**
         * Format: int32
         * @description package history 总结果数
         */
        packageHistoryCount: number;
        /** @description package service 结果列表 */
        packageServices: components['schemas']['com.moego.server.grooming.dto.GroomingPackageServiceDTO'][];
      };
      /** @description package service 结果列表 */
      'com.moego.server.grooming.dto.GroomingPackageServiceDTO': {
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        packageId: number;
        /** Format: int32 */
        remainingQuantity: number;
        /**
         * Format: int32
         * @deprecated
         */
        serviceId: number;
        /** @deprecated */
        serviceName: string;
        services: components['schemas']['com.moego.server.retail.dto.PackageInfoDto$Service'][];
        /** @deprecated */
        serviceUnitPrice: number;
        /** Format: int32 */
        totalQuantity: number;
      };
      'com.moego.server.grooming.dto.GroomingPackageServiceInfoDTO': {
        /** Format: int32 */
        packageId: number;
        packageName: string;
        /** Format: int32 */
        packageServiceId: number;
        /** Format: int32 */
        remainingQuantity: number;
        /** Format: int32 */
        serviceId: number;
        serviceName: string;
      };
      'com.moego.server.grooming.dto.GroomingPetCodeDTO': {
        codeNumber: string;
        color: string;
        description: string;
        /** Format: int32 */
        petCodeId: number;
      };
      'com.moego.server.grooming.dto.GroomingPetDetailDTO': {
        /** Format: int64 */
        associatedServiceId: string | number;
        colorCode: string;
        /** Format: int32 */
        dateType: number;
        /** Format: int32 */
        durationOverrideType: number;
        enableOperation: boolean;
        endDate: string;
        /** Format: int64 */
        endTime: string | number;
        /** Format: int32 */
        groomingId: number;
        /** Format: int32 */
        id: number;
        /** Format: int64 */
        lodgingId: string | number;
        operationList: components['schemas']['com.moego.server.grooming.dto.GroomingServiceOperationDTO'][];
        /** Format: int32 */
        petId: number;
        /** Format: int32 */
        petLifeStatus: number;
        petName: string;
        /** Format: int32 */
        priceOverrideType: number;
        /** Format: int32 */
        priceUnit: number;
        /** Format: int32 */
        quantity: number;
        /** Format: int32 */
        quantityPerDay: number;
        requireDedicatedStaff: boolean;
        /** Format: int32 */
        scopeTypePrice: number;
        /** Format: int32 */
        scopeTypeTime: number;
        /** Format: int32 */
        serviceId: number;
        /** Format: int32 */
        serviceItemType: number;
        serviceName: string;
        servicePrice: number;
        /** Format: int32 */
        serviceStatus: number;
        /** Format: int32 */
        serviceTime: number;
        /** Format: int32 */
        serviceType: number;
        specificDates: string;
        staffFirstName: string;
        /** Format: int32 */
        staffId: number;
        staffLastName: string;
        /** Format: int32 */
        starStaffId: number;
        startDate: string;
        /** Format: int64 */
        startTime: string | number;
        /** Format: date-time */
        updatedAt: string;
        /** Format: int32 */
        workMode: number;
      };
      'com.moego.server.grooming.dto.GroomingPetInfoDetailDTO': {
        avatarPath: string;
        behavior: string;
        birthday: string;
        breed: string;
        /** Format: int32 */
        breedMix: number;
        /** Format: int32 */
        customerId: number;
        emergencyContactName: string;
        emergencyContactPhone: string;
        /** Format: int32 */
        expiryNotification: number;
        fixed: string;
        /** Format: int32 */
        gender: number;
        groomingPetServiceDTOS: components['schemas']['com.moego.server.grooming.dto.GroomingPetServiceDTO'][];
        hairLength: string;
        healthIssues: string;
        /** Format: int32 */
        lifeStatus: number;
        petAnswerJson: string;
        petCodes: components['schemas']['com.moego.server.grooming.dto.GroomingPetCodeDTO'][];
        /** Format: int32 */
        petId: number;
        petName: string;
        petPhotoImage: string;
        petQuestionJson: string;
        /** Format: int32 */
        petTypeId: number;
        /** Format: int32 */
        status: number;
        typeName: string;
        vaccineBindings: components['schemas']['com.moego.server.grooming.dto.VaccineBindingRecordDto'][];
        vaccineStatus: boolean;
        vetAddress: string;
        vetName: string;
        vetPhone: string;
        weight: string;
      };
      'com.moego.server.grooming.dto.GroomingPetServiceDTO': {
        /** Format: int32 */
        dateType: number;
        /** Format: int32 */
        durationOverrideType: number;
        endDate: string;
        /** Format: int64 */
        endTime: string | number;
        lodgingInfos: components['schemas']['com.moego.server.grooming.dto.LodgingInfo'][];
        lodgingUnitName: string;
        operationList: components['schemas']['com.moego.server.grooming.dto.GroomingServiceOperationDTO'][];
        /** Format: int32 */
        petDetailId: number;
        /** Format: int32 */
        priceOverrideType: number;
        /** Format: int32 */
        quantityPerDay: number;
        /** Format: int32 */
        scopeTypePrice: number;
        /** Format: int32 */
        scopeTypeTime: number;
        /** Format: int32 */
        serviceId: number;
        /** Format: int32 */
        serviceItemType: number;
        serviceName: string;
        servicePrice: number;
        /** Format: int32 */
        serviceStatus: number;
        /** Format: int32 */
        serviceTime: number;
        /** Format: int32 */
        serviceType: number;
        specificDates: string[];
        staffFirstName: string;
        /** Format: int32 */
        staffId: number;
        staffLastName: string;
        startDate: string;
        /** Format: int64 */
        startTime: string | number;
        /** Format: int32 */
        workMode: number;
      };
      /** @description appointment service details */
      'com.moego.server.grooming.dto.GroomingPetServiceListInfoDTO': {
        address1: string;
        address2: string;
        alertNotes: string;
        appointmentDate: string;
        /**
         * Format: int32
         * @description 预约状态
         */
        appointmentStatus: number;
        appointmentTracking: components['schemas']['com.moego.server.grooming.dto.AppointmentTrackingViewDTO'];
        areas: components['schemas']['com.moego.server.business.dto.CertainAreaDTO'][];
        autoAssign: components['schemas']['com.moego.server.grooming.dto.AutoAssignDTO'];
        /** Format: int32 */
        bookOnlineStatus: number;
        /** Format: int64 */
        checkInTime: string | number;
        /** Format: int64 */
        checkOutTime: string | number;
        city: string;
        clientColor: string;
        clientFullAddress: string;
        clientPhoneNumber: string;
        coatType: string;
        colorCode: string;
        country: string;
        createdByFirstName: string;
        /** Format: int32 */
        createdById: number;
        createdByLastName: string;
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        customerAddressId: number;
        customerAvatar: string;
        customerFirstName: string;
        /** Format: int32 */
        customerId: number;
        customerLastName: string;
        hasPetParentAppAccount: boolean;
        /** Format: int32 */
        invoiceId: number;
        isAutoAccept: boolean;
        /** Format: int32 */
        isBlock: number;
        isNewCustomer: boolean;
        /**
         * Format: int32
         * @description 1: 已完全支付; 2: 未支付； 3：部分支付
         */
        isPaid: number;
        lat: string;
        lng: string;
        moePetCodeInfos: components['schemas']['com.moego.server.grooming.dto.PetCodeInfoDTO'][];
        noStartTime: boolean;
        operationList: components['schemas']['com.moego.server.grooming.dto.GroomingServiceOperationDTO'][];
        /** @description 已支付金额 */
        paidAmount: number;
        petAvatar: string;
        petBreed: string;
        /** Format: int32 */
        petDetailId: number;
        /** Format: int32 */
        petId: number;
        petName: string;
        /** Format: int32 */
        petTypeId: number;
        preAuthInfo: components['schemas']['com.moego.server.payment.dto.PreAuthDTO'];
        /** @description OB已支付的金额 */
        prepaidAmount: number;
        /**
         * Format: double
         * @description 预支付金额占总金额的比例
         */
        prepayRate: number;
        /** Format: int32 */
        prepayStatus: number;
        primaryContactFirstName: string;
        /** Format: int32 */
        primaryContactId: number;
        primaryContactLastName: string;
        /** @description 退款金额 */
        refundAmount: number;
        remainingAmount: number;
        /** Format: int32 */
        repeatId: number;
        serviceCategoryName: string;
        serviceColorCode: string;
        /** Format: int32 */
        serviceId: number;
        serviceItems: number[];
        serviceName: string;
        servicePrice: number;
        /** Format: int32 */
        serviceTime: number;
        /** Format: int32 */
        serviceType: number;
        /** Format: int32 */
        serviceTypeInclude: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int64 */
        startTime: string | number;
        state: string;
        /**
         * Format: int32
         * @deprecated
         */
        status: number;
        ticketComments: string;
        /** Format: int32 */
        ticketId: number;
        totalAmount: number;
        weight: string;
        zipcode: string;
      };
      'com.moego.server.grooming.dto.GroomingQuestionDTO': {
        /** Format: int32 */
        acceptCustomerType: number;
        /** Format: int32 */
        acceptPetEntryType: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        createTime: string | number;
        extraJson: string;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        isAllowChange: number;
        /** Format: int32 */
        isAllowDelete: number;
        /** Format: int32 */
        isAllowEdit: number;
        /** Format: int32 */
        isRequired: number;
        /** Format: int32 */
        isShow: number;
        key: string;
        placeholder: string;
        question: string;
        /** Format: int32 */
        questionType: number;
        /** Format: int32 */
        sort: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        type: number;
        /** Format: int64 */
        updateTime: string | number;
      };
      /** @description body_view question marked result url */
      'com.moego.server.grooming.dto.groomingreport.BodyViewUrl': {
        left: string;
        right: string;
      };
      'com.moego.server.grooming.dto.groomingreport.GroomingRecommendation': {
        /**
         * Format: int32
         * @description customer prefer frequency day
         */
        frequencyDay: number;
        /** @description formatted frequency text: Every xx days, Every xx weeks, Every xx months */
        frequencyText: string;
        /** Format: int32 */
        frequencyType: number;
        /** @description next appointment date, standard format: yyyy-MM-dd, need to be formatted by front end */
        nextAppointmentDate: string;
        /** @description next appointment date */
        nextAppointmentDateText: string;
      };
      /** @description 保存成功后或者 merge 最新 template 的 grooming report info */
      'com.moego.server.grooming.dto.groomingreport.GroomingReportInfoDTO': {
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        content: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportInfoDTO$GroomingReportContent'];
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        groomingId: number;
        /**
         * Format: int32
         * @description grooming report id
         */
        id: number;
        /**
         * Format: int32
         * @description link opened count
         */
        linkOpenedCount: number;
        /** Format: int32 */
        petId: number;
        /**
         * Format: int32
         * @description pet type id: 1-dog,2-cat
         */
        petTypeId: number;
        /** @description grooming report status */
        status: string;
        /**
         * Format: int64
         * @description last submitted time
         */
        submittedTime: string | number;
        template: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportInfoDTO$GroomingReportTemplate'];
        /**
         * Format: int64
         * @description grooming report used template published time
         */
        templatePublishTime: string | number;
        /** @description theme code */
        themeCode: string;
        /**
         * Format: int32
         * @description last update staff id
         */
        updateBy: number;
        /** Format: int64 */
        updateTime: string | number;
        /** @description unique id for grooming report */
        uuid: string;
        /** @description unique id for share */
        uuidForShare: string;
      };
      /** @description fill in content */
      'com.moego.server.grooming.dto.groomingreport.GroomingReportInfoDTO$GroomingReportContent': {
        /** @description feedback questions */
        feedbacks: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportInfoDTO$GroomingReportQuestion'][];
        /** @description pet condition questions */
        petConditions: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportInfoDTO$GroomingReportQuestion'][];
        recommendation: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingRecommendation'];
        /** @description showcase: before and after urls */
        showcase: string[];
      };
      /** @description pet condition questions */
      'com.moego.server.grooming.dto.groomingreport.GroomingReportInfoDTO$GroomingReportQuestion': {
        category: string;
        choices: string[];
        customOptions: string[];
        /** Format: int32 */
        id: number;
        key: string;
        options: string[];
        placeholder: string;
        required: boolean;
        show: boolean;
        text: string;
        title: string;
        type: string;
        urls: components['schemas']['com.moego.server.grooming.dto.groomingreport.BodyViewUrl'];
      };
      /** @description current used template */
      'com.moego.server.grooming.dto.groomingreport.GroomingReportInfoDTO$GroomingReportTemplate': {
        facebookReviewLink: string;
        googleReviewLink: string;
        lightThemeColor: string;
        /** Format: int32 */
        nextAppointmentDateFormatType: number;
        requireAfterPhoto: boolean;
        requireBeforePhoto: boolean;
        showFacebookReview: boolean;
        showGoogleReview: boolean;
        showNextAppointment: boolean;
        showOverallFeedback: boolean;
        showPetCondition: boolean;
        showReviewBooster: boolean;
        showServiceStaffName: boolean;
        showShowcase: boolean;
        showYelpReview: boolean;
        thankYouMessage: string;
        themeCode: string;
        themeColor: string;
        title: string;
        yelpReviewLink: string;
      };
      'com.moego.server.grooming.dto.groomingreport.GroomingReportInfoForInputDTO': {
        groomingReportInfo: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportInfoDTO'];
        /** @description 是否需要更新template */
        needRefreshTemplate: boolean;
      };
      'com.moego.server.grooming.dto.groomingreport.GroomingReportPreviewDataDTO': {
        reportSummary: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO'];
        sampleValue: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportPreviewDataDTO$GroomingReportSampleValue'];
      };
      /** @description sample value for grooming report */
      'com.moego.server.grooming.dto.groomingreport.GroomingReportPreviewDataDTO$GroomingReportSampleValue': {
        bodyViewUrls: components['schemas']['com.moego.server.grooming.dto.groomingreport.BodyViewUrl'];
        comment: string;
        petAvatarUrl: string;
        showcaseUrls: string[];
      };
      'com.moego.server.grooming.dto.groomingreport.GroomingReportQuestionDTO': {
        /** @description default options list, not allow to change */
        buildInOptions: string[];
        /** Format: int32 */
        businessId: number;
        /** @description category name */
        category: string;
        /** @description single_choice/multi_choice question's selected choice list */
        choices: string[];
        /** Format: int64 */
        createTime: string | number;
        /** @description single_choice/multi_choice question's custom options list */
        customOptions: string[];
        /** Format: int32 */
        id: number;
        /** @description is default question */
        isDefault: boolean;
        /** @description system default question key */
        key: string;
        /** @description single_choice/multi_choice question's options list */
        options: string[];
        /** @description options editable: true/false */
        optionsEditable: boolean;
        /** @description text_input question's placeholder */
        placeholder: string;
        /** @description required to fill in: true/false */
        required: boolean;
        /** @description required = false 时，true 直接展示在填写列表，false 放在 select more 中 */
        show: boolean;
        /**
         * Format: int32
         * @description sort value, in descending order
         */
        sort: number;
        /** Format: int32 */
        status: number;
        /** @description text_input question's input text */
        text: string;
        /** @description title */
        title: string;
        /** @description title editable: true/false */
        titleEditable: boolean;
        /** @description question type */
        type: string;
        /** @description type editable: true/false */
        typeEditable: boolean;
        /** Format: int64 */
        updateTime: string | number;
        urls: components['schemas']['com.moego.server.grooming.dto.groomingreport.BodyViewUrl'];
      };
      'com.moego.server.grooming.dto.groomingreport.GroomingReportRecordDTO': {
        /** @description 最近一次发送选择的 sending method: 1-sms, 2-email */
        lastSendingMethodList: number[];
        /**
         * Format: int32
         * @description grooming report pet id
         */
        petId: number;
        /** @description pet name */
        petName: string;
        /**
         * Format: int32
         * @description pet type id: 1-dog,2-cat,3-other
         */
        petTypeId: number;
        /**
         * Format: int32
         * @description grooming report id
         */
        reportId: number;
        /** @description grooming report status */
        reportStatus: string;
        /** @description 系统当前设置的 sending method: 1-sms, 2-email */
        sendingMethodList: number[];
        /**
         * Format: int32
         * @description 系统当前设置的 sending type: 1-manually 2-automatically
         */
        sendingType: number;
        sentRecords: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportRecordDTO$GroomingReportSendRecord'];
        /**
         * Format: int64
         * @description grooming report used template published time
         */
        templatePublishTime: string | number;
      };
      'com.moego.server.grooming.dto.groomingreport.GroomingReportRecordDTO$GroomingReportSendLog': {
        /** Format: int32 */
        errorCode: number;
        errorMessage: string;
        /** Format: int32 */
        sentStatus: number;
      };
      /** @description 最近一次的发送记录 */
      'com.moego.server.grooming.dto.groomingreport.GroomingReportRecordDTO$GroomingReportSendRecord': {
        email: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportRecordDTO$GroomingReportSendLog'];
        sms: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportRecordDTO$GroomingReportSendLog'];
      };
      /** @description grooming report related resource list */
      'com.moego.server.grooming.dto.groomingreport.GroomingReportResourceDTO': {
        /** @description 当前主题下的封面 url */
        coverUrl: string;
        themeCode: string;
      };
      'com.moego.server.grooming.dto.groomingreport.GroomingReportSettingDTO': {
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        id: number;
        /** @description sending method: 1-sms, 2-email */
        sendingMethodList: number[];
        /**
         * Format: int32
         * @description sending type: 1-manually, 2-automatically
         */
        sendingType: number;
        /**
         * Format: int64
         * @description template published time
         */
        templatePublishTime: string | number;
        /** Format: int32 */
        updateBy: number;
        /** Format: int64 */
        updateTime: string | number;
      };
      /** @description report summary info */
      'com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO': {
        businessInfo: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO$BusinessInfo'];
        groomingInfo: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO$GroomingInfo'];
        nextGroomingInfo: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO$GroomingInfo'];
        petInfo: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO$PetInfo'];
        presetTags: string[];
        reportInfo: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportInfoDTO'];
        /** @description grooming report related resource list */
        resourceList: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportResourceDTO'][];
        reviewBoosterConfig: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO$ReviewBoosterConfig'];
        reviewBoosterRecord: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO$ReviewBoosterRecord'];
        themeConfig: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportThemeConfigDTO'];
      };
      /** @description business name, logo */
      'com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO$BusinessInfo': {
        /** @description Address 1 */
        address1: string;
        /** @description Address 2 */
        address2: string;
        /** @description Address city */
        addressCity: string;
        /** @description Address country */
        addressCountry: string;
        /** @description Address latitude */
        addressLat: string;
        /** @description Address longitude */
        addressLng: string;
        /** @description Address state */
        addressState: string;
        /** @description Address zipcode */
        addressZipcode: string;
        avatarPath: string;
        /** @description business book online enable */
        bookOnlineEnable: boolean;
        /** @description business book online name */
        bookOnlineName: string;
        /** Format: int32 */
        businessMode: number;
        businessName: string;
        /** @description business date format */
        dateFormat: string;
        /** @description business setting phone number */
        phoneNumber: string;
        /** Format: int32 */
        timeFormatType: number;
      };
      /** @description next appointment info */
      'com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO$GroomingInfo': {
        appointmentDate: string;
        /** @deprecated */
        appointmentDateTimeText: string;
        /** Format: int32 */
        appointmentEndTime: number;
        /** Format: int32 */
        appointmentId: number;
        /** Format: int32 */
        appointmentStartTime: number;
        /** Format: int32 */
        arrivalAfterStartTime: number;
        /** Format: int32 */
        arrivalBeforeStartTime: number;
        petServiceDetails: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO$PetServiceDetailInfo'][];
        /** Format: int32 */
        status: number;
      };
      /** @description pet name, breed, avatar path, gender, weight */
      'com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO$PetInfo': {
        avatarPath: string;
        /** Format: int32 */
        gender: number;
        genderText: string;
        petBreed: string;
        /** Format: int32 */
        petId: number;
        petName: string;
        /** Format: int32 */
        petTypeId: number;
        weight: string;
        weightWithUnit: string;
      };
      'com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO$PetServiceDetailInfo': {
        petAvatarPath: string;
        /** Format: int32 */
        petId: number;
        petName: string;
        /** Format: int32 */
        petTypeId: number;
        serviceDetails: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO$ServiceDetailInfo'][];
      };
      'com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO$ReviewBoosterConfig': {
        positiveFacebook: string;
        positiveGoogle: string;
        /** Format: int32 */
        positiveScore: number;
        positiveYelp: string;
      };
      /** @description review booster record */
      'com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO$ReviewBoosterRecord': {
        /** Format: int32 */
        positiveScore: number;
        reviewContent: string;
        /** Format: int32 */
        reviewTime: number;
      };
      'com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO$ServiceDetailInfo': {
        /** Format: int32 */
        petId: number;
        /** Format: int32 */
        serviceDuration: number;
        /** Format: int32 */
        serviceId: number;
        serviceName: string;
        /** Format: int32 */
        serviceType: number;
        staffAvatarPath: string;
        staffFirstName: string;
        /** Format: int32 */
        staffId: number;
        staffLastName: string;
        /** Format: int32 */
        startTime: number;
      };
      'com.moego.server.grooming.dto.groomingreport.GroomingReportTemplateDTO': {
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        createTime: string | number;
        /** @description facebook review icon jump link */
        facebookReviewLink: string;
        /** @description google review icon jump link */
        googleReviewLink: string;
        /** Format: int32 */
        id: number;
        /**
         * Format: int64
         * @description last publish time
         */
        lastPublishTime: string | number;
        /** @description light theme color */
        lightThemeColor: string;
        /** Format: int32 */
        nextAppointmentDateFormatType: number;
        questions: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportTemplateDTO$TemplateQuestions'];
        /** @description required after photo, unused, reserved field */
        requireAfterPhoto: boolean;
        /** @description required before photo */
        requireBeforePhoto: boolean;
        /** @description show facebook review icon */
        showFacebookReview: boolean;
        /** @description show google review icon */
        showGoogleReview: boolean;
        /** @description show next appointment section */
        showNextAppointment: boolean;
        /** @description show overall feedback section */
        showOverallFeedback: boolean;
        /** @description show pet condition when fill in */
        showPetCondition: boolean;
        /** @description show review booster on report page */
        showReviewBooster: boolean;
        /** @description show staff name */
        showServiceStaffName: boolean;
        /** @description show showcase section */
        showShowcase: boolean;
        /** @description show yelp review icon */
        showYelpReview: boolean;
        /** @description thank you message */
        thankYouMessage: string;
        /** @description default theme code */
        themeCode: string;
        /** @description theme color */
        themeColor: string;
        /** @description customized grooming report title */
        title: string;
        /** Format: int32 */
        updateBy: number;
        /** Format: int64 */
        updateTime: string | number;
        /** @description yelp review icon jump link */
        yelpReviewLink: string;
      };
      /** @description feedback, pet condition question list */
      'com.moego.server.grooming.dto.groomingreport.GroomingReportTemplateDTO$TemplateQuestions': {
        feedbacks: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportQuestionDTO'][];
        petConditions: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingReportQuestionDTO'][];
      };
      'com.moego.server.grooming.dto.groomingreport.GroomingReportThemeConfigDTO': {
        code: string;
        color: string;
        emailBottomImgUrl: string;
        icon: string;
        imgUrl: string;
        lightColor: string;
        name: string;
        recommend: boolean;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        tag: number;
      };
      'com.moego.server.grooming.dto.GroomingServiceOperationDTO': {
        /** Format: int32 */
        businessId?: number;
        comment?: string;
        /** Format: int32 */
        duration?: number;
        /** Format: int32 */
        groomingId?: number;
        /** Format: int32 */
        groomingServiceId?: number;
        /** Format: int64 */
        id?: string | number;
        operationName: string;
        /** Format: int32 */
        petId?: number;
        price: number;
        priceRatio?: number;
        /** Format: int32 */
        staffId?: number;
        /** Format: int32 */
        startTime?: number;
      };
      'com.moego.server.grooming.dto.GroomingTicketCommentsDTO': {
        editFirstName: string;
        editLastName: string;
        /** Format: int64 */
        editTime: string | number;
        /** Format: int32 */
        groomingNoteId: number;
        ticketComments: string;
        /** Format: int32 */
        updateBy: number;
      };
      'com.moego.server.grooming.dto.GroomingTicketDetailDTO': {
        additionalNote: components['schemas']['com.moego.server.grooming.dto.GroomingTicketNotesDTO'];
        allPetsStartAtSameTime: boolean;
        appointmentDate: string;
        appointmentEndDate: string;
        /** Format: int32 */
        appointmentEndTime: number;
        /** Format: int32 */
        appointmentStartTime: number;
        /**
         * Format: int32
         * @description 预约状态
         */
        appointmentStatus: number;
        appointmentTracking: components['schemas']['com.moego.server.grooming.dto.AppointmentTrackingViewDTO'];
        autoAssign: components['schemas']['com.moego.server.grooming.dto.AutoAssignDTO'];
        /** Format: int32 */
        bookOnlineStatus: number;
        /** Format: int32 */
        businessId: number;
        cancelReason: string;
        /** Format: int64 */
        checkInTime: string | number;
        /** Format: int64 */
        checkOutTime: string | number;
        colorCode: string;
        createByFirstName: string;
        createByLastName: string;
        /** Format: int32 */
        createdById: number;
        /** Format: int64 */
        createTime: string | number;
        customerAnswerJson: string;
        /** Format: int32 */
        customerId: number;
        customerQuestionJson: string;
        groomingAlertNotes: components['schemas']['com.moego.server.grooming.dto.GroomingTicketNotesDTO'];
        groomingCustomerInfo: components['schemas']['com.moego.server.grooming.dto.GroomingCustomerInfoDTO'];
        groomingTicketComments: components['schemas']['com.moego.server.grooming.dto.GroomingTicketCommentsDTO'];
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        invoiceId: number;
        isAutoAccept: boolean;
        /** Format: int32 */
        isPaid: number;
        /** Format: int32 */
        isWaitingList: number;
        /** Format: int32 */
        noShow: number;
        noShowFee: number;
        /** Format: int32 */
        noShowInvoiceId: number;
        noShowPaymentStatus: string;
        /** Format: int32 */
        noShowStatus: number;
        noStartTime: boolean;
        /** @description 已支付金额 */
        paidAmount: number;
        petInfoDetails: components['schemas']['com.moego.server.grooming.dto.GroomingPetInfoDetailDTO'][];
        pickupNotificationFailedReason: string;
        /**
         * Format: int32
         * @description pickup notification 发送状态
         */
        pickupNotificationSendStatus: number;
        preAuthInfo: components['schemas']['com.moego.server.payment.dto.PreAuthDTO'];
        /** @description 预先支付的金额 */
        prepaidAmount: number;
        /**
         * Format: double
         * @description 预支付金额占总金额的比例
         */
        prepayRate: number;
        /** Format: int32 */
        prepayStatus: number;
        /** Format: int64 */
        readyTime: string | number;
        /** @description 退款金额 */
        refundAmount: number;
        /** Format: int32 */
        repeatId: number;
        requiredSign: boolean;
        reviewBoosterSent: boolean;
        /** @description 当前 ticket 包含的 service 类型 */
        serviceItems: number[];
        /** Format: int32 */
        source: number;
        sourcePlatform: string;
        /**
         * Format: int32
         * @deprecated
         */
        status: number;
        /** Format: int64 */
        updatedById: string | number;
        /**
         * Format: int64
         * @description 关联的 waitList
         */
        waitListId: string | number;
      };
      'com.moego.server.grooming.dto.GroomingTicketNotesDTO': {
        editFirstName: string;
        editLastName: string;
        /** Format: int64 */
        editTime: string | number;
        /** Format: int32 */
        groomingNoteId: number;
        note: string;
        /** Format: int32 */
        updateBy: number;
      };
      'com.moego.server.grooming.dto.GroomingTicketWindowDetailDTO': {
        additionalNote: string;
        alertNotes: string;
        appointmentDate: string;
        appointmentEndDate: string;
        /** Format: int32 */
        appointmentStartTime: number;
        arrivalWindowTimeString: string;
        autoAssign: components['schemas']['com.moego.server.grooming.dto.AutoAssignDTO'];
        /** Format: int32 */
        bookOnlineStatus: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        checkInTime: string | number;
        /** Format: int64 */
        checkOutTime: string | number;
        colorCode: string;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        createdById: number;
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        customerId: number;
        estimatedTotalPrice: number;
        evaluationServiceDetails: components['schemas']['com.moego.server.grooming.dto.EvaluationServiceDetailDTO'][];
        groomingPetDetails: components['schemas']['com.moego.server.grooming.dto.GroomingPetDetailDTO'][];
        /** Format: int32 */
        id: number;
        isAutoAccept: boolean;
        /** Format: int32 */
        isWaitingList: number;
        noStartTime: boolean;
        oldAppointmentDate: string;
        /** Format: int32 */
        oldAppointmentEndTime: number;
        /** Format: int32 */
        oldAppointmentStartTime: number;
        /** Format: int32 */
        repeatId: number;
        /** Format: int32 */
        source: number;
        /** Format: int32 */
        status: number;
        ticketComments: string;
      };
      'com.moego.server.grooming.dto.HistoryCommentsDTO': {
        appointmentDate: string;
        appointmentEndDate: string;
        /** Format: int32 */
        businessId: number;
        comments: string;
        /** Format: int64 */
        createTime: string | number;
        /**
         * Format: int32
         * @description 创建者
         */
        creator: number;
        creatorFirstName: string;
        creatorLastName: string;
        /**
         * Format: int32
         * @description 编辑者
         */
        editor: number;
        editorFirstName: string;
        editorLastName: string;
        /** Format: int32 */
        groomingId: number;
        /** Format: int64 */
        updateTime: string | number;
      };
      /** @description 根据targetType=65 查询出的该订单的messageDetail id列表 */
      'com.moego.server.grooming.dto.IdAndCreateTimeDTO': {
        /** Format: int32 */
        createTime: number;
        /** Format: int32 */
        id: number;
      };
      'com.moego.server.grooming.dto.InvoicePayOnlineDTO': {
        avatarPath: string;
        /** Format: int32 */
        businessId: number;
        businessInfo: components['schemas']['com.moego.server.business.dto.MoeBusinessDto'];
        businessName: string;
        businessPreference: components['schemas']['com.moego.common.dto.BusinessPreferenceDto'];
        depositInfo: components['schemas']['com.moego.server.grooming.dto.DepositDto'];
        invoiceInfo: components['schemas']['com.moego.server.grooming.dto.InvoiceSummaryDTO'];
        /** @description business 是否在白名单中 */
        isInInvoiceWhiteList: boolean;
        paymentSetting: components['schemas']['com.moego.server.payment.dto.PaymentSettingForClientDTO'];
        preferredTip: components['schemas']['com.moego.server.customer.dto.PreferredTipDto'];
        squareInfo: components['schemas']['com.moego.server.payment.dto.GetSquareTokenResponse'];
        /** @description for stripe */
        stripeAccountId: string;
        tipConfig: components['schemas']['com.moego.server.payment.dto.SmartTipConfigForClientDTO'];
      };
      'com.moego.server.grooming.dto.InvoiceSummaryDTO': {
        amountDue: number;
        appliedPackageServices: components['schemas']['com.moego.server.grooming.dto.InvoiceSummaryDTO$PackageServiceDTO'][];
        /** Format: int64 */
        appointmentCheckInTime: string | number;
        /** Format: int64 */
        appointmentCheckOutTime: string | number;
        appointmentDate: string;
        /** Format: int32 */
        appointmentEndTime: number;
        /** Format: int32 */
        appointmentStartTime: number;
        /** Format: int32 */
        appointmentStatus: number;
        /** @description 本次订单如果是prepay，关联的booking fee，用于C端展示 */
        bookingFeeAmount: number;
        /** Format: int32 */
        businessId: number;
        businessName: string;
        /** Format: int64 */
        checkInTime: string | number;
        /** Format: int64 */
        checkOutTime: string | number;
        /** Format: int64 */
        completeTime: string | number;
        confirmationId: string;
        convenienceFee: number;
        /** Format: int32 */
        createBy: number;
        /** Format: int64 */
        createTime: string | number;
        customerEmail: string;
        customerFirstName: string;
        /** Format: int32 */
        customerId: number;
        customerLastName: string;
        depositInfo: components['schemas']['com.moego.server.grooming.dto.DepositDto'];
        discountAmount: number;
        discountedSubTotalAmount: number;
        discountedSubTotalNoTipAmount: number;
        /** @description add discount details for client credit story, same as com.moego.server.grooming.dto.OrderInvoiceSummaryDTO.discountList */
        discountList: components['schemas']['com.moego.server.grooming.dto.OrderInvoiceSummaryDTO$DiscountDTO'][];
        discountRate: number;
        discountType: string;
        extraChargeReason: string;
        fulfillmentStatus: string;
        /** Format: int32 */
        groomingId: number;
        /** Format: int32 */
        id: number;
        /** @description 提前计算的processingFee，paymentSetting.processingFeePayBy = 1 (client)  */
        initProcessingFee: number;
        items: components['schemas']['com.moego.server.grooming.dto.InvoiceSummaryDTO$ItemDTO'][];
        orderType: string;
        originalAmount: number;
        paidAmount: number;
        paymentAmount: number;
        paymentSummary: components['schemas']['com.moego.common.dto.PaymentSummary'];
        refundChannel: components['schemas']['com.moego.server.payment.dto.RefundChannelDTO'];
        refundedAmount: number;
        remainAmount: number;
        /** @description 本次支付是否需要增加processing fee */
        requiredProcessingFee: boolean;
        status: string;
        subTotalAmount: number;
        taxAmount: number;
        tipsAmount: number;
        tipsRate: number;
        tipsType: string;
        totalAmount: number;
        totalAmountWithFee: number;
        totalCollected: number;
        type: string;
        /** Format: int32 */
        updateBy: number;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.grooming.dto.InvoiceSummaryDTO$ItemDTO': {
        /** Format: int64 */
        createTime: string | number;
        discountAmount: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        invoiceId: number;
        /** Format: int32 */
        petDetailId: number;
        petDetails: components['schemas']['com.moego.server.grooming.dto.InvoiceSummaryDTO$PetDetailDTO'][];
        /** Format: int32 */
        purchasedQuantity: number;
        /** Format: int32 */
        quantity: number;
        serviceDescription: string;
        /** Format: int32 */
        serviceId: number;
        serviceName: string;
        serviceUnitPrice: number;
        taxAmount: number;
        /** Format: int32 */
        taxId: number;
        taxRate: number;
        totalListPrice: number;
        totalSalePrice: number;
        /** @description item类型：service、product、package、noshow */
        type: string;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.grooming.dto.InvoiceSummaryDTO$PackageServiceDTO': {
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        invoiceId: number;
        /** Format: int32 */
        invoiceItemId: number;
        /** Format: int32 */
        packageId: number;
        packageName: string;
        /** Format: int32 */
        packageServiceId: number;
        /** Format: int32 */
        quantity: number;
        /** Format: int32 */
        serviceId: number;
        serviceName: string;
        servicePrice: number;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.grooming.dto.InvoiceSummaryDTO$PetDetailDTO': {
        petBreed: string;
        /** Format: int32 */
        petId: number;
        petName: string;
        /** Format: int32 */
        serviceId: number;
        servicePrice: number;
        /** Format: int32 */
        serviceTime: number;
        staffFirstName: string;
        staffLastName: string;
        /** Format: int64 */
        startTime: string | number;
      };
      'com.moego.server.grooming.dto.LodgingInfo': {
        /** Format: int64 */
        lodgingTypeId: string | number;
        lodgingTypeName: string;
        /** Format: int64 */
        lodgingUnitId: string | number;
        lodgingUnitName: string;
        /** Format: int32 */
        sort: number;
      };
      'com.moego.server.grooming.dto.MoeGroomingServiceDTO': {
        /** Format: int32 */
        bookOnlineAvailable: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        categoryId: number;
        colorCode: string;
        /** Format: int64 */
        companyId: string | number;
        description: string;
        /** Format: int32 */
        duration: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        inactive: number;
        /** Format: int32 */
        isAllStaff: number;
        name: string;
        price: number;
        /** Format: int32 */
        priceUnit: number;
        /** Format: int32 */
        serviceItemType: number;
        /** Format: int32 */
        showBasePrice: number;
        /** Format: int32 */
        sort: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        taxId: number;
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.grooming.dto.MoeRepeatInfoDTO': {
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        customerId: number;
        endOn: string;
        existsAppointments: components['schemas']['com.moego.server.grooming.dto.RepeatAppointmentDto'][];
        /**
         * Format: int32
         * @description 当前预约 upcoming 数量小于 repeat expiry reminder 时 upcoming 的数量，不为空时需要展示
         */
        expiryUpcomingCount: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        isNotice: number;
        /** Format: int32 */
        monthDay: number;
        /** Format: int32 */
        monthWeekDay: number;
        /** Format: int32 */
        monthWeekTimes: number;
        /** Format: int32 */
        repeatBy: number;
        repeatByDays: number[];
        /** Format: int32 */
        repeatEvery: number;
        /** Format: int32 */
        repeatEveryType: number;
        /** Format: int32 */
        repeatType: number;
        setEndOn: string;
        /** @deprecated */
        setEndOnDate: string;
        /** Format: int32 */
        ssAfterDays: number;
        /** Format: int32 */
        ssBeforeDays: number;
        /** Format: int32 */
        ssFlag: number;
        /** Format: int32 */
        staffId: number;
        startsOn: string;
        /** @deprecated */
        startsOnDate: string;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        times: number;
        type: string;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.grooming.dto.ob.BookOnlinePaymentGroupSettingDTO': {
        /** Format: int32 */
        acceptClient: number;
        acceptRule: string;
        cancellationPolicy: string;
        depositAmount: number;
        /** Format: int32 */
        depositPercentage: number;
        /** Format: int32 */
        depositType: number;
        /** Format: int32 */
        paymentType: number;
        preAuthPolicy: string;
        /** Format: int32 */
        preAuthTipEnable: number;
        prepayPolicy: string;
        /** Format: int32 */
        prepayTipEnable: number;
        /** Format: int32 */
        prepayType: number;
      };
      'com.moego.server.grooming.dto.ob.BookOnlinePaymentSettingDTO': {
        cancellationPolicy: string;
        certainGroupSetting?: components['schemas']['com.moego.server.grooming.dto.ob.BookOnlinePaymentGroupSettingDTO'];
        depositAmount: number;
        /** Format: int32 */
        depositPercentage: number;
        /** Format: int32 */
        depositType: number;
        /** Format: int32 */
        paymentType: number;
        preAuthPolicy: string;
        /** Format: int32 */
        preAuthTipEnable: number;
        prepayPolicy: string;
        /** Format: int32 */
        prepayTipEnable: number;
        /** Format: int32 */
        prepayType: number;
      };
      /** @description pet param info, include weight and breedId */
      'com.moego.server.grooming.dto.ob.OBPetDataDTO': {
        /** @description pet breed */
        breed: string;
        /** @description coat */
        coat?: string;
        /** @description hair length, compatible with old version */
        hairLength?: string;
        /**
         * Format: int32
         * @description pet id
         */
        petId?: number;
        /**
         * Format: int32
         * @description pet type id, 1-dog, 2-cat, 11-other
         */
        petTypeId: number;
        /** @description pet weight */
        weight?: string;
      };
      'com.moego.server.grooming.dto.ob.OBServiceListDto': {
        addonsList: components['schemas']['com.moego.server.grooming.dto.ServiceCategoryDTO'][];
        applicableServiceList: components['schemas']['com.moego.server.grooming.dto.PetApplicableServiceDTO'][];
        petAddonsList: {
          [key: string]: components['schemas']['com.moego.server.grooming.dto.ServiceCategoryDTO'][] | undefined;
        };
        petServiceList: {
          [key: string]: components['schemas']['com.moego.server.grooming.dto.ServiceCategoryDTO'][] | undefined;
        };
        serviceList: components['schemas']['com.moego.server.grooming.dto.ServiceCategoryDTO'][];
      };
      /** @description pet applicable service DTO list */
      'com.moego.server.grooming.dto.ob.PetApplicableDTO': {
        applicable: boolean;
        /** Format: int32 */
        petId: number;
      };
      /** @description pet param info */
      'com.moego.server.grooming.dto.ob.PetDataDTO': {
        /**
         * Format: int32
         * @description pet id
         */
        petId: number;
        /** @description service ids */
        serviceIds: number[];
      };
      /** @description selected pet service */
      'com.moego.server.grooming.dto.ob.SelectedPetServiceDTO': {
        petDataDTO: components['schemas']['com.moego.server.grooming.dto.ob.OBPetDataDTO'];
        serviceIdList: number[];
      };
      'com.moego.server.grooming.dto.OrderInvoiceSummaryDTO': {
        appliedPackageServices: components['schemas']['com.moego.server.grooming.dto.PackageServiceDTO'][];
        appointmentInfo: components['schemas']['com.moego.server.grooming.dto.OrderInvoiceSummaryDTO$AppointmentInfoDTO'];
        /** @description 本次订单如果是prepay，关联的booking fee，用于C端展示 */
        bookingFeeAmount: number;
        /** Format: int32 */
        businessId: number;
        businessName: string;
        /** Format: int64 */
        completeTime: string | number;
        confirmationId: string;
        convenienceFee: number;
        /** Format: int32 */
        createBy: number;
        /** Format: int64 */
        createTime: string | number;
        customerEmail: string;
        customerFirstName: string;
        /** Format: int32 */
        customerId: number;
        customerLastName: string;
        depositInfo: components['schemas']['com.moego.server.grooming.dto.DepositDto'];
        discountAmount: number;
        discountedSubTotalAmount: number;
        discountList: components['schemas']['com.moego.server.grooming.dto.OrderInvoiceSummaryDTO$DiscountDTO'][];
        /** @description 订单中的discountMap */
        discountMap: {
          [key: string]:
            | components['schemas']['com.moego.server.grooming.dto.OrderInvoiceSummaryDTO$DiscountDTO']
            | undefined;
        };
        extraChargeReason: string;
        fulfillmentStatus: string;
        /** Format: int32 */
        groomingId: number;
        /** Format: int32 */
        id: number;
        /** @description 提前计算的processingFee，paymentSetting.processingFeePayBy = 1 (client)  */
        initProcessingFee: number;
        items: components['schemas']['com.moego.server.grooming.dto.OrderInvoiceSummaryDTO$OrderItemDTO'][];
        membershipList: components['schemas']['com.moego.server.grooming.dto.OrderInvoiceSummaryDTO$MembershipDTO'][];
        /** Format: int64 */
        orderRefId: string | number;
        orderType: string;
        /** Format: int32 */
        orderVersion: number;
        paidAmount: number;
        paymentAmount: number;
        paymentStatus: string;
        paymentSummary: components['schemas']['com.moego.common.dto.PaymentSummary'];
        preAuthInfo: components['schemas']['com.moego.server.payment.dto.PreAuthDTO'];
        /** @description 根据targetType=65 查询出的该订单的messageDetail id列表 */
        receiptSentList: components['schemas']['com.moego.server.grooming.dto.IdAndCreateTimeDTO'][];
        refundedAmount: number;
        /** @description remainAmount = orderModel.remainAmount (must be non-negative) */
        remainAmount: number;
        /** @description remainAmountV2 = totalAmount - paidAmount + refundedAmount (may be negative) */
        remainAmountV2: number;
        /** @description 本次支付是否需要增加processing fee */
        requiredProcessingFee: boolean;
        status: string;
        subTotalAmount: number;
        /** @description 订单中所有service的subTotalAmount，不包含tax、tips、discount */
        subTotalAmountMap: {
          [key: string]: number | undefined;
        };
        taxAmount: number;
        /** @description tips计算基数，同旧字段originAmount，是订单中服务的原价 */
        tipBasedAmount: number;
        tipsAmount: number;
        tipsRate: number;
        /** @description tip split 计算基数 */
        tipsSplitAmount: number;
        tipsType: string;
        totalAmount: number;
        totalAmountWithFee: number;
        totalCollected: number;
        type: string;
        /** Format: int32 */
        updateBy: number;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.grooming.dto.OrderInvoiceSummaryDTO$AppointmentInfoDTO': {
        appointmentDate: string;
        /** Format: int32 */
        appointmentEndTime: number;
        /** Format: int32 */
        appointmentStartTime: number;
        /** Format: int32 */
        appointmentStatus: number;
        /** Format: int64 */
        checkInTime: string | number;
        /** Format: int64 */
        checkOutTime: string | number;
      };
      /** @description add discount details for client credit story, same as com.moego.server.grooming.dto.OrderInvoiceSummaryDTO.discountList */
      'com.moego.server.grooming.dto.OrderInvoiceSummaryDTO$DiscountDTO': {
        allowedAllClients: boolean;
        allowedAllProducts: boolean;
        allowedAllServices: boolean;
        allowedAllThing: boolean;
        /** Format: int32 */
        applySequence: number;
        applyType: string;
        description: string;
        discountAmount: number;
        discountCode: string;
        /** Format: int64 */
        discountCodeId: string | number;
        discountRate: number;
        discountType: string;
        /** Format: int64 */
        id: string | number;
        productNames: string[];
        serviceNames: string[];
      };
      'com.moego.server.grooming.dto.OrderInvoiceSummaryDTO$MembershipDTO': {
        description: string;
        discountAmount: number;
        /** Format: int64 */
        id: string | number;
        name: string;
      };
      'com.moego.server.grooming.dto.OrderInvoiceSummaryDTO$OrderItemDTO': {
        /** Format: int64 */
        createTime: string | number;
        description: string;
        discountAmount: number;
        discountInfo: components['schemas']['com.moego.server.grooming.dto.OrderInvoiceSummaryDTO$DiscountDTO'];
        /** Format: int32 */
        id: number;
        membershipDetailList: components['schemas']['com.moego.server.grooming.dto.OrderInvoiceSummaryDTO$MembershipDTO'][];
        name: string;
        /** Format: int32 */
        objectId: number;
        /** Format: int32 */
        orderId: number;
        petDetails: components['schemas']['com.moego.server.grooming.dto.OrderItemPetDetailDTO'][];
        petEvaluationDetails: components['schemas']['com.moego.server.grooming.dto.OrderItemPetEvaluationDetailDTO'][];
        /** Format: int32 */
        petId: number;
        /** Format: int32 */
        purchasedQuantity: number;
        /** Format: int32 */
        quantity: number;
        /** Format: int32 */
        staffId: number;
        subTotalAmount: number;
        taxAmount: number;
        taxInfo: components['schemas']['com.moego.server.grooming.dto.OrderInvoiceSummaryDTO$TaxDTO'];
        totalAmount: number;
        /** @description item类型：service、product、package、noshow */
        type: string;
        unitPrice: number;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.grooming.dto.OrderInvoiceSummaryDTO$TaxDTO': {
        /** Format: int64 */
        id: string | number;
        taxAmount: number;
        /** Format: int32 */
        taxId: number;
        taxRate: number;
      };
      'com.moego.server.grooming.dto.OrderItemPetDetailDTO': {
        /** Format: int64 */
        lodgingId: string | number;
        lodgingInfos: components['schemas']['com.moego.server.grooming.dto.LodgingInfo'][];
        lodgingTypeName: string;
        lodgingUnitName: string;
        operationList: components['schemas']['com.moego.server.grooming.dto.GroomingServiceOperationDTO'][];
        petBreed: string;
        /** Format: int32 */
        petDetailId: number;
        /** Format: int32 */
        petId: number;
        petName: string;
        /** Format: int32 */
        quantity: number;
        /** Format: int32 */
        serviceId: number;
        /** Format: int32 */
        serviceItemType: number;
        servicePrice: number;
        /** Format: int32 */
        serviceTime: number;
        staffFirstName: string;
        /** Format: int32 */
        staffId: number;
        staffLastName: string;
        /** Format: int64 */
        startTime: string | number;
      };
      'com.moego.server.grooming.dto.OrderItemPetEvaluationDetailDTO': {
        petBreed: string;
        /** Format: int32 */
        petId: number;
        petName: string;
        /** Format: int32 */
        quantity: number;
        /** Format: int32 */
        serviceId: number;
        /** Format: int32 */
        serviceItemType: number;
        servicePrice: number;
        /** Format: int32 */
        serviceTime: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int64 */
        startTime: string | number;
      };
      'com.moego.server.grooming.dto.PackageServiceDTO': {
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        invoiceId: number;
        /** Format: int32 */
        invoiceItemId: number;
        /** Format: int32 */
        packageId: number;
        packageName: string;
        /** Format: int32 */
        packageServiceId: number;
        /** Format: int32 */
        quantity: number;
        /** Format: int32 */
        serviceId: number;
        serviceName: string;
        servicePrice: number;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.grooming.dto.PetApplicableServiceDTO': {
        applicableAddon: number[];
        applicableService: number[];
        /** Format: int32 */
        petId: number;
      };
      'com.moego.server.grooming.dto.PetCodeInfoDTO': {
        codeNumber: string;
        color: string;
        description: string;
        /** Format: int32 */
        petCodeId: number;
      };
      'com.moego.server.grooming.dto.PetLastServiceDTO': {
        lastAddOnsIds: number[];
        lastServiceIds: number[];
        /** Format: int32 */
        petId: number;
        serviceList: components['schemas']['com.moego.server.grooming.dto.SimpleServiceDto'][];
      };
      'com.moego.server.grooming.dto.PetServiceDTO': {
        /** Format: int32 */
        addOnServiceFilter: number;
        allowedPetSizeList: string;
        /** Format: int32 */
        bookOnlineAvailable: number;
        /** Format: int32 */
        breedFilter: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        categoryId: number;
        /** Format: int32 */
        coatFilter: number;
        colorCode: string;
        description: string;
        /** Format: int32 */
        duration: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        inactive: number;
        isSavePrice: boolean;
        isSaveTime: boolean;
        name: string;
        /** Format: int32 */
        petSizeFilter: number;
        price: number;
        serviceFilter: boolean;
        /** Format: int32 */
        showBasePrice: number;
        /** Format: int32 */
        sort: number;
        /** Format: int32 */
        type: number;
        weightDownLimit: number;
        /** Format: int32 */
        weightFilter: number;
        weightUpLimit: number;
      };
      'com.moego.server.grooming.dto.PreAuthAmountDTO': {
        /**
         * @deprecated
         * @description 本次支付的 booking fee
         */
        bookingFee: number;
        /** @description 本次支付的折扣金额 */
        discountAmount: number;
        /** @description 初始化processing fee */
        initProcessingFee: number;
        serviceChargeAmount: number;
        serviceChargeList: components['schemas']['com.moego.server.grooming.dto.ServiceChargeDTO'][];
        serviceTotal: number;
        taxAmount: number;
        /** @description 使用的 membership ids */
        usedMembershipIds: (string | number)[];
      };
      'com.moego.server.grooming.dto.PrepayAmountDTO': {
        /** @description 本次支付的折扣金额 */
        discountAmount: number;
        /**
         * @deprecated
         * @description 本次支付的 booking fee
         */
        fee: number;
        /** @description 随机产生的 id，用于后续支付和提交流程绑定 */
        guid: string;
        /** @description 初始化 processing fee */
        initProcessingFee: number;
        /** @description 需要支付的 service charge 费用 */
        serviceChargeAmount: number;
        /** @description mandatory 的 service charge 列表 */
        serviceChargeList: components['schemas']['com.moego.server.grooming.dto.ServiceChargeDTO'][];
        /** @description full pay 的服务总价 或 deposit 金额，包含 service、service charge，不包含 tax、booking fee */
        subTotal: number;
        /** @description 选中的服务的tax金额，当查询 deposit 时，tax 为空 */
        taxAmount: number;
        /** @description 使用的 membership ids */
        usedMembershipIds: (string | number)[];
      };
      'com.moego.server.grooming.dto.printcard.ActivityCardAddOnColumn': {
        addOnName: string;
        associatedServiceName: string;
        avatarPath: string;
        breed: string;
        clientFirstName: string;
        clientLastName: string;
        gender: string;
        lodgingInfo: components['schemas']['com.moego.server.grooming.dto.LodgingInfo'];
        /** Format: int32 */
        petId: number;
        petName: string;
        /** Format: int32 */
        petTypeId: number;
        /** Format: int32 */
        quantityPerDay: number;
        staffName: string;
        /** Format: int32 */
        startTime: number;
        weight: string;
      };
      'com.moego.server.grooming.dto.printcard.ActivityCardDetail': {
        addOns: components['schemas']['com.moego.server.grooming.dto.printcard.ActivityCardAddOnColumn'][];
        feedingInstructions: components['schemas']['com.moego.server.grooming.dto.printcard.ActivityCardFeedingColumn'][];
        medicationInstructions: components['schemas']['com.moego.server.grooming.dto.printcard.ActivityCardMedicationColumn'][];
      };
      'com.moego.server.grooming.dto.printcard.ActivityCardFeedingColumn': {
        avatarPath: string;
        breed: string;
        clientFirstName: string;
        clientLastName: string;
        gender: string;
        instruction: components['schemas']['com.moego.server.grooming.dto.printcard.FeedingInstruction'][];
        lodgingInfo: components['schemas']['com.moego.server.grooming.dto.LodgingInfo'];
        /** Format: int32 */
        petId: number;
        petName: string;
        /** Format: int32 */
        petTypeId: number;
        serviceName: string;
        /** Format: int32 */
        time: number;
        weight: string;
      };
      'com.moego.server.grooming.dto.printcard.ActivityCardMedicationColumn': {
        avatarPath: string;
        breed: string;
        clientFirstName: string;
        clientLastName: string;
        gender: string;
        instruction: components['schemas']['com.moego.server.grooming.dto.printcard.MedicationInstruction'][];
        lodgingInfo: components['schemas']['com.moego.server.grooming.dto.LodgingInfo'];
        /** Format: int32 */
        petId: number;
        petName: string;
        /** Format: int32 */
        petTypeId: number;
        serviceName: string;
        /** Format: int32 */
        time: number;
        weight: string;
      };
      'com.moego.server.grooming.dto.printcard.ExtraServiceDetail': {
        /** Format: int32 */
        dateType: number;
        /** Format: int64 */
        endTime: string | number;
        /** Format: int32 */
        quantityPerDay: number;
        requireDedicatedStaff: boolean;
        serviceName: string;
        /** Format: int32 */
        serviceType: number;
        specificDates: string[];
        staffName: string;
        /** Format: int64 */
        startTime: string | number;
      };
      'com.moego.server.grooming.dto.printcard.FeedingInstruction': {
        feedingAmount: string;
        feedingInstruction: string;
        feedingNote: string;
        feedingSource: string;
        feedingType: string;
        feedingUnit: string;
        timeLabelList: string[];
        timeList: number[];
      };
      'com.moego.server.grooming.dto.printcard.MedicationInstruction': {
        medicationAmount: string;
        medicationName: string;
        medicationNote: string;
        medicationUnit: string;
        timeLabelList: string[];
        timeList: number[];
      };
      'com.moego.server.grooming.dto.printcard.PetBelonging': {
        area: string;
        /** Format: int64 */
        id: string | number;
        name: string;
      };
      'com.moego.server.grooming.dto.printcard.PrintCardAppointment': {
        alertNotes: string;
        appointmentDate: string;
        /** Format: int32 */
        appointmentStartTime: number;
        ticketComments: string;
      };
      'com.moego.server.grooming.dto.printcard.PrintCardCustomer': {
        agreementMap: {
          [key: string]: string | undefined;
        };
        clientNotes: string[];
        email: string;
        lastName: string;
        name: string;
        phone: string;
        tagList: string[];
      };
      'com.moego.server.grooming.dto.printcard.PrintCardDetail': {
        appointment: components['schemas']['com.moego.server.grooming.dto.printcard.PrintCardAppointment'];
        customer: components['schemas']['com.moego.server.grooming.dto.printcard.PrintCardCustomer'];
        /** Format: int32 */
        groomingId: number;
        historyList: components['schemas']['com.moego.server.grooming.dto.printcard.PrintCardHistory'][];
        pet: components['schemas']['com.moego.server.grooming.dto.printcard.PrintCardPet'];
        /** Format: int32 */
        petId: number;
        serviceList: components['schemas']['com.moego.server.grooming.dto.printcard.PrintCardServiceInfo'][];
      };
      'com.moego.server.grooming.dto.printcard.PrintCardHistory': {
        appointmentDate: string;
        serviceNameList: string[];
        staffNameList: string[];
        /** Format: int32 */
        startTime: number;
        ticketComment: string;
      };
      'com.moego.server.grooming.dto.printcard.PrintCardOperation': {
        /** Format: int32 */
        duration: number;
        price: string;
        /** Format: int32 */
        staffId: number;
        staffName: string;
        /** Format: int32 */
        startTime: number;
        task: string;
      };
      'com.moego.server.grooming.dto.printcard.PrintCardPet': {
        avatarPath: string;
        birthday: string;
        breed: string;
        gender: string;
        healthIssues: string;
        petAppearanceColor: string;
        petAppearanceNotes: string;
        petCodeBindings: components['schemas']['com.moego.server.grooming.dto.printcard.PrintPetCodeBinding'][];
        petCodeList: string[];
        petFixed: string;
        petName: string;
        petNotes: string[];
        /** Format: int32 */
        petTypeId: number;
        vaccineMap: {
          [key: string]: string | undefined;
        };
        vetName: string;
        vetPhoneNumber: string;
        weight: string;
      };
      'com.moego.server.grooming.dto.printcard.PrintCardServiceInfo': {
        /** @enum {string} */
        careType: ComMoegoServerGroomingDtoPrintcardPrintCardServiceInfoCareType;
        /** Format: int32 */
        duration: number;
        enableOperation: boolean;
        lodgingRoom: string;
        operationList: components['schemas']['com.moego.server.grooming.dto.printcard.PrintCardOperation'][];
        price: string;
        /** Format: int32 */
        priceUnit: number;
        /**
         * @deprecated
         * @enum {string}
         */
        serviceItem: ComMoegoServerGroomingDtoPrintcardPrintCardServiceInfoServiceItem;
        serviceName: string;
        /** Format: int32 */
        serviceType: number;
        /** Format: int32 */
        staffId: number;
        staffName: string;
        /** Format: int32 */
        startTime: number;
      };
      'com.moego.server.grooming.dto.printcard.PrintPetCodeBinding': {
        /** @description pet code abbreviation */
        abbreviation: string;
        /** @description pet code color */
        color: string;
        /** @description description */
        description: string;
        /** @description pet code unique comment */
        uniqueComment: string;
      };
      'com.moego.server.grooming.dto.printcard.SplitLodgingInfo': {
        endDate: string;
        /** Format: int64 */
        lodgingUnitId: string | number;
        lodgingUnitName: string;
        startDate: string;
      };
      'com.moego.server.grooming.dto.printcard.StayCardAppointment': {
        alertNotes: string;
        appointmentEndDate: string;
        /** Format: int32 */
        appointmentEndTime: number;
        appointmentStartDate: string;
        /** Format: int32 */
        appointmentStartTime: number;
        lodgingRoom: string;
        lodgingTypeName: string;
        /** @enum {string} */
        mainServiceItemType: ComMoegoServerGroomingDtoPrintcardStayCardAppointmentMainServiceItemType;
        splitLodgingInfos: components['schemas']['com.moego.server.grooming.dto.printcard.SplitLodgingInfo'][];
        ticketComments: string;
      };
      'com.moego.server.grooming.dto.printcard.StayCardDetail': {
        addOns: string[];
        appointment: components['schemas']['com.moego.server.grooming.dto.printcard.StayCardAppointment'];
        customer: components['schemas']['com.moego.server.grooming.dto.printcard.PrintCardCustomer'];
        extraServiceDetails: components['schemas']['com.moego.server.grooming.dto.printcard.ExtraServiceDetail'][];
        feedingInstructions: components['schemas']['com.moego.server.grooming.dto.printcard.FeedingInstruction'][];
        /** Format: int32 */
        groomingId: number;
        medicationInstructions: components['schemas']['com.moego.server.grooming.dto.printcard.MedicationInstruction'][];
        pet: components['schemas']['com.moego.server.grooming.dto.printcard.PrintCardPet'];
        petBelongings: components['schemas']['com.moego.server.grooming.dto.printcard.PetBelonging'][];
        /** Format: int32 */
        petId: number;
      };
      'com.moego.server.grooming.dto.RepeatAppointmentDto': {
        appointmentDate: string;
        /** Format: int32 */
        appointmentId: number;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        endTime: number;
        /** Format: int32 */
        scheduleType: number;
        staffIdList: number[];
        /** Format: int32 */
        startTime: number;
        /** Format: int32 */
        status: number;
      };
      /** @description preview date list */
      'com.moego.server.grooming.dto.RepeatPreviewInfoDTO': {
        /**
         * Format: int32
         * @description 已存在预约的 id
         */
        appointmentId: number;
        /** @description appointment date */
        date: string;
        /**
         * Format: int32
         * @description 入参传的 serviceTime，和老接口保持一致
         */
        duration: number;
        /**
         * Format: int32
         * @description appointment 的结束时间
         */
        endTime: number;
        /** @description 是否是 history appointment */
        isHistory: boolean;
        /** @description 是否冲突: true-不冲突, false-冲突 */
        isNotConflict: boolean;
        /**
         * Format: int32
         * @description schedule 类型: 1-非 ss 预约, 2-ss 找到时间的预约, 3-ss 找不到时间的预约
         */
        scheduleType: number;
        /** @description 预约内每个 staff 的 conflict info */
        staffConflictInfoList: components['schemas']['com.moego.server.grooming.dto.StaffConflictInfoDTO'][];
        /** @description 预约内的 staff id list */
        staffIdList: number[];
        /**
         * Format: int32
         * @description appointment 的开始时间
         */
        startTime: number;
      };
      'com.moego.server.grooming.dto.RepeatPreviewSummaryDTO': {
        /**
         * Format: int32
         * @description 有冲突的 date 数量
         */
        conflictCount: number;
        /** @description preview date list */
        previewInfoList: components['schemas']['com.moego.server.grooming.dto.RepeatPreviewInfoDTO'][];
        /**
         * Format: int32
         * @description save preview 会移除的 existing appointment 数量
         */
        removedCount: number;
        /**
         * Format: int32
         * @description repeat date 总数
         */
        totalCount: number;
      };
      'com.moego.server.grooming.dto.report.MobileSummaryDTO': {
        /** Format: int32 */
        cancelledAppts: number;
        /** Format: int32 */
        confirmedAppts: number;
        customerIds: number[];
        discount: number;
        earnedDiscount: number;
        earnedNoTipTax: number;
        earnedRevenue: number;
        earnedServiceCharge: number;
        earnedTax: number;
        earnedTips: number;
        expectedDiscount: number;
        expectedNoTipTax: number;
        expectedRevenue: number;
        expectedServiceCharge: number;
        expectedTax: number;
        expectedTips: number;
        /** Format: int32 */
        finishedAppts: number;
        /** Format: int32 */
        finishedPets: number;
        /** Format: int32 */
        intakeFormSubmission: number;
        netSaleRevenue: number;
        /** Format: int32 */
        noShowAppts: number;
        /** Format: int32 */
        onlineBookingNum: number;
        petIds: number[];
        taxes: number;
        tips: number;
        /** Format: int32 */
        totalAppts: number;
        totalRefund: number;
        totalUnpaid: number;
        /** Format: int32 */
        unconfirmedAppts: number;
      };
      'com.moego.server.grooming.dto.report.ReportAppointment': {
        apptDate: string;
        /** Format: int32 */
        apptId: number;
        cancelByName: string;
        /** Format: int32 */
        cancelByType: number;
        /** Format: int32 */
        customerId: number;
        customerName: string;
        /** Format: int32 */
        endTime: number;
        noShow: boolean;
        noShowCharged: boolean;
        orderId: string;
        /** Format: int32 */
        startTime: number;
        /** Format: int32 */
        status: number;
        unpaidAmount: number;
      };
      'com.moego.server.grooming.dto.report.ReportAppointmentResponse': {
        appt: components['schemas']['com.moego.server.grooming.dto.report.ReportAppointment'][];
        /** Format: int32 */
        businessId: number;
        endDate: string;
        startDate: string;
        /** @enum {string} */
        type: ComMoegoServerGroomingDtoReportReportAppointmentResponseType;
      };
      'com.moego.server.grooming.dto.report.ReportApptsNumberDTO': {
        /** Format: int32 */
        cancelledAppts: number;
        /** Format: int32 */
        noShowAppts: number;
        /** Format: int32 */
        onlineBookingAppts: number;
        /** Format: int32 */
        unclosedAppts: number;
        /** Format: int32 */
        unpaidAppts: number;
        /** Format: int32 */
        upcomingAppts: number;
        /** Format: int32 */
        waitingListAppts: number;
      };
      'com.moego.server.grooming.dto.report.ReportPaymentSummaryDto': {
        apptCollectedRevenue: number;
        apptPaidAmount: number;
        apptRefundAmount: number;
        apptSale: number;
        collectedProductPrice: number;
        collectedServicePrice: number;
        collectedTax: number;
        collectedTips: number;
        discount: number;
        netSaleRevenue: number;
        noShowCollectedRevenue: number;
        noShowFee: number;
        noShowPaidAmount: number;
        noShowRefundAmount: number;
        paymentMethod: string;
        sumAmount: number;
        /** Format: int32 */
        ticketNum: number;
        totalCollectedRevenue: number;
        totalPayment: number;
        totalRefund: number;
      };
      'com.moego.server.grooming.dto.SaveRepeatAppointmentListResultDTO': {
        /** Format: int32 */
        notifyAppointmentId: number;
        /** Format: int32 */
        notifyType: number;
        /** Format: int32 */
        updatedCount: number;
      };
      'com.moego.server.grooming.dto.ServiceCategoryDTO': {
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        id: number;
        name: string;
        petServices: components['schemas']['com.moego.server.grooming.dto.PetServiceDTO'][];
        /** Format: int32 */
        sort: number;
        /** Format: int32 */
        type: number;
      };
      /** @description mandatory 的 service charge 列表 */
      'com.moego.server.grooming.dto.ServiceChargeDTO': {
        /** Format: int64 */
        businessId: string | number;
        /** Format: date-time */
        createdAt: string;
        /** Format: int64 */
        createdBy: string | number;
        description: string;
        /** Format: int64 */
        id: string | number;
        isActive: boolean;
        isDeleted: boolean;
        isMandatory: boolean;
        name: string;
        price: number;
        serviceItemTypes: ComMoegoServerGroomingDtoServiceChargeDTOServiceItemTypes[];
        taxAmount: number;
        /** Format: int32 */
        taxId: number;
        /** Format: date-time */
        updatedAt: string;
        /** Format: int64 */
        updatedBy: string | number;
      };
      'com.moego.server.grooming.dto.SimpleServiceDto': {
        /** Format: int32 */
        serviceId: number;
        servicePrice: number;
        /** Format: int32 */
        serviceTime: number;
      };
      'com.moego.server.grooming.dto.ss.ScheduleTimeSlot': {
        /** Format: int32 */
        addUpMinutes: number;
        /** Format: double */
        driveInMiles: number;
        /** Format: int32 */
        driveInMinutes: number;
        /** Format: double */
        driveOutMiles: number;
        /** Format: int32 */
        driveOutMinutes: number;
        /** Format: int32 */
        endTime: number;
        isDriveFromStart: boolean;
        isDriveToEnd: boolean;
        /** Format: int32 */
        startTime: number;
      };
      'com.moego.server.grooming.dto.ss.SmartScheduleResultDto': {
        available: boolean;
        dayMap: {
          [key: string]: components['schemas']['com.moego.server.grooming.dto.ss.SmartScheduleStaffMap'] | undefined;
        };
      };
      'com.moego.server.grooming.dto.ss.SmartScheduleStaffMap': {
        dayStr: string;
        staffMap: {
          [key: string]: components['schemas']['com.moego.server.grooming.dto.ss.SmartScheduleVO'] | undefined;
        };
      };
      'com.moego.server.grooming.dto.ss.SmartScheduleVO': {
        availableRange: components['schemas']['com.moego.server.grooming.dto.ss.ScheduleTimeSlot'][];
        /** Format: int32 */
        businessId: number;
        date: string;
        firstSlot: components['schemas']['com.moego.server.grooming.dto.ss.ScheduleTimeSlot'];
        staffCheckStatus: components['schemas']['com.moego.server.grooming.dto.ss.StaffCheckStatus'];
        /** Format: int32 */
        staffId: number;
      };
      'com.moego.server.grooming.dto.ss.StaffCheckStatus': {
        allGivenStaffs: number[];
        checkedStaffs: number[];
        /** Format: int32 */
        foundStaff: number;
      };
      /** @description 预约内每个 staff 的 conflict info */
      'com.moego.server.grooming.dto.StaffConflictInfoDTO': {
        /** Format: int32 */
        conflictType: number;
        /** Format: int32 */
        duration: number;
        isNotConflict: boolean;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        startTime: number;
      };
      /** @description staff 对应的pet service total duration */
      'com.moego.server.grooming.dto.StaffPetServiceDuration': {
        /**
         * Format: int64
         * @description staff id
         */
        staffId: string | number;
        /**
         * Format: int32
         * @description all pet service duration total
         */
        totalDuration: number;
      };
      'com.moego.server.grooming.dto.TipSplitDetailDTO': {
        /** @description tips to business */
        businessTipAmount: number;
        /**
         * Format: int32
         * @description customized tip type: 1-amount, 2-percentage
         */
        customizedType: number;
        /**
         * Format: int64
         * @description 订单id
         */
        orderId: string | number;
        /**
         * Format: int32
         * @description tip split method: 1-by service, 2-by equally, 3-customized
         */
        splitMethod: number;
        /** @description staff按三种分配方式的金额 */
        staffTipAmountList: components['schemas']['com.moego.server.grooming.dto.TipSplitDetailDTO$StaffTipAmountDTO'][];
      };
      /** @description staff按三种分配方式的金额 */
      'com.moego.server.grooming.dto.TipSplitDetailDTO$StaffTipAmountDTO': {
        /** @description 不同的Split method对应的tips金额 */
        amountMap: {
          [key: string]: components['schemas']['com.moego.server.grooming.dto.AmountPercentagePair'] | undefined;
        };
        /**
         * Format: int32
         * @description staff id
         */
        staffId: number;
      };
      'com.moego.server.grooming.dto.VaccineBindingRecordDto': {
        documentUrls: string[];
        expirationDate: string;
        /** Format: int32 */
        vaccineBindingId: number;
        /** Format: int32 */
        vaccineId: number;
        vaccineName: string;
      };
      'com.moego.server.grooming.dto.WaitingListPetDetailDTO': {
        operationList: components['schemas']['com.moego.server.grooming.dto.GroomingServiceOperationDTO'][];
        petBreed: string;
        /** Format: int32 */
        petDetailId: number;
        /** Format: int32 */
        petId: number;
        petName: string;
        /** Format: int32 */
        serviceId: number;
        serviceName: string;
        staffFirstName: string;
        /** Format: int32 */
        staffId: number;
        staffLastName: string;
      };
      'com.moego.server.grooming.dto.waitlist.DatePreferenceDTO': {
        dayOfWeek: number[];
        exactDate: string[];
        isAnyDate: boolean;
      };
      'com.moego.server.grooming.dto.waitlist.Staff': {
        avatarPath: string;
        firstName: string;
        /** Format: int32 */
        id: number;
        lastName: string;
      };
      'com.moego.server.grooming.dto.waitlist.StaffPreferenceDTO': {
        isAnyone: boolean;
        staffList: components['schemas']['com.moego.server.grooming.dto.waitlist.Staff'][];
      };
      'com.moego.server.grooming.dto.waitlist.TimePreferenceDTO': {
        exactStartTime: number[];
        isAnyTime: boolean;
        timeRange: components['schemas']['com.moego.server.business.dto.TimeRangeDto'][];
      };
      'com.moego.server.grooming.dto.waitlist.WaitListCompatibleDTO': {
        /** Format: date-time */
        createAt: string;
        datePreference: components['schemas']['com.moego.server.grooming.dto.waitlist.DatePreferenceDTO'];
        /** Format: int64 */
        realWaitListId: string | number;
        staffPreference: components['schemas']['com.moego.server.grooming.dto.waitlist.StaffPreferenceDTO'];
        timePreference: components['schemas']['com.moego.server.grooming.dto.waitlist.TimePreferenceDTO'];
      };
      'com.moego.server.grooming.mapperbean.MoeBookOnlineGallery': {
        /** Format: int32 */
        accountId: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        createTime: number;
        /** Format: int32 */
        id: number;
        imagePath: string;
        /** Format: int32 */
        isDelete: number;
        /** Format: int32 */
        isStar: number;
        /** Format: int32 */
        sort: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        updateTime: number;
      };
      /** @description notification for client is not valid, use auto message api instead */
      'com.moego.server.grooming.mapperbean.MoeBookOnlineNotification': {
        /** Format: int32 */
        acceptBusinessType: number;
        /** Format: int32 */
        acceptClientType: number;
        acceptEmailContentTemplate: string;
        acceptEmailSubjectTemplate: string;
        acceptTemplate: string;
        /** Format: int32 */
        autoMoveBusinessType: number;
        /** Format: int32 */
        autoMoveClientType: number;
        autoMoveEmailContentTemplate: string;
        autoMoveEmailSubjectTemplate: string;
        autoMoveTemplate: string;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        declineBusinessType: number;
        /** Format: int32 */
        declineClientType: number;
        declineEmailContentTemplate: string;
        declineEmailSubjectTemplate: string;
        declineTemplate: string;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        submitBusinessType: number;
        /** Format: int32 */
        submitClientType: number;
        submitEmailContentTemplate: string;
        submitEmailSubjectTemplate: string;
        submitTemplate: string;
      };
      'com.moego.server.grooming.mapperbean.MoeBookOnlineProfile': {
        address: string;
        addressDetails: components['schemas']['com.moego.server.grooming.web.vo.client.AddressVO'];
        avatarPath: string;
        businessEmail: string;
        businessHoursJson: string;
        /** Format: int32 */
        businessId: number;
        businessName: string;
        buttonColor: string;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        createTime: number;
        description: string;
        facebook: string;
        google: string;
        /** Format: int32 */
        id: number;
        instagram: string;
        language: string;
        other: string;
        phoneNumber: string;
        tiktok: string;
        /** Format: int32 */
        updateTime: number;
        website: string;
        yelp: string;
      };
      'com.moego.server.grooming.mapperbean.MoeBookOnlineStaffTime': {
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        staffId: number;
        staffSlots: string;
        staffTimes: string;
        /** Format: int32 */
        status: number;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.grooming.mapperbean.MoeBusinessBookOnline': {
        /** Format: int32 */
        acceptClient: number;
        /** Format: int32 */
        allowedSimplifySubmit: number;
        /** Format: int32 */
        appointmentInterval: number;
        /** Format: int32 */
        arrivalWindowAfterMin: number;
        /** Format: int32 */
        arrivalWindowBeforeMin: number;
        /** Format: int32 */
        autoAccept: number;
        /** Format: int32 */
        autoMoveWait: number;
        /** Format: int32 */
        autoRefundDeposit: number;
        /** Format: int32 */
        availableTimeSync: number;
        /** Format: int32 */
        availableTimeType: number;
        bookingRangeEndDate: string;
        /** Format: int32 */
        bookingRangeEndOffset: number;
        /** Format: int32 */
        bookingRangeEndType: number;
        /** Format: int32 */
        bookingRangeStartOffset: number;
        bookOnlineName: string;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        bySlotFarthestAvailable: number;
        /** Format: int32 */
        bySlotSoonestAvailable: number;
        /** Format: int32 */
        bySlotTimeslotFormat: number;
        /** Format: int32 */
        bySlotTimeslotMins: number;
        cancellationPolicy: string;
        /** Format: int64 */
        companyId: string | number;
        county: string;
        /** Format: int64 */
        createTime: string | number;
        depositAmount: number;
        /** Format: int32 */
        depositPercentage: number;
        /** Format: int32 */
        depositType: number;
        description: string;
        displayStaffSelectionPage: boolean;
        /** Format: int32 */
        enableNoShowFee: number;
        /** Format: int32 */
        fakeIt: number;
        /** Format: int32 */
        farestAvailable: number;
        /** Format: int32 */
        groupAcceptClient: number;
        groupCancellationPolicy: string;
        groupDepositAmount: number;
        /** Format: int32 */
        groupDepositPercentage: number;
        /** Format: int32 */
        groupDepositType: number;
        groupFilterRule: string;
        /** Format: int32 */
        groupPaymentType: number;
        groupPreAuthPolicy: string;
        /** Format: int32 */
        groupPreAuthTipEnable: number;
        groupPrepayPolicy: string;
        /** Format: int32 */
        groupPrepayTipEnable: number;
        /** Format: int32 */
        groupPrepayType: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        isByRadius: number;
        /** Format: int32 */
        isByZipcode: number;
        /** Format: int32 */
        isCheckExistingClient: number;
        /** Format: int32 */
        isEnable: number;
        /** Format: int32 */
        isNeedAddress: number;
        /** Format: int32 */
        isNeedSelectTime: number;
        isNeedSendRenewNotification: boolean;
        /** Format: int32 */
        isRedirect: number;
        /** Format: int32 */
        isRequireAgreement: number;
        /** Format: int32 */
        maxAvailableDist: number;
        /** Format: int32 */
        maxAvailableTime: number;
        /** Format: int32 */
        needWithinArea: number;
        /** Format: int32 */
        newClientFlowType: number;
        noShowFee: number;
        overLimitTips: string;
        paymentOptionMap: {
          [key: string]: components['schemas']['com.moego.server.grooming.dto.BookOnlineDTO$PaymentOption'] | undefined;
        };
        placeName: string;
        preAuthPolicy: string;
        /** Format: int32 */
        preAuthTipEnable: number;
        prepayPolicy: string;
        /** Format: int32 */
        prepayTipEnable: number;
        /** Format: int32 */
        prepayType: number;
        requestSubmittedAutoType: string;
        /** Format: int32 */
        serviceAreaEnable: number;
        serviceAreas: number[];
        /** Format: int32 */
        serviceFilter: number;
        settingLat: string;
        settingLng: string;
        settingLocation: string;
        /** Format: int32 */
        showOneAvailableTime: number;
        /** Format: int32 */
        smartScheduleEnable: number;
        /** Format: int32 */
        smartScheduleMaxDist: number;
        /** Format: int32 */
        smartScheduleMaxTime: number;
        /** Format: int32 */
        soonestAvailable: number;
        state: string;
        stateAbbreviation: string;
        /** Format: int32 */
        timeslotFormat: number;
        /** Format: int32 */
        timeslotMins: number;
        /** Format: int64 */
        updateTime: string | number;
        /** Format: int32 */
        useVersion: number;
        /** Format: int32 */
        weightLimit: number;
        /** Format: int32 */
        weightLimitNotify: number;
        zipCode: string;
        zipCodes: string;
      };
      'com.moego.server.grooming.mapperbean.MoeGroomingDataRule': {
        /** Format: int32 */
        alertNotes: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        city: number;
        /** Format: int32 */
        clientFullAddress: number;
        /** Format: int32 */
        clientFullName: number;
        /** Format: int32 */
        clientPhoneNumber: number;
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        id: number;
        monthlyViewCustomization: string;
        /** Format: int32 */
        petNameBreed: number;
        /** Format: int32 */
        serviceName: number;
        /** Format: int32 */
        servicePrice: number;
        /** Format: int32 */
        ticketComments: number;
        /** Format: int64 */
        updateTime: string | number;
        /** Format: int32 */
        zipcode: number;
      };
      'com.moego.server.grooming.mapperbean.MoeGroomingRepeat': {
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        customerId: number;
        endOn: string;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        isNotice: number;
        /** Format: int32 */
        monthDay: number;
        /** Format: int32 */
        monthWeekDay: number;
        /** Format: int32 */
        monthWeekTimes: number;
        repeatBy: string;
        repeatByDays: string;
        /** Format: int32 */
        repeatEvery: number;
        /** Format: int32 */
        repeatEveryType: number;
        /** Format: int32 */
        repeatType: number;
        /** Format: date-time */
        setEndOn: string;
        /** Format: int32 */
        ssAfterDays: number;
        /** Format: int32 */
        ssBeforeDays: number;
        /** Format: int32 */
        ssFlag: number;
        /** Format: int32 */
        staffId: number;
        /** Format: date-time */
        startsOn: string;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        times: number;
        type: string;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.grooming.mapperbean.MoeZipcode': {
        countryName: string;
        county: string;
        /** Format: date-time */
        createdAt: string;
        /** Format: int32 */
        id: number;
        lat: string;
        lng: string;
        placeId: string;
        placeName: string;
        state: string;
        stateAbbreviation: string;
        status: boolean;
        /** Format: date-time */
        updatedAt: string;
        zipCode: string;
      };
      'com.moego.server.grooming.params.AddRepeatParams': {
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        customerId: number;
        isExistAppt: boolean;
        /** Format: int32 */
        isNotice: number;
        /** Format: int32 */
        monthDay: number;
        /** Format: int32 */
        monthWeekDay: number;
        /** Format: int32 */
        monthWeekTimes: number;
        repeatBy: string;
        /** Format: int32 */
        repeatEvery: number;
        /** Format: int32 */
        repeatEveryType: number;
        /** Format: int32 */
        repeatType: number;
        setEndOn: string;
        smartSchedule: boolean;
        /** Format: int32 */
        ssAfterDays: number;
        /** Format: int32 */
        ssBeforeDays: number;
        /** Format: int32 */
        staffId: number;
        startsOn: string;
        /** Format: int32 */
        times: number;
        type: string;
      };
      'com.moego.server.grooming.params.appointment.conflict.BatchConflictCheckParams': {
        conflictCheckParamsList: components['schemas']['com.moego.server.grooming.params.appointment.conflict.ConflictCheckParams'][];
      };
      'com.moego.server.grooming.params.appointment.conflict.ConflictCheckParams': {
        /**
         * Format: int32
         * @description 当前 appointment id，用于检查冲突时排除自己，新预约不用传
         */
        appointmentId?: number;
        /** @description 检查冲突的日期 */
        date: string;
        /**
         * Format: int32
         * @description 检查冲突的时长
         */
        duration?: number;
        /** @description 检查冲突的 staff 信息 */
        staffConflictCheckParams: components['schemas']['com.moego.server.grooming.params.RepeatStaffInfoParams'][];
        /**
         * Format: int32
         * @description 检查冲突的时间
         */
        startTime?: number;
      };
      'com.moego.server.grooming.params.appointment.DeletePetParams': {
        allPetsStartAtSameTime?: boolean;
        /** Format: int64 */
        appointmentId: string | number;
        /** Format: int32 */
        petId: number;
        /**
         * Format: int32
         * @description repeat type: 1-only this, 2-apply to upcoming, 3-apply to all
         */
        repeatType?: number;
      };
      'com.moego.server.grooming.params.appointment.EditAppointmentColorCodeParams': {
        /**
         * Format: int64
         * @description appointment id
         */
        appointmentId: string | number;
        /** @description color code */
        colorCode: string;
        /**
         * Format: int32
         * @description repeat type: 1-only this, 2-apply to upcoming, 3-apply to all
         */
        repeatType: number;
      };
      'com.moego.server.grooming.params.appointment.EditAppointmentPetDetailsParams': {
        allPetsStartAtSameTime?: boolean;
        /** Format: int64 */
        appointmentId: string | number;
        petList: components['schemas']['com.moego.server.grooming.params.appointment.PetParams'][];
        /**
         * Format: int32
         * @description repeat type: 1-only this, 2-apply to upcoming, 3-apply to all
         */
        repeatType?: number;
      };
      'com.moego.server.grooming.params.appointment.EditPetParams': {
        allPetsStartAtSameTime?: boolean;
        /** Format: int64 */
        appointmentId: string | number;
        /** Format: int32 */
        originPetId?: number;
        petParams: components['schemas']['com.moego.server.grooming.params.appointment.PetParams'];
        /**
         * Format: int32
         * @description repeat type: 1-only this, 2-apply to upcoming, 3-apply to all
         */
        repeatType?: number;
      };
      'com.moego.server.grooming.params.appointment.OperationParams': {
        /** Format: int32 */
        duration: number;
        operationName: string;
        price: number;
        priceRatio: number;
        /** Format: int64 */
        staffId: string | number;
      };
      'com.moego.server.grooming.params.appointment.PetInstructionFeedingParams': {
        /** Format: int32 */
        instruction: number;
        scheduleList: components['schemas']['com.moego.server.grooming.params.appointment.PetInstructionScheduleParams'][];
        /** Format: int32 */
        source: number;
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.grooming.params.appointment.PetInstructionParams': {
        feedingParamsList: components['schemas']['com.moego.server.grooming.params.appointment.PetInstructionFeedingParams'][];
        medicationParamsList: components['schemas']['com.moego.server.grooming.params.appointment.PetInstructionScheduleParams'][];
        /** Format: int32 */
        petId: number;
      };
      'com.moego.server.grooming.params.appointment.PetInstructionScheduleParams': {
        amount: number;
        timeList: number[];
        /** Format: int32 */
        units: number;
      };
      'com.moego.server.grooming.params.appointment.PetParams': {
        instruction?: components['schemas']['com.moego.server.grooming.params.appointment.PetInstructionParams'];
        /** Format: int32 */
        petId: number;
        serviceList?: components['schemas']['com.moego.server.grooming.params.appointment.ServiceAndOperationParams'][];
      };
      'com.moego.server.grooming.params.appointment.QuickAddAppointmentParam': {
        alertNotes?: string;
        allPetsStartAtSameTime?: boolean;
        colorCode: string;
        /** Format: int64 */
        customerId: string | number;
        /** @description end date, YYYY-MM-DD */
        endDate?: string;
        /**
         * Format: int32
         * @description end time, 当天的分钟数
         */
        endTime?: number;
        petList?: components['schemas']['com.moego.server.grooming.params.appointment.PetParams'][];
        preAuthParams?: components['schemas']['com.moego.server.grooming.params.PreAuthParams'];
        /** Format: int32 */
        source: number;
        /** @description start date, YYYY-MM-DD */
        startDate: string;
        /**
         * Format: int32
         * @description start time, 当天的分钟数
         */
        startTime: number;
        ticketComment?: string;
      };
      'com.moego.server.grooming.params.appointment.ServiceAndOperationParams': {
        enableOperation?: boolean;
        endDate?: string;
        /** Format: int64 */
        lodgingId?: string | number;
        operationList?: components['schemas']['com.moego.server.grooming.params.appointment.OperationParams'][];
        /**
         * @description 保存时间的类型，1 - this future, 2 - do not save, 3 - this following, 4 - all upcoming
         * @enum {string}
         */
        scopeTypePrice?: ComMoegoServerGroomingParamsAppointmentServiceAndOperationParamsScopeTypePrice;
        /**
         * @description 保存价格的类型，1 - this future, 2 - do not save, 3 - this following, 4 - all upcoming
         * @enum {string}
         */
        scopeTypeTime?: ComMoegoServerGroomingParamsAppointmentServiceAndOperationParamsScopeTypeTime;
        /** Format: int32 */
        serviceId: number;
        /** @enum {string} */
        serviceItemEnum?: ComMoegoServerGroomingParamsAppointmentServiceAndOperationParamsServiceItemEnum;
        servicePrice?: number;
        /** Format: int32 */
        serviceTime?: number;
        /** Format: int32 */
        serviceType?: number;
        /** Format: int32 */
        staffId?: number;
        startDate?: string;
        /** Format: int32 */
        startTime?: number;
        /** Format: int32 */
        workMode?: number;
      };
      'com.moego.server.grooming.params.appointment.SetMultiPetsStartTime': {
        allPetsStartAtSameTime: boolean;
        /** Format: int64 */
        appointmentId: string | number;
        petIds: number[];
      };
      'com.moego.server.grooming.params.appointment.StaffUpcomingAppointmentCountParams': {
        /** @description 需要查 upcoming appointment count 的 business id list，不传默认查询 staff 所有 working location */
        businessIds?: (string | number)[];
        /**
         * Format: int64
         * @description 需要查 upcoming appointment count 的 staff id，必传
         */
        staffId: string | number;
        /** Format: int32 */
        tokenCompanyId?: number;
      };
      'com.moego.server.grooming.params.appointment.StaffUpcomingOperationCountParams': {
        /** Format: int64 */
        businessId: string | number;
        /** Format: int64 */
        sourceStaffId: string | number;
        /** Format: int64 */
        targetStaffId: string | number;
      };
      'com.moego.server.grooming.params.appointment.TransferAppointmentParamsV2': {
        /**
         * Format: int64
         * @description 需要转移 appointment 的 staff id，必传
         */
        sourceStaffId: string | number;
        /** @description 需要转移 appointment 的 business id 和 target staff id list，必传 */
        transferList: components['schemas']['com.moego.server.grooming.params.appointment.TransferAppointmentParamsV2$LocationStaffPair'][];
      };
      /** @description 需要转移 appointment 的 business id 和 target staff id list，必传 */
      'com.moego.server.grooming.params.appointment.TransferAppointmentParamsV2$LocationStaffPair': {
        /** Format: int64 */
        businessId: string | number;
        /** Format: int64 */
        targetStaffId: string | number;
      };
      'com.moego.server.grooming.params.appointment.UpdateActionTimeParams': {
        /**
         * Format: int64
         * @description action time
         */
        actionTime: string | number;
        /**
         * Format: int64
         * @description appointment id
         */
        appointmentId: string | number;
        /**
         * Format: int32
         * @description 预约状态
         */
        statusForTimeToChange: number;
      };
      'com.moego.server.grooming.params.appointment.WaitListRescheduleParams': {
        /**
         * Format: int32
         * @description staff id
         */
        staffId: number;
        /** @description start date, YYYY-MM-DD */
        startDate: string;
        /**
         * Format: int32
         * @description start time, 当天的分钟数
         */
        startTime: number;
        /**
         * Format: int64
         * @description waiting list id
         */
        waitListId?: string | number;
      };
      'com.moego.server.grooming.params.AppointmentBatchCheckParams': {
        appointmentCheckParamList: components['schemas']['com.moego.server.grooming.params.AppointmentCheckParams'][];
      };
      'com.moego.server.grooming.params.AppointmentBlockParams': {
        appointmentDate?: string;
        appointmentTime: string;
        checkGcExport?: boolean;
        colorCode: string;
        desc: string;
        /** Format: int32 */
        endTime: number;
        /** Format: int32 */
        repeatId?: number;
        /** Format: int32 */
        source?: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        startTime: number;
        /** Format: int32 */
        ticketId?: number;
        /** Format: int32 */
        tokenBusinessId?: number;
        /** Format: int64 */
        tokenCompanyId?: string | number;
        /** Format: int32 */
        tokenStaffId?: number;
      };
      'com.moego.server.grooming.params.AppointmentCheckParams': {
        appointmentDate?: string;
        appointmentTime: string;
        /** Format: int32 */
        businessId?: number;
        /** Format: int32 */
        duration: number;
        /** Format: int32 */
        groomingId?: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        startTime: number;
        /** Format: int32 */
        tokenStaffId?: number;
      };
      'com.moego.server.grooming.params.AppointmentParams': {
        additionalNote?: string;
        alertNotes?: string;
        allPetsStartAtSameTime?: boolean;
        appointmentDateString: string;
        /** Format: int32 */
        appointmentStartTime?: number;
        bookingFlowId?: string;
        /** Format: int32 */
        bookOnlineStatus?: number;
        /** Format: int32 */
        businessId?: number;
        colorCode: string;
        /** Format: int64 */
        companyId?: string | number;
        /** Format: int32 */
        createdById?: number;
        /** Format: int32 */
        customerAddressId?: number;
        /** Format: int32 */
        customerId?: number;
        endDate?: string;
        /** Format: int32 */
        id?: number;
        isAutoAccept?: boolean;
        isNeedUpdateAlertNotes?: boolean;
        /** Format: int32 */
        isPaid?: number;
        isRepeatFirstAppt?: boolean;
        /** Format: int32 */
        isWaitingList?: number;
        noStartTime?: boolean;
        /** Format: int32 */
        outOfArea?: number;
        petServices?: components['schemas']['com.moego.server.grooming.params.PetDetailParams'][];
        preAuthCardNumber?: string;
        preAuthEnable?: boolean;
        preAuthPaymentMethod?: string;
        /** Format: int32 */
        repeatId?: number;
        /** Format: int64 */
        repeatUpdateDaysDiff?: string | number;
        /** Format: int32 */
        scheduleType?: number;
        /** Format: int32 */
        source?: number;
        sourcePlatform?: string;
        /** Format: int32 */
        status?: number;
        ticketComments?: string;
      };
      'com.moego.server.grooming.params.AppointmentRepeatModifyParams': {
        appointmentDateString?: string;
        /** Format: int32 */
        appointmentStartTime?: number;
        /** Format: int32 */
        businessId?: number;
        endDate?: string;
        /** Format: int32 */
        id: number;
        preAuthParams?: components['schemas']['com.moego.server.grooming.params.PreAuthParams'];
        /** Format: int32 */
        repeatType: number;
        serviceList?: components['schemas']['com.moego.server.grooming.params.PetDetailParams'][];
        /** Format: int32 */
        staffId?: number;
        startAtSameTime?: boolean;
      };
      'com.moego.server.grooming.params.AppointmentRepeatParams': {
        alertNotes?: string;
        allPetsStartAtSameTime?: boolean;
        appointmentDateString: string;
        /** Format: int32 */
        appointmentStartTime: number;
        /** Format: int32 */
        businessId?: number;
        colorCode: string;
        /** Format: int64 */
        companyId?: string | number;
        /** Format: int32 */
        createdById?: number;
        /** Format: int32 */
        customerAddressId?: number;
        /** Format: int32 */
        customerId?: number;
        endDate?: string;
        petServices: components['schemas']['com.moego.server.grooming.params.PetDetailParams'][];
        preAuthCardNumber?: string;
        preAuthEnable?: boolean;
        preAuthPaymentMethod?: string;
        /** Format: int32 */
        repeatId?: number;
        /** Format: int32 */
        scheduleType?: number;
        /** Format: int32 */
        source: number;
        ticketComments?: string;
      };
      'com.moego.server.grooming.params.ApptReopenParams': {
        /** Format: int32 */
        groomingId: number;
      };
      'com.moego.server.grooming.params.BlockRepeatParams': {
        colorCode: string;
        desc: string;
        /** Format: int32 */
        endTime: number;
        repeatParams?: components['schemas']['com.moego.server.grooming.params.AddRepeatParams'];
        /** Format: int32 */
        source?: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        startTime: number;
      };
      'com.moego.server.grooming.params.BookOnlineAgreementParams': {
        /** Format: int32 */
        agreementConfirmed: number;
        agreementContent: string;
        agreementHeader: string;
        /** Format: int32 */
        agreementId: number;
        signature: string;
      };
      'com.moego.server.grooming.params.BookOnlineCustomerAdditionalParams': {
        preferredDay: number[];
        /** Format: int32 */
        preferredFrequencyDay: number;
        /** Format: int32 */
        preferredFrequencyType: number;
        /** Format: int32 */
        preferredGroomerId: number;
        preferredTime: number[];
        referralSourceDesc: string;
        /** Format: int32 */
        referralSourceId: number;
      };
      'com.moego.server.grooming.params.BookOnlineCustomerParams': {
        address: string;
        address1: string;
        address2: string;
        /** Format: int32 */
        addressId: number;
        answersMap: {
          [key: string]: any;
        };
        /** Format: date-time */
        birthday: string;
        /** @description credit card token for stripe or square */
        chargeToken: string;
        city: string;
        country: string;
        /** Format: int32 */
        customerId: number;
        email: string;
        firstName: string;
        /** @description true if client supplied */
        hasStripeCard: boolean;
        isProfileRequestAddress: boolean;
        lastName: string;
        lat: string;
        lng: string;
        phoneNumber: string;
        state: string;
        stripeCustomerId: string;
        zipcode: string;
      };
      'com.moego.server.grooming.params.BookOnlinePetParams': {
        /** @deprecated */
        addOnIds?: number[];
        addons?: components['schemas']['com.moego.server.grooming.params.BookOnlinePetParams$Addon'][];
        avatarPath?: string;
        behavior?: string;
        /** @description 日期格式： 2020-02-08 */
        birthday?: string;
        breed: string;
        /** Format: int32 */
        breedMix?: number;
        emergencyContactName?: string;
        emergencyContactPhone?: string;
        endDate?: string;
        /** Format: int32 */
        expiryNotification?: number;
        fixed?: string;
        /** Format: int32 */
        gender?: number;
        hairLength?: string;
        healthIssues?: string;
        isSelected?: boolean;
        /** Format: int32 */
        petId?: number;
        petImage?: string;
        petName: string;
        petQuestionAnswers?: {
          [key: string]: any;
        };
        /** Format: int32 */
        petTypeId?: number;
        /** Format: int32 */
        serviceId?: number;
        startDate?: string;
        vaccineList?: components['schemas']['com.moego.common.params.VaccineParams'][];
        vetAddress?: string;
        vetName?: string;
        vetPhone?: string;
        virtualPetId?: boolean;
        weight?: string;
      };
      'com.moego.server.grooming.params.BookOnlinePetParams$Addon': {
        dates: string[];
        /** Format: int32 */
        dateType: number;
        /** Format: int32 */
        id: number;
        isEveryDay: boolean;
        /** Format: int32 */
        quantityPerDay: number;
        startDate: string;
      };
      'com.moego.server.grooming.params.BookOnlineQuestionListParams': {
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        questions: components['schemas']['com.moego.server.grooming.params.BookOnlineQuestionParams'][];
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.grooming.params.BookOnlineQuestionParams': {
        /** Format: int32 */
        acceptCustomerType: number;
        /** Format: int32 */
        acceptPetEntryType: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int64 */
        createTime: string | number;
        extraJson: string;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        isAllowChange: number;
        /** Format: int32 */
        isAllowDelete: number;
        /** Format: int32 */
        isAllowEdit: number;
        /** Format: int32 */
        isRequired: number;
        /** Format: int32 */
        isShow: number;
        placeholder: string;
        question: string;
        /** Format: int32 */
        questionType: number;
        /** Format: int32 */
        sort: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        type: number;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.grooming.params.BookOnlineSubmitParams': {
        agreements: components['schemas']['com.moego.server.grooming.params.BookOnlineAgreementParams'][];
        /** @description 日期格式： 2020-02-08 */
        appointmentDate?: string;
        /** Format: int32 */
        appointmentStartTime?: number;
        bookOnlineCustomerAdditionalParams?: components['schemas']['com.moego.server.grooming.params.BookOnlineCustomerAdditionalParams'];
        /**
         * Format: int32
         * @description 内部赋值字段
         */
        businessId?: number;
        /** @description 临时字段，value=base64(customerId)，仅用来提供给 workflow 临时使用，尽快过度到 session 获取，不可靠值 */
        clientId?: string;
        /**
         * Format: int64
         * @description 内部赋值字段
         */
        companyId?: string | number;
        customerData: components['schemas']['com.moego.server.grooming.params.BookOnlineCustomerParams'];
        discountCodeParams?: components['schemas']['com.moego.server.grooming.params.ob.DiscountCodeParams'];
        /** @description Appointment end date */
        endDate?: string;
        fromPetParentPortal?: boolean;
        /** Format: int32 */
        isAgreePolicy?: number;
        membershipParams?: components['schemas']['com.moego.server.grooming.params.ob.MembershipParams'];
        note?: string;
        orderId?: string;
        /** Format: int32 */
        outOfArea?: number;
        petData: components['schemas']['com.moego.server.grooming.params.BookOnlinePetParams'][];
        preAuthDetail?: components['schemas']['com.moego.server.grooming.params.ob.PreAuthDetailParams'];
        prepayDetail?: components['schemas']['com.moego.server.grooming.params.ob.PrepayDetailParams'];
        /** @description prepay guid，用于找到支付记录，绑定到新创建的订单，无支付时不需传 */
        prepayGuid?: string;
        /** @enum {string} */
        sourceType?: ComMoegoServerGroomingParamsBookOnlineSubmitParamsSourceType;
        /** Format: int32 */
        staffId?: number;
      };
      'com.moego.server.grooming.params.BusinessBookOnlineParams': {
        /** Format: int32 */
        acceptBusinessType: number;
        /** Format: int32 */
        acceptClient: number;
        /** Format: int32 */
        acceptClientType: number;
        acceptEmailContentTemplate: string;
        acceptEmailSubjectTemplate: string;
        acceptTemplate: string;
        /** Format: int32 */
        allowedSimplifySubmit: number;
        /**
         * Format: int32
         * @deprecated
         */
        appointmentInterval: number;
        /** Format: int32 */
        arrivalWindowAfterMin: number;
        /** Format: int32 */
        arrivalWindowBeforeMin: number;
        /** Format: int32 */
        autoAccept: number;
        /** Format: int32 */
        autoMoveBusinessType: number;
        /** Format: int32 */
        autoMoveClientType: number;
        autoMoveEmailContentTemplate: string;
        autoMoveEmailSubjectTemplate: string;
        autoMoveTemplate: string;
        /** Format: int32 */
        autoMoveWait: number;
        /** Format: int32 */
        availableTimeType: number;
        bookingRangeEndDate: string;
        /** Format: int32 */
        bookingRangeEndOffset: number;
        /** Format: int32 */
        bookingRangeEndType: number;
        /** Format: int32 */
        bookingRangeStartOffset: number;
        bookOnlineName: string;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        bySlotFarthestAvailable: number;
        /** Format: int32 */
        bySlotSoonestAvailable: number;
        /** Format: int32 */
        bySlotTimeslotFormat: number;
        /** Format: int32 */
        bySlotTimeslotMins: number;
        cancellationPolicy: string;
        county: string;
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        declineBusinessType: number;
        /** Format: int32 */
        declineClientType: number;
        declineEmailContentTemplate: string;
        declineEmailSubjectTemplate: string;
        declineTemplate: string;
        depositAmount: number;
        /** Format: int32 */
        depositPercentage: number;
        /** Format: int32 */
        depositType: number;
        description: string;
        displayStaffSelectionPage: boolean;
        /** Format: int32 */
        enableNoShowFee: number;
        /** Format: int32 */
        fakeIt: number;
        /** Format: int32 */
        farestAvailable: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        isByRadius: number;
        /** Format: int32 */
        isByZipcode: number;
        /** Format: int32 */
        isCheckExistingClient: number;
        /** Format: int32 */
        isEnable: number;
        /** Format: int32 */
        isNeedAddress: number;
        /** Format: int32 */
        isNeedSelectTime: number;
        /** Format: int32 */
        isRedirect: number;
        /** Format: int32 */
        isRequireAgreement: number;
        /** Format: int32 */
        maxAvailableDist: number;
        /** Format: int32 */
        maxAvailableTime: number;
        /** Format: int32 */
        needWithinArea: number;
        /** Format: int32 */
        newClientFlowType: number;
        noShowFee: number;
        overLimitTips: string;
        placeName: string;
        preAuthPolicy: string;
        /** Format: int32 */
        preAuthTipEnable: number;
        prepayPolicy: string;
        /** Format: int32 */
        prepayTipEnable: number;
        /** Format: int32 */
        prepayType: number;
        requestSubmittedAutoType: string;
        /** Format: int32 */
        serviceAreaEnable: number;
        serviceAreas: number[];
        /** Format: int32 */
        serviceFilter: number;
        settingLat: string;
        settingLng: string;
        settingLocation: string;
        /** Format: int32 */
        showOneAvailableTime: number;
        /** Format: int32 */
        smartScheduleEnable: number;
        /**
         * Format: int32
         * @deprecated
         */
        smartScheduleMaxDist: number;
        /**
         * Format: int32
         * @deprecated
         */
        smartScheduleMaxTime: number;
        /** Format: int32 */
        soonestAvailable: number;
        state: string;
        stateAbbreviation: string;
        /** Format: int32 */
        submitBusinessType: number;
        /** Format: int32 */
        submitClientType: number;
        submitEmailContentTemplate: string;
        submitEmailSubjectTemplate: string;
        submitTemplate: string;
        /** Format: int32 */
        timeslotFormat: number;
        /** Format: int32 */
        timeslotMins: number;
        /** Format: int64 */
        updateTime: string | number;
        /** Format: int32 */
        useVersion: number;
        /** Format: int32 */
        weightLimit: number;
        /** Format: int32 */
        weightLimitNotify: number;
        zipCode: string;
        zipCodes: string;
      };
      'com.moego.server.grooming.params.CalendarCheckParams': {
        appointmentDate: string;
        /** Format: int32 */
        duration: number;
        /** Format: int32 */
        groomingId: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        startTime: number;
      };
      'com.moego.server.grooming.params.CancelParams': {
        /** Format: int32 */
        accountId: number;
        /** @description 控制是否自动触发关联的订单的 refund。true 需要， false 不需要 */
        autoRefundOrder: boolean;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        cancelByType: number;
        cancelReason: string;
        /** @description 是否检查预约状态，如果是 finish 则不 cancelled */
        checkFinishStatus: boolean;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        noShow: number;
        /** @description 预先支付金额是否退款，只针对OB订单有效 */
        refundPrepaid: boolean;
        releasePreAuth: boolean;
        /**
         * Format: int32
         * @description repeat 预约取消种类，1-只取消当前 2-this one and following 3-all
         */
        repeatType: number;
      };
      'com.moego.server.grooming.params.CheckParams': {
        /** Format: int32 */
        businessId?: number;
        /** Format: int32 */
        id: number;
        /** Format: int64 */
        time?: string | number;
      };
      'com.moego.server.grooming.params.ColorEditParams': {
        /** Format: int32 */
        businessId?: number;
        colorCode: string;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        repeatType: number;
      };
      'com.moego.server.grooming.params.ConfirmParams': {
        /** Format: int32 */
        accountId: number;
        /** Format: int32 */
        businessId: number;
        /**
         * Format: int32
         * @description message method
         */
        confirmByMethod: number;
        /** Format: int32 */
        confirmByType: number;
        /** Format: int32 */
        id: number;
      };
      'com.moego.server.grooming.params.DeleteAppointmentParams': {
        appointmentDate: string;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        ids: number[];
        /** Format: int32 */
        repeatId: number;
      };
      'com.moego.server.grooming.params.DepositVo': {
        amount: number;
        /**
         * Format: int32
         * @description 通过cookie解析出的token 自动设置
         */
        businessId?: number;
        description?: string;
        /** Format: int32 */
        invoiceId?: number;
        /** @description 顾客支付时是否需要添加processing fee */
        requiredProcessingFee?: boolean;
        /** Format: int32 */
        staffId?: number;
      };
      'com.moego.server.grooming.params.EditCommentsParams': {
        /**
         * Format: int32
         * @deprecated
         */
        accountId: number;
        alertNotes: string;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int64 */
        petId: string | number;
        /**
         * Format: int32
         * @description 修改 repeat 预约的 comments 类型：1-only this, 2-this and following, 3-all
         */
        repeatType: number;
        /** Format: int32 */
        staffId: number;
        ticketComments: string;
        /** Format: int32 */
        ticketId: number;
      };
      'com.moego.server.grooming.params.EditContextParams': {
        /** Format: int32 */
        appointmentId: number;
        context: string;
      };
      'com.moego.server.grooming.params.EditIdParams': {
        /** Format: int32 */
        accountId: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        id: number;
        isCalledByTaskModule: boolean;
        /** @description 内部同步参数，标记是否为定金 */
        isDeposit: boolean;
        isFromOB: boolean;
        /** Format: int32 */
        isPaid: number;
        nowShowFee: number;
      };
      'com.moego.server.grooming.params.EditPetDetailParams': {
        appointmentDate?: string;
        endDate?: string;
        /** Format: int32 */
        groomingId: number;
        /** Format: int32 */
        petDetailId: number;
        /** Format: int32 */
        serviceTime: number;
        /** Format: int64 */
        startTime: string | number;
      };
      'com.moego.server.grooming.params.EditRepeatParams': {
        /** Format: int32 */
        businessId?: number;
        endsOn?: string;
        /** Format: int32 */
        repeatId: number;
        /** Format: int32 */
        staffId?: number;
        /** Format: int32 */
        times?: number;
        /** @description 1：次数 	 2：设置结束时间 */
        type: string;
      };
      /** @description content to preview */
      'com.moego.server.grooming.params.groomingreport.GroomingReportContentParams': {
        feedbacks: components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportQuestionParams'][];
        petConditions: components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportQuestionParams'][];
        recommendation: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingRecommendation'];
        showcase: string[];
      };
      'com.moego.server.grooming.params.groomingreport.GroomingReportInfoParams': {
        content: components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportContentParams'];
        /** Format: int32 */
        groomingId?: number;
        /** Format: int32 */
        id?: number;
        /** Format: int32 */
        petId?: number;
        /** Format: int32 */
        petTypeId?: number;
        /**
         * Format: int64
         * @description grooming report used template published time
         */
        templatePublishTime?: string | number;
        /** @description theme code */
        themeCode?: string;
      };
      'com.moego.server.grooming.params.groomingreport.GroomingReportPreviewParams': {
        content: components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportContentParams'];
        /**
         * Format: int32
         * @description preview actual report
         */
        reportId: number;
        template: components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportTemplateParams'];
        /** @description theme code */
        themeCode: string;
      };
      'com.moego.server.grooming.params.groomingreport.GroomingReportQuestionParams': {
        /** @description default options list, not allow to change */
        buildInOptions: string[];
        /** @description category name: Professional feedback/Pet conditions */
        category: string;
        /** @description default selected options */
        choices: string[];
        /** @description single_choice/multi_choice, custom options list */
        customOptions: string[];
        /**
         * Format: int32
         * @description question id, not need for creating a new question
         */
        id: number;
        /** @description system default question key */
        key: string;
        /** @description single_choice/multi_choice, options list */
        options: string[];
        /** @description question options editable */
        optionsEditable: boolean;
        /** @description text_input question placeholder */
        placeholder: string;
        /** @description is required to fill in */
        required: boolean;
        /** @description non required question show at question list */
        show: boolean;
        /**
         * Format: int32
         * @description sort value, in descending order
         */
        sort: number;
        /** Format: int32 */
        status: number;
        /** @description text_input question default value */
        text: string;
        /** @description question title */
        title: string;
        /** @description question title editable */
        titleEditable: boolean;
        /** @description question type: single_choice/multi_choice/text_input/body_view */
        type: string;
        /** @description question type editable */
        typeEditable: boolean;
        urls: components['schemas']['com.moego.server.grooming.dto.groomingreport.BodyViewUrl'];
      };
      'com.moego.server.grooming.params.groomingreport.GroomingReportSettingParams': {
        /** @description sending method: 1-sms, 2-email */
        sendingMethodList: number[];
        /**
         * Format: int32
         * @description sending type: 1-manually, 2-automatically
         */
        sendingType: number;
      };
      'com.moego.server.grooming.params.groomingreport.GroomingReportTemplateParams': {
        /** @description facebook review icon jump link */
        facebookReviewLink: string;
        /** @description google review icon jump link */
        googleReviewLink: string;
        /** @description light theme color: #xxxxxx or #xxx, moe_grooming_report_template.light_theme_color */
        lightThemeColor: string;
        /** Format: int32 */
        nextAppointmentDateFormatType: number;
        questions: components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportTemplateParams$TemplateQuestionParams'];
        /** @description is after photo required when edit grooming report */
        requireAfterPhoto: boolean;
        /** @description is before photo required when edit grooming report */
        requireBeforePhoto: boolean;
        /** @description show facebook review icon */
        showFacebookReview: boolean;
        /** @description show google review icon */
        showGoogleReview: boolean;
        /** @description is next appointment section show */
        showNextAppointment: boolean;
        /** @description show overall feedback section */
        showOverallFeedback: boolean;
        /** @description show pet condition section when edit or show on client page */
        showPetCondition: boolean;
        /** @description show review booster on report page */
        showReviewBooster: boolean;
        /** @description show staff name */
        showServiceStaffName: boolean;
        /** @description is showcase section show */
        showShowcase: boolean;
        /** @description show yelp review icon */
        showYelpReview: boolean;
        /** @description thank you message in grooming report, moe_grooming_report_template.thank_you_message */
        thankYouMessage: string;
        /** @description default theme code */
        themeCode: string;
        /** @description theme color: #xxxxxx or #xxx, moe_grooming_report_template.theme_color */
        themeColor: string;
        /** @description customized grooming report title */
        title: string;
        /** @description yelp review icon jump link */
        yelpReviewLink: string;
      };
      /** @description question list: overall feedback, pet conditions */
      'com.moego.server.grooming.params.groomingreport.GroomingReportTemplateParams$TemplateQuestionParams': {
        feedbacks: components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportQuestionParams'][];
        petConditions: components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportQuestionParams'][];
      };
      'com.moego.server.grooming.params.groomingreport.GroomingReportViewCountParams': {
        uuid: string;
      };
      'com.moego.server.grooming.params.IdParams': {
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        repeatType: number;
      };
      'com.moego.server.grooming.params.InvoiceAmountVo': {
        /** Format: int32 */
        invoiceId: number;
        /**
         * Format: int64
         * @description 最近一次修改时间
         */
        lastModifiedTime?: string | number;
        /** @description 内部控制位，是否忽略response, 是否增量记录tips */
        omitResult?: boolean;
        value?: number;
        /** @description amount or percentage */
        valueType?: string;
      };
      'com.moego.server.grooming.params.MoeBookOnlineGalleryBatchParams': {
        /** Format: int32 */
        businessId: number;
        imageIds: number[];
      };
      'com.moego.server.grooming.params.MoeBookOnlineGalleryParams': {
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        id: number;
        imagePath: string;
        /** Format: int32 */
        staffId: number;
      };
      'com.moego.server.grooming.params.MoeBookOnlineProfileParams': {
        address: string;
        avatarPath: string;
        businessEmail: string;
        businessHoursJson: string;
        /** Format: int32 */
        businessId: number;
        businessName: string;
        buttonColor: string;
        /** Format: int32 */
        createTime: number;
        description: string;
        facebook: string;
        google: string;
        /** Format: int32 */
        id: number;
        instagram: string;
        language: string;
        other: string;
        phoneNumber: string;
        /** Format: int32 */
        updateTime: number;
        website: string;
        yelp: string;
      };
      'com.moego.server.grooming.params.MoeBookOnlineStaffTimeParams': {
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        staffId: number;
        staffSlots: string;
        staffTimes: string;
      };
      'com.moego.server.grooming.params.ob.BookOnlinePaymentGroupParams': {
        /** Format: int32 */
        acceptClient: number;
        acceptRule?: string;
        cancellationPolicy?: string;
        depositAmount?: number;
        /** Format: int32 */
        depositPercentage?: number;
        /** Format: int32 */
        depositType?: number;
        /** Format: int32 */
        paymentType?: number;
        preAuthPolicy?: string;
        /** Format: int32 */
        preAuthTipEnable?: number;
        prepayPolicy?: string;
        /** Format: int32 */
        prepayTipEnable?: number;
        /** Format: int32 */
        prepayType?: number;
      };
      'com.moego.server.grooming.params.ob.BusinessBookOnlinePaymentParams': {
        cancellationPolicy: string;
        depositAmount: number;
        /** Format: int32 */
        depositPercentage: number;
        /** Format: int32 */
        depositType: number;
        paymentGroupSetting: components['schemas']['com.moego.server.grooming.params.ob.BookOnlinePaymentGroupParams'];
        /** Format: int32 */
        paymentType: number;
        preAuthPolicy: string;
        /** Format: int32 */
        preAuthTipEnable: number;
        prepayPolicy: string;
        /** Format: int32 */
        prepayTipEnable: number;
        /** Format: int32 */
        prepayType: number;
      };
      'com.moego.server.grooming.params.ob.CustomerPetsParams': {
        /** Format: int32 */
        customerId: number;
        petIds?: number[];
      };
      'com.moego.server.grooming.params.ob.CustomerPetsUpdateParams': {
        /** @description Key: custom_xxx, Value: answer */
        clientCustomQuestionMap?: {
          [key: string]: any;
        };
        customer?: components['schemas']['com.moego.server.customer.params.UpdateCustomerInfoParams'];
        /** Format: int32 */
        customerId: number;
        newAddresses?: components['schemas']['com.moego.server.customer.dto.CustomerAddressDto'][];
        /** @description Key: petId, Value: custom question answer */
        petCustomQuestionMap?: {
          [key: string]:
            | {
                [key: string]: any;
              }
            | undefined;
        };
        /** @description Customer pet update params */
        pets?: components['schemas']['com.moego.server.customer.params.CustomerPetUpdateParams'][];
        primaryAddress?: components['schemas']['com.moego.server.customer.dto.CustomerAddressDto'];
      };
      'com.moego.server.grooming.params.ob.DiscountCodeParams': {
        /** @description Discount amount */
        discountAmount: number;
        /** @description Discount code */
        discountCode: string;
        /**
         * Format: int64
         * @description Discount code id
         */
        discountCodeId: string | number;
      };
      /** @description Membership apply params */
      'com.moego.server.grooming.params.ob.MembershipParams': {
        /** @description Membership ids */
        membershipIds: (string | number)[];
      };
      'com.moego.server.grooming.params.ob.MobileGroomingParams': {
        /** Format: int32 */
        allowedSimplifySubmit: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        isByRadius: number;
        /** Format: int32 */
        isByZipcode: number;
        /** Format: int32 */
        isCheckExistingClient: number;
        /** Format: int32 */
        isNeedAddress: number;
        /** Format: int32 */
        maxAvailableDist: number;
        /** Format: int32 */
        maxAvailableTime: number;
        /** Format: int32 */
        serviceAreaEnable: number;
        settingLat: string;
        settingLng: string;
        settingLocation: string;
        /** Format: int32 */
        smartScheduleEnable: number;
        zipCodes: string;
      };
      'com.moego.server.grooming.params.ob.NotificationParams': {
        /** Format: int32 */
        acceptBusinessType: number;
        /** Format: int32 */
        acceptClientType: number;
        acceptEmailContentTemplate: string;
        acceptEmailSubjectTemplate: string;
        acceptTemplate: string;
        /** Format: int32 */
        autoMoveBusinessType: number;
        /** Format: int32 */
        autoMoveClientType: number;
        autoMoveEmailContentTemplate: string;
        autoMoveEmailSubjectTemplate: string;
        autoMoveTemplate: string;
        /** Format: int32 */
        declineBusinessType: number;
        /** Format: int32 */
        declineClientType: number;
        declineEmailContentTemplate: string;
        declineEmailSubjectTemplate: string;
        declineTemplate: string;
        /** Format: int32 */
        submitBusinessType: number;
        /** Format: int32 */
        submitClientType: number;
        submitEmailContentTemplate: string;
        submitEmailSubjectTemplate: string;
        submitTemplate: string;
      };
      'com.moego.server.grooming.params.ob.OBAnonymousParams': {
        /** @description Customized URL domain, demo URL: crazycutepetspa.moego.online */
        domain: string;
        /** @description Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa */
        name: string;
      };
      'com.moego.server.grooming.params.ob.PreAuthDetailParams': {
        bookingFee: number;
        cardNumber: string;
        chargeToken: string;
        paymentMethodId: string;
        serviceChargeAmount: number;
        serviceTotal: number;
        taxAmount: number;
        tipsAmount: number;
      };
      /** @description Prepay detail */
      'com.moego.server.grooming.params.ob.PrepayDetailParams': {
        /** @description Prepay service charge amount */
        serviceChargeAmount: number;
        /** @description Prepay service total */
        serviceTotal: number;
        /** @description Prepay tax amount */
        taxAmount: number;
      };
      'com.moego.server.grooming.params.ob.ServiceParams': {
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        serviceFilter: number;
        serviceList: components['schemas']['com.moego.server.grooming.params.ob.ServiceSingleParams'][];
      };
      'com.moego.server.grooming.params.ob.ServiceSingleParams': {
        /** Format: int32 */
        bookOnlineAvailable: number;
        /** Format: int32 */
        isAllStaff: number;
        /** Format: int32 */
        serviceId: number;
        /** Format: int32 */
        showBasePrice: number;
        staffIdList: number[];
      };
      'com.moego.server.grooming.params.ObAvailableTimeRequest': {
        /** Format: int32 */
        businessId?: number;
        /**
         * Format: int32
         * @description 需要获取的天数
         */
        count: number;
        /** Format: int32 */
        customerId?: number;
        /** @description 开始查询的日期 */
        date: string;
        /**
         * Format: int32
         * @description 需要查询的最远天数
         */
        farthestDay?: number;
        lat?: string;
        lng?: string;
        /** @description pet预约的服务信息： key是petId， value是 service id数组 */
        petServices?: {
          [key: string]: number[] | undefined;
        };
        /** @description 第一个可用天返回全量 time slot，其余天仅返回每半天的第一个可用 time slot */
        queryPerHalfDay?: boolean;
        querySmartScheduling?: boolean;
        serviceIds: number[];
        zipcode?: string;
      };
      'com.moego.server.grooming.params.PackageUsedParams': {
        /** Format: int32 */
        businessId?: number;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        groomingId: number;
        /** Format: int32 */
        invoiceId?: number;
        /** Format: int32 */
        packageId: number;
        /** Format: int32 */
        packageServiceId: number;
        /** Format: int32 */
        quantity: number;
        /** Format: int32 */
        serviceId: number;
      };
      'com.moego.server.grooming.params.PetDataForServiceParams': {
        breed: string;
        coat: string;
        /** Format: int32 */
        petId: number;
        /** Format: int64 */
        petSizeId: string | number;
        /** Format: int32 */
        petTypeId: number;
        weight: string;
      };
      'com.moego.server.grooming.params.PetDetailParams': {
        /** Format: int32 */
        durationOverrideType: number;
        enableOperation: boolean;
        endDate: string;
        /** Format: int32 */
        endTime: number;
        feedings: components['schemas']['com.moego.server.grooming.params.PetDetailParams$FeedingParams'][];
        /** Format: int32 */
        groomingId: number;
        /** Format: int64 */
        lodgingId: string | number;
        medications: components['schemas']['com.moego.server.grooming.params.PetDetailParams$MedicationParams'][];
        operationList: components['schemas']['com.moego.server.grooming.dto.GroomingServiceOperationDTO'][];
        /** Format: int32 */
        petId: number;
        /** Format: int32 */
        priceOverrideType: number;
        /**
         * Format: int32
         * @description 定制service price应用范围:     2: do not save
         *         1: this appt and new
         *         3: this and following
         *         4: all upcoming
         *
         */
        scopeTypePrice: number;
        /**
         * Format: int32
         * @description 定制service duration应用范围    2: do not save
         *         1: this appt and new
         *         3: this and following
         *         4: all upcoming
         *
         */
        scopeTypeTime: number;
        /** Format: int32 */
        serviceId: number;
        /** @enum {string} */
        serviceItemEnum: ComMoegoServerGroomingParamsPetDetailParamsServiceItemEnum;
        servicePrice: number;
        /** Format: int32 */
        serviceTime: number;
        /** Format: int32 */
        serviceType: number;
        /** Format: int32 */
        staffId: number;
        star: boolean;
        startDate: string;
        /** Format: int32 */
        startTime: number;
        /** Format: int32 */
        workMode: number;
      };
      'com.moego.server.grooming.params.PetDetailParams$FeedingParams': {
        feedingAmount: string;
        feedingInstruction: string;
        feedingNote: string;
        feedingSource: string;
        feedingTimes: components['schemas']['com.moego.server.grooming.params.PetDetailParams$ScheduleTimeParams'][];
        feedingType: string;
        feedingUnit: string;
      };
      'com.moego.server.grooming.params.PetDetailParams$MedicationParams': {
        medicationAmount: string;
        medicationName: string;
        medicationNote: string;
        medicationTimes: components['schemas']['com.moego.server.grooming.params.PetDetailParams$ScheduleTimeParams'][];
        medicationUnit: string;
      };
      'com.moego.server.grooming.params.PetDetailParams$ScheduleTimeParams': {
        extraJson: {
          [key: string]: string | undefined;
        };
        /** Format: int32 */
        scheduleTime: number;
      };
      'com.moego.server.grooming.params.PetServicePageParams': {
        /** Format: int32 */
        inactive: number;
        keyword?: string;
        pagination: components['schemas']['com.moego.common.utils.Pagination'];
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.grooming.params.PreAuthAmountParams': {
        customerData?: components['schemas']['com.moego.server.grooming.params.BookOnlineCustomerParams'];
        discountCode?: string;
        petData: components['schemas']['com.moego.server.grooming.params.BookOnlinePetParams'][];
        /** Format: int32 */
        staffId?: number;
      };
      'com.moego.server.grooming.params.PreAuthParams': {
        preAuthCardNumber: string;
        preAuthEnable: boolean;
        preAuthPaymentMethod: string;
      };
      'com.moego.server.grooming.params.PrepayAmountParams': {
        customerData?: components['schemas']['com.moego.server.grooming.params.BookOnlineCustomerParams'];
        /** Format: int32 */
        depositType?: number;
        discountCode?: string;
        petData: components['schemas']['com.moego.server.grooming.params.BookOnlinePetParams'][];
        /** Format: int32 */
        prepayType: number;
        /** Format: int32 */
        staffId?: number;
      };
      'com.moego.server.grooming.params.PreviewRepeatParams': {
        /** @description 是否应用client preference */
        applyClientPreference?: boolean;
        /**
         * Format: int32
         * @description buffer time in minutes, 与其他Appointment需要间隔的时间
         */
        bufferTime?: number;
        /**
         * Format: int32
         * @description 当前 repeat 所属的 customer id
         */
        customerId?: number;
        /** @description repeat 结束时间 */
        endOn?: string;
        /**
         * Format: int32
         * @description 已存在预约的 id，检查冲突时需要排除这个预约
         */
        existAppointmentId?: number;
        /**
         * @deprecated
         * @description 是否基于已存在的预约，废弃字段，老版客户端使用
         */
        isExistAppt?: boolean;
        /** Format: int32 */
        isNotice?: number;
        /**
         * Format: int32
         * @description repeatType = 3, repeatEveryType = 2时, 指定每月的第几天
         */
        monthDay?: number;
        /**
         * Format: int32
         * @description repeatType = 3, repeatEveryType = 1时, 指定每月的第几周的周几
         */
        monthWeekDay?: number;
        /**
         * Format: int32
         * @description repeatType = 3, repeatEveryType = 1时, 指定每月的第几周
         */
        monthWeekTimes?: number;
        /**
         * Format: int32
         * @description 每周几，repeatType = 2 时必填，新版 repeat 1-7 分别代表周一到周日，旧版 repeat 的 0-6 分别代表周日到周六，兼容旧版
         */
        repeatBy?: number;
        /** @description 一周中哪几天重复，用于替换 repeatBy 字段，支持多天 */
        repeatByDays?: number[];
        /**
         * Format: int32
         * @description 重复频率: every x days/weeks, repeatType = 1/2 时必填
         */
        repeatEvery?: number;
        /**
         * Format: int32
         * @description repeatType = 3 时月重复类型: 1-每月第几个的周几, 2-每月第几天
         */
        repeatEveryType?: number;
        /**
         * Format: int32
         * @description 编辑已有记录时必传
         */
        repeatId?: number;
        repeatStaffInfoParams: components['schemas']['com.moego.server.grooming.params.RepeatStaffInfoParams'][];
        /**
         * Format: int32
         * @description 重复类型: 1-day, 2-week, 3-month
         */
        repeatType: number;
        /** @description 设置结束时间 */
        setEndOn?: string;
        /** @description smart schedule 开关: true/false */
        smartSchedule?: boolean;
        /**
         * Format: int32
         * @description smart schedule 设置，最多往后查询多少天
         */
        ssAfterDays?: number;
        /**
         * Format: int32
         * @description smart schedule 设置，最多往前查询多少天
         */
        ssBeforeDays?: number;
        /** @description repeat 开始时间 */
        startsOn: string;
        /**
         * Format: int32
         * @description 重复次数
         */
        times?: number;
        /** @description repeat 结束类型: 1-次数, 2-结束日期 */
        type: string;
        /** @description 当前已存在预约日期修改，同时更新 appointment date/startTime 和 repeat rules 时需要传，会对 repeat appointment date 整体偏移 */
        updateAppointmentDate?: string;
        /**
         * Format: int32
         * @description 当前已存在预约的 startTime 修改，同时更新 appointment date/startTime 和 repeat rules 时需要传，会覆盖 upcoming 的 startTime
         */
        updateAppointmentStartTime?: number;
      };
      'com.moego.server.grooming.params.PreviewSplitTipParams': {
        /** @description 指定分配给 Business 的 Tips 金额 */
        businessTipAmount: number;
        /** @description 自定义配置 */
        customizedConfig?: components['schemas']['com.moego.server.grooming.params.PreviewSplitTipParams$CustomizedTip'][];
        /**
         * Format: int32
         * @description 自定义分配的类型: 1 - 按金额自定义分配，此时 CustomizedConfig 中 Amount 字段生效; 2 - 按比例自定义分配，此时 CustomizedConfig 中 Percentage 字段生效
         */
        customizedType?: number;
        deleted?: boolean;
        /**
         * Format: int64
         * @description order ID
         */
        orderId: string | number;
        /**
         * Format: int32
         * @description 分配方式: 1 - 按服务金额比例分配; 2 - 所有人均分; 3 - 自定义，此时 CustomizedType 参数生效
         */
        splitMethod: number;
      };
      /** @description 自定义配置 */
      'com.moego.server.grooming.params.PreviewSplitTipParams$CustomizedTip': {
        /** @description 按金额分配时，分得的比例 */
        amount?: number;
        /**
         * Format: int32
         * @description 按比例分配时，分得的比例
         */
        percentage?: number;
        /**
         * Format: int64
         * @description staff ID
         */
        staffId: string | number;
      };
      'com.moego.server.grooming.params.QueryServiceByPetIdsParams': {
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        customerId: number;
        petData?: components['schemas']['com.moego.server.grooming.params.PetDataForServiceParams'][];
        petIds: number[];
        /** Format: int32 */
        type?: number;
      };
      'com.moego.server.grooming.params.QueryServiceParams': {
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        petId: number;
        /** Format: int32 */
        type?: number;
      };
      'com.moego.server.grooming.params.RepeatStaffInfoParams': {
        /**
         * Format: int32
         * @description  服务结束时间，Daycare 用
         */
        endTime?: number;
        /**
         * Format: int32
         * @description 服务持续时间 校验是否冲突用，Grooming 用
         */
        serviceTime?: number;
        /**
         * Format: int32
         * @description 员工id 校验是否冲突用，Grooming 用
         */
        staffId?: number;
        /**
         * Format: int32
         * @description 服务开始时间 校验是否冲突用
         */
        startTime: number;
      };
      'com.moego.server.grooming.params.RepeatWithSSParams': {
        /** @description 是否应用client preference */
        applyClientPreference?: boolean;
        /**
         * Format: int32
         * @description buffer time in minutes, 与其他Appointment需要间隔的时间
         */
        bufferTime?: number;
        /**
         * Format: int32
         * @description 当前 repeat 所属的 customer id
         */
        customerId?: number;
        /** @description repeat 结束时间 */
        endOn?: string;
        /**
         * Format: int32
         * @description 已存在预约的 id，检查冲突时需要排除这个预约
         */
        existAppointmentId?: number;
        /**
         * @deprecated
         * @description 是否基于已存在的预约，废弃字段，老版客户端使用
         */
        isExistAppt?: boolean;
        /** Format: int32 */
        isNotice?: number;
        /**
         * Format: int32
         * @description repeatType = 3, repeatEveryType = 2时, 指定每月的第几天
         */
        monthDay?: number;
        /**
         * Format: int32
         * @description repeatType = 3, repeatEveryType = 1时, 指定每月的第几周的周几
         */
        monthWeekDay?: number;
        /**
         * Format: int32
         * @description repeatType = 3, repeatEveryType = 1时, 指定每月的第几周
         */
        monthWeekTimes?: number;
        /**
         * Format: int32
         * @description 每周几，repeatType = 2 时必填，新版 repeat 1-7 分别代表周一到周日，旧版 repeat 的 0-6 分别代表周日到周六，兼容旧版
         */
        repeatBy?: number;
        /** @description 一周中哪几天重复，用于替换 repeatBy 字段，支持多天 */
        repeatByDays?: number[];
        /**
         * Format: int32
         * @description 重复频率: every x days/weeks, repeatType = 1/2 时必填
         */
        repeatEvery?: number;
        /**
         * Format: int32
         * @description repeatType = 3 时月重复类型: 1-每月第几个的周几, 2-每月第几天
         */
        repeatEveryType?: number;
        /**
         * Format: int32
         * @description 编辑已有记录时必传
         */
        repeatId?: number;
        repeatStaffInfoParams: components['schemas']['com.moego.server.grooming.params.RepeatStaffInfoParams'][];
        /**
         * Format: int32
         * @description 重复类型: 1-day, 2-week, 3-month
         */
        repeatType: number;
        /** @description 设置结束时间 */
        setEndOn?: string;
        /** @description smart schedule开关 true/false */
        smartSchedule: boolean;
        /**
         * Format: int32
         * @description smart schedule 设置，最多往后查询多少天
         */
        ssAfterDays?: number;
        /**
         * Format: int32
         * @description smart schedule 设置，最多往前查询多少天
         */
        ssBeforeDays?: number;
        /** @description repeat 开始时间 */
        startsOn: string;
        /**
         * Format: int32
         * @description 重复次数
         */
        times?: number;
        /** @description repeat 结束类型: 1-次数, 2-结束日期 */
        type: string;
        /** @description 当前已存在预约日期修改，同时更新 appointment date/startTime 和 repeat rules 时需要传，会对 repeat appointment date 整体偏移 */
        updateAppointmentDate?: string;
        /**
         * Format: int32
         * @description 当前已存在预约的 startTime 修改，同时更新 appointment date/startTime 和 repeat rules 时需要传，会覆盖 upcoming 的 startTime
         */
        updateAppointmentStartTime?: number;
      };
      'com.moego.server.grooming.params.SaveRepeatAppointmentListParams': {
        /** Format: int32 */
        appointmentId?: number;
        appointmentList: components['schemas']['com.moego.server.grooming.params.SaveRepeatAppointmentParams'][];
        /**
         * Format: int32
         * @description repeat rules id, 当 type = 1 时必填
         */
        repeatId?: number;
        /** Format: int32 */
        saveType: number;
      };
      'com.moego.server.grooming.params.SaveRepeatAppointmentParams': {
        alertNotes?: string;
        allPetsStartAtSameTime?: boolean;
        appointmentDateString?: string;
        /** Format: int32 */
        appointmentId?: number;
        /** Format: int32 */
        appointmentStartTime?: number;
        colorCode: string;
        /** Format: int32 */
        createdById?: number;
        /** Format: int32 */
        customerAddressId?: number;
        /** Format: int32 */
        customerId: number;
        endDate?: string;
        preAuthParams?: components['schemas']['com.moego.server.grooming.params.PreAuthParams'];
        /** Format: int32 */
        scheduleType?: number;
        serviceList: components['schemas']['com.moego.server.grooming.params.PetDetailParams'][];
        /**
         * Format: int32
         * @description 预约来源: 22018-web, 17216-android, 17802-ios, 22168-OB, 19826-google calendar, 23426-DM
         */
        source: number;
        ticketComments?: string;
      };
      'com.moego.server.grooming.params.SaveRepeatParams': {
        /**
         * Format: int32
         * @description 当前 repeat 所属的 customer id
         */
        customerId?: number;
        /** @description repeat 结束时间 */
        endOn?: string;
        /** Format: int32 */
        isNotice?: number;
        /**
         * Format: int32
         * @description repeatType = 3, repeatEveryType = 2时, 指定每月的第几天
         */
        monthDay?: number;
        /**
         * Format: int32
         * @description repeatType = 3, repeatEveryType = 1时, 指定每月的第几周的周几
         */
        monthWeekDay?: number;
        /**
         * Format: int32
         * @description repeatType = 3, repeatEveryType = 1时, 指定每月的第几周
         */
        monthWeekTimes?: number;
        /**
         * Format: int32
         * @description 每周几，repeatType = 2 时必填，新版 repeat 1-7 分别代表周一到周日，旧版 repeat 的 0-6 分别代表周日到周六，兼容旧版
         */
        repeatBy?: number;
        /** @description 一周中哪几天重复，用于替换 repeatBy 字段，支持多天 */
        repeatByDays?: number[];
        /**
         * Format: int32
         * @description 重复频率: every x days/weeks, repeatType = 1/2 时必填
         */
        repeatEvery?: number;
        /**
         * Format: int32
         * @description repeatType = 3 时月重复类型: 1-每月第几个的周几, 2-每月第几天
         */
        repeatEveryType?: number;
        /**
         * Format: int32
         * @description 编辑已有记录时必传
         */
        repeatId?: number;
        /**
         * Format: int32
         * @description 重复类型: 1-day, 2-week, 3-month
         */
        repeatType: number;
        /** @description 设置结束时间 */
        setEndOn?: string;
        /** @description 是否开启 smart schedule */
        smartSchedule?: boolean;
        /**
         * Format: int32
         * @description smart schedule 设置，最多往后查询多少天
         */
        ssAfterDays?: number;
        /**
         * Format: int32
         * @description smart schedule 设置，最多往前查询多少天
         */
        ssBeforeDays?: number;
        /** @description repeat 开始时间 */
        startsOn: string;
        /**
         * Format: int32
         * @description 重复次数
         */
        times?: number;
        /** @description repeat 结束类型: 1-次数, 2-结束日期 */
        type: string;
      };
      'com.moego.server.grooming.params.ss.SmartScheduleRequest': {
        addressLat: string;
        addressLng: string;
        addressZipcode?: string;
        /**
         * Format: int32
         * @description 需要运用客户偏好的客户ID，过滤出指定的week和time
         */
        applyClientPreferenceCustomerId?: number;
        /**
         * Format: int32
         * @description buffer time in minutes, 与其他Appointment需要间隔的时间
         */
        bufferTime?: number;
        /** @description 是否校验Certain Area Certain Day， 默认false */
        checkCACD?: boolean;
        /**
         * Format: int32
         * @description 需要获取的天数
         */
        count: number;
        /** @description 开始查询的日期 */
        date?: string;
        /** @description one of date or dates must be provided, uses dates first */
        dates?: string[];
        /** @description 是否关闭ss功能， 默认false */
        disableSmartScheduling?: boolean;
        /**
         * Format: int32
         * @description 需要查询的最远天数
         */
        farthestDay: number;
        /**
         * Format: int32
         * @description 需要排除掉的groomingId
         */
        filterGroomingId?: number;
        fromOB?: boolean;
        /** @description pet param info */
        petParamListForSS?: components['schemas']['com.moego.server.grooming.dto.ob.PetDataDTO'][];
        /** @description 第一个可用天返回全量 time slot，其余天仅返回每半天的第一个可用 time slot */
        queryPerHalfDay?: boolean;
        /**
         * Format: int32
         * @description time in minutes
         */
        serviceDuration: number;
        staffIds: number[];
        /** @description staff 对应的pet service total duration */
        staffPetServiceDurationList?: components['schemas']['com.moego.server.grooming.dto.StaffPetServiceDuration'][];
        /** @description available staff map time in minutes */
        staffServiceDuration?: {
          [key: string]: number | undefined;
        };
        /** @description ob time 使用 working hour 的 staff */
        syncWithWorkingHourStaff?: number[];
      };
      'com.moego.server.grooming.params.status.StatusRevertParams': {
        /** Format: int32 */
        groomingId: number;
      };
      'com.moego.server.grooming.params.status.StatusUpdateParams': {
        checkOut: components['schemas']['com.moego.server.grooming.params.status.StatusUpdateParams$CheckOut'];
        /** Format: int32 */
        groomingId: number;
        /**
         * Format: int32
         * @description message method
         */
        messageMethodForPickupNotification: number;
        /**
         * Format: int32
         * @description 预约状态
         */
        status: number;
      };
      'com.moego.server.grooming.params.status.StatusUpdateParams$CheckOut': {
        /** Format: date */
        endDate: string;
      };
      'com.moego.server.grooming.params.TransferAppointmentParams': {
        /** Format: int32 */
        sourceStaffId: number;
        /** Format: int32 */
        targetStaffId: number;
      };
      'com.moego.server.grooming.result.ServiceOperationListResult': {
        operations: components['schemas']['com.moego.server.grooming.dto.appointment.AppointmentOperationDTO'][];
        /** Format: int32 */
        petDetailId: number;
        /** Format: int32 */
        petId: number;
        /** Format: int32 */
        serviceId: number;
        serviceName: string;
      };
      'com.moego.server.grooming.service.dto.BatchDrivingDisplayInfo': {
        /** @description 查询日期内的 driving info 列表 */
        dateDrivingInfoList: components['schemas']['com.moego.server.grooming.service.dto.BatchDrivingDisplayInfo$DateDrivingDisplayInfo'][];
      };
      /** @description 查询日期内的 driving info 列表 */
      'com.moego.server.grooming.service.dto.BatchDrivingDisplayInfo$DateDrivingDisplayInfo': {
        /** @description 日期: yyyy-MM-dd */
        date: string;
        /** @description 当前日期下 staff driving info 列表 */
        staffDrivingInfoList: components['schemas']['com.moego.server.grooming.service.dto.BatchDrivingDisplayInfo$StaffDrivingDisplayInfo'][];
      };
      /** @description 当前日期下 staff driving info 列表 */
      'com.moego.server.grooming.service.dto.BatchDrivingDisplayInfo$StaffDrivingDisplayInfo': {
        /** @description 单个 staff driving info 列表 */
        drivingInfoList: components['schemas']['com.moego.server.grooming.service.dto.DrivingDisplayInfo'][];
        /**
         * Format: int32
         * @description staff id
         */
        staffId: number;
      };
      'com.moego.server.grooming.service.dto.client.ClientUpdateApptDTO': {
        /** @description Appointment date */
        apptDate: string;
        /**
         * Format: int32
         * @description Appointment start time
         */
        apptStartTime: number;
        /** @description Ticket unique identifier */
        bookingId: string;
        /**
         * Format: int32
         * @description Staff id
         */
        staffId: number;
      };
      'com.moego.server.grooming.service.dto.CompanyServiceOBSettingUpdateDto': {
        allowBookingWithOtherCareType?: boolean;
        /** Format: int32 */
        bookOnlineAvailable?: number;
        /** Format: int32 */
        isAllStaff?: number;
        /** Format: int32 */
        locationId: number;
        /** Format: int32 */
        serviceId: number;
        /** Format: int32 */
        showBasePrice?: number;
        staffIdList?: number[];
      };
      'com.moego.server.grooming.service.dto.CompanyServiceUpdateDto': {
        applyUpcomingAppt?: boolean;
        /** Format: int32 */
        bookOnlineAvailable?: number;
        /** Format: int32 */
        breedFilter?: number;
        /** Format: int32 */
        categoryId?: number;
        /** Format: int32 */
        coatFilter?: number;
        colorCode?: string;
        customizedBreed?: components['schemas']['com.moego.server.grooming.service.dto.ServicePetTypeBreedsDTO'][];
        customizedCoat?: number[];
        customizedPetSizes?: (string | number)[];
        description?: string;
        /** Format: int32 */
        duration: number;
        /** Format: int32 */
        inactive?: number;
        /** Format: int32 */
        isAllLocation?: number;
        /** Format: int32 */
        isAllStaff?: number;
        locationOverrideList?: components['schemas']['com.moego.server.grooming.service.dto.ServiceLocationOverrideDto'][];
        name?: string;
        petSizeFilter?: boolean;
        price: number;
        serviceFilter?: boolean;
        /** Format: int32 */
        serviceId: number;
        /** Format: int32 */
        showBasePrice?: number;
        /** Format: int32 */
        singleLocationId?: number;
        /** Format: int32 */
        taxId: number;
        /** Format: int32 */
        type: number;
        /** Format: int32 */
        weightFilter?: number;
        weightRange?: number[];
      };
      'com.moego.server.grooming.service.dto.CustomerAppointmentListDTO': {
        /** Format: int64 */
        endRow: string | number;
        evaluationServiceList: components['schemas']['com.moego.server.grooming.dto.EvaluationServiceDTO'][];
        hasNextPage: boolean;
        hasPreviousPage: boolean;
        isFirstPage: boolean;
        isLastPage: boolean;
        list: components['schemas']['com.moego.server.grooming.dto.CustomerGrooming'][];
        /** Format: int32 */
        navigateFirstPage: number;
        /** Format: int32 */
        navigateLastPage: number;
        navigatepageNums: number[];
        /** Format: int32 */
        navigatePages: number;
        /** Format: int32 */
        nextPage: number;
        /** Format: int32 */
        pageNum: number;
        /** Format: int32 */
        pages: number;
        /** Format: int32 */
        pageSize: number;
        petList: components['schemas']['com.moego.server.customer.dto.CustomerPetDetailDTO'][];
        /** Format: int32 */
        prePage: number;
        serviceList: components['schemas']['com.moego.server.grooming.dto.MoeGroomingServiceDTO'][];
        /** Format: int32 */
        size: number;
        staffList: components['schemas']['com.moego.server.grooming.service.dto.CustomerAppointmentListStaffDTO'][];
        /** Format: int64 */
        startRow: string | number;
        /** Format: int64 */
        total: string | number;
      };
      'com.moego.server.grooming.service.dto.CustomerAppointmentListStaffDTO': {
        avatarPath: string;
        /** Format: int32 */
        businessId: number;
        firstName: string;
        /** Format: int32 */
        id: number;
        lastName: string;
        /** Format: int32 */
        status: number;
      };
      /** @description 单个 staff driving info 列表 */
      'com.moego.server.grooming.service.dto.DrivingDisplayInfo': {
        /** Format: double */
        drivingMiles: number;
        /** Format: int32 */
        drivingMinutes: number;
        /**
         * Format: double
         * @description 最后一个预约到 endLocation 的距离
         */
        drivingOutMiles: number;
        /**
         * Format: int32
         * @description 最后一个预约到 endLocation 的时间
         */
        drivingOutMinutes: number;
        /** Format: int32 */
        groomingId: number;
        /** Format: int32 */
        intervalTime: number;
        /** Format: int32 */
        unitOfDistanceType: number;
      };
      /** @description 货币汇率返回结果 */
      'com.moego.server.grooming.service.dto.ExchangeRateDto': {
        /** @description 货币汇率当前基准 */
        base: string;
        /** @description 货币汇率当前日期 */
        date: string;
        /** @description 货币汇率结果，如：{"AUD":1.292316,"EUR":0.828905,"GBP":0.720665,"USD":1,"CAD":1.240376,"NZD":1.388655,"ZAR":14.404399,"BRL":5.4526} */
        rates: {
          [key: string]: any;
        };
        /**
         * Format: int32
         * @description 货币汇率当前时间戳
         */
        timestamp: number;
      };
      'com.moego.server.grooming.service.dto.GoogleCalendarAuthInfoDto': {
        /** @description google email */
        email: string;
        /**
         * Format: int32
         * @description google auth id
         */
        googleAuthId: number;
      };
      'com.moego.server.grooming.service.dto.GoogleCalendarSettingDto': {
        /** @description 日历名字模板 */
        calendarName: string;
        /**
         * Format: int64
         * @description 连接时间
         */
        createTime: string | number;
        /** @description 日历事件描述模板 */
        eventDescription: string;
        /** @description 日历事件标题模板 */
        eventTitle: string;
        /** @description google 授权的email */
        googleAuthEmail: string;
        /**
         * Format: int32
         * @description googleAuthId
         */
        googleAuthId: number;
        /** @description google 授权 URL */
        redirectUri: string;
        /**
         * Format: int32
         * @description 设置信息的数据
         */
        settingId: number;
        /**
         * Format: int32
         * @description 当前设置所属的staffId
         */
        staffId: number;
        /** Format: int32 */
        status: number;
        /** @description 当前有效的被同步staff */
        syncedStaffIdList: number[];
        /** Format: int32 */
        syncType: number;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.grooming.service.dto.GroomingInvoiceSendUrlDto': {
        id: string;
        sendUrl: string;
      };
      'com.moego.server.grooming.service.dto.GroomingServiceUpcomingAppointmentCountDto': {
        /** Format: int32 */
        serviceId: number;
        /** Format: int32 */
        upcomingCount: number;
      };
      'com.moego.server.grooming.service.dto.GroomingStaffUpcomingAppointmentCountDto': {
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        upcomingCount: number;
      };
      'com.moego.server.grooming.service.dto.MoeGroomingServiceDto': {
        /** Format: int32 */
        bookOnlineAvailable: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        categoryId: number;
        colorCode: string;
        /** Format: int64 */
        createTime: string | number;
        description: string;
        /** Format: int32 */
        duration: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        inactive: number;
        /** Format: int32 */
        isAllStaff: number;
        /** Format: int32 */
        isSaveDuration: number;
        /** Format: int32 */
        isSavePrice: number;
        name: string;
        price: number;
        /** Format: int32 */
        showBasePrice: number;
        /** Format: int32 */
        sort: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        taxId: number;
        /** Format: int32 */
        type: number;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.grooming.service.dto.ob.OBClientApptDTO': {
        /** @description last appt date, contains business time zone */
        appointmentDate: string;
        /** Format: int32 */
        appointmentEndTime: number;
        /**
         * Format: int32
         * @description last appt start time, minutes units
         */
        appointmentStartTime: number;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        id: number;
        orderId: string;
        petDetails: components['schemas']['com.moego.server.grooming.service.dto.ob.OBClientApptDTO$OBClientApptPetDetailDTO'][];
        petInfoList: components['schemas']['com.moego.server.grooming.service.dto.ob.OBClientApptDTO$OBPetInfoDTO'][];
        /** @description recommend date to book for next appt, grooming report's recommend date */
        recommendDate: string;
        serviceInfoList: components['schemas']['com.moego.server.grooming.service.dto.ob.OBClientApptDTO$OBServiceInfoDTO'][];
        staffInfoList: components['schemas']['com.moego.server.grooming.service.dto.ob.OBClientApptDTO$OBStaffInfoDTO'][];
      };
      'com.moego.server.grooming.service.dto.ob.OBClientApptDTO$OBClientApptPetDetailDTO': {
        /** Format: int32 */
        groomingId: number;
        /** Format: int32 */
        petDetailId: number;
        /** Format: int32 */
        petId: number;
        /** Format: int32 */
        serviceId: number;
        /** @description pet service price, customize price */
        servicePrice: number;
        /**
         * Format: int32
         * @description pet service time, customize time
         */
        serviceTime: number;
        /** Format: int32 */
        staffId: number;
      };
      'com.moego.server.grooming.service.dto.ob.OBClientApptDTO$OBPetInfoDTO': {
        avatarPath: string;
        breed: string;
        /**
         * Format: int32
         * @description 1-normal, 2-passed away
         */
        lifeStatus: number;
        /** Format: int32 */
        petId: number;
        petName: string;
        /**
         * Format: int32
         * @description pet type id, 1-dog, 2-cat, 11-other
         */
        petTypeId: number;
        /** Format: int32 */
        status: number;
        /** @description pet weight */
        weight: string;
      };
      'com.moego.server.grooming.service.dto.ob.OBClientApptDTO$OBServiceInfoDTO': {
        /** @description staff can do this service */
        availableStaffIdList: number[];
        /** Format: int32 */
        bookOnlineAvailable: number;
        /** Format: int32 */
        breedFilter: number;
        /** Format: int32 */
        inactive: number;
        /** Format: int32 */
        isAllStaff: number;
        /** @description latest service name */
        name: string;
        /** Format: int32 */
        serviceId: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        type: number;
        /** @description key: petTypeId, value: service available breed list, **ALL_BREEDS** means all breeds are allowed */
        typeBreedMap: {
          [key: string]: string | undefined;
        };
        weightDownLimit: number;
        /** Format: int32 */
        weightFilter: number;
        weightUpLimit: number;
      };
      'com.moego.server.grooming.service.dto.ob.OBClientApptDTO$OBStaffInfoDTO': {
        avatarPath: string;
        /** Format: int32 */
        bookOnlineAvailable: number;
        firstName: string;
        /** Format: int32 */
        inactive: number;
        lastName: string;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        status: number;
      };
      'com.moego.server.grooming.service.dto.ob.OBLastApptVO': {
        /** @description Is there a last appt */
        isExists: boolean;
        lastAppt: components['schemas']['com.moego.server.grooming.service.dto.ob.OBClientApptDTO'];
      };
      'com.moego.server.grooming.service.dto.OBAvailableTimeDto': {
        available: boolean[];
        staffList: components['schemas']['com.moego.server.grooming.service.dto.OBAvailableTimeStaffDto'][];
      };
      'com.moego.server.grooming.service.dto.OBAvailableTimeStaffAvailableTimeDto': {
        am: number[];
        pm: number[];
      };
      'com.moego.server.grooming.service.dto.OBAvailableTimeStaffDto': {
        availableTime: components['schemas']['com.moego.server.grooming.service.dto.OBAvailableTimeStaffAvailableTimeDto'];
        firstName: string;
        /** Format: int32 */
        id: number;
        lastName: string;
      };
      /** @description 账本列表 */
      'com.moego.server.grooming.service.dto.QBAccountDto': {
        accountId: string;
        accountName: string;
      };
      'com.moego.server.grooming.service.dto.QBAccountReturnDto': {
        /** @description 账本列表 */
        accountList: components['schemas']['com.moego.server.grooming.service.dto.QBAccountDto'][];
      };
      /** @description qb business setting 信息 */
      'com.moego.server.grooming.service.dto.QBBusinessSettingDto': {
        /**
         * @deprecated
         * @description 账本id
         */
        accountId: string;
        /**
         * @deprecated
         * @description 账本名
         */
        accountName: string;
        /** Format: int32 */
        businessId: number;
        /** @description 当前qb连接的company name */
        connectCompanyName: string;
        /** @description 当前qb连接的登录email */
        connectEmail: string;
        /**
         * Format: int32
         * @description 连接id
         */
        connectId: number;
        /** Format: int32 */
        connectStatus: number;
        /** Format: int32 */
        enableSync: number;
        /**
         * @deprecated
         * @description 上次qb connect连接的email
         */
        lastConnectEmail: string;
        lastDisconnectedTime: string;
        /** @description 当前连接已同步过的内容，最小同步日期 */
        minSyncDate: string;
        /** @deprecated */
        oauthUrl: string;
        /**
         * Format: int32
         * @description 设置id
         */
        setttingId: number;
        /** @description 预约开始同步日期(默认创建时间) 年-月-日 */
        syncBeginDate: string;
        /**
         * Format: int32
         * @description qb sync 版本, 决定了走新旧同步逻辑
         */
        userVersion: number;
      };
      'com.moego.server.grooming.service.dto.QBConnectReturnDto': {
        connectCompanyName: string;
        connectEmail: string;
        /** Format: int32 */
        connectId: number;
        selectedBusinessIds: number[];
      };
      'com.moego.server.grooming.service.dto.QbInvoiceStatusDto': {
        /** @description 是否同步过 */
        isSync: boolean;
        /** @description qb invoice 跳转连接 */
        qbInvoiceLink: string;
        /**
         * Format: int64
         * @description 同步时间戳(10位)
         */
        syncTime: string | number;
      };
      'com.moego.server.grooming.service.dto.RouteOptimizationDetail': {
        drivingInMiles: number;
        /** Format: int32 */
        drivingInMinutes: number;
        drivingOutMiles: number;
        /** Format: int32 */
        drivingOutMinutes: number;
        /** Format: int32 */
        groomingId: number;
        /** Format: int32 */
        startTime: number;
      };
      'com.moego.server.grooming.service.dto.RouteOptimizationRequest': {
        /**
         * Format: date
         * @description 查询日期
         */
        date: string;
        /** @description 结束地址经度 */
        endLocationLat: string;
        /** @description 结束地址纬度 */
        endLocationLnt: string;
        /**
         * Format: int32
         * @description 员工编号
         */
        staffId: number;
        /** @description 开始地址经度 */
        startLocationLat: string;
        /** @description 开始地址纬度 */
        startLocationLnt: string;
      };
      'com.moego.server.grooming.service.dto.RouteOptimizationResponse': {
        detailList: components['schemas']['com.moego.server.grooming.service.dto.RouteOptimizationDetail'][];
        optimizedDrivingMiles: number;
        /** Format: int32 */
        optimizedDrivingMinutes: number;
        optimizedPolyline: string[];
        savedDrivingMiles: number;
        /** Format: int32 */
        savedDrivingMinutes: number;
        /** Format: int32 */
        unitOfDistanceType: number;
        unoptimizedPolyline: string[];
        /** Format: int32 */
        wayPointCount: number;
      };
      'com.moego.server.grooming.service.dto.ServiceCategoryListDto': {
        /** Format: int32 */
        categoryId: number;
        /** Format: int64 */
        createTime: string | number;
        name: string;
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.grooming.service.dto.ServiceCategorySaveDto': {
        name: string;
        /** Format: int32 */
        serviceItemType?: number;
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.grooming.service.dto.ServiceCategoryUpdateDto': {
        /** Format: int32 */
        categoryId?: number;
        name: string;
        /** Format: int32 */
        serviceItemType?: number;
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.grooming.service.dto.ServiceGroupByCategoryDto': {
        /** Format: int32 */
        categoryId: number;
        name: string;
        serviceList: components['schemas']['com.moego.server.grooming.service.dto.ServiceUpdateDto'][];
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.grooming.service.dto.ServiceLocationOverrideDto': {
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        duration: number;
        price: number;
        /** Format: int32 */
        taxId: number;
      };
      'com.moego.server.grooming.service.dto.ServicePetTypeBreedsDTO': {
        breeds: string[];
        isAll: boolean;
        /** Format: int32 */
        petTypeId: number;
      };
      'com.moego.server.grooming.service.dto.ServiceSaveDto': {
        /** Format: int32 */
        breedFilter?: number;
        /** Format: int32 */
        categoryId: number;
        /** Format: int32 */
        coatFilter?: number;
        colorCode: string;
        customizedBreed?: components['schemas']['com.moego.server.grooming.service.dto.ServicePetTypeBreedsDTO'][];
        customizedCoat?: number[];
        customizedPetSizes?: (string | number)[];
        description?: string;
        /** Format: int32 */
        duration: number;
        /** Format: int32 */
        inactive?: number;
        /** Format: int32 */
        isAllLocation?: number;
        locationOverrideList?: components['schemas']['com.moego.server.grooming.service.dto.ServiceLocationOverrideDto'][];
        name: string;
        petSizeFilter?: boolean;
        price: number;
        serviceFilter?: boolean;
        /** Format: int32 */
        serviceId?: number;
        /** Format: int32 */
        singleLocationId?: number;
        /** Format: int32 */
        taxId: number;
        /** Format: int32 */
        type: number;
        /** Format: int32 */
        weightFilter?: number;
        weightRange?: number[];
      };
      'com.moego.server.grooming.service.dto.ServiceTaxUpdateForApptDto': {
        /** Format: int32 */
        groomingId: number;
        /** Format: int32 */
        serviceId: number;
        /** Format: int32 */
        taxId: number;
      };
      'com.moego.server.grooming.service.dto.ServiceUpdateDto': {
        allowBookingWithOtherCareType?: boolean;
        applyUpcomingAppt?: boolean;
        /** Format: int32 */
        bookOnlineAvailable?: number;
        /** Format: int32 */
        breedFilter?: number;
        bundleServiceIdList?: (string | number)[];
        /** Format: int32 */
        categoryId?: number;
        /** Format: int32 */
        coatFilter?: number;
        colorCode?: string;
        customizedBreed?: components['schemas']['com.moego.server.grooming.service.dto.ServicePetTypeBreedsDTO'][];
        customizedCoat?: number[];
        customizedPetSizes?: (string | number)[];
        description?: string;
        /** Format: int32 */
        duration?: number;
        /** Format: int32 */
        inactive?: number;
        /** Format: int32 */
        isAllLocation?: number;
        /** Format: int32 */
        isAllStaff?: number;
        locationOverrideList?: components['schemas']['com.moego.server.grooming.service.dto.ServiceLocationOverrideDto'][];
        name?: string;
        petSizeFilter?: boolean;
        price?: number;
        serviceFilter?: boolean;
        /** Format: int32 */
        serviceId: number;
        /** Format: int32 */
        showBasePrice?: number;
        /** Format: int32 */
        singleLocationId?: number;
        staffIdList?: number[];
        /** Format: int32 */
        taxId?: number;
        /** Format: int32 */
        type?: number;
        /** Format: int32 */
        weightFilter?: number;
        weightRange?: number[];
      };
      'com.moego.server.grooming.service.params.QueryCustomerPackageInfoParam': {
        /**
         * Format: int32
         * @description history分页页数，默认1
         */
        historyPageNum?: number;
        /**
         * Format: int32
         * @description history分页大小，默认20
         */
        historyPageSize?: number;
        /**
         * Format: int32
         * @description package id
         */
        id: number;
      };
      'com.moego.server.grooming.service.params.ServiceAreaPicCacheOperationParams': {
        factorsHash: string;
        url?: string;
      };
      'com.moego.server.grooming.web.AbandonedScheduleMessageSettingController$UpdateAbandonedScheduleMessageSettingParam': {
        abandonedSteps: ComMoegoServerGroomingWebAbandonedScheduleMessageSettingController$UpdateAbandonedScheduleMessageSettingParamAbandonedSteps[];
        clientTypes: ComMoegoServerGroomingWebAbandonedScheduleMessageSettingController$UpdateAbandonedScheduleMessageSettingParamClientTypes[];
        isEnabled: boolean;
        message: string;
        onTypeDays: ComMoegoServerGroomingWebAbandonedScheduleMessageSettingController$UpdateAbandonedScheduleMessageSettingParamOnTypeDays[];
        /** Format: int32 */
        onTypeMinute: number;
        /** @enum {string} */
        sendOutType: ComMoegoServerGroomingWebAbandonedScheduleMessageSettingController$UpdateAbandonedScheduleMessageSettingParamSendOutType;
        /** Format: int32 */
        waitForTypeHour: number;
        /** Format: int32 */
        waitForTypeMinute: number;
      };
      'com.moego.server.grooming.web.dto.Business2021SummaryDto': {
        /**
         * Format: int32
         * @description 全年service duration累加
         */
        allServiceDurationHours: number;
        /** @description 最长工作时间的天 */
        longestWorkingDay: string;
        /**
         * Format: int32
         * @description 最长工作时间的和时长
         */
        longestWorkingHours: number;
        /** @description 照顾过最多的pet breed */
        petBreedMost: string;
        /**
         * Format: int32
         * @description 照顾过最多的pet breed 数量
         */
        petBreedMostCount: number;
        /**
         * Format: int32
         * @description 照顾过的pet数量
         */
        takeCarePetCount: number;
      };
      'com.moego.server.grooming.web.dto.ob.AvailableStaffTimeListDto': {
        availabilityList: components['schemas']['com.moego.server.grooming.dto.BookOnlineStaffAvailabilityDTO'][];
        staffList: components['schemas']['com.moego.server.grooming.mapperbean.MoeBookOnlineStaffTime'][];
      };
      'com.moego.server.grooming.web.dto.ob.BusinessInfoDto': {
        isAvailable: boolean;
        /** Format: int32 */
        isEnable: number;
        isInPackageWhitelist: boolean;
        isMerged: boolean;
        setting: components['schemas']['com.moego.server.grooming.mapperbean.MoeBusinessBookOnline'];
      };
      'com.moego.server.grooming.web.dto.ob.GalleryDto': {
        /** Format: int32 */
        imageId: number;
        imagePath: string;
        /** Format: int32 */
        isStar: number;
        /** Format: int32 */
        sort: number;
      };
      'com.moego.server.grooming.web.dto.ob.GalleryListDto': {
        list: components['schemas']['com.moego.server.grooming.web.dto.ob.GalleryDto'][];
      };
      'com.moego.server.grooming.web.dto.ob.InfoDto': {
        businessInfo: components['schemas']['com.moego.server.grooming.web.dto.ob.BusinessInfoDto'];
        paymentSetting: components['schemas']['com.moego.server.payment.dto.PaymentSettingForClientDTO'];
        squareInfo: components['schemas']['com.moego.server.payment.dto.GetSquareTokenResponse'];
        tipConfig: components['schemas']['com.moego.server.payment.dto.SmartTipConfigForClientDTO'];
        twilioInfo: components['schemas']['com.moego.server.message.dto.BusinessTwilioNumberDTO'];
      };
      'com.moego.server.grooming.web.dto.ob.IsAvailableDto': {
        isAvailable: boolean;
      };
      'com.moego.server.grooming.web.dto.ob.LastApptDto': {
        lastAppt: components['schemas']['com.moego.server.grooming.dto.CustomerGroomingAppointmentDTO'];
      };
      'com.moego.server.grooming.web.dto.ob.OBSubmitResultDTO': {
        /** @description 是否自动接收 request，必须满足 1.开启 auto accept; 2.包含 apptDate & apptTime */
        autoAcceptRequest: boolean;
      };
      'com.moego.server.grooming.web.dto.ob.OBTimeSlotSimpleDTO': {
        /**
         * Format: int32
         * @deprecated
         * @description the number of days for the furthest query, max value is 365
         */
        farthestDay?: number;
        /** @description latitude */
        lat?: string;
        /** @description longitude */
        lng?: string;
        /**
         * @deprecated
         * @description enable smart schedule flag
         */
        querySmartScheduling?: boolean;
        /** @description start date of the query */
        startDate: string;
        /** @description zipcode */
        zipcode?: string;
      };
      'com.moego.server.grooming.web.dto.ob.ProfileDto': {
        company: components['schemas']['com.moego.server.grooming.web.dto.ob.ProfileDto$OBBusinessProfileCompanyVO'];
        profile: components['schemas']['com.moego.server.grooming.mapperbean.MoeBookOnlineProfile'];
      };
      'com.moego.server.grooming.web.dto.ob.ProfileDto$OBBusinessProfileCompanyVO': {
        /** Format: int32 */
        enterpriseId: number;
      };
      'com.moego.server.grooming.web.dto.ob.QuestionListDto': {
        questions: components['schemas']['com.moego.server.grooming.dto.GroomingQuestionDTO'][];
      };
      'com.moego.server.grooming.web.dto.ob.SettingInfoDto': {
        acceptCustomerType: components['schemas']['com.moego.server.grooming.web.dto.ob.SettingInfoDto$AcceptCustomerTypes'];
        bookOnlineInfo: components['schemas']['com.moego.server.grooming.mapperbean.MoeBusinessBookOnline'];
        bookOnlineNotification: components['schemas']['com.moego.server.grooming.mapperbean.MoeBookOnlineNotification'];
        businessName: string;
        /** @description Whether the merge operation of the landing page has been performed */
        isMerged: boolean;
        /** @description New users and those who have never used OB */
        isNew: boolean;
        paymentSetting: components['schemas']['com.moego.server.grooming.dto.ob.BookOnlinePaymentSettingDTO'];
      };
      'com.moego.server.grooming.web.dto.ob.SettingInfoDto$AcceptCustomerTypes': {
        /** Format: int32 */
        boarding: number;
        /** Format: int32 */
        daycare: number;
        /** Format: int32 */
        grooming: number;
      };
      'com.moego.server.grooming.web.dto.service.AddServiceCategoryResultDto': {
        /** Format: int32 */
        categoryId: number;
        result: boolean;
      };
      'com.moego.server.grooming.web.dto.service.AddServiceResultDto': {
        result: boolean;
        /** Format: int32 */
        serviceId: number;
      };
      'com.moego.server.grooming.web.dto.service.ServiceCategoryGroupedResultDto': {
        categoryList: components['schemas']['com.moego.server.grooming.service.dto.ServiceGroupByCategoryDto'][];
      };
      'com.moego.server.grooming.web.dto.service.ServiceCategoryListResultDto': {
        serviceCategoryList: components['schemas']['com.moego.server.grooming.service.dto.ServiceCategoryListDto'][];
      };
      'com.moego.server.grooming.web.dto.ThanksgivingResultDto': {
        /** Format: int32 */
        annualPetCount: number;
        /** Format: int32 */
        petCount: number;
      };
      'com.moego.server.grooming.web.params.ApptEditSaveCheckParams': {
        /** Format: int32 */
        groomingId: number;
        serviceList?: components['schemas']['com.moego.server.grooming.params.PetDetailParams'][];
      };
      'com.moego.server.grooming.web.params.BatchDrivingInfoParams': {
        /**
         * Format: date
         * @description 查询结束日期，非必传，不传时，只查 startDate 一天
         */
        endDate?: string;
        /** @description 查询 staff id 列表，不能为空 */
        staffIdList: number[];
        /**
         * Format: date
         * @description 查询开始日期，必传
         */
        startDate: string;
      };
      'com.moego.server.grooming.web.params.calendar.CardRescheduleParams': {
        /**
         * Format: int32
         * @description appointment id
         */
        appointmentId: number;
        /**
         * Format: int32
         * @description card type
         */
        cardType: number;
        /**
         * Format: int32
         * @description end time
         */
        endTime: number;
        /**
         * Format: int64
         * @description card id
         */
        id: string | number;
        /** @description pet detail ids */
        petDetailIds: number[];
        /**
         * @description repeat type: 1-only this, 2-apply to upcoming, 3-apply to all
         * @enum {string}
         */
        repeatType: ComMoegoServerGroomingWebParamsCalendarCardRescheduleParamsRepeatType;
        /**
         * Format: int32
         * @description staff id
         */
        staffId: number;
        /** @description start date */
        startDate: string;
        /**
         * Format: int32
         * @description start time
         */
        startTime: number;
      };
      'com.moego.server.grooming.web.params.DrivingInfoCalculationParams': {
        /** @description 目标地点经度，不能为空 */
        addressLat: string;
        /** @description 目标地点纬度，不能为空 */
        addressLng: string;
        /** Format: int32 */
        businessId?: number;
        /** @description client 出发列表 */
        clientIdsFrom?: (string | number)[];
        /** @description client 到达列表 */
        clientIdsTo?: (string | number)[];
        /** @description van 出发列表 */
        vanStaffIdsFrom?: (string | number)[];
        /** @description van 到达列表 */
        vanStaffIdsTo?: (string | number)[];
      };
      'com.moego.server.grooming.web.params.MoeGroomingAppointmentSpecialParam': {
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        repeatId?: number;
      };
      'com.moego.server.grooming.web.params.MoeGroomingRepeatParam': {
        /** Format: int64 */
        createTime?: string | number;
        endOn?: string;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        isNotice?: number;
        /** Format: int32 */
        monthDay?: number;
        /** Format: int32 */
        monthWeekDay?: number;
        /** Format: int32 */
        monthWeekTimes?: number;
        repeatBy?: string;
        /** Format: int32 */
        repeatEvery?: number;
        /** Format: int32 */
        repeatEveryType?: number;
        /** Format: int32 */
        repeatType?: number;
        /** Format: date-time */
        setEndOn?: string;
        /** Format: int32 */
        ssAfterDays?: number;
        /** Format: int32 */
        ssBeforeDays?: number;
        /** Format: int32 */
        ssFlag?: number;
        /** Format: int32 */
        staffId?: number;
        /** Format: date-time */
        startsOn?: string;
        /** Format: int32 */
        status?: number;
        /** Format: int32 */
        times?: number;
        type?: string;
        /** Format: int64 */
        updateTime?: string | number;
      };
      /** @description abandon client info */
      'com.moego.server.grooming.web.params.OBAbandonClientParams': {
        /** @description address line1 */
        address1: string;
        /** @description address line2 */
        address2: string;
        /**
         * Format: int32
         * @description existing address id
         */
        addressId: number;
        /** @description custom question answers, key:custom_questionID, value: answer */
        answersMap: {
          [key: string]: string | undefined;
        };
        /** @description city */
        city: string;
        /** @description country */
        country: string;
        /**
         * Format: int32
         * @description existing client id
         */
        customerId: number;
        /** @description abandon client unique email */
        email: string;
        /** @description abandon client first name */
        firstName: string;
        isProfileRequestAddress: boolean;
        /** @description abandon client last name */
        lastName: string;
        /** @description latitude */
        lat: string;
        /** @description longitude */
        lng: string;
        /** @description abandon client unique phone number */
        phoneNumber: string;
        /** @description referer link */
        referer: string;
        /** @description state */
        state: string;
        /** @description zipcode */
        zipcode: string;
      };
      'com.moego.server.grooming.web.params.OBAbandonParams': {
        /** @description signed agreement info */
        agreements?: components['schemas']['com.moego.server.grooming.web.params.OBAbandonParams$OBAbandonAgreementParams'][];
        /** @description selected appointment date, yyyy-MM-dd */
        appointmentDate?: string;
        /**
         * Format: int32
         * @description selected appointment minutes
         */
        appointmentStartTime?: number;
        /**
         * Format: int32
         * @description boarding/daycare service pet arrival time
         */
        arrivalTime?: number;
        /** @description boarding end date, yyyy-MM-dd */
        boardingEndDate?: string;
        /** @description boarding start date, yyyy-MM-dd */
        boardingStartDate?: string;
        /** @description unique ID for each booking flow' */
        bookingFlowId: string;
        bookOnlineCustomerAdditionalParams?: components['schemas']['com.moego.server.grooming.params.BookOnlineCustomerAdditionalParams'];
        /**
         * @description selected care type
         * @enum {string}
         */
        careType?: ComMoegoServerGroomingWebParamsOBAbandonParamsCareType;
        customerData?: components['schemas']['com.moego.server.grooming.web.params.OBAbandonClientParams'];
        /** @description daycare dates, yyyy-MM-dd */
        daycareDates?: string[];
        /**
         * @description unrecoverable type
         * @enum {string}
         */
        deleteType?: ComMoegoServerGroomingWebParamsOBAbandonParamsDeleteType;
        /**
         * @description new ob step unique ID
         * @enum {string}
         */
        nextStep: ComMoegoServerGroomingWebParamsOBAbandonParamsNextStep;
        /** @description additional note, alert note */
        note?: string;
        /** @description payment ob seg rule */
        paymentSegSettingRule?: string;
        /** @description selected pet info */
        petData?: components['schemas']['com.moego.server.grooming.web.params.OBAbandonPetParams'][];
        /**
         * Format: int32
         * @description boarding/daycare service pet pickup time
         */
        pickupTime?: number;
        /** @description http header referer */
        referrer?: string;
        /** @description skip abandon step */
        skipAbandon?: boolean;
        /**
         * Format: int32
         * @description selected staff id
         */
        staffId?: number;
        /** @description payment ob seg */
        usePaymentSegSetting?: boolean;
      };
      /** @description signed agreement info */
      'com.moego.server.grooming.web.params.OBAbandonParams$OBAbandonAgreementParams': {
        /**
         * Format: int32
         * @description agreement id
         */
        agreementId: number;
        /** @description agreement signature base64 */
        signature: string;
      };
      /** @description selected pet info */
      'com.moego.server.grooming.web.params.OBAbandonPetParams': {
        /** @description selected add-on id list */
        addOnIds: number[];
        /** @description pet image */
        avatarPath: string;
        /** @description behavior name */
        behavior: string;
        /** @description pet birthday date, yyyy-MM-dd */
        birthday: string;
        /** @description pet breed name */
        breed: string;
        /** @description emergency contact name */
        emergencyContactName: string;
        /** @description emergency contact phone number */
        emergencyContactPhone: string;
        /** @description fixed name */
        fixed: string;
        /** Format: int32 */
        gender: number;
        /** @description hair length name */
        hairLength: string;
        /** @description health issues */
        healthIssues: string;
        /**
         * Format: int32
         * @description existing pet id
         */
        petId: number;
        /** @description pet name */
        petName: string;
        /** @description custom question answers, key:custom_questionID, value: answer */
        petQuestionAnswers: {
          [key: string]: string | undefined;
        };
        /**
         * Format: int32
         * @description 1-dog, 2-cat, 11-other
         */
        petTypeId: number;
        /**
         * Format: int32
         * @description selected service id
         */
        serviceId: number;
        /** @description vaccine list */
        vaccineList: components['schemas']['com.moego.server.grooming.web.params.OBAbandonPetParams$OBAbandonPetVaccineParams'][];
        /** @description vet address */
        vetAddress: string;
        /** @description vet name */
        vetName: string;
        /** @description vet phone number */
        vetPhone: string;
        /** @description pet weight */
        weight: string;
      };
      /** @description vaccine list */
      'com.moego.server.grooming.web.params.OBAbandonPetParams$OBAbandonPetVaccineParams': {
        /** @description upload multi vaccine document url */
        documentUrls?: string[];
        /** @description vaccine expiration date */
        expirationDate?: string;
        /** @description upload vaccine document url */
        vaccineDocument?: string;
        /**
         * Format: int32
         * @description vaccine id
         */
        vaccineId: number;
      };
      'com.moego.server.grooming.web.params.OBBusinessStaffParams': {
        /**
         * Format: int32
         * @deprecated
         * @description business id
         */
        businessId?: number;
        /**
         * @deprecated
         * @description business name
         */
        businessName?: string;
        /**
         * Format: int32
         * @description existing client id, or null for new user
         */
        customerId?: number;
        /** @description selected pet service */
        petServiceList: components['schemas']['com.moego.server.grooming.dto.ob.SelectedPetServiceDTO'][];
        timeSlotParam: components['schemas']['com.moego.server.grooming.web.dto.ob.OBTimeSlotSimpleDTO'];
      };
      'com.moego.server.grooming.web.params.OBClientInfoParams': {
        agreements: components['schemas']['com.moego.server.grooming.params.BookOnlineAgreementParams'][];
        /** @description 日期格式： 2020-02-08 */
        appointmentDate?: string;
        /** Format: int32 */
        appointmentStartTime?: number;
        bookOnlineCustomerAdditionalParams?: components['schemas']['com.moego.server.grooming.params.BookOnlineCustomerAdditionalParams'];
        /**
         * Format: int32
         * @deprecated
         */
        businessId?: number;
        /** @deprecated */
        businessName?: string;
        customerData: components['schemas']['com.moego.server.grooming.params.BookOnlineCustomerParams'];
        discountCodeParams?: components['schemas']['com.moego.server.grooming.params.ob.DiscountCodeParams'];
        /** Format: int32 */
        isAgreePolicy?: number;
        membershipParams?: components['schemas']['com.moego.server.grooming.params.ob.MembershipParams'];
        note?: string;
        orderId?: string;
        /** Format: int32 */
        outOfArea?: number;
        petData: components['schemas']['com.moego.server.grooming.params.BookOnlinePetParams'][];
        preAuthDetail?: components['schemas']['com.moego.server.grooming.params.ob.PreAuthDetailParams'];
        prepayDetail?: components['schemas']['com.moego.server.grooming.params.ob.PrepayDetailParams'];
        /** @description prepay guid，用于找到支付记录，绑定到新创建的订单，无支付时不需传 */
        prepayGuid?: string;
        /** @enum {string} */
        sourceType?: ComMoegoServerGroomingWebParamsOBClientInfoParamsSourceType;
        /** Format: int32 */
        staffId?: number;
      };
      'com.moego.server.grooming.web.params.OBLandingPageConfigParams': {
        /** @description About us, Inherited from moe_book_online_profile.description */
        aboutUs: string;
        amenities: components['schemas']['com.moego.server.grooming.web.params.OBLandingPageConfigParams$LandingPageAmenitiesParams'];
        clientReviews: components['schemas']['com.moego.server.grooming.web.params.OBLandingPageConfigParams$OBConfigClientReviewParams'];
        /** @description Landing page gallery */
        galleryList: components['schemas']['com.moego.server.grooming.web.params.OBLandingPageConfigParams$OBLandingPageGalleryParams'][];
        /** @description GA4 PROPERTY ID */
        gaMeasurementId: string;
        /** @description Business site live status */
        isPublished: boolean;
        /** @description Page components */
        pageComponents: {
          [key: string]: boolean | undefined;
        };
        /** @description Showcase after image */
        showcaseAfterImage: string;
        /** @description Showcase before image */
        showcaseBeforeImage: string;
        teams: components['schemas']['com.moego.server.grooming.web.params.OBLandingPageConfigParams$OBConfigTeamParams'][];
        /** @description thank you page url */
        thankYouPageUrl: string;
        /** @description Theme color, Inherited from moe_book_online_profile.button_color */
        themeColor: string;
        /** @description Customized URL domain name */
        urlDomainName: string;
        /** @description Welcome page message, Inherited from moe_business_book_online.description */
        welcomePageMessage: string;
      };
      /** @description Amenity, 8 features and 4 payment options */
      'com.moego.server.grooming.web.params.OBLandingPageConfigParams$LandingPageAmenitiesParams': {
        /** @description Features */
        features: {
          [key: string]: boolean | undefined;
        };
        /** @description Payment */
        payment: {
          [key: string]: boolean | undefined;
        };
      };
      'com.moego.server.grooming.web.params.OBLandingPageConfigParams$OBConfigClientReviewParams': {
        isDisplayClientReviewShowcasePhoto: boolean;
        reviewRecords: components['schemas']['com.moego.server.grooming.web.params.OBLandingPageConfigParams$OBConfigClientReviewRecordParams'][];
      };
      'com.moego.server.grooming.web.params.OBLandingPageConfigParams$OBConfigClientReviewRecordParams': {
        /** Format: int32 */
        customerId?: number;
        /** Format: int32 */
        petId?: number;
        /** Format: int32 */
        reviewId: number;
      };
      'com.moego.server.grooming.web.params.OBLandingPageConfigParams$OBConfigTeamParams': {
        instagramLink?: string;
        introduction?: string;
        isEnabled?: boolean;
        /** Format: int32 */
        staffId: number;
        tags?: string[];
      };
      /** @description Landing page gallery */
      'com.moego.server.grooming.web.params.OBLandingPageConfigParams$OBLandingPageGalleryParams': {
        /** @description Gallery image path */
        imagePath?: string;
        /**
         * Format: int32
         * @description Gallery sort number
         */
        sort: number;
      };
      'com.moego.server.grooming.web.params.OBLandingPageMergeParams': {
        profile: components['schemas']['com.moego.server.grooming.web.params.OBLandingPageMergeParams$OBMergeProfileParams'];
        workingHours: components['schemas']['com.moego.server.business.dto.BusinessWorkingHourDayDetailDTO'];
      };
      /** @description Update biz settings profile params */
      'com.moego.server.grooming.web.params.OBLandingPageMergeParams$OBMergeProfileParams': {
        /** @description Full address */
        address: string;
        /** @description Address 1 */
        address1: string;
        /** @description Address 2 */
        address2: string;
        /** @description Address city */
        addressCity: string;
        /** @description Address country */
        addressCountry: string;
        /** @description Address latitude */
        addressLat: string;
        /** @description Address longitude */
        addressLng: string;
        /** @description Address state */
        addressState: string;
        /** @description Address zipcode */
        addressZipcode: string;
        /** @description Business info logo path */
        avatarPath: string;
        /** @description Business info name */
        businessName: string;
        /** @description Facebook */
        facebook: string;
        /** @description Google */
        google: string;
        /** @description Instagram */
        instagram: string;
        /** @description Business info phone number */
        phoneNumber: string;
        /** @description Website */
        website: string;
        /** @description Yelp */
        yelp: string;
      };
      'com.moego.server.grooming.web.params.OBMetricsParams': {
        endDate: string;
        /** @description Metrics name */
        metrics: components['schemas']['com.moego.server.grooming.web.params.OBMetricsParams$OBMetricParams'][];
        startDate: string;
      };
      /** @description Metrics name */
      'com.moego.server.grooming.web.params.OBMetricsParams$OBMetricParams': {
        /**
         * @description Metrics name
         * @enum {string}
         */
        name: ComMoegoServerGroomingWebParamsOBMetricsParams$OBMetricParamsName;
        /** @description Metrics type, sum */
        types: ComMoegoServerGroomingWebParamsOBMetricsParams$OBMetricParamsTypes[];
      };
      'com.moego.server.grooming.web.params.OBServiceParams': {
        /**
         * Format: int32
         * @deprecated
         * @description business id
         */
        businessId?: number;
        /**
         * @deprecated
         * @description business name
         */
        businessName?: string;
        /**
         * Format: int32
         * @description existing client id
         */
        customerId?: number;
        /** @description filter service by pet info */
        petDataList: components['schemas']['com.moego.server.grooming.dto.ob.OBPetDataDTO'][];
      };
      'com.moego.server.grooming.web.params.OBTimeSlotParams': {
        /**
         * Format: int32
         * @deprecated
         * @description business id
         */
        businessId?: number;
        /**
         * @deprecated
         * @description business name
         */
        businessName?: string;
        /**
         * Format: int32
         * @description number of days of results
         */
        count?: number;
        /**
         * Format: int32
         * @description existing client id
         */
        customerId?: number;
        /** @description start date of the query */
        date?: string;
        /** @description one of date or dates must be provided, uses dates first */
        dates?: string[];
        /**
         * Format: int32
         * @deprecated
         * @description the number of days for the furthest query, max value is 365
         */
        farthestDay?: number;
        /** @description latitude */
        lat?: string;
        /** @description longitude */
        lng?: string;
        /** @description pet param info, include weight and breedId */
        petParamList: components['schemas']['com.moego.server.grooming.dto.ob.OBPetDataDTO'][];
        /** @description pet service info, key is petId, value is service id array */
        petServices?: {
          [key: string]: number[] | undefined;
        };
        /** @description The query time slots ends at the end of the month of the first available date */
        queryEndOfTheMonth?: boolean;
        /** @description full time slot data on the first available date, returns at most one per half day of the remaining days */
        queryPerHalfDay?: boolean;
        /**
         * @deprecated
         * @description enable smart schedule flag
         */
        querySmartScheduling?: boolean;
        /** @description service id */
        serviceIds: number[];
        /** @description staff id */
        staffIdList?: number[];
        /** @description zipcode */
        zipcode?: string;
      };
      'com.moego.server.grooming.web.params.SearchAbandonedClientParam': {
        filter: components['schemas']['com.moego.server.grooming.web.params.SearchAbandonedClientParam$Filter'];
        page?: components['schemas']['com.moego.server.grooming.web.params.SearchAbandonedClientParam$Page'];
        /** Format: int32 */
        pageNo?: number;
        /** Format: int32 */
        pageSize?: number;
        query?: components['schemas']['com.moego.server.grooming.web.params.SearchAbandonedClientParam$Query'];
        sort?: components['schemas']['com.moego.server.grooming.web.params.SearchAbandonedClientParam$Sort'];
      };
      'com.moego.server.grooming.web.params.SearchAbandonedClientParam$Filter': {
        abandonedStep: ComMoegoServerGroomingWebParamsSearchAbandonedClientParam$FilterAbandonedStep[];
        abandonStatus: ComMoegoServerGroomingWebParamsSearchAbandonedClientParam$FilterAbandonStatus[];
        careTypes: ComMoegoServerGroomingWebParamsSearchAbandonedClientParam$FilterCareTypes[];
        /** @description 从 filter 中反选的 abandoned record */
        excludeBookingFlowIds: string[];
        /** @description 从 filter 中正选的 abandoned record，如果正选和反选都有，那么正选优先 */
        includeBookingFlowIds: string[];
        /**
         * Format: int32
         * @description 最后一次联系时间发生在多少分钟前，包括 mass text 和 mass email 两种
         */
        lastContactBeforeMins: number;
        /**
         * Format: int32
         * @description 最后一次联系时间发生在多少分钟内，包括 mass text 和 mass email 两种
         */
        lastContactWithInMins: number;
        leadType: ComMoegoServerGroomingWebParamsSearchAbandonedClientParam$FilterLeadType[];
        timeRange: components['schemas']['com.moego.server.grooming.web.params.SearchAbandonedClientParam$TimeRange'];
      };
      'com.moego.server.grooming.web.params.SearchAbandonedClientParam$Page': {
        /** Format: int32 */
        pageNumber: number;
        /** Format: int32 */
        pageSize: number;
      };
      'com.moego.server.grooming.web.params.SearchAbandonedClientParam$Query': {
        keyword: string;
      };
      'com.moego.server.grooming.web.params.SearchAbandonedClientParam$Sort': {
        /** @enum {string} */
        order: ComMoegoServerGroomingWebParamsSearchAbandonedClientParam$SortOrder;
        /** @enum {string} */
        property: ComMoegoServerGroomingWebParamsSearchAbandonedClientParam$SortProperty;
      };
      'com.moego.server.grooming.web.params.SearchAbandonedClientParam$TimeRange': {
        /** Format: int64 */
        endTimeSec: string | number;
        /** Format: int64 */
        startTimeSec: string | number;
      };
      'com.moego.server.grooming.web.params.ServiceCategoryBatchUpdateParams': {
        categorySaveList: components['schemas']['com.moego.server.grooming.service.dto.ServiceCategoryUpdateDto'][];
        /** Format: int32 */
        serviceItemType?: number;
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.grooming.web.params.SubmitBookingRequestParams': {
        agreements: components['schemas']['com.moego.server.grooming.params.BookOnlineAgreementParams'][];
        appointmentDate?: string;
        /** Format: int32 */
        appointmentStartTime?: number;
        bookOnlineCustomerAdditionalParams?: components['schemas']['com.moego.server.grooming.params.BookOnlineCustomerAdditionalParams'];
        customerData: components['schemas']['com.moego.server.grooming.web.params.SubmitBookingRequestParams$Customer'];
        discountCodeParams?: components['schemas']['com.moego.server.grooming.params.ob.DiscountCodeParams'];
        /** Format: int32 */
        isAgreePolicy?: number;
        note?: string;
        orderId?: string;
        /** Format: int32 */
        outOfArea?: number;
        petData: components['schemas']['com.moego.server.grooming.params.BookOnlinePetParams'][];
        preAuthDetail?: components['schemas']['com.moego.server.grooming.params.ob.PreAuthDetailParams'];
        /** @description prepay guid，用于找到支付记录，绑定到新创建的订单，无支付时不需传 */
        prepayGuid?: string;
        /** Format: int32 */
        staffId?: number;
      };
      'com.moego.server.grooming.web.params.SubmitBookingRequestParams$Customer': {
        address: string;
        address1: string;
        address2: string;
        /** Format: int32 */
        addressId: number;
        answersMap: {
          [key: string]: any;
        };
        /** Format: date-time */
        birthday: string;
        /** @description credit card token for stripe or square */
        chargeToken: string;
        city: string;
        country: string;
        /** Format: int32 */
        customerId: number;
        email: string;
        firstName: string;
        /** @description true if client supplied */
        hasStripeCard: boolean;
        isProfileRequestAddress: boolean;
        lastName: string;
        lat: string;
        lng: string;
        phoneNumber: string;
        state: string;
        stripeCustomerId: string;
        zipcode: string;
      };
      'com.moego.server.grooming.web.params.SubmitQuestionnaireParams': {
        /** @description form detail */
        formDetail: string;
      };
      'com.moego.server.grooming.web.params.waitlist.AddWaitListParam': {
        allPetsStartAtSameTime?: boolean;
        /** Format: int32 */
        customerId: number;
        datePreference?: components['schemas']['com.moego.server.grooming.web.params.waitlist.DatePreference'];
        petServices?: components['schemas']['com.moego.server.grooming.web.params.waitlist.WaitListPetServiceParam'][];
        staffPreference?: components['schemas']['com.moego.server.grooming.web.params.waitlist.StaffPreference'];
        ticketComment?: string;
        timePreference?: components['schemas']['com.moego.server.grooming.web.params.waitlist.TimePreference'];
        /**
         * Format: date
         * @description valid from, YYYY-MM-DD
         */
        validFrom?: string;
        /**
         * Format: date
         * @description valid till, YYYY-MM-DD
         */
        validTill?: string;
      };
      'com.moego.server.grooming.web.params.waitlist.CalendarViewParam': {
        /** Format: date */
        endDate: string;
        /** Format: date */
        startDate: string;
      };
      'com.moego.server.grooming.web.params.waitlist.DatePreference': {
        /** @description day of week, value, from 0 (Sunday) to 6 (Saturday) */
        dayOfWeek: number[];
        /** @description exact date, YYYY-MM-DD */
        exactDate: string[];
        isAnyDate: boolean;
      };
      'com.moego.server.grooming.web.params.waitlist.FilterParams': {
        /**
         * Format: date-time
         * @description availability query range end, yyyy-MM-ddTHH:mm:ss
         */
        availableEndDateTime: string;
        /**
         * Format: date-time
         * @description availability query range start, yyyy-MM-ddTHH:mm:ss
         */
        availableStartDateTime: string;
        /** @description new, recurring, prospect */
        clientType: string[];
        dateList: string[];
        isAnyDate: boolean;
        isAnyTime: boolean;
        isAvailable: boolean;
        isOnGoing: boolean;
        /** @description search list that not in service area */
        isOutOfArea: boolean;
        serviceAreaIdList: number[];
        staffIdList: number[];
        timeRangeList: components['schemas']['com.moego.server.business.dto.TimeRangeDto'][];
      };
      'com.moego.server.grooming.web.params.waitlist.FromAppointmentParam': {
        allPetsStartAtSameTime?: boolean;
        /** Format: int64 */
        appointmentId: string | number;
        datePreference?: components['schemas']['com.moego.server.grooming.web.params.waitlist.DatePreference'];
        /** @description if need delete original appointment/ob request */
        deleteOriginalAppointment?: boolean;
        petList?: components['schemas']['com.moego.server.grooming.params.appointment.PetParams'][];
        staffPreference?: components['schemas']['com.moego.server.grooming.web.params.waitlist.StaffPreference'];
        ticketComment?: string;
        timePreference?: components['schemas']['com.moego.server.grooming.web.params.waitlist.TimePreference'];
        /**
         * Format: date
         * @description valid from, YYYY-MM-DD
         */
        validFrom?: string;
        /**
         * Format: date
         * @description valid till, YYYY-MM-DD
         */
        validTill?: string;
      };
      'com.moego.server.grooming.web.params.waitlist.GetWaitListParam': {
        filters: components['schemas']['com.moego.server.grooming.web.params.waitlist.FilterParams'];
        /** @description Query keyword */
        keyword?: string;
        /**
         * Format: int32
         * @description Page number
         */
        pageNum: number;
        /**
         * Format: int32
         * @description Page size
         */
        pageSize: number;
        sort: components['schemas']['com.moego.server.grooming.web.params.waitlist.SortParams'];
      };
      'com.moego.server.grooming.web.params.waitlist.SmartScheduleParam': {
        /**
         * Format: date-time
         * @description available slot range end, yyyy-MM-ddTHH:mm:ss
         */
        endDateTime?: string;
        /**
         * Format: int32
         * @description Page number
         */
        pageNum: number;
        /**
         * Format: int32
         * @description Page size
         */
        pageSize: number;
        /**
         * Format: date-time
         * @description available slot query range start, yyyy-MM-ddTHH:mm:ss
         */
        startDateTime?: string;
        /**
         * Format: int64
         * @description Wait list id
         */
        waitListId: string | number;
      };
      /** @description Sort params */
      'com.moego.server.grooming.web.params.waitlist.SortParams': {
        /**
         * @description Sort order
         * @enum {string}
         */
        order: ComMoegoServerGroomingWebParamsWaitlistSortParamsOrder;
        /**
         * @description Sort property
         * @enum {string}
         */
        property: ComMoegoServerGroomingWebParamsWaitlistSortParamsProperty;
      };
      'com.moego.server.grooming.web.params.waitlist.StaffPreference': {
        isAnyone: boolean;
        staffIds: number[];
      };
      'com.moego.server.grooming.web.params.waitlist.TimePreference': {
        /** @description exact time 预约起始时间，value 为当天的分钟数 */
        exactStartTime: number[];
        isAnyTime: boolean;
        /** @description 偏好的时间段 */
        timeRange: components['schemas']['com.moego.server.business.dto.TimeRangeDto'][];
      };
      'com.moego.server.grooming.web.params.waitlist.UpdateWaitListParam': {
        allPetsStartAtSameTime?: boolean;
        datePreference?: components['schemas']['com.moego.server.grooming.web.params.waitlist.DatePreference'];
        /**
         * Format: int64
         * @description waiting list id
         */
        id: string | number;
        petServices?: components['schemas']['com.moego.server.grooming.web.params.waitlist.WaitListPetServiceParam'][];
        staffPreference?: components['schemas']['com.moego.server.grooming.web.params.waitlist.StaffPreference'];
        ticketComment?: string;
        timePreference?: components['schemas']['com.moego.server.grooming.web.params.waitlist.TimePreference'];
        /**
         * Format: date
         * @description valid from, YYYY-MM-DD
         */
        validFrom?: string;
        /**
         * Format: date
         * @description valid till, YYYY-MM-DD
         */
        validTill?: string;
      };
      'com.moego.server.grooming.web.params.waitlist.WaitListPetServiceParam': {
        /** Format: int32 */
        petId: number;
        serviceList?: components['schemas']['com.moego.server.grooming.web.params.waitlist.WaitListServiceAndOperationParams'][];
      };
      'com.moego.server.grooming.web.params.waitlist.WaitListServiceAndOperationParams': {
        /** Format: int32 */
        serviceId: number;
      };
      'com.moego.server.grooming.web.query.ClientApptQuery': {
        /** Format: int32 */
        apptType: number;
        /**
         * Format: int32
         * @description Page number, default is 1
         */
        pageNum: number;
        /**
         * Format: int32
         * @description Page size, default is 10
         */
        pageSize: number;
        sort: components['schemas']['com.moego.common.params.PageQuery$SortQuery'];
        /** @description Sort by multiple field */
        sortList: components['schemas']['com.moego.common.params.PageQuery$SortQuery'][];
      };
      'com.moego.server.grooming.web.result.ob.BriefOBProfileResult$BriefOBProfileDTO': {
        /** Format: int32 */
        businessId: number;
        urlDomainName: string;
      };
      'com.moego.server.grooming.web.vo.AbandonedClientPageDTOCom.moego.server.grooming.web.vo.ob.AbandonedClientVO': {
        /** Format: int32 */
        abandonedClientAbandonedCount: number;
        /** Format: int32 */
        abandonedClientTotalCount: number;
        dataList: components['schemas']['com.moego.server.grooming.web.vo.ob.AbandonedClientVO'][];
        end: boolean;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        /** Format: int64 */
        total: string | number;
      };
      'com.moego.server.grooming.web.vo.AbandonedScheduleMessageSettingVO': {
        abandonedSteps: ComMoegoServerGroomingWebVoAbandonedScheduleMessageSettingVOAbandonedSteps[];
        /** Format: int32 */
        businessId: number;
        clientTypes: ComMoegoServerGroomingWebVoAbandonedScheduleMessageSettingVOClientTypes[];
        /** Format: int32 */
        id: number;
        isEnabled: boolean;
        message: string;
        onTypeDays: ComMoegoServerGroomingWebVoAbandonedScheduleMessageSettingVOOnTypeDays[];
        /** Format: int32 */
        onTypeMinute: number;
        /** @enum {string} */
        sendOutType: ComMoegoServerGroomingWebVoAbandonedScheduleMessageSettingVOSendOutType;
        /** Format: int32 */
        waitForTypeHour: number;
        /** Format: int32 */
        waitForTypeMinute: number;
      };
      'com.moego.server.grooming.web.vo.ApplyPackageServiceVo': {
        checkRefund?: boolean;
        /** Format: int32 */
        invoiceId: number;
        /** Format: int32 */
        packageServiceId?: number;
        /** Format: int32 */
        quantity?: number;
        /** Format: int32 */
        serviceId?: number;
      };
      'com.moego.server.grooming.web.vo.BookOnlinePetLimitRequest': {
        allBreed: boolean;
        breedIdList: number[];
        /** Format: int64 */
        findId: string | number;
        /** Format: int32 */
        maxNumber: number;
        /** Format: int32 */
        petTypeId: number;
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.grooming.web.vo.BookOnlinePetLimitResponse': {
        petLimitDTOList: components['schemas']['com.moego.server.grooming.dto.BookOnlinePetLimitDTO'][];
      };
      'com.moego.server.grooming.web.vo.Business2023SummaryVo': {
        businessSummary: components['schemas']['com.moego.server.grooming.web.vo.Business2023SummaryVo$Business2023Summary'];
        ownerSummary: components['schemas']['com.moego.server.grooming.web.vo.Business2023SummaryVo$Owner2023Summary'];
        staff2023Summary: components['schemas']['com.moego.server.grooming.web.vo.Business2023SummaryVo$Staff2023Summary'];
      };
      'com.moego.server.grooming.web.vo.Business2023SummaryVo$Business2023Summary': {
        longestWorkingDay: components['schemas']['com.moego.server.grooming.web.vo.Business2023SummaryVo$LongestWorkingDay2023'];
        mostPetBreeds: components['schemas']['com.moego.server.grooming.web.vo.Business2023SummaryVo$MostPetBreed'][];
        mostPetServices: components['schemas']['com.moego.server.grooming.web.vo.Business2023SummaryVo$MostPetService'][];
        mostPopularPetName: string;
        reviewSummary: components['schemas']['com.moego.server.grooming.web.vo.Business2023SummaryVo$Review2023Summary'];
        /** Format: int32 */
        takeCaredPetCnt: number;
        /** Format: int32 */
        totalServiceTimeInMin: number;
      };
      'com.moego.server.grooming.web.vo.Business2023SummaryVo$LongestWorkingDay2023': {
        date: string;
        /** Format: int32 */
        workingTimeInMin: number;
      };
      'com.moego.server.grooming.web.vo.Business2023SummaryVo$MostPetBreed': {
        breed: string;
        /** Format: int32 */
        count: number;
      };
      'com.moego.server.grooming.web.vo.Business2023SummaryVo$MostPetService': {
        /** Format: int32 */
        count: number;
        serviceName: string;
      };
      'com.moego.server.grooming.web.vo.Business2023SummaryVo$Owner2023Summary': {
        groomingReportSummary: components['schemas']['com.moego.server.grooming.web.vo.Business2023SummaryVo$Owner2023Summary$GroomingReport2023Summary'];
        marketingCampaignsSummary: components['schemas']['com.moego.server.grooming.web.vo.Business2023SummaryVo$Owner2023Summary$MarketingCampaigns2023Summary'];
        moegoPaySummary: components['schemas']['com.moego.server.grooming.web.vo.Business2023SummaryVo$Owner2023Summary$MoegoPay2023Summary'];
        onlineBookingSummary: components['schemas']['com.moego.server.grooming.web.vo.Business2023SummaryVo$Owner2023Summary$OnlineBooking2023Summary'];
      };
      'com.moego.server.grooming.web.vo.Business2023SummaryVo$Owner2023Summary$GroomingReport2023Summary': {
        mostFrequentMood: string;
        /** Format: int32 */
        reportSendCnt: number;
      };
      'com.moego.server.grooming.web.vo.Business2023SummaryVo$Owner2023Summary$MarketingCampaigns2023Summary': {
        /** Format: int32 */
        contributedBookingsCnt: number;
        /** Format: int32 */
        marketingEmailsOpenedCnt: number;
        /** Format: int32 */
        marketingEmailsSentCnt: number;
      };
      'com.moego.server.grooming.web.vo.Business2023SummaryVo$Owner2023Summary$MoegoPay2023Summary': {
        feeSaved: string;
        tipsBoosted: string;
        transactionAmount: string;
      };
      'com.moego.server.grooming.web.vo.Business2023SummaryVo$Owner2023Summary$OnlineBooking2023Summary': {
        /** Format: int32 */
        abandonedBookingsRecovered: number;
        /** Format: int32 */
        obRequestCnt: number;
        recoveredRevenue: number;
      };
      'com.moego.server.grooming.web.vo.Business2023SummaryVo$Review2023Summary': {
        avgScore: string;
        /** Format: int32 */
        reviewCnt: number;
      };
      'com.moego.server.grooming.web.vo.Business2023SummaryVo$Staff2023Summary': {
        longestWorkingDay: components['schemas']['com.moego.server.grooming.web.vo.Business2023SummaryVo$LongestWorkingDay2023'];
        mostPetBreeds: components['schemas']['com.moego.server.grooming.web.vo.Business2023SummaryVo$MostPetBreed'][];
        mostPetServices: components['schemas']['com.moego.server.grooming.web.vo.Business2023SummaryVo$MostPetService'][];
        mostPopularPetName: string;
        reviewSummary: components['schemas']['com.moego.server.grooming.web.vo.Business2023SummaryVo$Review2023Summary'];
        /** Format: int32 */
        takeCaredPetCnt: number;
        /** Format: int32 */
        totalServiceTimeInMin: number;
      };
      /** @description Online booking business address details */
      'com.moego.server.grooming.web.vo.client.AddressVO': {
        /** @description Address1 */
        address1: string;
        /** @description Address2 */
        address2: string;
        /** @description City */
        city: string;
        /** @description Country */
        country: string;
        /** @description Latitude */
        lat: string;
        /** @description Longitude */
        lng: string;
        /** @description State */
        state: string;
        /** @description Zipcode */
        zipcode: string;
      };
      /** @description Appointment detail */
      'com.moego.server.grooming.web.vo.client.ApptDetailVO': {
        /** @description Appointment date, yyyy-MM-dd */
        apptDate: string;
        /**
         * Format: int32
         * @description Appointment end time, minute offset of the day
         */
        apptEndTime: number;
        /**
         * Format: int32
         * @description Appointment start time, minute offset of the day
         */
        apptStartTime: number;
        /** Format: int32 */
        apptType: number;
        autoAssign: components['schemas']['com.moego.server.grooming.dto.AutoAssignDTO'];
        /** @description Ticket unique identifier */
        bookingId: string;
        /**
         * Format: int32
         * @description Business id
         */
        businessId: number;
        /**
         * Format: int32
         * @description Customer id
         */
        customerId: number;
        noStartTime: boolean;
        /** Format: int32 */
        paymentStatus: number;
        /**
         * Format: int32
         * @description Source, 22168-ob, 22018-web, 17216-android, 17802-ios
         */
        source: number;
        /** Format: int32 */
        status: number;
      };
      /** @description Business info */
      'com.moego.server.grooming.web.vo.client.BusinessInfoVO': {
        /** @description Address 1 */
        address1: string;
        /** @description Address 2 */
        address2: string;
        /** @description Address city */
        addressCity: string;
        /** @description Address country */
        addressCountry: string;
        /** @description Address latitude */
        addressLat: string;
        /** @description Address longitude */
        addressLng: string;
        /** @description Address state */
        addressState: string;
        /** @description Address zipcode */
        addressZipcode: string;
        /** Format: int32 */
        appType: number;
        /** @description Business info logo path */
        avatarPath: string;
        /**
         * Format: int32
         * @description Business id
         */
        businessId: number;
        /** Format: int32 */
        businessMode: number;
        /** @description Business info name */
        businessName: string;
        /** @description Country currency codes, USD */
        currencyCode: string;
        /** @description Country currency symbol, $ */
        currencySymbol: string;
        /** @description Business date format */
        dateFormat: string;
        /** Format: int32 */
        dateFormatType: number;
        /** @description Business owner account email */
        email: string;
        mobileArrivalWindowSetting: components['schemas']['com.moego.server.message.dto.ArrivalWindowSettingDto'];
        /** @description Business info phone number */
        phoneNumber: string;
        /** @description Business time format */
        timeFormat: string;
        /** Format: int32 */
        timeFormatType: number;
        /** @description Business timezone name */
        timezoneName: string;
      };
      /** @description Business twilio info */
      'com.moego.server.grooming.web.vo.client.BusinessTwilioVO': {
        twilioNumber: string;
      };
      'com.moego.server.grooming.web.vo.client.ClientApptDetailVO': {
        appt: components['schemas']['com.moego.server.grooming.web.vo.client.ApptDetailVO'];
        businessInfo: components['schemas']['com.moego.server.grooming.web.vo.client.BusinessInfoVO'];
        businessTwilio: components['schemas']['com.moego.server.grooming.web.vo.client.BusinessTwilioVO'];
        clientInfo: components['schemas']['com.moego.server.grooming.web.vo.client.ClientInfoVO'];
        /** @description Grooming report records */
        groomingReportRecords: components['schemas']['com.moego.server.grooming.web.vo.client.GroomingReportRecordVO'][];
        /** @description Appointment details */
        itemList: components['schemas']['com.moego.server.grooming.web.vo.client.ClientApptItemVO'][];
        obConfig: components['schemas']['com.moego.server.grooming.web.vo.client.OBConfigVO'];
        obProfile: components['schemas']['com.moego.server.grooming.web.vo.client.OBProfileVO'];
        prepayPayment: components['schemas']['com.moego.server.grooming.web.vo.client.PaymentDetailVO'];
        serviceAddress: components['schemas']['com.moego.server.grooming.web.vo.client.AddressVO'];
      };
      /** @description Appointment details */
      'com.moego.server.grooming.web.vo.client.ClientApptItemVO': {
        petDetail: components['schemas']['com.moego.server.grooming.web.vo.client.PetDetailVO'];
        serviceDetail: components['schemas']['com.moego.server.grooming.web.vo.client.ServiceDetailVO'];
        staffDetail: components['schemas']['com.moego.server.grooming.web.vo.client.StaffDetailVO'];
      };
      'com.moego.server.grooming.web.vo.client.ClientApptListVO': {
        apptPage: components['schemas']['com.moego.common.dto.PageDTOCom.moego.server.grooming.web.vo.client.ClientApptVO'];
        /** @description Business info */
        businessInfoList: components['schemas']['com.moego.server.grooming.web.vo.client.BusinessInfoVO'][];
        /** @description Online booking config */
        obConfigList: components['schemas']['com.moego.server.grooming.web.vo.client.OBConfigVO'][];
        /** @description Online booking profile */
        obProfileList: components['schemas']['com.moego.server.grooming.web.vo.client.OBProfileVO'][];
      };
      'com.moego.server.grooming.web.vo.client.ClientApptVO': {
        /** @description Appointment date, yyyy-MM-dd */
        apptDate: string;
        /**
         * Format: int32
         * @description Appointment end time, minute offset of the day
         */
        apptEndTime: number;
        /**
         * Format: int32
         * @description Appointment start time, minute offset of the day
         */
        apptStartTime: number;
        autoAssign: components['schemas']['com.moego.server.grooming.dto.AutoAssignDTO'];
        /** @description Ticket unique identifier */
        bookingId: string;
        /**
         * Format: int32
         * @description Business id
         */
        businessId: number;
        noStartTime: boolean;
        /**
         * Format: int32
         * @description Source, 22168-ob, 22018-web, 17216-android, 17802-ios
         */
        source: number;
        /**
         * Format: int64
         * @description Submit time, timestamp
         */
        submitTime: string | number;
      };
      /** @description Business client info */
      'com.moego.server.grooming.web.vo.client.ClientInfoVO': {
        /** @description Is block message */
        isBlockMessage: boolean;
        /** @description Is block ob */
        isBlockOnlineBooking: boolean;
      };
      /** @description Stripe credit card info */
      'com.moego.server.grooming.web.vo.client.CreditCardDetailVO': {
        /** @description Card number */
        cardNumber: string;
        /** @description Card type */
        cardType: string;
        /** @description Expire month */
        expMonth: string;
        /** @description Expire year */
        expYear: string;
      };
      /** @description Grooming report records */
      'com.moego.server.grooming.web.vo.client.GroomingReportRecordVO': {
        /**
         * Format: int32
         * @description grooming report pet id
         */
        petId: number;
        /**
         * Format: int32
         * @description grooming report id
         */
        reportId: number;
        /** @description grooming report uuid */
        reportUuid: string;
        /** @description grooming report title */
        title: string;
      };
      /** @description Online booking config */
      'com.moego.server.grooming.web.vo.client.OBConfigVO': {
        /** Format: int32 */
        arrivalWindowAfterMin: number;
        /** Format: int32 */
        arrivalWindowBeforeMin: number;
        /** Format: int32 */
        availableTimeType: number;
        bookingRangeEndDate: string;
        /** Format: int32 */
        bookingRangeEndOffset: number;
        /** Format: int32 */
        bookingRangeEndType: number;
        /** Format: int32 */
        bookingRangeStartOffset: number;
        /** @description Book online name */
        bookOnlineName: string;
        /** Format: int32 */
        bySlotTimeslotFormat: number;
        /**
         * Format: int32
         * @description By slots time slot interval
         */
        bySlotTimeslotMins: number;
        displayStaffSelectionPage: boolean;
        /** Format: int32 */
        enableOB: number;
        /**
         * Format: int32
         * @description How far can your client book
         */
        farthestDay: number;
        isNeedSendRenewNotification: boolean;
        /** @description Smart scheduling enable */
        querySmartScheduling: boolean;
        /** Format: int32 */
        timeslotFormat: number;
        /**
         * Format: int32
         * @description By working hours time slot interval
         */
        timeslotMins: number;
        /** Format: int32 */
        useVersion: number;
      };
      /** @description Prepayment / Deposit */
      'com.moego.server.grooming.web.vo.client.OBDepositVO': {
        /** @description Deposit amount, amount - convenience fee */
        depositAmount: number;
        /** @description Fees, booking fee + convenience fee */
        fees: number;
        /** @description Paid amount */
        paidAmount: number;
        /** Format: int32 */
        prepayType: number;
        /** @description Service charge amount */
        serviceChargeAmount: number;
        /** @description Service total */
        serviceTotal: number;
        /** @description Tax */
        tax: number;
        /** @description Tips */
        tips: number;
      };
      /** @description Online booking profile */
      'com.moego.server.grooming.web.vo.client.OBProfileVO': {
        /** @description Online booking business address */
        address: string;
        addressDetails: components['schemas']['com.moego.server.grooming.web.vo.client.AddressVO'];
        /** @description Business avatar path */
        avatarPath: string;
        /**
         * Format: int32
         * @description Business id
         */
        businessId: number;
        /** @description Online booking business name */
        businessName: string;
        /** @description Business owner account email */
        email: string;
        /** @description Online booking business address */
        phoneNumber: string;
      };
      /** @description Payment info */
      'com.moego.server.grooming.web.vo.client.PaymentDetailVO': {
        creditCard: components['schemas']['com.moego.server.grooming.web.vo.client.CreditCardDetailVO'];
        detail: components['schemas']['com.moego.server.grooming.web.vo.client.OBDepositVO'];
        /** Format: int32 */
        stripePaymentMethod: number;
      };
      /** @description Pet detail */
      'com.moego.server.grooming.web.vo.client.PetDetailVO': {
        /** @description Pet avatar path */
        avatarPath: string;
        /** @description Pet breed */
        breed: string;
        /**
         * Format: int32
         * @description Pet id
         */
        petId: number;
        /** @description Pet name */
        petName: string;
        /**
         * Format: int32
         * @description Pet type
         */
        petTypeId: number;
        /** @description Pet weight */
        weight: string;
      };
      /** @description Pet detail */
      'com.moego.server.grooming.web.vo.client.ServiceDetailVO': {
        /**
         * Format: int32
         * @description Service end time, minute offset of the day
         */
        endTime: number;
        /**
         * Format: int32
         * @description Service id
         */
        serviceId: number;
        /** @description Service name */
        serviceName: string;
        /** @description Service price, keep two decimal places */
        servicePrice: number;
        /**
         * Format: int32
         * @description Service duration in minutes
         */
        serviceTime: number;
        /** Format: int32 */
        serviceType: number;
        /**
         * Format: int32
         * @description Service start time, minute offset of the day
         */
        startTime: number;
      };
      'com.moego.server.grooming.web.vo.client.StaffDetailVO': {
        /** @description Staff avatar path */
        avatarPath: string;
        /** @description Staff first name */
        firstName: string;
        /** @description Staff last name */
        lastName: string;
        /**
         * Format: int32
         * @description Staff id
         */
        staffId: number;
      };
      'com.moego.server.grooming.web.vo.client.UpdateApptVO': {
        /** @description Auto accept request flag */
        autoAcceptRequest: boolean;
        /** @description Update success flag */
        updateSuccess: boolean;
      };
      'com.moego.server.grooming.web.vo.CreateCalendarSyncSettingVo': {
        /**
         * Format: int32
         * @description 通过oauth/save 获取到的数据，需要回传
         */
        googleAuthId: number;
        /** @description 被同步的staff id 数组 */
        syncedStaffIdList: number[];
        /** Format: int32 */
        syncType: number;
      };
      'com.moego.server.grooming.web.vo.CreateNoShowInvoiceVo': {
        amount: number;
        /** Format: int32 */
        groomingId: number;
      };
      'com.moego.server.grooming.web.vo.CustomerDeleteSaveServiceVo': {
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        petId: number;
        /** Format: int32 */
        serviceId: number;
      };
      'com.moego.server.grooming.web.vo.CustomerSaveServiceVo': {
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        duration?: number;
        /** Format: int32 */
        isSaveDuration?: number;
        /** Format: int32 */
        isSavePrice?: number;
        /** Format: int32 */
        lastServiceId: number;
        /** Format: int32 */
        newServiceId: number;
        /** Format: int32 */
        petId: number;
        price?: number;
        serviceFee?: number;
        /** Format: int32 */
        serviceTime?: number;
      };
      'com.moego.server.grooming.web.vo.DeleteApplyPackageServiceVo': {
        /** Format: int64 */
        applyPackageId: string | number;
        checkRefund?: boolean;
        /** Format: int64 */
        invoiceId: string | number;
      };
      'com.moego.server.grooming.web.vo.DeleteIdVo': {
        /** Format: int32 */
        id: number;
      };
      'com.moego.server.grooming.web.vo.DeleteResult': {
        /** Format: int32 */
        affectedCount: number;
      };
      'com.moego.server.grooming.web.vo.DistanceMatrixUsageSummaryVo': {
        filename: string;
        /** Format: int32 */
        keyCount: number;
        /** Format: int32 */
        requestCount: number;
      };
      'com.moego.server.grooming.web.vo.DrivingInfoCalculationVo': {
        /** @description customer id to driving info */
        clientDrivingFromMap: {
          [key: string]:
            | components['schemas']['com.moego.server.grooming.web.vo.DrivingInfoCalculationVo$DrivingCalculationVo']
            | undefined;
        };
        /** @description customer id to driving info */
        clientDrivingToMap: {
          [key: string]:
            | components['schemas']['com.moego.server.grooming.web.vo.DrivingInfoCalculationVo$DrivingCalculationVo']
            | undefined;
        };
        /** @description van staff id to driving info */
        vanStaffDrivingFromMap: {
          [key: string]:
            | components['schemas']['com.moego.server.grooming.web.vo.DrivingInfoCalculationVo$DrivingCalculationVo']
            | undefined;
        };
        /** @description van staff id to driving info */
        vanStaffDrivingToMap: {
          [key: string]:
            | components['schemas']['com.moego.server.grooming.web.vo.DrivingInfoCalculationVo$DrivingCalculationVo']
            | undefined;
        };
      };
      /** @description van staff id to driving info */
      'com.moego.server.grooming.web.vo.DrivingInfoCalculationVo$DrivingCalculationVo': {
        /** Format: double */
        drivingMiles: number;
        /** Format: int32 */
        drivingMinutes: number;
      };
      'com.moego.server.grooming.web.vo.ExportVO': {
        url: string;
      };
      'com.moego.server.grooming.web.vo.GoogleCalendarOauthVo': {
        /** @description google 返回的code */
        code: string;
        /** @description google 返回的scope */
        scope: string;
      };
      'com.moego.server.grooming.web.vo.GroomingCustomerQueryVO': {
        appointmentDate: string;
        /** @deprecated */
        appointmentStatus: number[];
        /** @description 预约状态，1 - unconfirmed，2 - confirmed，3 - finished，4 - cancelled，5 - ready，6 - checkin */
        appointmentStatusList: number[];
        appointmentTime: string;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        endTime: number;
        /** Format: int32 */
        orderBy: number;
        /** Format: int32 */
        orderType: number;
        /** Format: int32 */
        pageNum: number;
        /** Format: int32 */
        pageSize: number;
        paymentStatus: number[];
        /** Format: int32 */
        searchBusinessId: number;
        serviceItems: number[];
        skipCancel: boolean;
        /** Format: int32 */
        time: number;
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.grooming.web.vo.InvoiceIdVo': {
        checkRefund?: boolean;
        /** Format: int32 */
        invoiceId: number;
      };
      'com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO': {
        /** @description abandon status, abandoned, contacted, recovered */
        abandonStatus: string;
        /** @enum {string} */
        abandonStep: ComMoegoServerGroomingWebVoObAbandonClientRecordVOAbandonStep;
        /** Format: int32 */
        abandonTime: number;
        additionalNote: string;
        address: components['schemas']['com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO$AddressDetailVO'];
        appointmentDate: string;
        appointmentEndDate: string;
        /** Format: int32 */
        appointmentEndTime: number;
        /** Format: int32 */
        appointmentStartTime: number;
        bookingFlowId: string;
        /** @enum {string} */
        careType: ComMoegoServerGroomingWebVoObAbandonClientRecordVOCareType;
        customer: components['schemas']['com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO$ClientDetailVO'];
        /** @description has customer profile request update */
        hasRequestUpdate: boolean;
        /**
         * Format: int64
         * @description The last emailed time of this abandon record
         */
        lastEmailedTime: string | number;
        /**
         * Format: int64
         * @description The last texted time of this abandon record
         */
        lastTextedTime: string | number;
        petDetails: components['schemas']['com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO$PetDetail'][];
        pets: components['schemas']['com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO$AbandonPetDetailVO'][];
        preference: components['schemas']['com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO$ClientPreferenceVO'];
        services: components['schemas']['com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO$AbandonServiceDetailVO'][];
        specificDates: string[];
        staff: components['schemas']['com.moego.server.grooming.web.vo.client.StaffDetailVO'];
      };
      'com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO$AbandonPetDetailVO': {
        addOnIds: number[];
        avatarPath: string;
        behavior: string;
        birthday: string;
        breed: string;
        emergencyContactName: string;
        emergencyContactPhone: string;
        fixed: string;
        /** Format: int32 */
        gender: number;
        hairLength: string;
        healthIssues: string;
        /** Format: int32 */
        petId: number;
        petName: string;
        /** Format: int32 */
        petTypeId: number;
        questionAnswerList: components['schemas']['com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO$QuestionAnswerVO'][];
        /** Format: int32 */
        serviceId: number;
        vaccineList: components['schemas']['com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO$AbandonPetDetailVO$VaccineDetailVO'][];
        vetAddress: string;
        vetName: string;
        vetPhoneNumber: string;
        weight: string;
      };
      'com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO$AbandonPetDetailVO$VaccineDetailVO': {
        documentUrls: string[];
        expirationDate: string;
        vaccineDocument: string;
        /** Format: int32 */
        vaccineId: number;
      };
      'com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO$AbandonServiceDetailVO': {
        /** Format: int32 */
        duration: number;
        price: number;
        /** Format: int32 */
        serviceId: number;
        serviceName: string;
        /** Format: int32 */
        serviceType: number;
      };
      'com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO$AddressDetailVO': {
        address1: string;
        address2: string;
        /** Format: int32 */
        addressId: number;
        city: string;
        country: string;
        lat: string;
        lng: string;
        state: string;
        zipcode: string;
      };
      'com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO$ClientDetailVO': {
        /** Format: int32 */
        customerId: number;
        email: string;
        firstName: string;
        lastName: string;
        phoneNumber: string;
        questionAnswerList: components['schemas']['com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO$QuestionAnswerVO'][];
        referer: string;
      };
      'com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO$ClientPreferenceVO': {
        preferredDay: number[];
        /** Format: int32 */
        preferredFrequencyDay: number;
        /** Format: int32 */
        preferredFrequencyType: number;
        /** Format: int32 */
        preferredGroomerId: number;
        preferredTime: number[];
        /** Format: int32 */
        referralSourceId: number;
      };
      'com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO$PetDetail': {
        /** Format: int32 */
        duration: number;
        /** Format: int32 */
        durationOverrideType: number;
        /** Format: int32 */
        petId: number;
        price: number;
        /** Format: int32 */
        priceOverrideType: number;
        /** Format: int32 */
        serviceId: number;
        serviceName: string;
        /** Format: int32 */
        serviceType: number;
        /** Format: int32 */
        staffId: number;
      };
      'com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO$QuestionAnswerVO': {
        answer: string;
        key: string;
        question: string;
      };
      'com.moego.server.grooming.web.vo.ob.AbandonedClientFilterVO': {
        filter: string;
        options: components['schemas']['com.moego.server.grooming.web.vo.ob.AbandonedClientFilterVO$Option'][];
      };
      'com.moego.server.grooming.web.vo.ob.AbandonedClientFilterVO$Option': {
        /** Format: int32 */
        count: number;
        value: string;
      };
      'com.moego.server.grooming.web.vo.ob.AbandonedClientVO': {
        abandonStatus: string;
        abandonStep: string;
        /** Format: int64 */
        abandonTime: string | number;
        appointment: components['schemas']['com.moego.server.grooming.web.vo.ob.AbandonedClientVO$Appointment'];
        avatarPath: string;
        bookingFlowId: string;
        careType: string;
        /** Format: int32 */
        customerId: number;
        firstName: string;
        hasRequestUpdate: boolean;
        /** Format: int32 */
        id: number;
        /** Format: int64 */
        lastContactTime: string | number;
        lastName: string;
        pets: components['schemas']['com.moego.server.grooming.web.vo.ob.AbandonedClientVO$Pet'][];
        phoneNumber: string;
      };
      'com.moego.server.grooming.web.vo.ob.AbandonedClientVO$Appointment': {
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        status: number;
      };
      'com.moego.server.grooming.web.vo.ob.AbandonedClientVO$Pet': {
        breed: string;
        /** Format: int32 */
        petId: number;
        petName: string;
        /** Format: int32 */
        petTypeId: number;
      };
      'com.moego.server.grooming.web.vo.ob.BulkAddClientVO': {
        /** @description 从 filter 中反选的 abandoned record 映射出的 customer id */
        excludeCustomerIds: number[];
        /**
         * Format: int32
         * @description Number of failed add clients
         */
        fail: number;
        /** @description failed add client abandoned record ids */
        failIds: string[];
        /** @description 从 filter 中正选的 abandoned record 映射出的 customer id */
        includeCustomerIds: number[];
        /**
         * Format: int32
         * @description Number of successful add clients
         */
        success: number;
        /**
         * Format: int32
         * @description Total number of clients
         */
        total: number;
      };
      'com.moego.server.grooming.web.vo.ob.BulkDeleteAbandonedVO': {
        /**
         * Format: int32
         * @description Number of successful delete abandoned records
         */
        success: number;
      };
      /** @description Landing page components */
      'com.moego.server.grooming.web.vo.ob.component.BaseComponentVO': {
        /** @description Component name */
        component: string;
      };
      'com.moego.server.grooming.web.vo.ob.OBBusinessLocationVO': {
        /** @description book online profile address */
        address: string;
        addressDetails: components['schemas']['com.moego.server.grooming.web.vo.client.AddressVO'];
        /** @description book online profile avatar path */
        avatarPath: string;
        /** @description business book online name */
        bookOnlineName: string;
        /** @description book online profile business name */
        businessName: string;
        /** @description default true */
        isAvailable: boolean;
        /** Format: int32 */
        isEnable: number;
        /** @description book online profile business name */
        phoneNumber: string;
        /** @description Biz customized URL domain name */
        urlDomainName: string;
      };
      'com.moego.server.grooming.web.vo.ob.OBBusinessStaffVO': {
        /** @description Account avatar path */
        avatarPath: string;
        /** @description first available date */
        firstAvailableDate: string;
        /** @description Settings / Staff / First name */
        firstName: string;
        /**
         * Format: int32
         * @description staff id
         */
        id: number;
        /** @description Settings / Staff / Last name */
        lastName: string;
        /** @description pet applicable service DTO list */
        petApplicableDTOList: components['schemas']['com.moego.server.grooming.dto.ob.PetApplicableDTO'][];
      };
      'com.moego.server.grooming.web.vo.ob.OBClientDetailVO': {
        avatarPath: string;
        /** Format: int32 */
        businessId: number;
        clientColor: string;
        /** Format: int32 */
        customerId: number;
        email: string;
        firstName: string;
        lastName: string;
        newAddresses: components['schemas']['com.moego.server.customer.dto.CustomerAddressDto'][];
        phoneNumber: string;
        preferredDay: number[];
        /** Format: int32 */
        preferredFrequencyDay: number;
        /** Format: int32 */
        preferredFrequencyType: number;
        /** Format: int32 */
        preferredGroomerId: number;
        preferredTime: number[];
        primaryAddress: components['schemas']['com.moego.server.customer.dto.CustomerAddressDto'];
        questionAnswerList: components['schemas']['com.moego.server.grooming.web.vo.ob.OBRequestDetailVO$QuestionAnswerVO'][];
        /** Format: int32 */
        referralSourceId: number;
      };
      'com.moego.server.grooming.web.vo.ob.OBClientPetsDetailVO': {
        customer: components['schemas']['com.moego.server.grooming.web.vo.ob.OBClientDetailVO'];
        pets: components['schemas']['com.moego.server.grooming.web.vo.ob.OBPetDetailVO'][];
        requestCustomer: components['schemas']['com.moego.server.grooming.web.vo.ob.OBClientDetailVO'];
        requestPets: components['schemas']['com.moego.server.grooming.web.vo.ob.OBPetDetailVO'][];
      };
      'com.moego.server.grooming.web.vo.ob.OBLandingPageConfigClientVO': {
        /** @description GA4 MEASUREMENT ID */
        gaMeasurementId: string;
        /** @description Landing page components */
        pageComponents: components['schemas']['com.moego.server.grooming.web.vo.ob.component.BaseComponentVO'][];
        /** @description thank you page url */
        thankYouPageUrl: string;
        /** @description Theme color, Inherited from moe_book_online_profile.button_color */
        themeColor: string;
      };
      'com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO': {
        /** @description About us, Inherited from moe_book_online_profile.description */
        aboutUs: string;
        amenities: components['schemas']['com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO$LandingPageAmenitiesVO'];
        clientReviews: components['schemas']['com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO$LandingPageConfigClientReviewVO'];
        /** @description Landing page gallery */
        galleryList: components['schemas']['com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO$OBLandingPageGalleryVO'][];
        /** @description GA4 PROPERTY ID */
        gaMeasurementId: string;
        /** @description Publish status */
        isPublished: boolean;
        /** @description Page components */
        pageComponents: {
          [key: string]: boolean | undefined;
        };
        /** @description Showcase after image */
        showcaseAfterImage: string;
        /** @description Showcase before image */
        showcaseBeforeImage: string;
        supportMembership: boolean;
        teams: components['schemas']['com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO$LandingPageConfigTeamVO'][];
        /** @description thank you page url */
        thankYouPageUrl: string;
        /** @description Theme color, Inherited from moe_book_online_profile.button_color */
        themeColor: string;
        /** @description Customized URL domain name */
        urlDomainName: string;
        /** @description Welcome page message, Inherited from moe_business_book_online.description */
        welcomePageMessage: string;
      };
      /** @description Amenity, 8 features and 4 payment options */
      'com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO$LandingPageAmenitiesVO': {
        /** @description Features */
        features: {
          [key: string]: boolean | undefined;
        };
        /** @description Payment */
        payment: {
          [key: string]: boolean | undefined;
        };
      };
      'com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO$LandingPageConfigClientReviewRecordVO': {
        /** Format: int32 */
        customerId: number;
        customerName: string;
        /** Format: int32 */
        petId: number;
        /** Format: int32 */
        petTypeId: number;
        reviewContent: string;
        /** Format: int32 */
        reviewId: number;
        /** Format: date-time */
        reviewTime: string;
        showcasePhotoUrl: string;
      };
      'com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO$LandingPageConfigClientReviewVO': {
        isDisplayClientReviewShowcasePhoto: boolean;
        reviewRecords: components['schemas']['com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO$LandingPageConfigClientReviewRecordVO'][];
      };
      'com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO$LandingPageConfigTeamVO': {
        instagramLink: string;
        introduction: string;
        isEnabled: boolean;
        /** Format: int32 */
        staffId: number;
        staffName: string;
        tags: string[];
      };
      /** @description Landing page gallery */
      'com.moego.server.grooming.web.vo.ob.OBLandingPageConfigVO$OBLandingPageGalleryVO': {
        /** @description Gallery image path */
        imagePath: string;
        /**
         * Format: int32
         * @description Gallery sort number
         */
        sort: number;
      };
      'com.moego.server.grooming.web.vo.ob.OBLandingPageMergeVO': {
        bizProfile: components['schemas']['com.moego.server.grooming.web.vo.ob.OBLandingPageMergeVO$BizProfileMergeVO'];
        bizWorkingHours: components['schemas']['com.moego.server.business.dto.BusinessWorkingHourDayDetailDTO'];
        obProfile: components['schemas']['com.moego.server.grooming.web.vo.ob.OBLandingPageMergeVO$OBProfileMergeVO'];
        obWorkingHours: components['schemas']['com.moego.server.business.dto.BusinessWorkingHourDayDetailDTO'];
      };
      /** @description Business settings profile */
      'com.moego.server.grooming.web.vo.ob.OBLandingPageMergeVO$BizProfileMergeVO': {
        /** @description Full address */
        address: string;
        /** @description Address 1 */
        address1: string;
        /** @description Address 2 */
        address2: string;
        /** @description Address city */
        addressCity: string;
        /** @description Address country */
        addressCountry: string;
        /** @description Address latitude */
        addressLat: string;
        /** @description Address longitude */
        addressLng: string;
        /** @description Address state */
        addressState: string;
        /** @description Address zipcode */
        addressZipcode: string;
        /** @description Business info logo path */
        avatarPath: string;
        /** @description Business info name */
        businessName: string;
        /** @description Facebook */
        facebook: string;
        /** @description Google */
        google: string;
        /** @description Instagram */
        instagram: string;
        /** @description Business info phone number */
        phoneNumber: string;
        /** @description Website */
        website: string;
        /** @description Yelp */
        yelp: string;
      };
      /** @description OB business profile */
      'com.moego.server.grooming.web.vo.ob.OBLandingPageMergeVO$OBProfileMergeVO': {
        /** @description Business address */
        address: string;
        /** @description Business profile image path */
        avatarPath: string;
        /** @description Business info name */
        businessName: string;
        /** @description Facebook */
        facebook: string;
        /** @description Google */
        google: string;
        /** @description Instagram */
        instagram: string;
        /** @description Business phone */
        phoneNumber: string;
        /** @description Website */
        website: string;
        /** @description Yelp */
        yelp: string;
      };
      'com.moego.server.grooming.web.vo.ob.OBLandingPageServiceCategoryVO': {
        /**
         * Format: int32
         * @description Category ID
         */
        categoryId: number;
        /** @description Category name */
        categoryName: string;
        /** Format: int32 */
        categoryType: number;
        /** @description Service list */
        serviceList: components['schemas']['com.moego.server.grooming.web.vo.ob.OBLandingPageServiceCategoryVO$OBLandingPageServiceVO'][];
        /**
         * Format: int32
         * @description Category sort number
         */
        sort: number;
      };
      /** @description Service list */
      'com.moego.server.grooming.web.vo.ob.OBLandingPageServiceCategoryVO$OBLandingPageServiceVO': {
        /** @description Bundle service IDs */
        bundleServiceIds: (string | number)[];
        /** @description Color code, #ffffff */
        colorCode: string;
        /** @description Service description */
        description: string;
        /**
         * Format: int32
         * @description Service duration, minute
         */
        duration: number;
        /** @description Service price, business currency */
        price: number;
        /**
         * Format: int32
         * @description Service ID
         */
        serviceId: number;
        /** @description Service name */
        serviceName: string;
        /** Format: int32 */
        serviceType: number;
        /** Format: int32 */
        showBasePrice: number;
        /**
         * Format: int32
         * @description Sort number
         */
        sort: number;
      };
      'com.moego.server.grooming.web.vo.ob.OBMetricVO': {
        /** @description Metrics, key: metrics type, value: metrics value */
        metrics: {
          [key: string]: any;
        };
        /**
         * @description Metrics name
         * @enum {string}
         */
        name: ComMoegoServerGroomingWebVoObOBMetricVOName;
      };
      'com.moego.server.grooming.web.vo.ob.OBPetDetailVO': {
        addOnIds: number[];
        avatarPath: string;
        behavior: string;
        birthday: string;
        breed: string;
        emergencyContactName: string;
        emergencyContactPhone: string;
        fixed: string;
        /** Format: int32 */
        gender: number;
        hairLength: string;
        healthIssues: string;
        /** Format: int32 */
        petId: number;
        petName: string;
        /** Format: int32 */
        petTypeId: number;
        questionAnswerList: components['schemas']['com.moego.server.grooming.web.vo.ob.OBRequestDetailVO$QuestionAnswerVO'][];
        /** Format: int32 */
        serviceId: number;
        vaccineList: components['schemas']['com.moego.server.grooming.web.vo.ob.OBPetDetailVO$OBVaccineDetailVO'][];
        vetAddress: string;
        vetName: string;
        vetPhoneNumber: string;
        weight: string;
      };
      'com.moego.server.grooming.web.vo.ob.OBPetDetailVO$OBVaccineDetailVO': {
        documentUrls: string[];
        expirationDate: string;
        /** Format: int32 */
        vaccineBindingId: number;
        /** Format: int32 */
        vaccineId: number;
        vaccineName: string;
      };
      'com.moego.server.grooming.web.vo.ob.OBRequestDetailVO': {
        additionalNote: string;
        address: components['schemas']['com.moego.server.grooming.web.vo.ob.OBRequestDetailVO$AddressDetailVO'];
        appointmentDate: string;
        /** Format: int32 */
        appointmentStartTime: number;
        /**
         * Format: int32
         * @description appointment id
         */
        apptId: number;
        autoAssign: components['schemas']['com.moego.server.grooming.dto.AutoAssignDTO'];
        /** Format: int32 */
        bookOnlineStatus: number;
        /** Format: int64 */
        createTime: string | number;
        customer: components['schemas']['com.moego.server.grooming.web.vo.ob.OBClientDetailVO'];
        emergencyContact: components['schemas']['com.moego.server.customer.params.EmergencyContact'];
        discountCode: components['schemas']['com.moego.server.grooming.dto.DiscountCodeDTO'];
        /** @description has customer profile request update */
        hasRequestUpdate: boolean;
        isAutoAccept: boolean;
        /** Format: int32 */
        isPaid: number;
        /** Format: int32 */
        isWaitingList: number;
        noStartTime: boolean;
        pets: components['schemas']['com.moego.server.grooming.web.vo.ob.OBPetDetailVO'][];
        prepay: components['schemas']['com.moego.server.grooming.web.vo.ob.OBRequestDetailVO$OBPrepayDetailVO'];
        services: components['schemas']['com.moego.server.grooming.web.vo.ob.OBRequestDetailVO$OBServiceDetailVO'][];
        sourcePlatform: string;
        staff: components['schemas']['com.moego.server.grooming.web.vo.client.StaffDetailVO'];
        /** Format: int32 */
        status: number;
      };
      'com.moego.server.grooming.web.vo.ob.OBRequestDetailVO$AddressDetailVO': {
        address1: string;
        address2: string;
        /** Format: int32 */
        addressId: number;
        city: string;
        country: string;
        lat: string;
        lng: string;
        state: string;
        zipcode: string;
      };
      'com.moego.server.grooming.web.vo.ob.OBRequestDetailVO$OBPrepayDetailVO': {
        paidAmount: number;
        prepaidAmount: number;
        /** Format: double */
        prepayRate: number;
        /** Format: int32 */
        prepayStatus: number;
        refundAmount: number;
      };
      'com.moego.server.grooming.web.vo.ob.OBRequestDetailVO$OBServiceDetailVO': {
        /** Format: int32 */
        duration: number;
        /** Format: int32 */
        durationOverrideType: number;
        price: number;
        /** Format: int32 */
        priceOverrideType: number;
        /** Format: int32 */
        serviceId: number;
        serviceName: string;
        /** Format: int32 */
        serviceType: number;
      };
      'com.moego.server.grooming.web.vo.ob.OBRequestDetailVO$QuestionAnswerVO': {
        answer: string;
        key: string;
        question: string;
      };
      'com.moego.server.grooming.web.vo.PetServicePageVO': {
        categoryList: components['schemas']['com.moego.server.grooming.service.dto.ServiceGroupByCategoryDto'][];
        pagination: components['schemas']['com.moego.common.utils.Pagination'];
      };
      'com.moego.server.grooming.web.vo.QBCreateConnectVo': {
        authCode: string;
        realmId: string;
        /**
         * @description v1: 单个 business id， v2：逗号隔开的business id 列表
         * @example 10001, 10002
         */
        state: string;
        /**
         * Format: int32
         * @description 旧版本是 0， 新版本是 1
         */
        userVersion?: number;
      };
      /** @description 设置更新实体类 */
      'com.moego.server.grooming.web.vo.QBSettingUpdateVo': {
        /** @description 账本id */
        accountId: string;
        /** @description 账本名 */
        accountName: string;
        /**
         * Format: int32
         * @description 指定更新的 business
         * @example 100001
         */
        businessId: number;
        /** Format: int32 */
        enableSync: number;
        /** @description 预约开始同步日期(默认创建时间) 年-月-日 */
        syncBeginDate: string;
      };
      'com.moego.server.grooming.web.vo.QbSetUpVo': {
        /**
         * Format: int32
         * @description 连接id
         */
        connectId: number;
        /** @description Activate时忽略的商家id列表 */
        dismissedBusinessIds?: number[];
        /** @description 预约开始同步日期(默认创建时间) 年-月-日 */
        syncBeginDate?: string;
      };
      'com.moego.server.grooming.web.vo.QuestionnaireVO': {
        formDetail: string;
      };
      'com.moego.server.grooming.web.vo.ServiceAreaPicCacheVO': {
        /** Format: int32 */
        businessId: number;
        /** Format: date-time */
        createdAt: string;
        factorsHash: string;
        /** Format: int32 */
        id: number;
        /** Format: date-time */
        updatedAt: string;
        url: string;
      };
      'com.moego.server.grooming.web.vo.SortIdListVo': {
        idList: number[];
      };
      'com.moego.server.grooming.web.vo.SubmitBookingRequestResult': {
        autoAcceptRequest: boolean;
        /** Format: int32 */
        customerId: number;
        /** Format: int64 */
        orderId: string | number;
      };
      'com.moego.server.grooming.web.vo.SummaryVo': {
        businessSummary: components['schemas']['com.moego.server.grooming.web.vo.SummaryVo$BusinessSummary'];
        staffSummary: components['schemas']['com.moego.server.grooming.web.vo.SummaryVo$StaffSummary'];
      };
      'com.moego.server.grooming.web.vo.SummaryVo$BusinessSummary': {
        moeGoPaySummary: components['schemas']['com.moego.server.grooming.web.vo.SummaryVo$BusinessSummary$MoegoPaySummary'];
        onlineBookingSummary: components['schemas']['com.moego.server.grooming.web.vo.SummaryVo$BusinessSummary$OnlineBookingSummary'];
        staffSummaries: components['schemas']['com.moego.server.grooming.web.vo.SummaryVo$StaffSummary'];
      };
      'com.moego.server.grooming.web.vo.SummaryVo$BusinessSummary$MoegoPaySummary': {
        /** Format: int32 */
        count: number;
        enabled: boolean;
        /** Format: double */
        totalAmount: number;
      };
      'com.moego.server.grooming.web.vo.SummaryVo$BusinessSummary$OnlineBookingSummary': {
        /** Format: int32 */
        count: number;
        enabled: boolean;
      };
      'com.moego.server.grooming.web.vo.SummaryVo$StaffSummary': {
        longestWorkingDay: components['schemas']['com.moego.server.grooming.web.vo.SummaryVo$StaffSummary$LongestWorkingDay'];
        mostPetBreed: components['schemas']['com.moego.server.grooming.web.vo.SummaryVo$StaffSummary$MostPetBreed'];
        service: components['schemas']['com.moego.server.grooming.web.vo.SummaryVo$StaffSummary$Service'];
        takeCaredPet: components['schemas']['com.moego.server.grooming.web.vo.SummaryVo$StaffSummary$TakeCaredPet'];
      };
      'com.moego.server.grooming.web.vo.SummaryVo$StaffSummary$LongestWorkingDay': {
        date: string;
        /** Format: int32 */
        hours: number;
      };
      'com.moego.server.grooming.web.vo.SummaryVo$StaffSummary$MostPetBreed': {
        breed: string;
        /** Format: int32 */
        count: number;
      };
      'com.moego.server.grooming.web.vo.SummaryVo$StaffSummary$Service': {
        /** Format: int32 */
        totalServiceHours: number;
      };
      'com.moego.server.grooming.web.vo.SummaryVo$StaffSummary$TakeCaredPet': {
        /** Format: int32 */
        count: number;
      };
      'com.moego.server.grooming.web.vo.UpcomingPreviewVo': {
        /** Format: int32 */
        customerId: number;
        /** @description 当share_range_type为3时，记录的所有apptIds，仅shareRangeType为3时生效 */
        shareApptIds?: number[];
        /** Format: int32 */
        shareApptStatus: number;
        /** Format: int32 */
        shareRangeType: number;
        /**
         * Format: int32
         * @description 不同type时的value
         */
        shareRangeValue?: number;
      };
      'com.moego.server.grooming.web.vo.UpdateCalendarSyncSettingVo': {
        /** @description 日历名字模板 */
        calendarName: string;
        /** @description 是否关闭链接 */
        disconnect: boolean;
        /** @description 日历事件描述模板 */
        eventDescription: string;
        /** @description 日历事件标题模板 */
        eventTitle: string;
        /** @description 被同步的staff id 数组 */
        syncedStaffIdList: number[];
      };
      'com.moego.server.grooming.web.vo.UpdateStatusResultVO': {
        notificationSent: boolean;
        reasonForNotificationFailed: string;
        statusUpdated: boolean;
      };
      'com.moego.server.grooming.web.vo.waitlist.AvailableInfoVO': {
        /** Format: int32 */
        availableCnt: number;
        date: string;
        /** Format: int32 */
        endTime: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        startTime: number;
      };
      'com.moego.server.grooming.web.vo.waitlist.CalendarViewVO': {
        date: string;
        /** Format: int32 */
        staffId: number;
        timeSlot: components['schemas']['com.moego.server.grooming.dto.ss.ScheduleTimeSlot'];
        waitList: components['schemas']['com.moego.server.grooming.web.vo.waitlist.WaitListVO'];
      };
      'com.moego.server.grooming.web.vo.waitlist.CustomerVO': {
        avatarPath: string;
        clientColor: string;
        firstName: string;
        /** Format: int32 */
        id: number;
        isDeleted: boolean;
        lastName: string;
        phoneNumber: string;
      };
      'com.moego.server.grooming.web.vo.waitlist.PetServiceVO': {
        breed: string;
        /** Format: int32 */
        petId: number;
        petName: string;
        /** Format: int32 */
        petTypeId: number;
        serviceList: components['schemas']['com.moego.server.grooming.web.vo.waitlist.ServiceAndOperationVO'][];
      };
      'com.moego.server.grooming.web.vo.waitlist.ServiceAndOperationVO': {
        /** Format: int32 */
        serviceId: number;
        serviceName: string;
        servicePrice: number;
      };
      'com.moego.server.grooming.web.vo.waitlist.SmartScheduleListVO': {
        page: components['schemas']['com.moego.common.utils.Pagination'];
        smartSchedules: components['schemas']['com.moego.server.grooming.web.vo.waitlist.WaitListSmartScheduleVO'][];
      };
      'com.moego.server.grooming.web.vo.waitlist.WaitListDetailVO': {
        allPetsStartAtSameTime: boolean;
        /** Format: int64 */
        appointmentId: string | number;
        certainAreaList: components['schemas']['com.moego.server.business.dto.CertainAreaDTO'][];
        createBy: components['schemas']['com.moego.server.grooming.dto.waitlist.Staff'];
        /** Format: date-time */
        createTime: string;
        customerInfo: components['schemas']['com.moego.server.grooming.web.vo.waitlist.CustomerVO'];
        datePreference: components['schemas']['com.moego.server.grooming.dto.waitlist.DatePreferenceDTO'];
        /** Format: int32 */
        duration: number;
        /** Format: int64 */
        id: string | number;
        /** Format: int32 */
        isPaid: number;
        paidAmount: number;
        petList: components['schemas']['com.moego.server.grooming.dto.GroomingPetInfoDetailDTO'][];
        prepay: components['schemas']['com.moego.server.grooming.web.vo.ob.OBRequestDetailVO$OBPrepayDetailVO'];
        refundAmount: number;
        staffPreference: components['schemas']['com.moego.server.grooming.dto.waitlist.StaffPreferenceDTO'];
        ticketComment: string;
        timePreference: components['schemas']['com.moego.server.grooming.dto.waitlist.TimePreferenceDTO'];
        /** Format: date */
        validTill: string;
      };
      'com.moego.server.grooming.web.vo.waitlist.WaitListListVO': {
        list: components['schemas']['com.moego.server.grooming.web.vo.waitlist.WaitListVO'][];
        page: components['schemas']['com.moego.common.utils.Pagination'];
      };
      'com.moego.server.grooming.web.vo.waitlist.WaitListSmartScheduleVO': {
        date: string;
        /** Format: int32 */
        staffId: number;
        timeSlot: components['schemas']['com.moego.server.grooming.dto.ss.ScheduleTimeSlot'];
      };
      'com.moego.server.grooming.web.vo.waitlist.WaitListVO': {
        /** Format: int64 */
        appointmentId: string | number;
        certainAreaList: components['schemas']['com.moego.server.business.dto.CertainAreaDTO'][];
        /** Format: date-time */
        createTime: string;
        customerInfo: components['schemas']['com.moego.server.grooming.web.vo.waitlist.CustomerVO'];
        datePreference: components['schemas']['com.moego.server.grooming.dto.waitlist.DatePreferenceDTO'];
        /** Format: int32 */
        duration: number;
        /** Format: int64 */
        id: string | number;
        isAvailable: boolean;
        petList: components['schemas']['com.moego.server.grooming.web.vo.waitlist.PetServiceVO'][];
        prepay: components['schemas']['com.moego.server.grooming.web.vo.ob.OBRequestDetailVO$OBPrepayDetailVO'];
        price: number;
        staffPreference: components['schemas']['com.moego.server.grooming.dto.waitlist.StaffPreferenceDTO'];
        ticketComment: string;
        timePreference: components['schemas']['com.moego.server.grooming.dto.waitlist.TimePreferenceDTO'];
        /** Format: date */
        validFrom: string;
        /** Format: date */
        validTill: string;
      };
      'com.moego.server.grooming.web.vo.WebsiteSummaryVO': {
        /** Format: double */
        appointmentCount: number;
        /** Format: double */
        customerCount: number;
        /** Format: double */
        petCount: number;
      };
      /** @description ArrivalWindowSettingDto */
      'com.moego.server.message.dto.ArrivalWindowSettingDto': {
        /**
         * Format: int32
         * @description arrival after 分钟数
         */
        arrivalAfter: number;
        /**
         * Format: int32
         * @description arrival before 分钟数
         */
        arrivalBefore: number;
        /** Format: int32 */
        businessId?: number;
        /** Format: int32 */
        id?: number;
        /**
         * Format: int32
         * @description 是否打开 1打开 0关闭
         */
        status: number;
      };
      /** @description Business twilio number */
      'com.moego.server.message.dto.BusinessTwilioNumberDTO': {
        /** Format: int32 */
        businessId: number;
        twilioNumber: string;
      };
      'com.moego.server.payment.dto.CanRefundChannel': {
        canRefundAmount: number;
        /** Format: int32 */
        paymentId: number;
        paymentMethod?: string;
      };
      'com.moego.server.payment.dto.GetSquareTokenResponse': {
        /** @description moego platform integrated application's id */
        appId: string;
        /** Format: int32 */
        businessId: number;
        defaultLocationId: string;
        expiresAt: string;
        merchantId: string;
        /** @description 是否有在square白名单内 */
        squareAccess: boolean;
        /** @description 是否与square connected */
        squareConnected: boolean;
      };
      /** @description payment setting */
      'com.moego.server.payment.dto.PaymentSettingForClientDTO': {
        /** Format: int32 */
        cardAuthEnable: number;
        /** @description 自定义费名 */
        customizedFeeName: string;
        /** @description 定制json报文 */
        customTipping: string;
        /** Format: int32 */
        preAuthBspd: number;
        /** Format: int32 */
        processingFeePayBy: number;
        /** Format: int32 */
        skipTipping: number;
      };
      'com.moego.server.payment.dto.PreAuthDTO': {
        cardFunding: string;
        cardNumber: string;
        cardType: string;
        /** Format: int32 */
        customerId: number;
        expMonth: string;
        expYear: string;
        feesDetail: components['schemas']['com.moego.server.payment.dto.PreAuthFeesDTO'];
        inBSPD: boolean;
        isCapture: boolean;
        method: string;
        /** Format: int32 */
        paymentId: number;
        preAuthAmount: number;
        preAuthCardNumber: string;
        preAuthFailedMessage: string;
        /** Format: int64 */
        preAuthFinishTime: string | number;
        /** Format: int32 */
        preAuthId: number;
        preAuthPaymentMethod: string;
        /** Format: int32 */
        preAuthStatus: number;
        /** Format: int64 */
        preAuthTime: string | number;
        /** Format: int32 */
        ticketId: number;
      };
      'com.moego.server.payment.dto.PreAuthFeesDTO': {
        bookingFee: number;
        convenienceFee: number;
        tipsAmount: number;
      };
      'com.moego.server.payment.dto.RefundChannelDTO': {
        channelList: components['schemas']['com.moego.server.payment.dto.CanRefundChannel'][];
        /** Format: int32 */
        invoiceId: number;
        isCombination: boolean;
        refundAmount: number;
      };
      /** @description business tip config */
      'com.moego.server.payment.dto.SmartTipConfigForClientDTO': {
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        enable: number;
        /** Format: int32 */
        lowerPreferredTip: number;
        lowerTip: components['schemas']['com.moego.server.payment.params.TipMap'];
        /** Format: int32 */
        lowerTipType: number;
        /** @description Smart tip区分低档和高档的阈值，大于0，最多2位小数 */
        threshold: number;
        /** Format: int32 */
        upperPreferredTip: number;
        upperTip: components['schemas']['com.moego.server.payment.params.TipMap'];
        /** Format: int32 */
        upperTipType: number;
      };
      /** @description Smart tip关闭时全局tip配置/Smart tip打开时高档位tip配置 */
      'com.moego.server.payment.params.TipMap': {
        amountConfig: components['schemas']['com.moego.server.payment.params.TipMap$AmountTipMap'];
        percentageConfig: components['schemas']['com.moego.server.payment.params.TipMap$PercentageTipMap'];
      };
      /** @description by amount 3个tip配置 */
      'com.moego.server.payment.params.TipMap$AmountTipMap': {
        high: number;
        low: number;
        medium: number;
      };
      /** @description by percentage 3个tip配置 */
      'com.moego.server.payment.params.TipMap$PercentageTipMap': {
        /** Format: int32 */
        high: number;
        /** Format: int32 */
        low: number;
        /** Format: int32 */
        medium: number;
      };
      'com.moego.server.retail.dto.PackageInfoDto$Service': {
        name: string;
        /** Format: int32 */
        serviceId: number;
        unitPrice: number;
      };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
  }
}
export enum PathsGroomingReportMobileApptGetParametersQueryType {
  UNPAID = 'UNPAID',
  UNCLOSED = 'UNCLOSED',
  CANCELLED = 'CANCELLED',
  NO_SHOW = 'NO_SHOW',
  UPCOMING = 'UPCOMING',
  WAIT_LIST = 'WAIT_LIST',
  ONLINE_BOOK = 'ONLINE_BOOK',
}
export enum ComMoegoCommonParamsPageQuery$SortQueryOrder {
  asc = 'asc',
  desc = 'desc',
}
export enum ComMoegoServerGroomingDtoAppointmentHistoryAppointmentActionHistoryActionType {
  Create = 'Create',
  Update_Status = 'Update Status',
  Change_Check_In_Time = 'Change Check In Time',
  Change_Check_Out_Time = 'Change Check Out Time',
  Send_Notification = 'Send Notification',
  Reschedule = 'Reschedule',
  Customer_Reply = 'Customer Reply',
  Cancel = 'Cancel',
  Edit_Pet_And_Services = 'Edit Pet And Services',
  Update_Notification_Status = 'Update Notification Status',
  Auto_Rollover = 'Auto Rollover',
}
export enum ComMoegoServerGroomingDtoAppointmentHistoryCancelLogDTOCanceledByType {
  BY_BUSINESS = 'BY_BUSINESS',
  BY_CUSTOMER_REPLY_MESSAGE = 'BY_CUSTOMER_REPLY_MESSAGE',
  BY_DELETE_PET = 'BY_DELETE_PET',
  BY_CLIENT_PORTAL = 'BY_CLIENT_PORTAL',
  BY_PET_PARENT_APP = 'BY_PET_PARENT_APP',
  UNRECOGNIZED = 'UNRECOGNIZED',
}
export enum ComMoegoServerGroomingDtoAppointmentHistoryChangeTimeLogDTOChangedByType {
  BY_BUSINESS = 'BY_BUSINESS',
  BY_CUSTOMER_REPLY_MESSAGE = 'BY_CUSTOMER_REPLY_MESSAGE',
  BY_DELETE_PET = 'BY_DELETE_PET',
  BY_CLIENT_PORTAL = 'BY_CLIENT_PORTAL',
  BY_PET_PARENT_APP = 'BY_PET_PARENT_APP',
  UNRECOGNIZED = 'UNRECOGNIZED',
}
export enum ComMoegoServerGroomingDtoAppointmentHistoryCustomerReplyLogDTOClientReplyTypeEnum {
  Confirm_Appointment = 'Confirm Appointment',
  Cancel_Appointment = 'Cancel Appointment',
}
export enum ComMoegoServerGroomingDtoAppointmentHistoryNotificationUpdateDTONotificationTypeEnum {
  Ready_for_pickup_message = 'Ready for pickup message',
  Review_booster = 'Review booster',
  Grooming_report = 'Grooming report',
}
export enum ComMoegoServerGroomingDtoAppointmentHistorySendNotificationLogDTONotificationType {
  Ready_for_pickup_message = 'Ready for pickup message',
  Review_booster = 'Review booster',
  Grooming_report = 'Grooming report',
}
export enum ComMoegoServerGroomingDtoPrintcardPrintCardServiceInfoCareType {
  SERVICE_ITEM_TYPE_UNSPECIFIED = 'SERVICE_ITEM_TYPE_UNSPECIFIED',
  GROOMING = 'GROOMING',
  BOARDING = 'BOARDING',
  DAYCARE = 'DAYCARE',
  EVALUATION = 'EVALUATION',
  DOG_WALKING = 'DOG_WALKING',
  GROUP_CLASS = 'GROUP_CLASS',
  UNRECOGNIZED = 'UNRECOGNIZED',
}
export enum ComMoegoServerGroomingDtoPrintcardPrintCardServiceInfoServiceItem {
  GROOMING = 'GROOMING',
  BOARDING = 'BOARDING',
  DAYCARE = 'DAYCARE',
  EVALUATION = 'EVALUATION',
  DOG_WALKING = 'DOG_WALKING',
  GROUP_CLASS = 'GROUP_CLASS',
}
export enum ComMoegoServerGroomingDtoPrintcardStayCardAppointmentMainServiceItemType {
  GROOMING = 'GROOMING',
  BOARDING = 'BOARDING',
  DAYCARE = 'DAYCARE',
  EVALUATION = 'EVALUATION',
  DOG_WALKING = 'DOG_WALKING',
  GROUP_CLASS = 'GROUP_CLASS',
}
export enum ComMoegoServerGroomingDtoReportReportAppointmentResponseType {
  UNPAID = 'UNPAID',
  UNCLOSED = 'UNCLOSED',
  CANCELLED = 'CANCELLED',
  NO_SHOW = 'NO_SHOW',
  UPCOMING = 'UPCOMING',
  WAIT_LIST = 'WAIT_LIST',
  ONLINE_BOOK = 'ONLINE_BOOK',
}
export enum ComMoegoServerGroomingDtoServiceChargeDTOServiceItemTypes {
  SERVICE_ITEM_TYPE_UNSPECIFIED = 'SERVICE_ITEM_TYPE_UNSPECIFIED',
  GROOMING = 'GROOMING',
  BOARDING = 'BOARDING',
  DAYCARE = 'DAYCARE',
  EVALUATION = 'EVALUATION',
  DOG_WALKING = 'DOG_WALKING',
  GROUP_CLASS = 'GROUP_CLASS',
  UNRECOGNIZED = 'UNRECOGNIZED',
}
export enum ComMoegoServerGroomingParamsAppointmentServiceAndOperationParamsScopeTypePrice {
  UNKNOWN = 'UNKNOWN',
  THIS_FUTURE = 'THIS_FUTURE',
  DO_NOT_SAVE = 'DO_NOT_SAVE',
  THIS_FOLLOWING = 'THIS_FOLLOWING',
  ALL_UPCOMING = 'ALL_UPCOMING',
}
export enum ComMoegoServerGroomingParamsAppointmentServiceAndOperationParamsScopeTypeTime {
  UNKNOWN = 'UNKNOWN',
  THIS_FUTURE = 'THIS_FUTURE',
  DO_NOT_SAVE = 'DO_NOT_SAVE',
  THIS_FOLLOWING = 'THIS_FOLLOWING',
  ALL_UPCOMING = 'ALL_UPCOMING',
}
export enum ComMoegoServerGroomingParamsAppointmentServiceAndOperationParamsServiceItemEnum {
  GROOMING = 'GROOMING',
  BOARDING = 'BOARDING',
  DAYCARE = 'DAYCARE',
  EVALUATION = 'EVALUATION',
  DOG_WALKING = 'DOG_WALKING',
  GROUP_CLASS = 'GROUP_CLASS',
}
export enum ComMoegoServerGroomingParamsBookOnlineSubmitParamsSourceType {
  MARKETING_CAMPAIGN_EMAIL = 'MARKETING_CAMPAIGN_EMAIL',
}
export enum ComMoegoServerGroomingParamsPetDetailParamsServiceItemEnum {
  GROOMING = 'GROOMING',
  BOARDING = 'BOARDING',
  DAYCARE = 'DAYCARE',
  EVALUATION = 'EVALUATION',
  DOG_WALKING = 'DOG_WALKING',
  GROUP_CLASS = 'GROUP_CLASS',
}
export enum ComMoegoServerGroomingWebAbandonedScheduleMessageSettingController$UpdateAbandonedScheduleMessageSettingParamAbandonedSteps {
  welcome_page = 'welcome_page',
  basic_info = 'basic_info',
  select_care_type = 'select_care_type',
  select_address = 'select_address',
  select_pet = 'select_pet',
  select_date = 'select_date',
  select_service = 'select_service',
  select_groomer = 'select_groomer',
  select_time = 'select_time',
  additional_pet_info = 'additional_pet_info',
  personal_info = 'personal_info',
  card_on_file = 'card_on_file',
  prepay = 'prepay',
  pre_auth = 'pre_auth',
  submit_appt = 'submit_appt',
}
export enum ComMoegoServerGroomingWebAbandonedScheduleMessageSettingController$UpdateAbandonedScheduleMessageSettingParamClientTypes {
  NEW_VISITORS = 'NEW_VISITORS',
  EXISTING_CLIENTS = 'EXISTING_CLIENTS',
}
export enum ComMoegoServerGroomingWebAbandonedScheduleMessageSettingController$UpdateAbandonedScheduleMessageSettingParamOnTypeDays {
  MONDAY = 'MONDAY',
  TUESDAY = 'TUESDAY',
  WEDNESDAY = 'WEDNESDAY',
  THURSDAY = 'THURSDAY',
  FRIDAY = 'FRIDAY',
  SATURDAY = 'SATURDAY',
  SUNDAY = 'SUNDAY',
}
export enum ComMoegoServerGroomingWebAbandonedScheduleMessageSettingController$UpdateAbandonedScheduleMessageSettingParamSendOutType {
  ON = 'ON',
  WAIT_FOR = 'WAIT_FOR',
}
export enum ComMoegoServerGroomingWebParamsCalendarCardRescheduleParamsRepeatType {
  Value1 = 1,
  Value2 = 2,
  Value3 = 3,
}
export enum ComMoegoServerGroomingWebParamsOBAbandonParamsCareType {
  GROOMING = 'GROOMING',
  BOARDING = 'BOARDING',
  DAYCARE = 'DAYCARE',
  EVALUATION = 'EVALUATION',
  DOG_WALKING = 'DOG_WALKING',
  GROUP_CLASS = 'GROUP_CLASS',
}
export enum ComMoegoServerGroomingWebParamsOBAbandonParamsDeleteType {
  TEST_CASE = 'TEST_CASE',
  MANUAL = 'MANUAL',
  BLOCK_CLIENT = 'BLOCK_CLIENT',
  SUBMITTED = 'SUBMITTED',
  out_of_service_area = 'out_of_service_area',
  no_applicable_pet = 'no_applicable_pet',
  no_applicable_service = 'no_applicable_service',
  no_available_service = 'no_available_service',
  HISTORICAL_DATA_OVERWRITTEN = 'HISTORICAL_DATA_OVERWRITTEN',
}
export enum ComMoegoServerGroomingWebParamsOBAbandonParamsNextStep {
  welcome_page = 'welcome_page',
  basic_info = 'basic_info',
  select_care_type = 'select_care_type',
  select_address = 'select_address',
  select_pet = 'select_pet',
  select_date = 'select_date',
  select_service = 'select_service',
  select_groomer = 'select_groomer',
  select_time = 'select_time',
  additional_pet_info = 'additional_pet_info',
  personal_info = 'personal_info',
  card_on_file = 'card_on_file',
  prepay = 'prepay',
  pre_auth = 'pre_auth',
  submit_appt = 'submit_appt',
}
export enum ComMoegoServerGroomingWebParamsOBClientInfoParamsSourceType {
  MARKETING_CAMPAIGN_EMAIL = 'MARKETING_CAMPAIGN_EMAIL',
}
export enum ComMoegoServerGroomingWebParamsOBMetricsParams$OBMetricParamsName {
  unique_visitors = 'unique_visitors',
  new_visitors = 'new_visitors',
  submitted_requests = 'submitted_requests',
  expected_revenue = 'expected_revenue',
  prepaid_revenue = 'prepaid_revenue',
  started_clients = 'started_clients',
  started_records = 'started_records',
  abandoned_clients = 'abandoned_clients',
  abandoned_records = 'abandoned_records',
  abandoned_proportion = 'abandoned_proportion',
  recoverable_clients = 'recoverable_clients',
  recoverable_records = 'recoverable_records',
  recoverable_proportion = 'recoverable_proportion',
  recovered_clients = 'recovered_clients',
  recovered_records = 'recovered_records',
  recovered_proportion = 'recovered_proportion',
  recoverable_revenue = 'recoverable_revenue',
  recovered_revenue = 'recovered_revenue',
  pending_requests_revenue = 'pending_requests_revenue',
}
export enum ComMoegoServerGroomingWebParamsOBMetricsParams$OBMetricParamsTypes {
  sum = 'sum',
  ring_rate = 'ring_rate',
  proportion = 'proportion',
}
export enum ComMoegoServerGroomingWebParamsSearchAbandonedClientParam$FilterAbandonedStep {
  welcome_page = 'welcome_page',
  basic_info = 'basic_info',
  select_care_type = 'select_care_type',
  select_address = 'select_address',
  select_pet = 'select_pet',
  select_date = 'select_date',
  select_service = 'select_service',
  select_groomer = 'select_groomer',
  select_time = 'select_time',
  additional_pet_info = 'additional_pet_info',
  personal_info = 'personal_info',
  card_on_file = 'card_on_file',
  prepay = 'prepay',
  pre_auth = 'pre_auth',
  submit_appt = 'submit_appt',
}
export enum ComMoegoServerGroomingWebParamsSearchAbandonedClientParam$FilterAbandonStatus {
  abandoned = 'abandoned',
  contacted = 'contacted',
  recovered = 'recovered',
}
export enum ComMoegoServerGroomingWebParamsSearchAbandonedClientParam$FilterCareTypes {
  GROOMING = 'GROOMING',
  BOARDING = 'BOARDING',
  DAYCARE = 'DAYCARE',
  EVALUATION = 'EVALUATION',
  DOG_WALKING = 'DOG_WALKING',
  GROUP_CLASS = 'GROUP_CLASS',
}
export enum ComMoegoServerGroomingWebParamsSearchAbandonedClientParam$FilterLeadType {
  non_client = 'non_client',
  new_visitor = 'new_visitor',
  existing_client = 'existing_client',
}
export enum ComMoegoServerGroomingWebParamsSearchAbandonedClientParam$SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}
export enum ComMoegoServerGroomingWebParamsSearchAbandonedClientParam$SortProperty {
  id = 'id',
  abandonTime = 'abandonTime',
}
export enum ComMoegoServerGroomingWebParamsWaitlistSortParamsOrder {
  asc = 'asc',
  desc = 'desc',
}
export enum ComMoegoServerGroomingWebParamsWaitlistSortParamsProperty {
  ticket_price = 'ticket_price',
  create_time = 'create_time',
}
export enum ComMoegoServerGroomingWebVoAbandonedScheduleMessageSettingVOAbandonedSteps {
  welcome_page = 'welcome_page',
  basic_info = 'basic_info',
  select_care_type = 'select_care_type',
  select_address = 'select_address',
  select_pet = 'select_pet',
  select_date = 'select_date',
  select_service = 'select_service',
  select_groomer = 'select_groomer',
  select_time = 'select_time',
  additional_pet_info = 'additional_pet_info',
  personal_info = 'personal_info',
  card_on_file = 'card_on_file',
  prepay = 'prepay',
  pre_auth = 'pre_auth',
  submit_appt = 'submit_appt',
}
export enum ComMoegoServerGroomingWebVoAbandonedScheduleMessageSettingVOClientTypes {
  NEW_VISITORS = 'NEW_VISITORS',
  EXISTING_CLIENTS = 'EXISTING_CLIENTS',
}
export enum ComMoegoServerGroomingWebVoAbandonedScheduleMessageSettingVOOnTypeDays {
  MONDAY = 'MONDAY',
  TUESDAY = 'TUESDAY',
  WEDNESDAY = 'WEDNESDAY',
  THURSDAY = 'THURSDAY',
  FRIDAY = 'FRIDAY',
  SATURDAY = 'SATURDAY',
  SUNDAY = 'SUNDAY',
}
export enum ComMoegoServerGroomingWebVoAbandonedScheduleMessageSettingVOSendOutType {
  ON = 'ON',
  WAIT_FOR = 'WAIT_FOR',
}
export enum ComMoegoServerGroomingWebVoObAbandonClientRecordVOAbandonStep {
  welcome_page = 'welcome_page',
  basic_info = 'basic_info',
  select_care_type = 'select_care_type',
  select_address = 'select_address',
  select_pet = 'select_pet',
  select_date = 'select_date',
  select_service = 'select_service',
  select_groomer = 'select_groomer',
  select_time = 'select_time',
  additional_pet_info = 'additional_pet_info',
  personal_info = 'personal_info',
  card_on_file = 'card_on_file',
  prepay = 'prepay',
  pre_auth = 'pre_auth',
  submit_appt = 'submit_appt',
}
export enum ComMoegoServerGroomingWebVoObAbandonClientRecordVOCareType {
  GROOMING = 'GROOMING',
  BOARDING = 'BOARDING',
  DAYCARE = 'DAYCARE',
  EVALUATION = 'EVALUATION',
  DOG_WALKING = 'DOG_WALKING',
  GROUP_CLASS = 'GROUP_CLASS',
}
export enum ComMoegoServerGroomingWebVoObOBMetricVOName {
  unique_visitors = 'unique_visitors',
  new_visitors = 'new_visitors',
  submitted_requests = 'submitted_requests',
  expected_revenue = 'expected_revenue',
  prepaid_revenue = 'prepaid_revenue',
  started_clients = 'started_clients',
  started_records = 'started_records',
  abandoned_clients = 'abandoned_clients',
  abandoned_records = 'abandoned_records',
  abandoned_proportion = 'abandoned_proportion',
  recoverable_clients = 'recoverable_clients',
  recoverable_records = 'recoverable_records',
  recoverable_proportion = 'recoverable_proportion',
  recovered_clients = 'recovered_clients',
  recovered_records = 'recovered_records',
  recovered_proportion = 'recovered_proportion',
  recoverable_revenue = 'recoverable_revenue',
  recovered_revenue = 'recovered_revenue',
  pending_requests_revenue = 'pending_requests_revenue',
}

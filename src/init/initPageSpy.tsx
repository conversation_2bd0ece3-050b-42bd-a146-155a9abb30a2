import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { getLogs, getTabLogs, getTabs, initPageSpy as init, obfuscate } from '@moego/pagespy';
import { useStore } from 'amos';
import { Modal } from '@moego/ui';
import React, { useCallback, useEffect, useRef } from 'react';
import { useHistory } from 'react-router';
import type { TopMessage } from '../container/log/sheet/workerHelper';
import { PATH_LOG_SHEET } from '../router/paths';
import { uploadFileDirectly, uploadSliceFile } from '../service/slice-upload';
import { ByteUnit } from '../utils/common';
import { GrowthBookFeatureList } from '../utils/growthBook/growthBook.config';
import { useBool } from '../utils/hooks/useBool';
import { openWindow } from '../utils/utils';
import { logger, LoggerModule } from '../utils/logger';
import { compressBuffer } from '../utils/files/compress';

const PAGE_SPY_BLACK_LIST = [PATH_LOG_SHEET];

const MIN_SIZE = 5 * ByteUnit.MB;

const INTERCOM_Z_INDEX = 2147483001;

export const useInitPageSpy = () => {
  const history = useHistory();
  const pathname = history?.location.pathname;
  const isBlack = PAGE_SPY_BLACK_LIST.some((v) => v.regex.test(pathname));
  const store = useStore();
  const pageSpyRef = useRef<ReturnType<typeof init> | null>(null);

  const _isPageSpyEnabled = useFeatureIsOn(GrowthBookFeatureList.EnablePageSpy);
  const isPageSpyEnabled = __DEV__ ? false : _isPageSpyEnabled;

  const visible = useBool(false);

  const handleMessage = useCallback(
    async (e: {
      data: {
        type: string;
        params: {
          start: number;
          end: number;
        };
        from: 'intercom' | 'manual';
        id: string;
      };
      source: MessageEventSource | null;
    }) => {
      if (e.data.type === 'uploadLogs') {
        try {
          if (e.data.from === 'intercom') {
            visible.open();
          }
          const tabs = await getTabs({
            start: e.data.params.start,
            end: e.data.params.end,
          });

          e.source?.postMessage({
            type: 'uploadLogsInit',
            data: tabs,
            id: e.data.id,
          } satisfies TopMessage);

          if (!tabs.length) {
            return;
          }
          // 使用 for await...of 替代 Promise.all, 防止内存爆炸
          for (const tab of tabs) {
            if (tab.meta.size <= MIN_SIZE) {
              await getTabLogs(
                {
                  tabId: tab.tabId,
                  end: e.data.params.end,
                },
                async ({ content, progress }) => {
                  const compressData = await compressBuffer(content.buffer, 'gzip');
                  const url = await uploadFileDirectly(new Uint8Array(compressData), {
                    filename: `${tab.tabId}.json`,
                    type: 'application/json',
                    contentEncoding: 'gzip',
                  });

                  e.source?.postMessage({
                    type: 'uploadLogsProgress',
                    id: e.data.id,
                    data: {
                      tabId: tab.tabId,
                      url,
                      progress,
                    },
                  } satisfies TopMessage);
                },
              );
              continue;
            }

            const fileName = `${tab.tabId}.${tab.meta.size}.${tab.meta.endTime}.json`;
            const id = `${tab.tabId}-${tab.meta.size}-${tab.meta.endTime}`;

            const url = await uploadSliceFile({
              fileName,
              id,
              getContent: (upload) =>
                getTabLogs(
                  {
                    tabId: tab.tabId,
                    end: e.data.params.end,
                  },
                  async ({ content, partNumber, progress }) => {
                    const buf = content.buffer;
                    await upload({
                      content: buf,
                      partNumber,
                      isLast: progress === 1,
                    });
                    e.source?.postMessage({
                      type: 'uploadLogsProgress',
                      id: e.data.id,
                      data: {
                        tabId: tab.tabId,
                        progress,
                      },
                    } satisfies TopMessage);
                  },
                ),
              contentEncoding: 'gzip',
              contentType: 'application/json',
            });

            e.source?.postMessage({
              type: 'uploadLogsProgress',
              id: e.data.id,
              data: {
                tabId: tab.tabId,
                progress: 1,
                url,
              },
            } satisfies TopMessage);
          }
        } catch (err) {
          console.error(err);
          e.source?.postMessage({
            type: 'error',
            id: e.data.id,
            error: 'Sorry, something went wrong. Please try again later.',
          });
        }
      }
    },
    [],
  );

  useEffect(() => {
    if (!isPageSpyEnabled || pageSpyRef.current !== null) {
      return;
    }

    window.addEventListener('message', handleMessage);
    if (!isBlack) {
      pageSpyRef.current = init({
        dataProcessor: {
          network: (data) => {
            if (['fetch', 'xhr'].includes(data.requestType)) {
              data.responseHeader?.forEach((item) => {
                if (item[0] === 'set-cookie') {
                  item[1] = obfuscate(item[1]);
                }
              });
              return true;
            }
            // 不记录上传，怕太大
            return !data.url.includes('https://moegonew.s3.us-west-2.amazonaws.com');
          },
        },
      });

      requestAnimationFrame(async () => {
        const logs = await getLogs(0, Date.now(), 'meta');
        const sizeList = logs.map((v) => v.meta.size);
        sizeList.sort();
        const totalSize = sizeList.reduce((acc, cur) => acc + cur, 0);
        const avgSize = totalSize / sizeList.length;

        const p75 = sizeList[Math.floor(sizeList.length * 0.75)];
        const p95 = sizeList[Math.floor(sizeList.length * 0.95)];
        const max = sizeList[sizeList.length - 1];

        const pageSpyData = {
          totalSize,
          avgSize,
          p75,
          p95,
          max,
        };
        logger.get(LoggerModule.PAGESPY).info('pageSpySize', pageSpyData);
      });
    }

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [isPageSpyEnabled, isBlack, store, handleMessage]);

  return (
    <Modal
      title="Open a new tab"
      isOpen={visible.value}
      onClose={visible.close}
      onConfirm={() => {
        openWindow(window.location.href);
        visible.close();
      }}
      zIndex={INTERCOM_Z_INDEX + 1}
      confirmText="Go to New Tab"
    >
      <span className="moe-font-manrope moe-font-light moe-text-[16px]">
        <span className="moe-font-bold">Please keep this tab open during upload - this may take a few minutes.</span>A
        new browser tab will be open for you to continue working.
      </span>
    </Modal>
  );
};

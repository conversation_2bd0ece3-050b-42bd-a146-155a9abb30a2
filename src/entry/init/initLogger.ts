import { selectAccount } from '../../modules/Account/store/account.selectors';
import { selectBusiness } from '../../modules/Business/store/business.selectors';
import { Logger, initLoggerContext } from '../../utils/logger';
import { autorun } from '../../utils/store/autorun';
import { printName } from '../../utils/utils';
import { store } from '../globals';

export async function initLogger() {
  initLoggerContext();

  const syncUser = autorun(
    () => [store.select(selectAccount()), store.select(selectBusiness())] as const,
    (account, business) => {
      Logger.setUser(
        account.id
          ? {
              id: account.id + '',
              email: account.email,
              username: printName(account),
              businessId: business.id,
              businessName: business.businessName,
            }
          : {},
      );
    },
    [],
  );

  store.subscribe(syncUser);
}

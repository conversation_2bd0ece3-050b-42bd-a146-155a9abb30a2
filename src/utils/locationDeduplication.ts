interface LocationData {
  latitude: number;
  longitude: number;
  timestamp: number;
}

const MIN_TIME_INTERVAL_MS = 5000;

let lastUploadedLocation: LocationData | null = null;

export const shouldUploadLocation = (newLocation: LocationData): boolean => {
  if (!lastUploadedLocation) {
    lastUploadedLocation = newLocation;
    return true;
  }

  const timeDiff = newLocation.timestamp - lastUploadedLocation.timestamp;
  if (timeDiff < MIN_TIME_INTERVAL_MS) {
    return false;
  }

  lastUploadedLocation = newLocation;
  return true;
};

export const resetLocationCache = () => {
  lastUploadedLocation = null;
};

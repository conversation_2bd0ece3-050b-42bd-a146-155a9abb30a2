import { resetLocationCache, shouldUploadLocation } from './locationDeduplication';

describe('Location Deduplication', () => {
  beforeEach(() => {
    resetLocationCache();
  });

  test('should upload first location', () => {
    const location = {
      latitude: 37.7749,
      longitude: -122.4194,
      timestamp: Date.now(),
    };

    expect(shouldUploadLocation(location)).toBe(true);
  });

  test('should not upload within 5 second interval', () => {
    const baseTime = Date.now();
    const location1 = {
      latitude: 37.7749,
      longitude: -122.4194,
      timestamp: baseTime,
    };
    const location2 = {
      latitude: 37.775,
      longitude: -122.4195,
      timestamp: baseTime + 3000,
    };

    expect(shouldUploadLocation(location1)).toBe(true);
    expect(shouldUploadLocation(location2)).toBe(false);
  });

  test('should upload after 5 second interval', () => {
    const baseTime = Date.now();
    const location1 = {
      latitude: 37.7749,
      longitude: -122.4194,
      timestamp: baseTime,
    };
    const location2 = {
      latitude: 37.775,
      longitude: -122.4195,
      timestamp: baseTime + 6000,
    };

    expect(shouldUploadLocation(location1)).toBe(true);
    expect(shouldUploadLocation(location2)).toBe(true);
  });

  test('should reset cache correctly', () => {
    const location = {
      latitude: 37.7749,
      longitude: -122.4194,
      timestamp: Date.now(),
    };

    shouldUploadLocation(location);
    resetLocationCache();
    expect(shouldUploadLocation(location)).toBe(true);
  });
});

import { Dd<PERSON><PERSON><PERSON>, DdSdkReactNative } from '@datadog/mobile-react-native';
import { type UserInfo } from '@datadog/mobile-react-native/lib/typescript/sdk/UserInfoSingleton/types';
import { moegoLogger } from '@moego/react-native-logger/src/loggerReactNative';
import { ExpoManifestExtra } from './NativeModules';

interface LoggerOptions {
  moduleName?: string;
  paths?: string[];
}

export class Logger {
  private map = new Map<string, Logger>();

  private paths: string[] = [];

  private context: object = {};

  constructor(options: LoggerOptions = {}) {
    const { moduleName, paths } = options;
    if (moduleName) {
      this.paths = [...(paths ?? []), moduleName];
      this.context = {
        'module-name': moduleName,
        'module-path': this.pathsToContext(this.paths),
      };
      this.setContext({});
    }
  }

  /**
   * Set the user information.
   * @param user: The user object (use builtin attributes: 'id', 'email', 'name', and/or any custom attribute).
   * @returns a Promise.
   */
  static readonly setUser = async (user: UserInfo): Promise<void> => {
    return DdSdkReactNative.setUser(user);
  };

  /**
   * get logger for a specific module
   * @param moduleName do not include `|` in the module name
   */
  get(moduleName: string): Logger {
    let logger = this.map.get(moduleName);
    if (!logger) {
      logger = new Logger({
        moduleName: moduleName,
        paths: this.paths,
      });
      this.map.set(moduleName, logger);
    }
    return logger;
  }

  setContext(context: object): void {
    this.context = { ...this.context, ...context };
  }

  debug(message: string, context?: object): void {
    DdLogs.debug(message, this.genContext(context));
  }

  info(message: string, context?: object): void {
    DdLogs.info(message, this.genContext(context));
  }

  warn(message: string, context?: object): void {
    DdLogs.warn(message, this.genContext(context));
  }

  error(message: string, context?: object): void {
    DdLogs.error(message, this.genContext(context));
  }

  private genContext(context?: object): object {
    return context ? { ...this.context, ...context } : this.context;
  }

  private pathsToContext(paths: string[]): object {
    if (paths.length === 0) {
      return {};
    }
    const [name, ...rest] = paths;
    return {
      n: name,
      p: this.pathsToContext(rest),
    };
  }
}

let initialized = false;
function initLoggerContext() {
  if (initialized) {
    console.debug('[Logger] Context already initialized.');
    return;
  }
  initialized = true;
  console.debug('[Logger] Initializing context...');

  moegoLogger.addGlobalContext({
    runtime_number: String(ExpoManifestExtra.RUNTIME_NUMBER),
    build_id: ExpoManifestExtra.CI_BUILD_ID ?? '',
  });
}

export const logger = new Logger();
export { initLoggerContext, moegoLogger };

export enum LoggerModule {
  NOTIFICATION = 'Notification',
  METRIC = 'metric',
  CALL = 'call',
}

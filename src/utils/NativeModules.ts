/*
 * @since 2021-04-11 12:31:24
 * <AUTHOR> <<EMAIL>>
 */

import Intercom from '@intercom/intercom-react-native';
import { MoegoMtrModule, TracerouteEvent } from '@moego/react-native-traceroute';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as NetInfo from '@react-native-community/netinfo';
import * as Sentry from '@sentry/react-native';
import * as Application from 'expo-application';
import * as Av from 'expo-av';
import * as Clipboard from 'expo-clipboard';
import _ConstantsDefault, * as Constants from 'expo-constants';
import * as Contacts from 'expo-contacts';
import * as NativeDevice from 'expo-device';
import * as FileSystem from 'expo-file-system';
import * as Font from 'expo-font';
import * as Haptics from 'expo-haptics';
import * as ImageManipulator from 'expo-image-manipulator';
import * as ImagePicker from 'expo-image-picker';
import * as Localization from 'expo-localization';
import * as Location from 'expo-location';
import * as MailComposer from 'expo-mail-composer';
import * as MediaLibrary from 'expo-media-library';
import * as Notifications from 'expo-notifications';
import * as ScreenOrientation from 'expo-screen-orientation';
import * as SplashScreen from 'expo-splash-screen';
import * as TaskManager from 'expo-task-manager';
import * as ExpoUpdates from 'expo-updates';
import * as VideoThumbnailsLib from 'expo-video-thumbnails';
import * as WebBrowser from 'expo-web-browser';
import * as DnsLookup from 'react-native-dns-lookup';
import type { Image as ImageCropPickerImage, Options as ImageCropPickerOptions } from 'react-native-image-crop-picker';
import UUID from 'react-native-uuid';
import * as ViewShot from 'react-native-view-shot';
import AppConfig from '../../app.config';
import { KnownShapeOf } from '../types/common';
import { isAppVersionCompatible } from './version';

/**
 * `expo-constants` 这个包有点不一样, 是个 es module, 同时存在 default 和 named exports
 * 无法通过单一名称导出, 所以非 default 用 {@link Constants}, default 用 {@link ConstantsDefault}.
 */
const ConstantsDefault: KnownShapeOf<Constants.Constants> = _ConstantsDefault;
export const ExpoManifest = _ConstantsDefault.expoConfig!;

export const ExpoManifestExtra: ReturnType<typeof AppConfig>['extra'] = ExpoManifest.extra as any;
const Updates = {
  ...ExpoUpdates,
  setEncodeExtraParamAsync: async (key: string, value: string | null | undefined) => {
    const isNil = typeof value !== 'string' && !value;
    const encodedValue = isNil ? null : encodeURIComponent(value);
    return ExpoUpdates.setExtraParamAsync(key, encodedValue);
  },
};

let SecureStore: typeof import('expo-secure-store') | null = null;
let ImageCropPicker: typeof import('react-native-image-crop-picker') | null = null;

try {
  (async () => {
    // 只支持 2.13.2 以上版本, 强制升级到 2.14.0 后可移除
    if (isAppVersionCompatible('2.13.2', Application.nativeApplicationVersion ?? '0')) {
      SecureStore = await import('expo-secure-store');
      ImageCropPicker = await import('react-native-image-crop-picker');
    }
  })();
} catch (e) {
  console.warn('[NativeModules]:', e);
}

const Device = {
  ...NativeDevice,
  deviceId: null as string | null,
};
export {
  Application,
  AsyncStorage,
  Av,
  Clipboard,
  Constants,
  ConstantsDefault,
  Contacts,
  Device,
  DnsLookup,
  FileSystem,
  Font,
  Haptics,
  ImageCropPicker,
  ImageManipulator,
  ImagePicker,
  Intercom,
  Localization,
  Location,
  MailComposer,
  MediaLibrary,
  MoegoMtrModule,
  NetInfo,
  Notifications,
  ScreenOrientation,
  SecureStore,
  Sentry,
  SplashScreen,
  TaskManager,
  TracerouteEvent,
  UUID,
  Updates,
  VideoThumbnailsLib,
  ViewShot,
  WebBrowser
};
export type { ImageCropPickerImage, ImageCropPickerOptions };

import dayjs from 'dayjs';
import { useMemo, useState } from 'react';
import {
  AUTO_UPLOAD_LOCATION_TASK_NAME,
  SENTRY_MODULE_INTERVAL_TIMER,
  SENTRY_MODULE_TASK_MANAGER,
  UPLOAD_INTERVAL_MILLISECONDS,
} from '../../entry/init/initTaskManager';
import {
  resetLocationTrackingConfig,
  resetLocationTrackingData,
  updateLocationTrackingConfig,
} from '../../modules/LocationTracking/store/LocationTracking.actions';
import { locationTrackingConfigBox } from '../../modules/LocationTracking/store/LocationTracking.boxes';
import { DATE_FORMAT_EXCHANGE } from '../DateTimeUtil';
import { resetLocationCache } from '../locationDeduplication';
import { Location, Sentry, TaskManager } from '../NativeModules';
import { useThunkDispatch } from '../store/action';
import { useListSelector } from '../store/selector';
import {
  clearTimerForeground,
  getTimerForeground,
  setTimerForeground,
  useCalcPermission,
  useCheckIsAllSet,
  useFetchPrerequisites,
  useGetLocationAndUploadForeground,
} from './useAutoUploadLocation.utils';
import { useLatestCallback } from './useLatestCallback';

export const useAutoUploadLocation = () => {
  const [lastDate, setLastDate] = useState(dayjs().format(DATE_FORMAT_EXCHANGE));
  const [{ isAllSet, autoUploadIsOn, notAllSetReason, bestAvailableMode }] = useListSelector(locationTrackingConfigBox);
  const dispatch = useThunkDispatch();
  const fetchPrerequisites = useFetchPrerequisites();

  // For both foreground and background
  const calcPermission = useCalcPermission();
  const checkIsAllSet = useCheckIsAllSet();

  // For foreground only
  const getLocationAndUploadForeground = useGetLocationAndUploadForeground();

  /**
   * start, foreground
   */
  const startTrackingForeground = useLatestCallback(async () => {
    try {
      const curTimer = getTimerForeground();
      dispatch(updateLocationTrackingConfig({ autoUploadIsOn: !!getTimerForeground() }));

      if (curTimer) {
        // clear old one and start new one
        clearTimerForeground(curTimer);
        dispatch(updateLocationTrackingConfig({ autoUploadIsOn: !!getTimerForeground() }));
      }

      await getLocationAndUploadForeground();
      const nextTimer = setInterval(async () => {
        await getLocationAndUploadForeground();
      }, UPLOAD_INTERVAL_MILLISECONDS);
      setTimerForeground(nextTimer);
      dispatch(updateLocationTrackingConfig({ autoUploadIsOn: !!getTimerForeground() }));
    } catch (e) {
      Sentry.captureException(e, {
        tags: {
          module: SENTRY_MODULE_INTERVAL_TIMER,
          function: 'startTrackingForeground',
        },
      });
    }
  });

  /**
   * start, background
   */
  const startTrackingBackground = useLatestCallback(async () => {
    try {
      const isTaskDefined = TaskManager.isTaskDefined(AUTO_UPLOAD_LOCATION_TASK_NAME);
      if (!isTaskDefined) {
        return;
      }

      const curStarted = await Location.hasStartedLocationUpdatesAsync(AUTO_UPLOAD_LOCATION_TASK_NAME);
      dispatch(updateLocationTrackingConfig({ autoUploadIsOn: curStarted }));

      if (curStarted) {
        return;
      }

      await Location.startLocationUpdatesAsync(AUTO_UPLOAD_LOCATION_TASK_NAME, {
        accuracy: Location.Accuracy.BestForNavigation,
        deferredUpdatesInterval: UPLOAD_INTERVAL_MILLISECONDS,
        timeInterval: UPLOAD_INTERVAL_MILLISECONDS,
        showsBackgroundLocationIndicator: false,
        foregroundService: {
          notificationTitle: 'MoeGo is using your location',
          notificationBody: 'To provide you with location related services',
        },
        pausesUpdatesAutomatically: false,
      });

      const nextStarted = await Location.hasStartedLocationUpdatesAsync(AUTO_UPLOAD_LOCATION_TASK_NAME);
      dispatch(updateLocationTrackingConfig({ autoUploadIsOn: nextStarted }));
    } catch (e) {
      Sentry.captureException(e, {
        tags: {
          module: SENTRY_MODULE_TASK_MANAGER,
          function: 'startTrackingBackground',
        },
      });
    }
  });

  /**
   * start, consider both
   */
  const startTracking = useLatestCallback(async () => {
    const { bestAvailableMode, isGranted } = await calcPermission();
    if (isGranted) {
      if (bestAvailableMode === 'BACKGROUND') {
        await startTrackingBackground();
      } else if (bestAvailableMode === 'FOREGROUND') {
        await startTrackingForeground();
      }
    }
  });

  /**
   * stop, foreground
   */
  const stopTrackingForeground = useLatestCallback(async () => {
    try {
      // 调用之前先判断下，防止停止不存在的 interval 从而产生报错
      const curTimer = getTimerForeground();
      dispatch(updateLocationTrackingConfig({ autoUploadIsOn: !!getTimerForeground() }));

      if (!curTimer) {
        return;
      }

      clearTimerForeground(curTimer);
      dispatch(updateLocationTrackingConfig({ autoUploadIsOn: !!getTimerForeground() }));
    } catch (e) {
      Sentry.captureException(e, {
        tags: {
          module: SENTRY_MODULE_INTERVAL_TIMER,
          function: 'stopTrackingForeground',
        },
      });
    }
  });

  /**
   * stop, background
   */
  const stopTrackingBackground = useLatestCallback(async () => {
    try {
      // 调用之前先判断下，防止停止未注册的任务从而产生报错
      const isRegistered = await TaskManager.isTaskRegisteredAsync(AUTO_UPLOAD_LOCATION_TASK_NAME);
      if (!isRegistered) {
        return;
      }

      await Location.stopLocationUpdatesAsync(AUTO_UPLOAD_LOCATION_TASK_NAME);
      const isStarted = await Location.hasStartedLocationUpdatesAsync(AUTO_UPLOAD_LOCATION_TASK_NAME);
      dispatch(updateLocationTrackingConfig({ autoUploadIsOn: isStarted }));
    } catch (e) {
      Sentry.captureException(e, {
        tags: {
          module: SENTRY_MODULE_TASK_MANAGER,
          function: 'stopTrackingBackground',
        },
      });
    }
  });

  /**
   * stop, consider both
   */
  const stopTracking = useLatestCallback(async () => {
    const { bestAvailableMode, isGranted } = await calcPermission();
    if (isGranted) {
      if (bestAvailableMode === 'BACKGROUND') {
        await stopTrackingBackground();
      } else if (bestAvailableMode === 'FOREGROUND') {
        await stopTrackingForeground();
      }
    }
  });

  /**
   * 解决 permission 变化的问题
   * 比如 permission 之前是 always (使用 background task)，现在用户切出去改成 while using app (使用 foreground timer)
   * 再次回到 app 的时候，需要清除掉之前的 background task
   */
  const clearOtherSide = useLatestCallback(async () => {
    const { bestAvailableMode, isGranted } = await calcPermission();
    if (isGranted) {
      if (bestAvailableMode === 'BACKGROUND') {
        await stopTrackingForeground();
      } else if (bestAvailableMode === 'FOREGROUND') {
        await stopTrackingBackground();
      }
    } else {
      await stopTrackingForeground();
      await stopTrackingBackground();
    }
  });

  const unregisterBackgroundTask = useLatestCallback(async () => {
    const isRegistered = await TaskManager.isTaskRegisteredAsync(AUTO_UPLOAD_LOCATION_TASK_NAME);
    if (isRegistered) {
      await TaskManager.unregisterTaskAsync(AUTO_UPLOAD_LOCATION_TASK_NAME);
    }
  });

  const clear = useLatestCallback(async () => {
    dispatch(resetLocationTrackingData());
    dispatch(resetLocationTrackingConfig());
    resetLocationCache();
    await stopTrackingForeground();
    await stopTrackingBackground();
    await unregisterBackgroundTask();
  });

  const handleOnDateChangeForeground = useLatestCallback(async () => {
    const now = dayjs();
    const date = now.format(DATE_FORMAT_EXCHANGE);
    const { bestAvailableMode, isGranted } = await calcPermission();
    if (isGranted && bestAvailableMode === 'FOREGROUND') {
      /**
       * 日期切换的时候需要重新获取 prerequisite（主要是另一天的 working range），同时重置历史数据
       */
      if (date !== lastDate) {
        await fetchPrerequisites();
        dispatch(resetLocationTrackingData());
        setLastDate(date);
      }
    }
  });

  const autoStartOrStopTracking = useLatestCallback(async () => {
    await clearOtherSide();
    await handleOnDateChangeForeground();
    const currentIsAllSet = await checkIsAllSet({ isNeedFetch: true });
    if (currentIsAllSet) {
      await startTracking();
    } else {
      await stopTracking();
    }
  });

  return useMemo(
    () => ({
      autoUploadIsOn,
      isAllSet,
      notAllSetReason,
      bestAvailableMode,
      checkIsAllSet,
      startTracking,
      stopTracking,
      autoStartOrStopTracking,
      clear,
    }),
    [
      autoUploadIsOn,
      isAllSet,
      notAllSetReason,
      bestAvailableMode,
      checkIsAllSet,
      startTracking,
      stopTracking,
      autoStartOrStopTracking,
      clear,
    ],
  );
};

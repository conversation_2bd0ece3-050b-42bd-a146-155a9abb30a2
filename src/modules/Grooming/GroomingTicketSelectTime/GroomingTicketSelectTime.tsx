import { ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import IconIcDropdownBlackSvg from '../../../../assets/images/ic-dropdown-black.svg';
import { MoeText } from '../../../components/MoeText';
import { DATE_FORMAT_EXCHANGE, DATE_TIME_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';
import { gotIt } from '../../../utils/alert';
import { createLazyStackScreen } from '../../../utils/createScreen';
import { useHeaderRightText } from '../../../utils/hooks/useEditMenu';
import { useThunkDispatch } from '../../../utils/store/action';
import { useListSelector } from '../../../utils/store/selector';
import { isNormal } from '../../../utils/store/utils';
import { printName } from '../../../utils/utils';
import { currentBusinessIdBox } from '../../Business/store/business.boxes';
import {
  CalendarDailyView,
  type DailyViewReschedulingInputOutputType,
} from '../../Calendar/components/CalendarDailyView/CalendarDailyView';
import {
  STAFF_ID_ALL,
  StaffSelectionModal,
} from '../../Calendar/components/CalendarHeader/components/StaffSelectionModal';
import { CalendarGranularity } from '../../Calendar/store/calendar_config.boxes';
import { selectAppointmentCalendarConfig } from '../../Calendar/store/calendar_config.selectors';
import { CalendarGroomingTicketRecord } from '../../Calendar/store/calendar_data.boxes';
import { selectCalendarSettings } from '../../Calendar/store/calendar_settings.selectors';
import { getApplicableServiceList } from '../../Service/store/actions/public/service.actions';
import { staffMapBox } from '../../Staff/store/staff.boxes';
import { selectAccessibleStaffList } from '../../Staff/store/workingRange.selectors';
import { type PATH_GROOMING_TICKET_SELECT_TIME } from '../Grooming.api';
import { getTicketConvertParams } from '../store/actions/private/grooming_editoradd.actions';
import { isServiceOperations } from '../store/grooming_editoradd.types';
import { styles } from './GroomingTicketSelectTime.styles';
import { GroomingTicketSelectTimeHeader } from './GroomingTicketSelectTimeHeader';
import { selectEditingGroomingTicketDuration } from './selectTime';
import { useConfirmValidate } from './useConfirmValidate';

function getMinuteAreMultiplesOf5() {
  const currentMinute = dayjs().minute();
  return Math.floor(currentMinute / 5) * 5;
}

export const GroomingTicketSelectTime = createLazyStackScreen<
  Parameters<(typeof PATH_GROOMING_TICKET_SELECT_TIME)['push']>[0]
>((props, navigation) => {
  const dispatch = useThunkDispatch();
  const { ticketId, staffId, dateTime, petDetailIds, onSelected } = props;
  const [showStaffModal, setShowStaffModal] = useState(false);
  const [businessId, allStaffList, staffMap, calendarSetting, ticketDuration, calendarConfig] = useListSelector(
    currentBusinessIdBox,
    selectAccessibleStaffList(),
    staffMapBox,
    selectCalendarSettings(),
    selectEditingGroomingTicketDuration(),
    selectAppointmentCalendarConfig(),
  );
  const selectedParams = useRef<DailyViewReschedulingInputOutputType[]>();
  const confirmValidate = useConfirmValidate(props, selectedParams);
  const ticketConvertParams = dispatch(getTicketConvertParams());
  const baseStartDate = dayjs(calendarConfig.getDate()).hour(dayjs().hour()).minute(getMinuteAreMultiplesOf5());
  const cards = CalendarGroomingTicketRecord.convertFromGroomingEditRecord(
    ticketConvertParams,
    true,
    calendarSetting.calendarViewStartAt,
    calendarSetting.calendarViewEndAt,
    /**
     * calendarConfig 中的时间格式是 YYYY-MM-DD，所以当在 new appt 选择 service 的时间时，默认时间是从 12:00 am 开始。
     * 所以在 baseStartDate 上加上当前时间的 hour。
     *
     * MER-653
     */
    baseStartDate.format(DATE_TIME_FORMAT_EXCHANGE),
  );
  const calcStaffId = staffId || cards?.[0]?.staffId || 0;
  const calcDateTime =
    (dateTime?.isValid() && dateTime) || (cards?.[0]?.startTime.isValid() && cards?.[0]?.startTime) || dayjs();
  const { isMultipleOperations } = useMemo(() => {
    const { petServices } = ticketConvertParams;
    const isMultipleOperations = !!petServices?.some((service) => isServiceOperations(service.operationList));
    // const operationStaffs = petServices?.map((service) => isServiceOperations(service.operationList) ? service.operationList.map((i) => i.staffId) : []).flat() ?? [];
    return {
      isMultipleOperations,
    };
  }, [ticketConvertParams]);
  const calcTicketId = ticketId || cards?.[0]?.ticketId || 0;
  const calcSplitModeStaffs =
    ticketConvertParams?.petServices
      ?.map((petService) => petService.staffId || 0)
      .filter((staffId, index, arr) => arr.indexOf(staffId, 0) === index) || [];
  const isMultiStaffSplitTicket = calcSplitModeStaffs.length > 1 || isMultipleOperations;

  const [selectedStaffs, setSelectedStaffs] = useState<number[]>(() => {
    if (isMultiStaffSplitTicket) {
      return allStaffList.filter((staff) => calcSplitModeStaffs.includes(staff));
    } else {
      return calcStaffId === STAFF_ID_ALL ? [...allStaffList] : [calcStaffId];
    }
  });

  const displayedStaffField = useMemo(() => {
    if (selectedStaffs.length > 1) {
      const isAll = selectedStaffs.length === allStaffList.length;
      return isAll ? 'All staff' : `${selectedStaffs.length} staff`;
    } else {
      const isAll = selectedStaffs.length === 1 && selectedStaffs?.[0] === STAFF_ID_ALL;
      return isAll ? 'All staff' : printName(staffMap.mustGetItem(selectedStaffs?.[0]));
    }
  }, [selectedStaffs, allStaffList, staffMap]);
  const [selectedDate, setSelectedDate] = useState<string>(calcDateTime.format(DATE_FORMAT_EXCHANGE));

  const petParamListForSS = useMemo(() => {
    if (!ticketConvertParams.petServices || ticketConvertParams.petServices.length === 0) {
      return [];
    }
    // 按 petId 分组
    const petGroups = ticketConvertParams.petServices.reduce(
      (acc, service) => {
        const { petId, serviceId } = service;
        if (!isNormal(petId)) {
          return acc;
        }

        if (!acc[petId]) {
          acc[petId] = [];
        }
        isNormal(serviceId) && acc[petId].push(serviceId);

        return acc;
      },
      {} as Record<number, number[]>,
    );

    // 转换为目标格式
    return Object.entries(petGroups).map(([petId, serviceIds]) => ({
      petId: Number(petId),
      serviceIds,
    }));
  }, [ticketConvertParams.petServices]);

  // 为了解决外部传了 dateTime 进来，但是依然使用 editBox 里面的 start time，导致 calendar view 中的卡片时间展示不正确。
  // 补充场景：create ticket 时，使用了 ss for repeat，但是部分 repeat appt 有 conflict，所以需要自己修改时间，这时候修改时间就会携带 dateTime 进入该页面并且不能使用 edit box 里面的时间。
  if (cards && cards[0] && dateTime) {
    cards[0].startTime = dateTime;
  }
  const stashedCards = useRef<CalendarGroomingTicketRecord[] | undefined>(cards);

  const onConfirm = () => {
    if (!confirmValidate()) {
      // 校验不通过
      return;
    }
    onSelected(selectedParams.current || []);
    navigation.goBack();
  };
  useHeaderRightText(onConfirm, 'Confirm');
  useEffect(() => {
    navigation.setOptions({
      headerTitle: () => (
        <TouchableOpacity
          style={styles.staffSelector}
          onPress={() => {
            if (isMultiStaffSplitTicket) {
              gotIt(
                'This ticket includes multiple staffs. For staff changing, please refer to the\nEdit Services page.',
                'OK',
                'Staff selection not available here',
              );
              return;
            }
            setShowStaffModal(true);
          }}
        >
          <MoeText style={styles.staffText} numberOfLines={1}>
            {displayedStaffField}
          </MoeText>
          <IconIcDropdownBlackSvg />
        </TouchableOpacity>
      ),
    });
  }, [selectedStaffs]);

  useEffect(() => {
    dispatch(
      getApplicableServiceList({
        businessId: businessId.toString(),
        onlyAvailable: false,
        selectedServiceIds: [],
        serviceType: ServiceType.SERVICE,
      }),
    );
  }, [businessId]);

  return (
    <View style={{ flex: 1 }}>
      <StaffSelectionModal
        menuIsVisible={showStaffModal}
        onTouchCancel={() => setShowStaffModal(false)}
        granularity={CalendarGranularity.Daily}
        viewMode={'Calendar'}
        hasShowAll
        onSelected={setSelectedStaffs}
        selectedStaffs={selectedStaffs}
      />
      <GroomingTicketSelectTimeHeader
        hideDrivingTimeBtn
        ticketId={calcTicketId}
        onDateSelected={setSelectedDate}
        selectedDate={selectedDate}
        selectedStaffs={selectedStaffs}
        params={{
          addressLat: ticketConvertParams.lat + '',
          addressLng: ticketConvertParams.lng + '',
          serviceDuration: ticketDuration,
          staffIds: allStaffList,
          petParamListForSS,
        }}
      />
      <CalendarDailyView
        channel={'rescheduling'}
        selectedDate={selectedDate}
        selectedStaffId={selectedStaffs}
        onSelected={(data, cards) => {
          selectedParams.current = data.map((row) => {
            return {
              ...row,
            };
          });
          stashedCards.current = cards;
        }}
        onDateSelected={setSelectedDate}
        cardParams={[
          {
            ticketId: calcTicketId,
            staffId: calcStaffId,
            dateTime: calcDateTime,
            petDetailIds: petDetailIds,
          },
        ]}
        stashedCards={stashedCards.current}
        isMultiStaffSplitTicket={isMultiStaffSplitTicket}
      />
    </View>
  );
});

export default GroomingTicketSelectTime;

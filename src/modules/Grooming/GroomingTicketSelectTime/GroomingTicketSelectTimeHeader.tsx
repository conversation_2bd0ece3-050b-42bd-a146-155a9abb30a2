import dayjs, { type Dayjs } from 'dayjs';
import React, { memo, useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import { Dot } from '../../../components/ui/Dot';
import { type PartialRequired } from '../../../types/common';
import { type OpenApiDefinitions } from '../../../types/openApi/schema';
import { DATE_FORMAT_EXCHANGE, MONTH_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';
import { animateLayout } from '../../../utils/animation';
import { useSyncState } from '../../../utils/hooks/react';
import { useBool } from '../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { COL_GRAY_WHITE, COL_GREEN, COL_TRANSPARENT } from '../../../utils/style/consts';
import { VS_ROW } from '../../../utils/style/misc';
import { BG_WHITE } from '../../../utils/style/preset/backgroundColor';
import { MT_2 } from '../../../utils/style/preset/margin';
import { CalendarStrip } from '../../Calendar/components/DateSelector/CalendarStrip';
import { DateSelectorHeader } from '../../Calendar/components/DateSelector/DateSelectorHeader';
import { EmbeddedCalendar } from '../../Calendar/components/EmbeddedCalendar/EmbeddedCalendar';
import { CalendarGranularity } from '../../Calendar/store/calendar_config.boxes';
import { type GetSelectTimeSlotListInput, getAvailableTimeSlotFlag } from './selectTime';

type AvailableData = Record<
  string,
  OpenApiDefinitions['grooming']['com.moego.server.grooming.dto.ss.SmartScheduleVO'][] | undefined
>;
export interface GroomingTicketSelectTimeHeaderProps {
  onDateSelected: (date: string) => void;
  ticketId?: number;
  selectedDate: string;
  selectedStaffs: number[];
  params: PartialRequired<GetSelectTimeSlotListInput, 'addressLat' | 'addressLng' | 'serviceDuration'>;
  hideDrivingTimeBtn?: boolean;
}

export const GroomingTicketSelectTimeHeader = memo<GroomingTicketSelectTimeHeaderProps>(
  ({ onDateSelected, selectedDate, selectedStaffs, params, hideDrivingTimeBtn, ticketId }) => {
    const date = selectedDate;

    const calendarVisible = useBool();

    const [week, setWeek] = useSyncState(
      () => dayjs(date, DATE_FORMAT_EXCHANGE).startOf('week').format(DATE_FORMAT_EXCHANGE),
      [date],
    );

    const [month, setMonth] = useSyncState(
      () => dayjs(date, DATE_FORMAT_EXCHANGE).format(MONTH_FORMAT_EXCHANGE),
      [date, calendarVisible.value],
    );

    const [availableData, setAvailableData] = useState<AvailableData>({});

    const lastQueryAvailable = useRef<{ date: string; count: number }>();

    const handleChangeValue = useLatestCallback((date: string | string[]) => {
      onDateSelected(Array.isArray(date) ? date[0] : date);
      animateLayout();
      calendarVisible.close();
    });

    const handlePrev = useLatestCallback(() => {
      calendarVisible.value
        ? setMonth(dayjs(month, MONTH_FORMAT_EXCHANGE).add(-1, 'month').format(MONTH_FORMAT_EXCHANGE))
        : setWeek(dayjs(week, DATE_FORMAT_EXCHANGE).add(-1, 'week').format(DATE_FORMAT_EXCHANGE));
    });

    const handleNext = useLatestCallback(() => {
      calendarVisible.value
        ? setMonth(dayjs(month, MONTH_FORMAT_EXCHANGE).add(1, 'month').format(MONTH_FORMAT_EXCHANGE))
        : setWeek(dayjs(week, DATE_FORMAT_EXCHANGE).add(1, 'week').format(DATE_FORMAT_EXCHANGE));
    });

    const handleToday = useLatestCallback(() => handleChangeValue(dayjs().format(DATE_FORMAT_EXCHANGE)));

    const handleToggle = useLatestCallback(() => {
      animateLayout();
      calendarVisible.toggle();
    });

    const getAvailableTimeSlot = useLatestCallback(async (date?: string) => {
      const firstDate = dayjs(date || calendarVisible.value ? month : week)
        .startOf(calendarVisible.value ? 'month' : 'week')
        .format(DATE_FORMAT_EXCHANGE);
      const count = calendarVisible.value ? dayjs(date).daysInMonth() : 7;
      if (lastQueryAvailable.current?.count === count && lastQueryAvailable.current?.date === firstDate) {
        return;
      }
      try {
        const { data } = await getAvailableTimeSlotFlag({
          addressLat: params.addressLat,
          addressLng: params.addressLng,
          addressZipcode: params.addressZipcode,
          serviceDuration: params.serviceDuration,
          staffIds: params.staffIds,
          count,
          date: firstDate,
          filterGroomingId: ticketId,
          petParamListForSS: params.petParamListForSS,
        });
        lastQueryAvailable.current = {
          date: firstDate,
          count,
        };
        setAvailableData({ ...availableData, ...data });
      } catch (error) {
        console.error('Error in fetch available time', error);
      }
    });

    useEffect(() => {
      getAvailableTimeSlot();
    }, [month, week, calendarVisible.value]);

    const renderDateExtra = (_date: Dayjs, _isSelected: boolean, _isToday: boolean, str: string) => {
      let amColor = availableData[str] ? COL_GRAY_WHITE : COL_TRANSPARENT;
      let pmColor = availableData[str] ? COL_GRAY_WHITE : COL_TRANSPARENT;
      const dayMap = availableData[str];
      if (dayMap?.length) {
        for (let i = 0; i < dayMap.length; i++) {
          if (!selectedStaffs.includes(dayMap[i].staffId)) {
            continue;
          }
          dayMap[i].availableRange.forEach((a) => {
            if (a.startTime < 12 * 60) {
              amColor = COL_GREEN;
            }
            if (a.endTime > 12 * 60) {
              pmColor = COL_GREEN;
            }
          });
        }
      }

      return (
        <View style={[VS_ROW, MT_2]}>
          <Dot color={amColor} size={6} marginRight={2} />
          <Dot color={pmColor} size={6} />
        </View>
      );
    };
    return (
      <View style={BG_WHITE}>
        <DateSelectorHeader
          value={date}
          toggled={calendarVisible.value}
          onToggle={handleToggle}
          onPrev={handlePrev}
          onNext={handleNext}
          onToday={handleToday}
          month={month}
          mode={CalendarGranularity.Daily}
          hideDrivingTimeBtn={hideDrivingTimeBtn}
        />
        {calendarVisible.value ? (
          <EmbeddedCalendar
            value={date}
            onChangeValue={handleChangeValue}
            month={month}
            onChangeMonth={setMonth}
            renderDateExtra={renderDateExtra}
          />
        ) : (
          <CalendarStrip
            value={date}
            onChangeValue={handleChangeValue}
            week={week}
            onChangeWeek={setWeek}
            renderDateExtra={renderDateExtra}
          />
        )}
      </View>
    );
  },
);

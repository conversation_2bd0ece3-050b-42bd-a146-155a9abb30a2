/*
 * @since 2021-09-30 12:48:34
 * <AUTHOR> <<EMAIL>>
 */

import dayjs, { type Dayjs } from 'dayjs';
import { type PartialRequired } from '../../../types/common';
import { type OpenApiDefinitions } from '../../../types/openApi/schema';
import { DATE_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';
import { http } from '../../../utils/http';
import { Entity } from '../../../utils/store/Entity';
import { action } from '../../../utils/store/action';
import { createRawBox } from '../../../utils/store/createBox';
import { isNormal, truly } from '../../../utils/store/utils';

export interface SmartSchedulingPageModel {
  // 日历可见?
  calendarVisible: boolean;
  // 当前选中的 staff id
  staffId: number | undefined;
  // 数据表
  data: Record<
    string,
    OpenApiDefinitions['grooming']['com.moego.server.grooming.dto.ss.SmartScheduleVO'][] | undefined
  >;
  // 是否为全量数据
  fullyLoadedMap: Record<string, boolean>;
  // 当前展示的日期
  date: Dayjs | undefined;
  month: Dayjs | undefined;
}

export class SmartSchedulingPageRecord extends Entity<SmartSchedulingPageModel>({
  calendarVisible: false,
  staffId: void 0,
  data: {},
  fullyLoadedMap: {},
  date: void 0,
  month: void 0,
}) {
  getSlotList() {
    if (!isNormal(this.staffId) || !this.date?.isValid()) {
      return void 0;
    }
    return this.data[this.date?.format(DATE_FORMAT_EXCHANGE)]?.find((s) => s.staffId === this.staffId)?.availableRange;
  }
}

/** We cannot use {@link ShallowContext} for navigation header is not in render tree */
export const smartSchedulingPageBox = createRawBox(
  'smartScheduling/pageState',
  new SmartSchedulingPageRecord(),
  {
    mergeData: (state, data: SmartSchedulingPageModel['data']) => state.set('data', { ...state.data, ...data }),
    mergeFullyLoadedMap: (state, fullyLoadedMap: SmartSchedulingPageModel['fullyLoadedMap']) =>
      state.set('fullyLoadedMap', { ...state.fullyLoadedMap, ...fullyLoadedMap }),
  },
  {},
);

export type GetSmartSchedulingSlotListInput = PartialRequired<
  OpenApiDefinitions['grooming']['com.moego.server.grooming.params.ss.SmartScheduleRequest'],
  | 'farthestDay'
  | 'addressLat'
  | 'addressLng'
  | 'count'
  | 'serviceDuration'
  | 'date'
  | 'queryPerHalfDay'
  | 'petParamListForSS'
>;

export const getSmartSchedulingSlotList = action(
  async (dispatch, _select, input: GetSmartSchedulingSlotListInput, filterGroomingId?: number) => {
    const r = await http.open('POST/grooming/v2/appointment/smartSchedule', {
      ...input,
      filterGroomingId,
    });
    const start = dayjs(input.date, DATE_FORMAT_EXCHANGE);
    const data: SmartSchedulingPageModel['data'] = {};
    const fullyLoadedMap: SmartSchedulingPageModel['fullyLoadedMap'] = {};
    let i = 0;
    let firstDate: string | undefined = void 0;
    for (; i < input.farthestDay; i++) {
      const date = start.add(i, 'day');
      const dayStr = date.format(DATE_FORMAT_EXCHANGE);

      // Dates in this loop can only be the first available date & former ones (which means no-available-time date)
      // So we could say that they are all fully loaded
      fullyLoadedMap[dayStr] = true;

      // Find the first available date and set the former with empty array
      if (r.dayMap[dayStr]) {
        firstDate = dayStr;
        break;
      } else {
        data[dayStr] = [];
      }
    }
    // Set info for the later
    for (const max = Math.min(input.farthestDay, input.count + i); i < max; i++) {
      const date = start.add(i, 'day');
      const dayStr = date.format(DATE_FORMAT_EXCHANGE);
      data[dayStr] = Object.values(r.dayMap[dayStr]?.staffMap || {}).filter(truly);

      // `queryPerHalfDay` means that backend only returns the full available time list for the first available date
      // And results of the rest dates are partial, only to indicate if AM / PM has available time

      if (input.queryPerHalfDay) {
        // No-available-time date can be marked as fully loaded
        if (!data[dayStr]?.length) {
          fullyLoadedMap[dayStr] = true;
        }
      } else {
        // If queryPerHalfDay is not set, then every day can be marked as fully loaded
        fullyLoadedMap[dayStr] = true;
      }
    }
    dispatch(smartSchedulingPageBox.mergeData(data));
    dispatch(smartSchedulingPageBox.mergeFullyLoadedMap(fullyLoadedMap));
    return { firstDate, data };
  },
);

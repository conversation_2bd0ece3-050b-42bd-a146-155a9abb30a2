import dayjs, { type Dayjs } from 'dayjs';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import React, { useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import IconIcWarningTriangleSvg from '../../../../assets/images/ic-warning-triangle.svg';
import { MoeText } from '../../../components/MoeText';
import { type OpenApiDefinitions } from '../../../types/openApi/schema';
import { DATE_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';
import { gotIt } from '../../../utils/alert';
import { animateLayout } from '../../../utils/animation';
import { createLazyStackScreen } from '../../../utils/createScreen';
import { useLatLng } from '../../../utils/geo';
import { useUnmount } from '../../../utils/hooks/react';
import { useHeaderRightText, useHeaderTitle } from '../../../utils/hooks/useEditMenu';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { useThunkDispatch } from '../../../utils/store/action';
import { useMoeStore } from '../../../utils/store/createMoeStore';
import { useListSelector } from '../../../utils/store/selector';
import { isNormal } from '../../../utils/store/utils';
import { VS_CONTAINER, VS_ROW_CENTER } from '../../../utils/style/misc';
import { BG_DANGER_LIGHT } from '../../../utils/style/preset/backgroundColor';
import { MR_6 } from '../../../utils/style/preset/margin';
import { PH_20, PV_12 } from '../../../utils/style/preset/padding';
import { TS_14_DANGER } from '../../../utils/style/preset/text';
import { toast, useToastCallback } from '../../../utils/toast';
import { printName, roundUpTo } from '../../../utils/utils';
import { selectBusiness } from '../../Business/store/business.selectors';
import {
  CalendarDailyView,
  type CalendarDailyViewRef,
} from '../../Calendar/components/CalendarDailyView/CalendarDailyView';
import { CalendarGroomingTicketRecord } from '../../Calendar/store/calendar_data.boxes';
import { staffMapBox } from '../../Staff/store/staff.boxes';
import { selectMobileAccessibleStaffList } from '../../Staff/store/workingRange.selectors';
import { type PATH_SMART_SCHEDULING } from '../SmartScheduling.api';
import { updateServiceListPriceDurationByStaff } from '../store/actions/private/smartScheduling.actions';
import { selectSmartSchedulingCurrentBuffer, selectSmartSchedulingSetting } from '../store/smartScheduling.selectors';
import { useSmartSchedulingReport } from '../store/smartScheduling.utils';
import {
  type GetSmartSchedulingSlotListInput,
  getSmartSchedulingSlotList,
  smartSchedulingPageBox,
} from '../store/smartSchedulingPage.store';
import { ActionBar } from './components/ActionBar';
import { CalendarModal } from './components/CalendarModal';
import { HeaderTitle } from './components/HeaderTitle';
import { useGetStaffDurationMapList } from './hooks/useGetStaffDurationMapList';

export const SmartScheduling = createLazyStackScreen<Parameters<(typeof PATH_SMART_SCHEDULING)['push']>[0]>(
  (params, navigation) => {
    const dispatch = useThunkDispatch();
    const store = useMoeStore();
    const [setting, page, staffList, business, currentBuffer] = useListSelector(
      selectSmartSchedulingSetting(),
      smartSchedulingPageBox,
      selectMobileAccessibleStaffList(),
      selectBusiness(),
      selectSmartSchedulingCurrentBuffer(),
    );
    const [staff] = useListSelector(staffMapBox.getItem(page.staffId));

    const startAddr = useLatLng(setting.startLocationLat, setting.startLocationLng);
    const endAddr = useLatLng(setting.endLocationLat, setting.endLocationLng);

    // selected info
    const [addingCard, setAddingCard] = useState<CalendarGroomingTicketRecord>();

    const dailyView = useRef<CalendarDailyViewRef>(null);
    const report = useSmartSchedulingReport();
    const getStaffDurationMapList = useGetStaffDurationMapList();

    const attachAddingCard = (
      [slot, startTime = slot?.startTime]: [
        OpenApiDefinitions['grooming']['com.moego.server.grooming.dto.ss.ScheduleTimeSlot'] | undefined,
        number | undefined,
      ] = [store.select(smartSchedulingPageBox).getSlotList()?.[0], void 0],
      animate = true,
    ) => {
      const page = store.select(smartSchedulingPageBox);
      if (!page.date || !page.staffId) {
        return;
      }
      let hour = 9;
      let minute = 0;
      if (startTime === void 0) {
        // slot is also undefined.
        if (addingCard) {
          hour = addingCard.startTime.hour();
          minute = addingCard.startTime.minute();
        } else if (params.addingCard?.startTime.isValid()) {
          hour = params.addingCard.startTime.hour();
          minute = params.addingCard.startTime.minute();
        }
      } else {
        hour = Math.floor(startTime / 60);
        minute = startTime % 60;
      }

      // SS 情况下 slot start time 是通过 Google map 算出来的，会出现各种情况（12:11、12:13 等）。
      // 将 start time round up to 5 minutes.
      const addingCardStartTime = page.date.hour(hour).minute(minute);
      const addingCardRoundUpStartTime = addingCardStartTime.minute(roundUpTo(addingCardStartTime.minute()));

      const curAddingCard = addingCard || params.addingCard || CalendarGroomingTicketRecord.create();
      const ticketList = dispatch(updateServiceListPriceDurationByStaff(curAddingCard.ticketList, page.staffId));

      const serviceTotalDuration = ticketList.length
        ? ticketList.reduce((acc, cur) => acc + cur.serviceTime, 0)
        : params.duration;

      setAddingCard(
        (addingCard || params.addingCard || CalendarGroomingTicketRecord.create()).merge({
          startTime: addingCardRoundUpStartTime,
          endTime: addingCardRoundUpStartTime.add(serviceTotalDuration * T_MINUTE),
          staffId: page.staffId,
          ticketList,
          driveInMiles: slot?.driveInMiles,
          driveOutMiles: slot?.driveOutMiles,
          driveInTime: slot?.driveInMinutes,
          driveOutTime: slot?.driveOutMinutes,
          isDriveFromStart: slot?.isDriveFromStart,
          isDriveToEnd: slot?.isDriveToEnd,
          modifying: true,
          lat: params.address.latitude,
          lng: params.address.longitude,
        }),
      );
      animate && dailyView.current?.scrollTo(hour - 1);
    };

    const handlePress = useLatestCallback((hour: number, minute: number) => {
      if (!addingCard) {
        return;
      }
      const startTime = hour * 60 + minute;
      const slot = page.getSlotList()?.find((s) => s.startTime <= startTime && s.endTime >= startTime);
      attachAddingCard([slot, startTime], false);
    });

    const handleSearch = useToastCallback(
      async (count: number, farthestDay: number = count, date: string, queryPerHalfDay: boolean) => {
        toast('loading', 'Searching...');
        // don't need to use staffPetServiceDurationList field if only one staff
        const staffDurationMapList = params.staffId
          ? undefined
          : getStaffDurationMapList({
              staffIdList: staffList,
              serviceDurationList: params.serviceDurationList,
            });
        const petParamListForSS =
          params.addingCard?.petInfoList
            ?.map(({ petId, petDetailList }) => ({
              petId: petId ?? -1,
              serviceIds: petDetailList.map(({ serviceId }) => serviceId),
            }))
            .filter(({ petId }) => isNormal(petId)) || [];

        const input: GetSmartSchedulingSlotListInput = {
          addressLat: params.address.latitude + '',
          addressLng: params.address.longitude + '',
          addressZipcode: params.zipcode,
          serviceDuration: params.duration,
          staffIds: params.staffId ? [params.staffId] : staffList,
          farthestDay,
          count,
          date,
          queryPerHalfDay,
          applyClientPreferenceCustomerId: params.applyClientPreferenceCustomerId,
          bufferTime: params.bufferTime ?? currentBuffer,
          staffPetServiceDurationList: staffDurationMapList,
          petParamListForSS,
        };
        report(params, input);
        return dispatch(getSmartSchedulingSlotList(input, params.filterGroomingId));
      },
    );

    const handleToggleCalendar = useLatestCallback(() => {
      animateLayout();
      if (!page.date) {
        return;
      }
      dispatch(
        smartSchedulingPageBox.merge({
          calendarVisible: !page.calendarVisible,
          month: page.calendarVisible ? page.month : page.date.startOf('month'),
        }),
      );
    });

    const handleChangeMonth = useLatestCallback(async (month: Dayjs) => {
      dispatch(smartSchedulingPageBox.merge({ month }));
      if (!month.isAfter(dayjs(params.date, DATE_FORMAT_EXCHANGE))) {
        return;
      }
      for (let i = 1, max = month.daysInMonth() + 1; i < max; i++) {
        const date = month.date(i);
        if (!page.data[date.format(DATE_FORMAT_EXCHANGE)]) {
          await handleSearch(max - i, max - i, date.format(DATE_FORMAT_EXCHANGE), true);
          break;
        }
      }
    });

    // 查询上/下一个可用 slot
    // 先在当前 date + staffId 的 slots 中查找, 找到直接返回, 没找到进入循环
    const handleNavigate = useLatestCallback(async (direction: 'next' | 'prev') => {
      let page = store.select(smartSchedulingPageBox);
      // 在获取当前页数据前，确认下这些数据是全量的还是部分的
      if (page.date && !page.fullyLoadedMap[page.date.format(DATE_FORMAT_EXCHANGE)]) {
        await handleSearch(1, 1, page.date.format(DATE_FORMAT_EXCHANGE), true);
        page = store.select(smartSchedulingPageBox);
      }
      if (!page.date || !page.staffId || !addingCard) {
        return;
      }

      const next = direction === 'next';
      const step = next ? 1 : -1;

      // 从 page 取数据
      let date = page.date;
      const staffId = page.staffId;
      let data = page.data[date.format(DATE_FORMAT_EXCHANGE)];
      if (!data) {
        // should not go here, cuz at least it should be an empty array
        return;
      }

      let staffIndex = data.findIndex((s) => s.staffId === staffId);
      if (staffIndex === -1) {
        // 如果今天当前员工无空闲时间，向后找的话就从第 1 个员工开始找(staffIndex=0)，向前找的话就从最后 1 个员工开始找(staffIndex=length)
        staffIndex = next ? 0 : data.length || -1;
      } else {
        // 如果今天当前员工有空闲时间，就在当前页面上进行选时间点的操作
        const startTime = addingCard.startTime.hour() * 60 + addingCard.startTime.minute();
        const a = data[staffIndex]!.availableRange;
        for (let i = 0; i < a.length; i++) {
          const v = a[i];

          const slotStartTime = next ? v.startTime : i === 0 ? -Infinity : a[i - 1].endTime + 1;
          const slotEndTime = next ? (i === a.length - 1 ? Infinity : a[i + 1].startTime - 1) : v.endTime;

          if (startTime >= slotStartTime && startTime <= slotEndTime) {
            // 因为要取下一个或上一个 slot，所以 i 不能是最后一个或第一个，否则取不到下一个 available slot。
            if (next ? i < a.length - 1 : i > 0) {
              const slot = a[i + step];
              attachAddingCard([slot, void 0]);
              return;
            } else {
              // 如果当前 page.data[staffIndex] 没有可用的 slot 了，则进入下一个 page.data[staffIndex] 并进入 while 循环。
              staffIndex += step;
              break;
            }
          }
        }
      }

      // 能够走到这里，有两种可能
      // 1. 指定了 staff，他今天没空，此时应该在今天范围内遍历所有有空的 staff(从前到后或从后到前都可以，取决于点左箭头还是右箭头)
      // 2. 没指定 staff，当前暂时所选员工今天没空，此时应该在今天范围内看上/下一个 staff 的情况
      // 此时的 staffIndex 已经设定好了初始值，后面按照这两个逻辑遍历即可
      while (true) {
        // 在循环里可能会切换日期，此时可能存在还没拉取当天数据的情况（只会在搜索范围天数的前面和后面）
        if (!data) {
          if (!next) {
            // 走到了搜索范围之前的日期，该天已经过了，直接 alert
            await gotIt(
              `No time slot available since ${business.formatDate(date.add(1, 'day'))}.`,
              void 0,
              'Not available',
            );
            return;
          } else if (date.diff(page.date, 'day') >= 360) {
            // 走到搜索范围之后且在一年后的日期，太远了，直接 alert
            await gotIt(
              `No time slot available until ${business.formatDate(date.add(-1, 'day'))}.`,
              void 0,
              'Not available',
            );
            return;
          } else {
            // 走到搜索范围之后且在一年内的日期，重新拉取数据，走下一页
            await handleSearch(31, 360, date.format(DATE_FORMAT_EXCHANGE), true);
            await handleNavigate(direction);
            return;
          }
        }

        // staffIndex 大于等于 date.length 或小于 0，则代表当前 page.data 遍历完了，需要进去下一或上一天并重新开始遍历。
        if (next ? staffIndex >= data.length : staffIndex < 0) {
          // 切换日期
          date = date.add(step, 'day');
          data = page.data[date.format(DATE_FORMAT_EXCHANGE)];
          // 没有数据的话就直接走 while 循环下一步到前面的代码（alert 或 handleSearch/handleNavigate）
          if (!data) {
            continue;
          }
          // 有数据的话就重置 staffIndex 到第一个或最后一个，然后走 while 循环下一步到后面的代码
          // 有数据也可能不全，所以这里再确认下这些数据是全量的还是部分的
          if (!page.fullyLoadedMap[date.format(DATE_FORMAT_EXCHANGE)]) {
            await handleSearch(1, 1, date.format(DATE_FORMAT_EXCHANGE), true);
            page = store.select(smartSchedulingPageBox);
            data = page.data[date.format(DATE_FORMAT_EXCHANGE)] || [];
          }
          staffIndex = next ? 0 : data.length - 1;
          continue;
        }

        // 走到这里，说明此刻又有当天数据，又有合理的 staffIndex，那接下来就 check 他是否 available
        const count = data[staffIndex].availableRange.length;
        if (!count) {
          staffIndex += step;
          continue;
        }

        const slot = data[staffIndex].availableRange[next ? 0 : count - 1];
        dispatch(smartSchedulingPageBox.merge({ staffId: data[staffIndex].staffId, date }));
        attachAddingCard([slot, void 0]);
        break;
      }
    });

    // 切换日期, 需要:
    // 1. 如果未加载数据, 需要加载
    // 2. 如果指定 staff 当天没有 slot, 需要切换
    // 3. 如果当天没有 slot, 则设置成传入的 staff id
    const handleSelectDate = useLatestCallback(async (date: Dayjs) => {
      const currentDate = date.format(DATE_FORMAT_EXCHANGE);
      // 这里要做一个判断，如果当前 isFullyLoaded，则保持原状，否则发起一个新的请求，只请求一天并且只 dispatch 该天的 state
      if (!page.fullyLoadedMap[currentDate]) {
        await handleSearch(1, 1, currentDate, true);
        handleSelectDate(date);
        return;
      }
      const data = page.data[currentDate];
      if (!data) {
        // 禁止选择没有搜索过的日期
        return;
      }
      const staffId = data.some((s) => s.staffId === page.staffId) ? page.staffId : data[0]?.staffId || page.staffId;
      dispatch(smartSchedulingPageBox.merge({ date, staffId }));
      attachAddingCard();
    });

    const handleSelectStaff = useLatestCallback((staffId: number) => {
      dispatch(smartSchedulingPageBox.merge({ staffId }));
      attachAddingCard();
    });

    // 初始化数据
    useEffect(() => {
      dispatch(
        smartSchedulingPageBox.merge({
          calendarVisible: false,
          staffId: params.staffId,
          date: void 0,
          month: dayjs(params.date, DATE_FORMAT_EXCHANGE).startOf('month'),
          data: {},
        }),
      );
      let expired = false;
      // 首次查询一年范围内的数据, 找不到就设置成初始状态
      (async () => {
        const r = await handleSearch(31, 360, params.date, true);
        if (expired) {
          return;
        }
        if (!r.firstDate) {
          await gotIt(
            `No time slot available from ${business.formatDate(params.date)} to ${business.formatDate(
              dayjs(params.date, DATE_FORMAT_EXCHANGE).add(360, 'day'),
            )}.`,
            'Go back',
            'Not available',
          );
          navigation.goBack();
          return;
        }
        dispatch(
          smartSchedulingPageBox.merge({
            staffId: r.data[r.firstDate]![0].staffId,
            date: dayjs(r.firstDate, DATE_FORMAT_EXCHANGE),
          }),
        );
        attachAddingCard();
      })();
      return () => {
        expired = true;
      };
    }, [params]);

    // clean up page state
    useUnmount(() => dispatch(smartSchedulingPageBox.reset()));

    useHeaderTitle(<HeaderTitle onToggleCalendar={handleToggleCalendar} onChangeMonth={handleChangeMonth} />, []);

    useHeaderRightText(
      () => {
        if (addingCard && page.date && page.staffId) {
          params.onSelect?.({
            staffId: page.staffId,
            date: page.date.format(DATE_FORMAT_EXCHANGE),
            startTime: addingCard.startTime.hour() * 60 + addingCard.startTime.minute(),
            hour: addingCard.startTime.hour(),
            minute: addingCard.startTime.minute(),
          });
        }
      },
      page.date && page.staffId ? 'Next' : void 0,
    );

    if (!page.month) {
      return null;
    }

    return (
      <View style={VS_CONTAINER}>
        <ActionBar onNavigate={handleNavigate} onSelectStaff={handleSelectStaff} allowSelect={!params.staffId} />
        {!page.getSlotList()?.length && page.staffId && (
          <View style={[PH_20, PV_12, VS_ROW_CENTER, BG_DANGER_LIGHT]}>
            <IconIcWarningTriangleSvg width={16} height={16} style={MR_6} />
            <MoeText style={TS_14_DANGER}>{`${printName(staff)} has no time slot available on this day.`}</MoeText>
          </View>
        )}
        <CalendarDailyView
          ref={dailyView}
          channel="smartscheduling"
          ticketId={params.addingCard?.ticketId}
          staffId={page.staffId}
          onPress={handlePress}
          startAddress={startAddr}
          endAddress={endAddr}
          addingCard={addingCard}
          date={page.date?.format(DATE_FORMAT_EXCHANGE)}
        />
        {page.calendarVisible && (
          <CalendarModal
            onToggleCalendar={handleToggleCalendar}
            onSelectDate={handleSelectDate}
            onChangeMonth={handleChangeMonth}
            startDate={params.date}
          />
        )}
      </View>
    );
  },
);

export default SmartScheduling;

import { CreateStaffTrackingParams } from '@moego/api-web/moego/api/organization/v1/staff_tracking_api';
import { StaffTrackingClient } from '../../../utils/clients';
import { shouldUploadLocation } from '../../../utils/locationDeduplication';
import { action } from '../../../utils/store/action';
import {
  LOCATION_TRACKING_DATA_MAX_COUNT,
  LocationTrackingConfig,
  LocationTrackingDataItem,
  locationTrackingConfigBox,
  locationTrackingDataBox,
} from './LocationTracking.boxes';

/**
 * reset
 */

export const resetLocationTrackingData = action(async (dispatch, select) => {
  dispatch(locationTrackingDataBox.reset());
});

export const resetLocationTrackingConfig = action(async (dispatch, select) => {
  dispatch(locationTrackingConfigBox.reset());
});

/**
 * local update
 */

export const updateLocationTrackingConfig = action(async (dispatch, select, input: Partial<LocationTrackingConfig>) => {
  const config = select(locationTrackingConfigBox);
  dispatch(locationTrackingConfigBox.setState({ ...config, ...input }));
});

export const appendLocationTrackingData = action(async (dispatch, select, input: LocationTrackingDataItem) => {
  const prev = select(locationTrackingDataBox);
  const next = [...prev, input].slice(-LOCATION_TRACKING_DATA_MAX_COUNT); // keep recent records is enough
  dispatch(locationTrackingDataBox.setState(next));
});

/**
 * remote update
 */

export const uploadLocationTracking = action(
  async (dispatch, select, coordinate: CreateStaffTrackingParams['coordinate']) => {
    try {
      const locationData = {
        latitude: coordinate.latitude,
        longitude: coordinate.longitude,
        timestamp: Date.now(),
      };

      if (!shouldUploadLocation(locationData)) {
        return;
      }

      const result = await StaffTrackingClient.createStaffTracking(
        {
          coordinate,
        },
        { autoToast: false },
      );
      return result;
    } catch {
      return;
    }
  },
);

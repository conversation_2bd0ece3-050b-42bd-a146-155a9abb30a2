import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { cloneDeep, intersection, unionBy } from 'lodash';
import React, { type ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { SectionList, StatusBar, TouchableOpacity, View, useWindowDimensions } from 'react-native';
import IconIconCrownSvg from '../../../../assets/images/icon-crown.svg';
import { Condition } from '../../../components/Condition';
import { MoeTag } from '../../../components/MoeTag';
import { MoeText } from '../../../components/MoeText';
import { SafeBottom } from '../../../components/SafeView';
import { showUpgradeModal } from '../../../components/UpgradeModal/UpgradeModal.boxes';
import { FloatingActionButton } from '../../../components/buttons/FloatingActionButton';
import { SaveButton } from '../../../components/buttons/SaveButton';
import { SearchInput } from '../../../components/inputs/SearchInput';
import { ScrollTabs } from '../../../components/tabs/ScrollTabs';
import { createLazyStackScreen } from '../../../utils/createScreen';
import { useASAndMultiLocation } from '../../../utils/hooks/useASAndMultiLocation';
import { useBool } from '../../../utils/hooks/useBool';
import { useHeaderRightText, useHeaderTitle } from '../../../utils/hooks/useEditMenu';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { useLoad } from '../../../utils/hooks/useLoad';
import { useSetState } from '../../../utils/hooks/useSetState';
import { useStackNavigation } from '../../../utils/navigation';
import { ReportActionName } from '../../../utils/report/eventType';
import { reportData } from '../../../utils/report/reportData';
import { useThunkDispatch } from '../../../utils/store/action';
import { useMoeStore } from '../../../utils/store/createMoeStore';
import { useListSelector } from '../../../utils/store/selector';
import { ID_INVALID, isNormal } from '../../../utils/store/utils';
import { COL_GRAY_DARK, COL_ORANGE } from '../../../utils/style/consts';
import { VS_CONTAINER, VS_ROW, VS_ROW_TOP } from '../../../utils/style/misc';
import { H_20, H_32 } from '../../../utils/style/preset/height';
import { ML_16, MR_4, MT_16 } from '../../../utils/style/preset/margin';
import { PH_20, PT_20 } from '../../../utils/style/preset/padding';
import { TS_18 } from '../../../utils/style/preset/text';
import { cardStyle, textStyle } from '../../../utils/style/utils';
import { toast } from '../../../utils/toast';
import { toggleItem } from '../../../utils/utils';
import { selectBusiness } from '../../Business/store/business.selectors';
import { selectPricingPermission } from '../../Business/store/company.selectors';
import { useEnableFeature } from '../../Common/store/featureEnable.hooks';
import { getCustomizedServiceList } from '../../Pet/store/petCustomizedSaved.actions';
import { getPetLastServices } from '../../Pet/store/pet_services.actions';
import { selectPetLastService } from '../../Pet/store/pet_services.selectors';
import { selectCurrentPermissions } from '../../Staff/store/role.selectors';
import {
  PATH_INACTIVE_SERVICE_LIST,
  PATH_SERVICE_CATEGORY_LIST,
  type PATH_SERVICE_LIST,
  type SingleSelectServiceListParams,
} from '../Service.api';
import { getServiceList } from '../store/actions/public/service.actions';
import { getServiceChargeList } from '../store/actions/public/serviceCharge.actions';
import { SERVICE_CHARGE_TYPE, ServiceType } from '../store/category.boxes';
import { selectBusinessServiceCategories } from '../store/category.selectors';
import { ServiceRecord, categoryServiceListBox, serviceMapBox } from '../store/service.boxes';
import { selectAllActiveServices, selectServiceChargeList } from '../store/service.selectors';
import { selectedToArray } from './ServiceList.options';
import { ToggleApplicableService } from './ToggleApplicableService';
import { AddServiceMenu } from './components/AddServiceMenu';
import { SectionHeader } from './components/SectionHeader';
import { SectionItem, ServiceChargeSectionItem } from './components/SectionItem';
import { useApplicableServiceList } from './hooks/useApplicableService';
import { useGetServiceChargeView } from './hooks/useGetServiceChargeView';

type SelectedIdsWithTypeParams = { id: number; serviceType?: number };

export const ServiceList = createLazyStackScreen<Parameters<(typeof PATH_SERVICE_LIST)['push']>[0]>(
  (params, navigation, _route) => {
    const store = useMoeStore();
    const dispatch = useThunkDispatch();
    const { petId, customerId, isDisableStaffOverride } = params as SingleSelectServiceListParams;
    const { width } = useWindowDimensions();
    const { isViewed, view } = useGetServiceChargeView();

    const selected = selectedToArray(params);
    const selectedIdsWithType: SelectedIdsWithTypeParams[] = selected.map((id) => ({
      id,
      serviceType: store.select(serviceMapBox.mustGetItem(id)).type,
    }));
    const selectable = params.mode === 'multiple_select' || params.mode === 'single_select';
    const { enable } = useEnableFeature('service_charge_enable');
    const loadingClientSavedInfo = useBool(false);
    const [state, setState] = useSetState(() => {
      let typeList = ServiceType.values.map((type) => ({
        label: (ServiceType.mapLabels[type] + 's') as ReactNode,
        value: type,
      }));
      if (!selectable && enable) {
        typeList = typeList.concat({ label: 'Service Charges', value: SERVICE_CHARGE_TYPE });
      }
      return {
        addMenuVisible: false,
        type: ServiceType.Service,
        typeList,
        keyword: '',
        selected: selected,
        selectedIdsWithType,
        originSelected: [...selected],
        selectable,
      };
    });
    const { isApplicableOpen, applicableIdsForOpen, isLoading, filterApplicable, fetchApplicable } =
      useApplicableServiceList({
        petId: petId?.toString(),
        customerId,
        selectedServiceIds: state.selected,
        serviceType: state.type,
        serviceItemType: ServiceItemType.GROOMING,
      });
    const enableFilterApplicable =
      isNormal(petId) &&
      isNormal(customerId) &&
      params.mode === 'multiple_select' &&
      state.type !== SERVICE_CHARGE_TYPE;

    /**
     * 临时方案：在编辑 service 的模式下，如果已存在了不可选的 service，就屏蔽掉过滤掉逻辑，只能添加不能删除
     * 长期方案需要优化这里的交互逻辑，涉及到比较大的重构，需要慎重考虑
     * 相关讨论：https://moegoworkspace.slack.com/archives/C070M3QGJTV/p1732587462185339
     */
    const isServiceListEditMode = params.mode === 'multiple_select' && state.originSelected.length > 0;
    const isOriginHasNotApplicable = useMemo(
      () => state.originSelected.some((id) => !applicableIdsForOpen.includes(id)),
      [state.originSelected, applicableIdsForOpen],
    );
    const isOnlyAddMode = isServiceListEditMode && isOriginHasNotApplicable;
    const notApplicableServiceIdsWithType = useMemo(
      () => selectedIdsWithType.filter((item) => !applicableIdsForOpen.includes(item.id)),
      [selectedIdsWithType, applicableIdsForOpen],
    );

    const isServiceCharge = state.type === SERVICE_CHARGE_TYPE;
    const serviceTypeLabel = isServiceCharge ? 'service charges' : ServiceType.mapLabels[state.type] + 's';
    const applicableServiceIdsFilter = useLatestCallback((service: SelectedIdsWithTypeParams[]) => {
      // 过滤当前tab下的不可选service
      return service.filter((item) => item.serviceType !== state.type || filterApplicable(item.id));
    });
    // enable 的值可能是异步获取的，当获取到 enable 值之后，需要看情况刷新 typeList
    useEffect(() => {
      if (!selectable && enable) {
        const label = (
          <View style={VS_ROW}>
            {isViewed ? null : <MoeTag label="New" theme="orange" containerStyle={[MR_4, H_20]} />}
            <MoeText style={textStyle(14, isServiceCharge ? COL_ORANGE : COL_GRAY_DARK, { lineHeight: 40 })}>
              Service Charges
            </MoeText>
          </View>
        );
        const typeItem = {
          label,
          value: SERVICE_CHARGE_TYPE,
        };

        // 不存在则直接插入，存在则修改。
        if (!state.typeList.find((i) => i.value === SERVICE_CHARGE_TYPE)) {
          setState({
            typeList: state.typeList.concat(typeItem),
          });
        } else {
          const index = state.typeList.findIndex((type) => type.value === SERVICE_CHARGE_TYPE);
          const nextList = cloneDeep(state.typeList);
          nextList.splice(index, 1);
          setState({
            typeList: nextList.concat(typeItem),
          });
        }
      }
    }, [selectable, enable, isServiceCharge, isViewed]);

    const [
      business,
      categoryList,
      serviceListMap,
      lastAllServiceList,
      permissions,
      allActiveServices,
      serviceChargeList,
      pricingPermission,
    ] = useListSelector(
      selectBusiness(),
      selectBusinessServiceCategories(state.type),
      categoryServiceListBox,
      selectPetLastService((params as SingleSelectServiceListParams).petId || ID_INVALID, state.type),
      selectCurrentPermissions(),
      selectAllActiveServices(),
      selectServiceChargeList(true),
      selectPricingPermission(),
    );
    const haveServiceChargePermission = pricingPermission.enable.has('serviceCharge');

    // last appointments中过滤出当前状态是active的services
    const lastOnlyActiveServiceList = useMemo(() => {
      const activeServices = allActiveServices.map((i) => i.serviceId);
      return lastAllServiceList.filter((serviceId) => activeServices.includes(serviceId));
    }, [lastAllServiceList, allActiveServices]);

    const handleServiceSelect = useLatestCallback(async (serviceId: number, done?: boolean) => {
      switch (params.mode) {
        case 'single_select':
          params.onSelect && params.onSelect(serviceId);
          break;
        case 'multiple_select':
          if (done) {
            let finalSelected = state.selected;
            // 多 service 编辑模式，取并集，不支持删除
            if (isOnlyAddMode) {
              finalSelected = [...new Set([...state.originSelected, ...state.selected])];
            }
            params.onSelect && params.onSelect(finalSelected);
          } else {
            const service = store.select(serviceMapBox.mustGetItem(serviceId));
            const isBundleService = !!service?.bundleServiceIdList?.length;
            let bundleServiceIds: number[] = [];

            if (isOnlyAddMode && state.originSelected?.includes(serviceId)) break;

            const toggledItemWithType = { id: serviceId, serviceType: state.type };
            const idsWithType = toggleItem(state.selectedIdsWithType, toggledItemWithType, 'id');

            if (
              idsWithType.length > state.selectedIdsWithType.length &&
              isBundleService &&
              state.type === ServiceType.Service
            ) {
              // 仅在添加 service 时，判断是否要附加 bundle service
              try {
                toast('loading', 'Fetching bundle add-ons...');
                const applicableIds = await fetchApplicable({
                  onlyAvailable: true,
                  serviceType: ServiceType.Addon,
                  selectedServiceIds: idsWithType.map((item) => item.id.toString()),
                  dispatchState: false,
                });
                bundleServiceIds = [...intersection(applicableIds, service!.bundleServiceIdList!.map(Number))];
              } finally {
                toast();
              }
            }

            // 需过滤当前tab下不可选的service
            const applicableSelectedIdsWithType = unionBy(
              applicableServiceIdsFilter(idsWithType),
              bundleServiceIds.map((id) => ({ id, serviceType: ServiceType.Addon })),
              'id',
            );
            setState({
              selected: applicableSelectedIdsWithType.map((item) => item.id),
              selectedIdsWithType: applicableSelectedIdsWithType,
            });
          }
          break;
        default:
          break;
      }
    });

    // 如果切换了show applicable services，需要过滤当前tab下不可选的service
    useEffect(() => {
      if (isLoading) {
        return;
      }

      let applicableSelectedIdsWithType = applicableServiceIdsFilter(state.selectedIdsWithType);
      // 当 applicable 关闭时，only add mode 拼接上不可选的 service，避免视觉上被删除
      if (!isApplicableOpen.value && isOnlyAddMode) {
        applicableSelectedIdsWithType = [
          ...new Set(applicableSelectedIdsWithType.concat(notApplicableServiceIdsWithType)),
        ];
      }

      setState({
        selected: applicableSelectedIdsWithType.map((item) => item.id),
        selectedIdsWithType: applicableSelectedIdsWithType,
      });
    }, [isApplicableOpen.value, isLoading]);

    const handleChangeType = (index: number) => {
      const type = state.typeList[index].value;
      if (type === SERVICE_CHARGE_TYPE) {
        reportData(ReportActionName.MobileFeeAck);
        view();
      }
      setState({ type });
    };

    const tabIndex = state.typeList.findIndex((item) => item.value === state.type);

    const searchInputWidth = width - (enableFilterApplicable ? 140 : 160);
    useHeaderTitle(
      <SearchInput
        value={state.keyword}
        theme="gray"
        autoFocus={false}
        placeholder={`Find ${serviceTypeLabel.toLowerCase()} by name`}
        containerStyle={{
          width: searchInputWidth + (isServiceCharge ? 40 : 0),
          marginTop: 10,
          position: 'absolute',
          left: -(searchInputWidth / 2 + 20),
        }}
        onChangeText={(keyword) => {
          setState({ keyword });
        }}
      />,
    );

    const load = useLoad(() => {
      if (isServiceCharge) {
        return dispatch(getServiceChargeList({ isActive: true }));
      }
      return dispatch(getServiceList(state.type));
    }, [state.type]);

    const isLoadingData = load.isLoading(categoryList) || isLoading || loadingClientSavedInfo.value;

    const getHeaderRightText = () => {
      if (isServiceCharge) return undefined;
      if (!state.selectable) return 'Categories';
      return state.originSelected.length ? 'Confirm' : 'Add';
    };

    useHeaderRightText(
      () => {
        if (state.selectable) {
          handleServiceSelect(ID_INVALID, true);
        } else {
          navigation.dispatch(PATH_SERVICE_CATEGORY_LIST.navigate({ type: state.type }));
        }
      },
      isLoadingData ? undefined : getHeaderRightText(),
    );

    useEffect(() => {
      const petId = (params as SingleSelectServiceListParams).petId;
      const customerId = (params as SingleSelectServiceListParams).customerId;
      if (state.selectable && petId) {
        dispatch(getPetLastServices(petId));
      }

      if (state.selectable && customerId && petId) {
        loadingClientSavedInfo.open();
        dispatch(getCustomizedServiceList(petId, customerId)).finally(() => loadingClientSavedInfo.close());
      }
    }, [state.selectable]);

    const sections = useMemo(() => {
      const originList = [-state.type].concat(categoryList).map((categoryId) => ({
        categoryId,
        data: serviceListMap.getList(ServiceRecord.listKey(business.id, state.type, categoryId)),
      }));

      const finnalList =
        lastOnlyActiveServiceList.length > 0
          ? [{ categoryId: undefined, data: lastOnlyActiveServiceList }, ...originList]
          : originList;

      return finnalList
        .map((item) => {
          const { categoryId, data } = item;
          return {
            categoryId,
            data: enableFilterApplicable ? data.filter(filterApplicable) : data,
          };
        })
        .filter((section) => section.data.length !== 0);
    }, [
      categoryList,
      serviceListMap,
      business.id,
      state.type,
      lastOnlyActiveServiceList,
      filterApplicable,
      enableFilterApplicable,
    ]);

    const isASAndMultiLocation = useASAndMultiLocation();

    const [sectionListRenderKey, setSectionListRenderKey] = useState(0);
    useEffect(() => {
      // 这个页面存在较多的竞态请求，与 Calling Modal 共存时存在渲染问题，会导致无法滚动
      // 这里在数据请求结束时强制重新加载与渲染 SectionList
      if (!isLoadingData) {
        setSectionListRenderKey(Math.floor(Math.random() * 10000));
      }
    }, [isLoadingData]);

    const renderContent = () => {
      if (isServiceCharge) {
        return (
          <SectionList
            key={sectionListRenderKey}
            contentContainerStyle={[PH_20, PT_20]}
            stickySectionHeadersEnabled={false}
            sections={[{ data: serviceChargeList }]}
            renderItem={({ item }) => {
              return (
                <ServiceChargeSectionItem
                  key={item.id}
                  id={item.id}
                  name={item.name}
                  price={item.price}
                  keyword={state.keyword}
                />
              );
            }}
            keyExtractor={(item) => item.id}
            onRefresh={load}
            refreshing={isLoadingData}
            ListFooterComponent={
              haveServiceChargePermission ? (
                <GoInactiveService title="View inactive service charges" />
              ) : (
                <ServiceChargeUpSellCard />
              )
            }
          />
        );
      }

      return (
        <SectionList
          key={sectionListRenderKey}
          contentContainerStyle={[PH_20, PT_20]}
          stickySectionHeadersEnabled={false}
          sections={sections}
          renderSectionHeader={({ section }) => {
            if (section.categoryId === undefined) {
              return (
                <SectionHeader
                  section={section}
                  title="LAST APPOINTMENT"
                  type={state.type}
                  // 如果是 multi-location 用户，这里不让 sort service
                  sortable={!state.selectable && !isASAndMultiLocation}
                />
              );
            }
            // 如果是 multi-location 用户，这里不让 sort service
            return (
              <SectionHeader
                section={section}
                type={state.type}
                sortable={!state.selectable && !isASAndMultiLocation}
              />
            );
          }}
          renderItem={({ item }) => {
            return (
              <SectionItem
                serviceId={item}
                keyword={state.keyword}
                type={state.type}
                selected={state.selected}
                selectable={state.selectable}
                onSelect={handleServiceSelect}
                isDisableStaffOverride={isDisableStaffOverride}
                petId={(params as SingleSelectServiceListParams).petId}
              />
            );
          }}
          keyExtractor={(item) => item.toString()}
          onRefresh={load}
          refreshing={isLoadingData}
          ListFooterComponent={<GoInactiveService title="View inactive services" />}
        />
      );
    };

    const handleActionPress = useLatestCallback(() => {
      setState({ addMenuVisible: true });
    });

    const handleAddServiceMenu = useCallback(() => setState({ addMenuVisible: false }), [setState]);

    return (
      <View style={VS_CONTAINER}>
        <StatusBar barStyle={'dark-content'} backgroundColor={'white'} />
        <ScrollTabs
          tabs={state.typeList.map((i) => ({ title: i.label, key: i.value }))}
          onChange={handleChangeType}
          index={tabIndex}
        />
        <Condition if={enableFilterApplicable}>
          <ToggleApplicableService
            serviceType={state.type}
            checked={isApplicableOpen.value}
            onChange={isApplicableOpen.as}
          />
        </Condition>
        {renderContent()}
        {permissions.has('addService') ? (
          <>
            <FloatingActionButton onPress={handleActionPress} />
            <AddServiceMenu
              visible={state.addMenuVisible}
              onClose={handleAddServiceMenu}
              onSelect={handleAddServiceMenu}
            />
          </>
        ) : null}
        <SafeBottom />
      </View>
    );
  },
);

const GoInactiveService = ({ title }: { title: string }) => {
  const navigation = useStackNavigation();
  return (
    <TouchableOpacity
      onPress={() => {
        navigation.dispatch(PATH_INACTIVE_SERVICE_LIST.navigate({}));
      }}
    >
      <MoeText style={[TS_18, { textAlign: 'center', paddingVertical: 50 }]}>{title}</MoeText>
    </TouchableOpacity>
  );
};

const ServiceChargeUpSellCard = () => {
  const dispatch = useThunkDispatch();
  return (
    <View style={cardStyle()}>
      <View style={VS_ROW_TOP}>
        <IconIconCrownSvg />
        <MoeText style={[ML_16, textStyle(14, COL_GRAY_DARK, { fontWeight: '400' })]}>
          Upgrade to manage additional charges with simplicity
        </MoeText>
      </View>
      <SaveButton
        text="Upgrade"
        style={[MT_16, H_32]}
        shadow={false}
        onPress={() => dispatch(showUpgradeModal('serviceCharge'))}
      />
    </View>
  );
};

export default ServiceList;

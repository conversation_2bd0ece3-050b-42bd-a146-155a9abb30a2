import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import {
  ServiceChargeAutoApplyCondition,
  ServiceChargeAutoApplyStatus,
  ServiceChargeAutoApplyTimeType,
} from '@moego/api-web/moego/models/order/v1/service_charge_model';
import dayjs from 'dayjs';
import { ANY } from 'monofile-utilities/lib/consts';
import React, { useEffect, useMemo, useState } from 'react';
import { FlatList, ScrollView, StatusBar, TouchableOpacity, View } from 'react-native';
import IconIcDisclosureIndicatorBlackSvg from '../../../../assets/images/ic-disclosure-indicator-black.svg';
import { Condition } from '../../../components/Condition';
import { MoeText } from '../../../components/MoeText';
import { SafeBottom } from '../../../components/SafeView';
import { BlockButton } from '../../../components/buttons/BlockButton';
import { RadioButton } from '../../../components/buttons/RadioButton';
import { SaveButton } from '../../../components/buttons/SaveButton';
import { MoeInput } from '../../../components/inputs/MoeInput';
import { ASConfirmModal } from '../../../components/modal/ASConfirmModal';
import { UpdateConfirmModal } from '../../../components/modal/UpdateConfirmModal';
import { useASDoubleConfirmValue } from '../../../components/modal/hooks/useASDoubleConfirmValue';
import { HLine } from '../../../components/ui/Line';
import { MoeSwitch } from '../../../components/ui/MoeSwitch';
import { dangerConfirmAsync, gotIt } from '../../../utils/alert';
import { createEnum } from '../../../utils/createEnum';
import { createLazyStackScreen } from '../../../utils/createScreen';
import { useAsyncEffect } from '../../../utils/hooks/react';
import { useASAndMultiLocation } from '../../../utils/hooks/useASAndMultiLocation';
import { useBool } from '../../../utils/hooks/useBool';
import { useHeaderTitle } from '../../../utils/hooks/useEditMenu';
import { RE_INPUT_ALLOW_NEGATIVE_AMOUNT, createUseInput } from '../../../utils/hooks/useInput';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { useThunkDispatch } from '../../../utils/store/action';
import { useMoeStore } from '../../../utils/store/createMoeStore';
import { useListSelector } from '../../../utils/store/selector';
import { COL_BLACK, COL_BLACK_LIGHT } from '../../../utils/style/consts';
import { VS_CENTER, VS_CONTAINER, VS_ROW, VS_ROW_BETWEEN } from '../../../utils/style/misc';
import { BG_WHITE } from '../../../utils/style/preset/backgroundColor';
import { BR_0, BR_8, BR_BL_0, BR_BR_0, BR_TL_0, BR_TR_0 } from '../../../utils/style/preset/border';
import { FG_DANGER, FG_GRAY_DARK } from '../../../utils/style/preset/foregroundColor';
import { H_48, H_60 } from '../../../utils/style/preset/height';
import { MB_12, MB_20, MG_10, ML_4, MT_20 } from '../../../utils/style/preset/margin';
import { PB_20, PH_10, PH_20, PL_10, PL_20, PL_8, PR_20, PT_0, PT_20, PV_0 } from '../../../utils/style/preset/padding';
import { TS_14_GRAY, TS_16_BLACK_DARK, TS_16_GRAY } from '../../../utils/style/preset/text';
import { cardStyle, textStyle, viewStyle } from '../../../utils/style/utils';
import { loading, toast, useToastCallback } from '../../../utils/toast';
import { selectBusiness } from '../../Business/store/business.selectors';
import { selectBDFeatureEnable } from '../../Business/store/company.selectors';
import { getTaxList } from '../../Business/store/tax.actions';
import { taxMapBox } from '../../Business/store/tax.boxes';
import { selectBusinessTaxes } from '../../Business/store/tax.selectors';
import { useGetASDeleteText } from '../../Pet/hooks/useGetASDeleteText';
import { type PATH_SERVICE_CHARGE_EDIT, PATH_SERVICE_TAX_SELECT } from '../Service.api';
import {
  addServiceCharge,
  removeServiceCharge,
  updateServiceCharge,
} from '../store/actions/private/serviceCharge.actions';
import {
  AutomationConditions,
  AutomationStatusLabel,
  type ServiceChargeRecord,
  serviceChargeMapBox,
} from '../store/service.boxes';

export const CareTypes = createEnum({
  boarding: [ServiceItemType.BOARDING, 'Boarding'],
  daycare: [ServiceItemType.DAYCARE, 'Daycare'],
});

const useMountAndPreset = (input: ReturnType<typeof useInput>, id?: string) => {
  const dispatch = useThunkDispatch();
  const store = useMoeStore();
  useAsyncEffect(async () => {
    await dispatch(getTaxList());
    if (id) {
      return;
    }
    const taxList = store.select(selectBusinessTaxes());
    if (taxList.length && !input.get('taxId')) {
      input.set('taxId', taxList[0]);
    }
  }, []);
};

const useInput = createUseInput<{
  name: string;
  price: string;
  taxId: any;
  description: string;
  isActive: boolean;
  applyUpcomingAppt: boolean;
  autoApplyStatus: ServiceChargeAutoApplyStatus;
  autoApplyCondition?: ServiceChargeAutoApplyCondition;
  autoApplyTime?: number;
  serviceItemTypes: ServiceItemType[];
  autoApplyTimeType: ServiceChargeAutoApplyTimeType;
}>({
  name: { required: true, trim: true, placeholder: 'Service charge name', initialValue: '' },
  price: {
    required: true,
    trim: true,
    pattern: RE_INPUT_ALLOW_NEGATIVE_AMOUNT,
    label: 'Price',
    placeholder: '0.00',
    initialValue: '',
    min: 0.01,
  },
  taxId: {
    required: true,
    placeholder: 'Tax',
    initialValue: ANY,
  },
  description: { initialValue: '', placeholder: 'description' },
  isActive: { initialValue: true },
  applyUpcomingAppt: { initialValue: false },
  autoApplyStatus: { initialValue: ServiceChargeAutoApplyStatus.AUTO_APPLY_DISABLED },
  autoApplyCondition: { initialValue: ServiceChargeAutoApplyCondition.UNSPECIFIED },
  autoApplyTime: { initialValue: 0 },
  serviceItemTypes: { initialValue: [] },
  autoApplyTimeType: { initialValue: ServiceChargeAutoApplyTimeType.CERTAIN_TIME },
});

const useApplyUpcomingAppt = (input: ReturnType<typeof useInput>, service?: ServiceChargeRecord) => {
  const values = input.values();
  const disable = useMemo(() => {
    if (service?.id && service) {
      const presetPrice = (service?.price || '0.00') + '';
      if (values.price !== presetPrice) return false;
      if (values.taxId !== service?.taxId) return false;
      if (values.autoApplyStatus !== service?.autoApplyStatus) return false;
    } else {
      if (values.autoApplyStatus !== ServiceChargeAutoApplyStatus.AUTO_APPLY_DISABLED) return false;
    }
    return true;
  }, [values.price, values.taxId, values.isActive, service, values.autoApplyStatus]);

  useEffect(() => {
    if (disable) {
      input.set('applyUpcomingAppt', false);
    }
  }, [disable]);

  return disable;
};

export const ServiceChargeEdit = createLazyStackScreen<Parameters<(typeof PATH_SERVICE_CHARGE_EDIT)['push']>[0]>(
  ({ id = '' }, navigation) => {
    const dispatch = useThunkDispatch();
    const isEdit = !!id;
    // 在 service id 不存在的时候使用 undefined 而不使用 serviceMapBox.mustGetItem(id) 返回默认 record 值是因为：
    // 使用 undefined 可以使用到 useInput 里面的 initialValue（特别是 categoryId 为 0 是 unCategory）。
    const [service, isBD] = useListSelector(
      id ? serviceChargeMapBox.mustGetItem(id) : undefined,
      selectBDFeatureEnable(),
    );
    const isASAndMultiLocation = useASAndMultiLocation();
    const [noShowAgain] = useASDoubleConfirmValue('updateServiceCharge');
    const modalVisible = useBool();
    const deleteText = useGetASDeleteText();
    const [applyUpcomingConfig, setApplyUpcomingConfig] = useState<{
      visible: boolean;
      action?: 'update' | 'delete' | 'close';
    }>();
    const presetPrice = (service?.price || '0.01') + '';
    const input = useInput({
      ...service,
      price: presetPrice,
    });
    const values = input.values();
    const [business, tax] = useListSelector(selectBusiness(), taxMapBox.getItem(input.get('taxId')));
    const saveText = !id ? 'Add' : 'Save';
    useHeaderTitle(id ? `Edit service charge` : `Add service charge`);
    const automationOptions = AutomationStatusLabel.values.filter(
      (item) => isBD || item !== ServiceChargeAutoApplyStatus.AUTO_APPLY_ENABLED_WITH_CONDITION,
    );
    const isCurrentApplyAutomation = values.autoApplyStatus !== ServiceChargeAutoApplyStatus.AUTO_APPLY_DISABLED;
    const isPreApplyAutomation = service?.autoApplyStatus !== ServiceChargeAutoApplyStatus.AUTO_APPLY_DISABLED;
    const { renderConditionDesc } = AutomationConditions.mapLabels[values.autoApplyCondition!] || {};
    const isCertainTime = values?.autoApplyTimeType === ServiceChargeAutoApplyTimeType.CERTAIN_TIME;
    const isBizHour = values?.autoApplyTimeType === ServiceChargeAutoApplyTimeType.BUSINESS_HOUR;
    const caretTypeStr = values?.serviceItemTypes?.map((type) => CareTypes.mapLabels[type]).join(' ') || '';
    const bizHour = (isBizHour && values?.autoApplyTime?.toString()) || '';
    const certainTime = (isCertainTime && business.formatTime(dayjs().setMinutes(values?.autoApplyTime || 0))) || '';

    const handleChangeAutoApplyStatus = (status: ServiceChargeAutoApplyStatus) => {
      input.set('autoApplyStatus', status);
      if (status === ServiceChargeAutoApplyStatus.AUTO_APPLY_ENABLED) {
        input.set('serviceItemTypes', [ServiceItemType.GROOMING]);
      } else {
        input.set('serviceItemTypes', []);
      }
    };

    const handleSave = async () => {
      const err = input.validate();
      if (err) {
        gotIt(err[1]);
        return;
      }
      try {
        const data = { ...values, price: +values.price };

        // 非bd用户不使用这两个参数
        if (!isBD) {
          data.autoApplyCondition = undefined;
          data.autoApplyTime = undefined;
        }

        // 如果 autoApplyCondition 不存在或者是默认值 0，说明用户并没有选这个选项
        // 后端不允许传 0，所以这里需要置空
        if (!data.autoApplyCondition) {
          data.autoApplyCondition = undefined;
        }

        loading();
        if (id) {
          await dispatch(updateServiceCharge(id, data));
          await toast.updated();
        } else {
          await dispatch(addServiceCharge(data));
          await toast.added();
        }
      } catch {
        toast();
      }
      input.preventLeave(false);
      navigation.goBack();
    };

    const handleSaveConfirm = useLatestCallback(async () => {
      const err = input.validate();
      if (err) {
        gotIt(err[1]);
        return;
      }

      /**
       * 弹窗逻辑：
       * 1. 删除service charge：status为active时，才出弹窗
       * 2. 新增service charge：status为active 并且 auto apply 为on时才出弹窗
       *
       * 【以下几点前提：price、tax、business、auto apply有改动】：
       * 3. status 总是为inactive时：不出弹窗
       * 4. status 总为active时：必出弹窗
       * 5. status 从 active切换为inactive时：必出弹窗
       * 6. status从inactive切换active时：auto apply 为 on 才出弹窗
       *
       */
      // 新建的 service charge （在mobile端，新建的service charge status 必为 active ）  auto apply 为 on 时才出弹窗
      if (!isEdit) {
        if (isCurrentApplyAutomation && values.isActive) {
          setApplyUpcomingConfig?.({ visible: true, action: 'update' });
          return;
        } else {
          setApplyUpcomingConfig?.({ visible: false });
        }
      } else if (checkApplyUpcomingAppt) {
        //  status没有状态切换，总是为inactive时：不出弹窗
        if (!values.isActive && !service?.isActive) {
          setApplyUpcomingConfig?.({ visible: false });
        } else if (values.isActive && service?.isActive) {
          //status 总为active时：必出弹窗
          // 如果 auto apply 从 on 到 off，出 remove 弹窗
          if (isPreApplyAutomation && !isCurrentApplyAutomation) {
            setApplyUpcomingConfig({ visible: true, action: 'close' });
          } else {
            setApplyUpcomingConfig({ visible: true, action: 'update' });
          }
          return;
        } else if (service?.isActive && !values.isActive) {
          // status 从 active切换为inactive时：必出弹窗,且为 remove 弹窗
          setApplyUpcomingConfig({ visible: true, action: 'close' });
          return;
        } else if (!service?.isActive && values.isActive && isCurrentApplyAutomation) {
          // status从inactive切换active时：auto apply 为 on 才出弹窗
          setApplyUpcomingConfig({ visible: true, action: 'update' });
          return;
        } else {
          setApplyUpcomingConfig?.({ visible: false });
        }
      } else if (isASAndMultiLocation && !noShowAgain && isEdit) {
        modalVisible.open();
        return;
      }
      await handleSave();
    });

    const handleDelete = useToastCallback(async () => {
      try {
        toast.loading();
        await dispatch(removeServiceCharge(id, values.applyUpcomingAppt));
        await toast.deleted();
      } catch {
        toast();
      }
      navigation.goBack();
    });

    useMountAndPreset(input, id);
    const checkApplyUpcomingAppt = !useApplyUpcomingAppt(input, service);

    const titleStyle = textStyle(16, COL_BLACK, {
      marginLeft: 4,
      marginBottom: 12,
    });

    return (
      <View style={VS_CONTAINER}>
        <StatusBar barStyle="dark-content" backgroundColor="white" />
        <ScrollView style={PH_10}>
          <View style={MT_20}>
            <MoeText style={[titleStyle, ML_4, MB_12]}>Name</MoeText>
            <View
              style={cardStyle({
                ...PV_0,
                ...PL_10,
              })}
            >
              <MoeInput containerStyle={[PT_0]} contentStyle={[PL_8]} {...input.field('name')} error={void 0} />
            </View>
          </View>

          <View style={MT_20}>
            <MoeText style={[titleStyle, ML_4, MB_12]}>Price & Duration</MoeText>
            <MoeInput
              inline
              containerStyle={[PT_0]}
              contentStyle={[PL_20, PR_20, BR_BL_0, BR_BR_0]}
              style={FG_GRAY_DARK}
              keyboardType="numeric"
              {...input.amountField('price', business.currencySymbol)}
              error={void 0}
            />
            <HLine />
            <TouchableOpacity
              style={cardStyle({
                ...VS_ROW_BETWEEN,
                ...H_60,
                ...PH_20,
                ...BR_TL_0,
                ...BR_TR_0,
              })}
              onPress={useLatestCallback(() => {
                navigation.dispatch(
                  PATH_SERVICE_TAX_SELECT.push({
                    selected: input.get('taxId'),
                    onSelect: (taxId) => {
                      input.set('taxId', taxId);
                    },
                  }),
                );
              })}
            >
              <MoeText style={textStyle(16, COL_BLACK)}>{tax ? `${tax.taxName} - ${tax.printRate()}` : ''}</MoeText>
              <IconIcDisclosureIndicatorBlackSvg color={COL_BLACK_LIGHT} />
            </TouchableOpacity>
          </View>

          <View style={MT_20}>
            <MoeText style={[titleStyle, ML_4, MB_12]}>Description</MoeText>
            <MoeInput containerStyle={[PT_0]} {...input.field('description')} />
          </View>
          {id ? (
            <View style={MT_20}>
              <MoeText style={[titleStyle, ML_4, MB_12]}>Active</MoeText>
              <View
                style={cardStyle({
                  ...VS_ROW_BETWEEN,
                  height: 60,
                  width: '100%',
                  paddingHorizontal: 20,
                })}
              >
                <MoeText style={textStyle(16, COL_BLACK)}>Active</MoeText>
                <MoeSwitch value={input.get('isActive')} onValueChange={(active) => input.set('isActive', active)} />
              </View>
            </View>
          ) : null}
          <View style={MT_20}>
            <MoeText style={[titleStyle, ML_4, MB_12]}>Auto-apply during checkout</MoeText>
            <FlatList
              scrollEnabled={false}
              data={automationOptions}
              renderItem={({ item, index }) => {
                const isFirst = index === 0;
                const isLast = index === automationOptions?.length - 1;
                const { autoApplyStatus, autoApplyCondition } = values;
                const selectedCertainCondition =
                  item === ServiceChargeAutoApplyStatus.AUTO_APPLY_ENABLED_WITH_CONDITION && item === autoApplyStatus;

                return (
                  <>
                    <TouchableOpacity
                      disabled={isBD}
                      style={[
                        cardStyle({
                          ...VS_ROW,
                          justifyContent: 'space-between',
                          height: 60,
                        }),
                        ...[
                          isFirst
                            ? [BR_BL_0, BR_BR_0, PT_20]
                            : isLast && !selectedCertainCondition
                              ? [BR_TL_0, BR_TR_0]
                              : [BR_0],
                        ],
                      ]}
                      onPress={() => handleChangeAutoApplyStatus(item)}
                    >
                      <MoeText style={isBD ? TS_16_GRAY : TS_16_BLACK_DARK}>
                        {AutomationStatusLabel.mapLabels[item]}
                      </MoeText>
                      <View style={VS_ROW}>
                        <RadioButton selected={autoApplyStatus === item} disabled={isBD} />
                      </View>
                    </TouchableOpacity>
                    <Condition if={isBD && selectedCertainCondition && autoApplyCondition}>
                      <HLine />
                      <View
                        style={[
                          cardStyle({
                            ...VS_ROW,
                            justifyContent: 'space-between',
                          }),
                          BR_TL_0,
                          BR_TR_0,
                          PB_20,
                        ]}
                      >
                        <MoeText style={isBD ? TS_16_GRAY : TS_16_BLACK_DARK}>
                          {renderConditionDesc?.(caretTypeStr, bizHour, certainTime)}
                        </MoeText>
                      </View>
                    </Condition>
                  </>
                );
              }}
              keyExtractor={String}
            />
            <Condition if={isBD}>
              <View style={viewStyle({ marginTop: -28 })}>
                <MoeText style={TS_14_GRAY}>
                  To configure auto-apply for service charge, please go to the web version.
                </MoeText>
              </View>
            </Condition>
          </View>
          {id ? (
            <BlockButton
              style={[VS_CENTER, H_48, BG_WHITE, BR_8, MT_20, MB_20]}
              textStyle={FG_DANGER}
              onPress={async () => {
                // 删除service charge：status为active时，才出弹窗
                if (service?.isActive) {
                  setApplyUpcomingConfig({ visible: true, action: 'delete' });
                } else {
                  await dangerConfirmAsync(deleteText + 'Are you sure to delete this service charge?', 'Delete');
                  await handleDelete();
                }
              }}
              text="Delete"
            />
          ) : null}
        </ScrollView>
        <SaveButton style={MG_10} onPress={() => handleSaveConfirm()} disabled={!input.isDirty()} text={saveText} />
        <ASConfirmModal
          visible={modalVisible.value}
          onClose={modalVisible.close}
          onConfirm={async () => {
            modalVisible.close();
            await handleSave();
          }}
          onCancel={modalVisible.close}
          description={`Are you sure to update this service charge?`}
          noShowAgainKey={'updateServiceCharge'}
        />
        <UpdateConfirmModal
          action={applyUpcomingConfig?.action}
          visible={!!applyUpcomingConfig?.visible}
          onClose={() => {
            setApplyUpcomingConfig({ visible: false });
            input.set('applyUpcomingAppt', false);
          }}
          setApplyUpcomingAppt={(val) => input.set('applyUpcomingAppt', val)}
          applyUpcomingAppt={input.get('applyUpcomingAppt')}
          onConfirm={async () => {
            if (applyUpcomingConfig?.action === 'delete') {
              await handleDelete();
            } else {
              await handleSave();
            }

            setApplyUpcomingConfig({ visible: false });
          }}
        />
        <SafeBottom />
      </View>
    );
  },
);

export default ServiceChargeEdit;

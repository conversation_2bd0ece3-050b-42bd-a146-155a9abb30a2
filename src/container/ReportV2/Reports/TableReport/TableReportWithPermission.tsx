import React, { useEffect } from 'react';
import { useSerialCallback } from '@moego/tools';
import { useDispatch } from 'amos';
import { getAllSubscriptionPlans } from '../../../../store/company/subscription.actions';
import { Spin } from '@moego/ui';

export const TableReportWithPermission = (props: { children: React.ReactElement | null }) => {
  const dispatch = useDispatch();
  const getPermission = useSerialCallback(async () => {
    await dispatch(getAllSubscriptionPlans());
  });

  useEffect(() => {
    getPermission();
  }, []);

  if (getPermission.isBusy()) {
    return (
      <section className="'moe-w-full moe-h-full moe-flex moe-justify-center moe-items-center moe-bg-white'">
        <Spin />
      </section>
    );
  }

  return props.children;
};

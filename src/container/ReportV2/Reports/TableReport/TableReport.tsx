import { CustomReportTableContextProvider, CustomReportsTablePortal } from '@moego/reporting';
import React from 'react';
import { useTableQueryParams } from './useTableQueryParams';
import { useTableReportState } from './useTableReportState';
import { TableReportWithPermission } from './TableReportWithPermission';

export const TableReport = () => {
  const { diagramId, active, initDates, filterList, dimensions, businessId } = useTableQueryParams();
  const profileCtxValue = useTableReportState({
    diagramId: diagramId!,
    createWithPreset: false,
    active,
    initDates,
    initFilterValues: filterList,
    initBusinessId: businessId,
    initGroupByDimensions: dimensions,
  });

  return (
    <TableReportWithPermission>
      <CustomReportTableContextProvider value={profileCtxValue}>
        <CustomReportsTablePortal className="moe-p-m" />
      </CustomReportTableContextProvider>
    </TableReportWithPermission>
  );
};

import { StyleConfigFormat } from '@moego/api-web/moego/models/reporting/v2/common_model';
import { type DiagramData } from '@moego/api-web/moego/models/reporting/v2/diagram_model';
import { isNil } from 'lodash';
import { type ReportTabKey } from '../../../router/paths';
import { renderCountableNounPlurals, toFixedNum } from '../../../utils/utils';

export const SUB_PAGE_RIGHT_CONTAINER_ID = 'insights-sub-page-right-container-id';

export interface PanelInfo {
  image: string;
}

export interface ReportLegacyUserTip {
  tipTxt: string;
  showTip: boolean;
}

export const UpgradePanelPlaceholder: Record<ReportTabKey, PanelInfo> = {
  overview: {
    image: '',
  },
  clients: {
    image: 'https://dcgll7qxxap6x.cloudfront.net/p/0/2024/6/2dff839d-3f3d-4bf4-a078-d7ce0fae4b17.png',
  },
  leads: {
    image: 'https://da2gvzglbnzm0.cloudfront.net/p/0/2024/6/upgrade_banner.png',
  },
  sales: {
    image: 'https://dcgll7qxxap6x.cloudfront.net/p/0/2024/6/0532a2a6-f39e-4286-b4de-84611e4dfa72.png',
  },
  staff: {
    image: 'https://dcgll7qxxap6x.cloudfront.net/p/0/2024/6/a052206f-03ec-4517-a865-c028f2da607e.png',
  },
  operation: {
    image: 'https://dcgll7qxxap6x.cloudfront.net/p/0/2024/6/b3e6be13-a96c-419e-b4be-47a12edb8958.png',
  },
  // 每个套餐都有，无需upgrade权限
  dailyRevenue: {
    image: '',
  },
};

export interface DashboardDiagramTypeConfig {
  fieldKey: keyof DiagramData;
}

export type DiagramDataTypes = Omit<DiagramData, 'diagramId'>;
export type DiagramValueTypes = DiagramDataTypes[keyof DiagramDataTypes];

export function formatDuration(rawValue: number | string, format: StyleConfigFormat) {
  if (isNil(rawValue)) {
    return '';
  }
  const rawNum = Number(rawValue);
  const isHour = format === StyleConfigFormat.DURATION_HOUR && rawNum >= 60;
  if (isHour) {
    return renderCountableNounPlurals(toFixedNum(rawNum / 60, 2), 'hour');
  }
  return renderCountableNounPlurals(rawNum, 'min');
}

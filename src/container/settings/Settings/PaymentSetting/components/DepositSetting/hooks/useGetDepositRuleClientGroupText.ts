import { type DepositRuleClientGroupFilter } from '@moego/bff-openapi/clients/client.order';
import { useDisplayBEFilters } from '../../../../../../Client/ClientList/components/hooks/useDisplayBEFilters';

export const useGetDepositRuleClientGroupText = () => {
  const displayBEFilters = useDisplayBEFilters();
  return (clientGroup: DepositRuleClientGroupFilter | null) => {
    if (!clientGroup || Object.keys(clientGroup).length === 0) {
      return 'Everyone';
    }
    try {
      const json = JSON.parse(clientGroup.existingCustomersFilterJson || '{}') || {};
      const texts: string[] = [];
      if (clientGroup.newVisitors) {
        texts.push('New visitors');
      }
      if (clientGroup.existingCustomers) {
        if (!json.filters?.length) {
          texts.push('All existing clients');
        } else {
          texts.push(...displayBEFilters(json.filters || []).map(({ title, value }) => `${title}: ${value}`));
        }
      }
      return texts.join(', ');
    } catch {
      return 'Invalid filter';
    }
  };
};

import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type DepositRuleServiceFilter_ServicesByType } from '@moego/bff-openapi/clients/client.order';
import { useServiceIdMapping } from './useServiceIdMapping';

export const useGetDepositRuleServiceTypeText = () => {
  const serviceIdMapping = useServiceIdMapping();
  return (serviceType: Partial<Record<ServiceItemType, DepositRuleServiceFilter_ServicesByType | null>> | null) => {
    if (!serviceType || Object.keys(serviceType).length === 0) {
      return 'All services';
    }

    const texts = [];
    const { BOARDING, DAYCARE, GROOMING } = ServiceItemType;
    const services = [
      { type: BOARDING, label: 'boarding services' },
      { type: DAYCARE, label: 'daycare services' },
      { type: GROOMING, label: 'grooming services' },
    ];

    for (const service of services) {
      const values = serviceType[service.type];
      if (!values) {
        continue;
      }
      if (values.isAll) {
        texts.push(`All ${service.label}`);
      } else {
        const serviceNames = values.serviceIds.map((id) => serviceIdMapping.get(+id)?.name || id);
        texts.push(...serviceNames);
      }
    }

    return texts.join(', ');
  };
};

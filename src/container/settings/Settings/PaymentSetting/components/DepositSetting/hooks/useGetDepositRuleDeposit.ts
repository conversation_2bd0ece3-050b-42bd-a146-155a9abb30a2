import { type DepositRule } from '@moego/bff-openapi/clients/client.order';
import { decimal2Number, money2Number } from '../../../../../../../utils/utils';
import { useSelector } from 'amos';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';

export const useGetDepositRuleDeposit = () => {
  const [business] = useSelector(selectCurrentBusiness);
  return (depositConfig: DepositRule['depositConfig'] | null) => {
    if (!depositConfig) {
      return null;
    }

    const config = Array.isArray(depositConfig) ? depositConfig : [depositConfig];
    const text = config
      .map((item) =>
        item.value
          ? item.case === 'depositByAmount'
            ? business.formatAmount(money2Number(item.value))
            : `${decimal2Number(item.value)}%`
          : '/',
      )
      .join(' ');

    return text;
  };
};

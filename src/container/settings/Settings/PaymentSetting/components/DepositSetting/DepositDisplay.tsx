import React from 'react';
import { type DepositRule } from '@moego/bff-openapi/clients/client.order';
import { useGetDepositRuleDeposit } from './hooks/useGetDepositRuleDeposit';

interface DepositDisplayProps {
  depositConfig: DepositRule['depositConfig'] | null;
}

export const DepositDisplay = ({ depositConfig }: DepositDisplayProps) => {
  const getDepositRuleDeposit = useGetDepositRuleDeposit();
  return <>{getDepositRuleDeposit(depositConfig)}</>;
};

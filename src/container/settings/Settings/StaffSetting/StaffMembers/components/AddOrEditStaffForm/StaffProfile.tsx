import { StaffLinkStatus } from '@moego/api-web/moego/models/organization/v1/staff_enums';
import { RoleType } from '@moego/api-web/moego/models/permission/v1/permission_enums';
import { type RoleModel } from '@moego/api-web/moego/models/permission/v1/permission_models';
import { MinorClockFilled, MinorInfoOutlined, MinorUserOutlined } from '@moego/icons-react';
import {
  Button,
  Checkbox,
  ColorPicker,
  Drawer,
  Form,
  Heading,
  Input,
  LegacySelect as Select,
  Tooltip,
  Typography,
  useFormState,
  useWatch,
} from '@moego/ui';
import { useDispatch, useSelector, useStore } from 'amos';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { AvatarUpload } from '../../../../../../../components/Upload/AvatarUpload';
import { currentCompanyIdBox } from '../../../../../../../store/company/company.boxes';
import { META_DATA_KEY_LIST } from '../../../../../../../store/metadata/metadata.config';
import { useMetaData } from '../../../../../../../store/metadata/metadata.hooks';
import { getRoleDetail } from '../../../../../../../store/permission/permission.actions';
import { permissionRolePermissionsBox } from '../../../../../../../store/permission/permission.boxes';
import {
  selectPermissionRoleList,
  selectPermissionRoleListWithoutEnterprise,
} from '../../../../../../../store/permission/permission.selectors';
import { selectStaff } from '../../../../../../../store/staff/staff.selectors';
import { c_primary } from '../../../../../../../style/_variables';
import { useBool } from '../../../../../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../../../../../utils/hooks/useSerialCallback';
import { RolePermissionGroup } from '../../../components/RoleSetting/RolePermissionGroup';
import { useAddOrEditStaffContext } from '../../hooks/useAddOrEditStaffContext';
import {
  type AddOrEditStaffFormFields,
  AddOrEditStaffFormFieldsMap,
  PRESET_STAFF_COLOR_CODE_LIST,
} from './AddOrEditStaffForm.config';
import { AddOrEditStaffFormAnchorItemsMap } from './AddOrEditStaffFormAnchor';
import { FormSection, HorizontalFormItemWrapper } from './addOrEditStaffForm.style';

export const StaffProfile = memo(({ staffId }: { staffId?: string }) => {
  const { Avatar, FirstName, LastName, ColorCode } = AddOrEditStaffFormFieldsMap;
  const { canEditStaffProfile } = useAddOrEditStaffContext();
  const [engagementCenter, , loading] = useMetaData<boolean>(META_DATA_KEY_LIST.EngagementCenter);
  const showEngagementCenter = !loading && engagementCenter;
  const disabled = !canEditStaffProfile;
  return (
    <FormSection className="moe-gap-m" id={AddOrEditStaffFormAnchorItemsMap.Profile}>
      <Heading size="3" className="moe-text-primary">
        Profile
      </Heading>
      <Form.Item name={Avatar} valuePropName="src">
        <AvatarUpload size="l" isDisabled={disabled} className="moe-self-start" />
      </Form.Item>
      <HorizontalFormItemWrapper className="moe-gap-x-m">
        <Form.Item
          name={FirstName}
          rules={{
            required: {
              value: true,
              message: 'First name is required.',
            },
            maxLength: {
              value: 50,
              message: 'First name should be less than 50 characters.',
            },
          }}
        >
          <Input
            isRequired
            isDisabled={disabled}
            label={AddOrEditStaffFormFieldsMap.mapLabels[FirstName].label}
            placeholder={AddOrEditStaffFormFieldsMap.mapLabels[FirstName].placeholder}
          />
        </Form.Item>
        <Form.Item
          name={LastName}
          rules={{
            required: {
              value: true,
              message: 'Last name is required.',
            },
            maxLength: {
              value: 50,
              message: 'Last name should be less than 50 characters.',
            },
          }}
        >
          <Input
            isRequired
            isDisabled={disabled}
            label={AddOrEditStaffFormFieldsMap.mapLabels[LastName].label}
            placeholder={AddOrEditStaffFormFieldsMap.mapLabels[LastName].placeholder}
          />
        </Form.Item>
      </HorizontalFormItemWrapper>
      <Condition if={showEngagementCenter}>
        <PhoneItem />
      </Condition>
      <EmailItem staffId={staffId} />
      <RoleItem staffId={staffId} />
      <div>
        <label className="moe-h6 moe-inline-flex moe-items-center moe-mb-s moe-text-primary">Color code</label>
        <Form.Item name={ColorCode}>
          <ColorPicker isDisabled={disabled} presetList={PRESET_STAFF_COLOR_CODE_LIST} defaultValue={c_primary} />
        </Form.Item>
      </div>
    </FormSection>
  );
});

const PhoneItem = () => {
  const { canEditStaffProfile } = useAddOrEditStaffContext();
  const disabled = !canEditStaffProfile;
  const { phoneNumber } = AddOrEditStaffFormFieldsMap;
  return (
    <Form.Item name={phoneNumber} label={AddOrEditStaffFormFieldsMap.mapLabels[phoneNumber].label}>
      <Input.Phone className="rr-block rr-ignore" isDisabled={disabled} />
    </Form.Item>
  );
};

const RoleItem = ({ staffId }: { staffId?: string }) => {
  const [id] = useSelector(currentCompanyIdBox);
  const [roleList, editingStaff] = useSelector(selectPermissionRoleList(id), selectStaff(Number(staffId)));

  const { canEditStaffProfile, form } = useAddOrEditStaffContext();
  const disabled = !canEditStaffProfile;
  const showPermissionDrawer = useBool();
  const { Role } = AddOrEditStaffFormFieldsMap;

  const roleOptions = useMemo(
    () =>
      roleList
        .filter((role) => role.type === RoleType.COMPANY_STAFF)
        .map((role) => {
          return {
            label: role.name,
            value: role.id.toString(),
          };
        }),
    [roleList],
  );

  const [roleId] = useWatch({
    control: form?.control,
    name: ['roleId'],
  });

  return (
    <>
      <HorizontalFormItemWrapper className="moe-gap-x-s moe-items-start">
        {editingStaff.isOwner() ? (
          <Select
            isRequired
            value="Owner"
            isReadOnly
            label={AddOrEditStaffFormFieldsMap.mapLabels[Role].label}
            prefix={<MinorUserOutlined />}
          />
        ) : (
          <Form.Item name={Role} rules={{ required: true }}>
            <Select
              isRequired
              isDisabled={disabled}
              label={AddOrEditStaffFormFieldsMap.mapLabels[Role].label}
              options={roleOptions}
              prefix={<MinorUserOutlined />}
            />
          </Form.Item>
        )}
        <Button
          variant="tertiary-legacy"
          onPress={showPermissionDrawer.open}
          className="moe-relative -moe-ml-s moe-mr-8px-400 moe-mt-[24px]"
        >
          Check permission
        </Button>
      </HorizontalFormItemWrapper>
      <RolePermissionDrawer
        defaultRoleId={roleId}
        isOpen={showPermissionDrawer.value}
        onClose={showPermissionDrawer.close}
      />
    </>
  );
};

const RolePermissionDrawer = ({
  defaultRoleId,
  isOpen,
  onClose,
}: {
  defaultRoleId?: string;
  isOpen: boolean;
  onClose: () => void;
}) => {
  const dispatch = useDispatch();
  const store = useStore();
  const [roleId, setRoleId] = useState(defaultRoleId ?? '');
  const [companyId] = useSelector(currentCompanyIdBox);
  const [roleList] = useSelector(selectPermissionRoleListWithoutEnterprise(companyId));
  const [roleDetail, setRoleDetail] = useState<RoleModel>();

  const setAndGetDetail = useSerialCallback(async (roleId?: string) => {
    if (roleId) {
      setRoleId(roleId);
      await dispatch(getRoleDetail(roleId));
      const { roleDetail } = store.select(permissionRolePermissionsBox.mustGetItem(Number(roleId)));
      setRoleDetail(roleDetail);
    }
  });

  useEffect(() => {
    if (isOpen) {
      setAndGetDetail(defaultRoleId);
    }
  }, [isOpen]);

  const roleOptions = useMemo(() => {
    const optionList = roleList.map((role) => ({
      label: role.name,
      value: role.id,
    }));
    // 对于roleList 里没有 company owner 的账号，需要手动添加
    if (!roleList.find((role) => role.type === RoleType.COMPANY_OWNER)) {
      optionList.unshift({
        label: 'Owner',
        value: '0',
      });
    }
    return optionList;
  }, [roleList]);

  return (
    <Drawer isOpen={isOpen} onClose={onClose} title="View role permission" footer={null}>
      <div className="moe-w-full !moe-self-start">
        <Select options={roleOptions} value={roleId} onChange={setAndGetDetail} label="Role" />
        {roleDetail?.permissionCategoryList.map((category, index, arr) => {
          return (
            <>
              <RolePermissionGroup
                key={'RolePermissionGroup' + index}
                id={Number(category.id)}
                roleId={Number(roleId)}
                title={category.displayName}
                permissionList={category.permissionList}
                editable={false}
              />
              <Condition if={index !== arr.length - 1}>
                <div className="moe-border-b-divider moe-border-b" />
              </Condition>
            </>
          );
        })}
      </div>
    </Drawer>
  );
};

const EmailItem = ({ staffId }: { staffId?: string }) => {
  const store = useStore();
  const [curStaff, editingStaff] = useSelector(selectStaff(), selectStaff(Number(staffId)));

  const { Email, isSendInvitationLink } = AddOrEditStaffFormFieldsMap;
  const { canEditStaffProfile, form } = useAddOrEditStaffContext();

  const linkStatus = staffId ? store.select(selectStaff(Number(staffId))).linkStatus : StaffLinkStatus.UNSPECIFIED;
  const isLinked = linkStatus === StaffLinkStatus.LINKED;
  const isPending = linkStatus === StaffLinkStatus.PENDING;

  const isEditingOwner = editingStaff.isOwner();
  const canEditSomeFields = curStaff.isOwner() || !isEditingOwner;

  // input, checkbox 状态分离
  const { email } = useWatch<AddOrEditStaffFormFields>();
  const { dirtyFields } = useFormState({ control: form?.control });
  const isEmailFieldEmpty = !email;
  const isEmailFieldDirty = dirtyFields?.email;
  const isEmailInputDisabled = !canEditStaffProfile;
  const isEmailSendInvitationDisabled = isEmailInputDisabled || (isPending && !isEmailFieldDirty) || isEmailFieldEmpty;

  // 把 email 改回原始值、或留空的情况
  useEffect(() => {
    if ((isPending && !isEmailFieldDirty) || isEmailFieldEmpty) {
      form?.setValue(isSendInvitationLink as 'isSendInvitationLink', false);
    }
  }, [isEmailFieldDirty, isEmailFieldEmpty, isPending]);

  return (
    <Condition if={canEditSomeFields}>
      <div>
        <div className="moe-flex moe-gap-x-s moe-items-center">
          <Form.Item name={Email} disabled={isLinked}>
            {isLinked ? (
              <Tooltip
                delay={0}
                content={
                  // 如果被编辑的是 owner profile ，不允许 unlink email，需要联系 support team
                  isEditingOwner
                    ? 'To change owner role account email address, please contact support team.'
                    : 'To change account email address, please unlink the staff first.'
                }
                side="top"
                sideOffset={12}
              >
                <Input
                  isReadOnly={isLinked || isEmailInputDisabled}
                  type="email"
                  className="moe-flex-1"
                  label={AddOrEditStaffFormFieldsMap.mapLabels[Email].label}
                  placeholder={AddOrEditStaffFormFieldsMap.mapLabels[Email].placeholder}
                  value={store.select(selectStaff(Number(staffId))).email ?? ''}
                />
              </Tooltip>
            ) : (
              <Input
                type="email"
                className="moe-flex-1"
                label={AddOrEditStaffFormFieldsMap.mapLabels[Email].label}
                placeholder={AddOrEditStaffFormFieldsMap.mapLabels[Email].placeholder}
                isDisabled={isEmailInputDisabled}
              />
            )}
          </Form.Item>
          <Condition if={!isLinked}>
            <Form.Item name={isSendInvitationLink} valuePropName="isSelected">
              <Checkbox className="moe-mt-m" isDisabled={isEmailSendInvitationDisabled}>
                {AddOrEditStaffFormFieldsMap.mapLabels[isSendInvitationLink].label}
                <Tooltip
                  delay={150}
                  content={AddOrEditStaffFormFieldsMap.mapLabels[isSendInvitationLink].tooltip}
                  side="top"
                >
                  <MinorInfoOutlined className="moe-ml-[4px] moe-cursor-pointer moe-text-icon-surface" />
                </Tooltip>
              </Checkbox>
            </Form.Item>
          </Condition>
        </div>
        <Condition if={isPending && !isEmailFieldDirty}>
          <div className="moe-flex moe-items-center moe-mt-xs">
            <MinorClockFilled className="moe-text-icon-warning" />
            <Typography.Markup variant="caption" className="moe-ml-xxs">
              Pending invitation
            </Typography.Markup>
          </div>
        </Condition>
      </div>
    </Condition>
  );
};

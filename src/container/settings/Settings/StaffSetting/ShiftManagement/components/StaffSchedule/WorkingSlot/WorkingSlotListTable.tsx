import React, { useMemo, useState, type ReactNode } from 'react';
import cn from 'classnames';
import dayjs from 'dayjs';
import { MinorEditOutlined, MinorErrorFilled, MinorPlusOutlined, MinorTrashOutlined } from '@moego/icons-react';
import { Button, IconButton, Input, Markup, Text, TimePicker, Tooltip } from '@moego/ui';
import SvgFormRequiredSvg from '../../../../../../../../assets/svg/form-required.svg';
import { SvgIcon } from '../../../../../../../../components/Icon/Icon';
import { LimitationRow } from '../../Limitation/LimitationRow';
import { useSelector } from 'amos';
import { selectCurrentBusiness } from '../../../../../../../../store/business/business.selectors';
import { TimeFormat } from '@moego/api-web/moego/models/organization/v1/company_enums';
import type { WorkingSlotValue } from '../../../../../../../../store/staff/staff.boxes';
import { StaffScheduleTestIds } from '../../../../../../../../config/testIds/staffSchedule';
import { useBool } from '../../../../../../../../utils/hooks/useBool';
import { SlotListEditDrawer } from '../SlotListEditDrawer';

export type WorkingSlotListValue = WorkingSlotValue['slotHourSettingList'];

export interface WorkingSlotListTableProps {
  className?: string;
  classNames?: {
    action?: string;
  };
  isDisabled?: boolean;
  // small size for drawer render
  size?: 'small' | 'default';
  value: WorkingSlotListValue;
  title?: ReactNode;
  validTimeRange?: [number, number] | null;
  onChange?: (v: WorkingSlotListValue) => void;
  onAdd?: () => void;
  onDelete?: (index: number) => void;
}

export const WorkingSlotListTable = (props: WorkingSlotListTableProps) => {
  const {
    value,
    onAdd,
    onDelete,
    onChange,
    title,
    className,
    classNames,
    validTimeRange,
    size = 'default',
    isDisabled,
  } = props;
  const onlyOneSlot = value.length === 1;
  const [business] = useSelector(selectCurrentBusiness);
  const isDrawerOpen = useBool(false);
  const [currentIndex, setCurrentIndex] = useState(-1);

  const isTimeInValidRange = (time: number) => {
    if (!validTimeRange) return true;
    const [start, end] = validTimeRange;
    return time >= start && time <= end;
  };

  const onStartTimeChange = (index: number, startTime: number) => {
    const nextSlotList = [...value];
    nextSlotList[index].startTime = startTime;
    nextSlotList.sort((a, b) => a.startTime - b.startTime);
    onChange?.(nextSlotList);
  };

  const onCapacityChange = (index: number, capacity: number) => {
    const nextSlotList = [...value];
    nextSlotList[index].capacity = capacity;
    onChange?.(nextSlotList);
  };

  const onSlotItemEditClick = (index: number) => {
    if (index > -1) {
      setCurrentIndex(index);
      isDrawerOpen.open();
    }
  };

  const onAddSlot = () => {
    const lastSlot = value[value.length - 1];
    const nextValue = [
      ...value,
      {
        startTime: lastSlot.startTime + 60,
        capacity: 1,
        limitationGroups: [],
      },
    ];
    onChange?.(nextValue);
    onAdd?.();
  };

  const onDeleteSlot = (index: number) => {
    const nextSlotList = [...value];
    nextSlotList.splice(index, 1);
    onChange?.(nextSlotList);
    onDelete?.(index);
  };

  const disabledTimeMap = useMemo(() => {
    return value.reduce((map, { startTime }) => {
      map.set(startTime, true);
      return map;
    }, new Map<number, boolean>());
  }, [value]);

  return (
    <div className={className}>
      {title}
      {/* Header Row */}
      <div
        className="moe-grid moe-grid-cols-3 moe-border-b moe-border-divider"
        style={{
          gap: size === 'small' ? '24px' : '32px',
          gridTemplateColumns: '260px 1fr',
        }}
      >
        <div className="moe-flex moe-items-center">
          <Markup className="moe-text-secondary" variant="small">
            Slot
          </Markup>
          <SvgIcon src={SvgFormRequiredSvg} color="#F3413B" className="!moe-ml-0 moe-mr-xxs" />
        </div>

        <div className="moe-flex moe-items-center">
          <Markup className="moe-text-secondary" variant="small">
            Pet maximum
          </Markup>
          <SvgIcon src={SvgFormRequiredSvg} color="#F3413B" className="!moe-ml-0 moe-mr-xxs" />
        </div>
      </div>
      {value.map(({ startTime, capacity }, rowIndex) => {
        const isTimePickerValid = isTimeInValidRange(startTime);

        return (
          <div
            key={rowIndex}
            className="moe-border-b moe-border-divider moe-py-[12px]"
            data-testid={`${StaffScheduleTestIds.SlotScheduleSlotListItem}-${rowIndex}`}
          >
            <div
              className="moe-grid moe-grid-cols-3 moe-gap-l"
              style={{
                gap: size === 'small' ? '24px' : '32px',
                gridTemplateColumns: '260px 1fr',
              }}
            >
              <TimePicker
                classNames={{
                  inputBox: cn({
                    '!moe-outline-warning hover:moe-outline-warning': !isTimePickerValid,
                  }),
                }}
                isDisabled={isDisabled}
                disabledTime={(date) => {
                  const minutes = date.getMinutes();

                  if (startTime === minutes) return false;
                  return disabledTimeMap.get(minutes) ?? false;
                }}
                isClearable={false}
                value={dayjs().setMinutes(startTime)}
                minuteStep={5}
                onChange={(v) => onStartTimeChange(rowIndex, v?.getMinutes() ?? 0)}
                use12Hours={business.timeFormatType === TimeFormat.HOUR_12}
              />

              <div className="moe-flex moe-justify-between">
                <div className="moe-flex moe-items-center">
                  <Input.Number
                    className={cn('moe-w-[180px]', {
                      'moe-w-[140px]': size === 'small',
                    })}
                    isStepper
                    isDisabled={isDisabled}
                    step={1}
                    minValue={0}
                    value={capacity}
                    onChange={(v) => onCapacityChange(rowIndex, v ?? 1)}
                    data-testid={`${StaffScheduleTestIds.SlotScheduleSlotListItemPetCapacityInput}-${rowIndex}`}
                  />
                  <Text className="moe-ml-xs moe-whitespace-nowrap" variant="regular-short">
                    pets
                  </Text>
                </div>
                <div
                  className={cn('moe-flex moe-items-center moe-justify-end moe-gap-xs', classNames?.action)}
                  style={{
                    gap: size === 'small' ? 0 : '8px',
                  }}
                >
                  <IconButton
                    isDisabled={isDisabled}
                    icon={<MinorEditOutlined />}
                    color="transparent"
                    onPress={() => onSlotItemEditClick(rowIndex)}
                    data-testid={`${StaffScheduleTestIds.SlotScheduleSlotListItemEditBtn}-${rowIndex}`}
                  />
                  <Tooltip content={onlyOneSlot ? 'There must be at least one slot.' : null}>
                    <IconButton
                      isDisabled={onlyOneSlot || isDisabled}
                      className={cn({
                        'moe-opacity-50': onlyOneSlot || isDisabled,
                      })}
                      icon={<MinorTrashOutlined />}
                      color="transparent"
                      onPress={() => onDeleteSlot(rowIndex)}
                      data-testid={`${StaffScheduleTestIds.SlotScheduleSlotListItemDeleteBtn}-${rowIndex}`}
                    />
                  </Tooltip>
                </div>
              </div>
            </div>
            {!isTimePickerValid && (
              <Text className="moe-mt-xxs moe-flex moe-items-center moe-gap-xxs moe-text-warning" variant="small">
                <MinorErrorFilled color="#DE921F" />
                Not in working time
              </Text>
            )}
            {value[rowIndex].limitationGroups.length > 0 && (
              <LimitationRow className="moe-mt-[12px]" value={value[rowIndex].limitationGroups} />
            )}
          </div>
        );
      })}
      {/* Add slot action */}
      <Button isDisabled={isDisabled} variant="tertiary" icon={<MinorPlusOutlined />} onPress={onAddSlot}>
        Add slot
      </Button>

      {currentIndex > -1 && (
        <SlotListEditDrawer
          title="Slot setting"
          visible={isDrawerOpen.value}
          onClose={isDrawerOpen.close}
          value={value[currentIndex]}
          onChange={(v) => {
            const nextSlotList = [...value];
            nextSlotList[currentIndex] = v;
            onChange?.(nextSlotList);
          }}
        />
      )}
    </div>
  );
};

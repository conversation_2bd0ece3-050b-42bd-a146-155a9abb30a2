import { MinorMoreOutlined } from '@moego/icons-react';
import { cn, Dropdown, IconButton, Text, Typography } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import React from 'react';
import IconCalendarCheck from '../../../../../../../../assets/svg/calendar-schedule-check.svg';
import { SvgIcon } from '../../../../../../../../components/Icon/Icon';
import { toastApi } from '../../../../../../../../components/Toast/Toast';
import { selectBusiness } from '../../../../../../../../store/business/business.selectors';
import { deleteStaffSlotOverride } from '../../../../../../../../store/staffSchedule/staffSchedule.actions';
import { editingWorkingHourStaffIdBox } from '../../../../../../../../store/staffSchedule/staffSchedule.boxes';
import {
  DateOverrideType,
  type StaffScheduleSlotOverride,
} from '../../../../../../../../store/staffSchedule/staffSchedule.types';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../../../utils/DateTimeUtil';
import { useStaffScheduleDrawerContext } from '../StaffScheduleDrawerContext';
import { useSerialCallback } from '@moego/tools';
import { withPl } from '../../../../../../../../utils/calculator';

export interface OverrideItemProps {
  type: DateOverrideType;
  data: StaffScheduleSlotOverride;
  businessId: number;
  className?: string;
  editable?: boolean;
}

export function OverrideItemBySlot(props: OverrideItemProps) {
  const { type, businessId, data, className, editable = type === DateOverrideType.Ongoing } = props;
  const date = data.overrideDate;
  const { slotHourSettingList, slotDailySetting, isAvailable } = data.value;
  const dispatch = useDispatch();
  const [business, staffId] = useSelector(selectBusiness(businessId), editingWorkingHourStaffIdBox);
  const { openAddOverrideDrawerBySlot } = useStaffScheduleDrawerContext();

  const isNoSetting = slotDailySetting.startTime === -1;
  const onDelete = useSerialCallback(async () => {
    await dispatch(deleteStaffSlotOverride([date], staffId, data.value));
    toastApi.success('Date override has been deleted successfully.');
  });

  const renderMainContent = () => {
    if (!isAvailable) {
      return (
        <Text variant="regular-short" className="moe-text-secondary">
          Not working
        </Text>
      );
    }

    if (isNoSetting) {
      return (
        <Text variant="regular-short" className="moe-text-danger">
          Set slots for the override date(s)
        </Text>
      );
    }

    return (
      <div className="moe-w-[160px] !moe-flex !moe-flex-col !moe-gap-y-[4px]">
        <Text variant="regular-short" className="moe-text-secondary">
          {business.formatFixedTime(slotDailySetting.startTime * T_MINUTE)} –{' '}
          {business.formatFixedTime(slotDailySetting.endTime * T_MINUTE)}
        </Text>
      </div>
    );
  };

  const renderSlotLabel = () => {
    if (!isAvailable || isNoSetting) return null;

    const slotSize = slotHourSettingList.length;

    return (
      <Text variant="regular-short" className="moe-text-secondary">
        {withPl(slotSize, 'slot')}
      </Text>
    );
  };

  return (
    <div
      className={cn(
        'hover:!moe-bg-neutral-sunken-0 !moe-cursor-pointer !moe-flex !moe-items-center !moe-justify-between !moe-px-[16px] !moe-py-[20px] !moe-border-0 !moe-border-b !moe-border-solid !moe-border-b-[#E6E6E6] last:!moe-border-b-0',
        !editable && '!moe-cursor-default',
        className,
      )}
    >
      <div className="!moe-flex moe-flex-1 !moe-gap-x-[40px]">
        <div className="!moe-w-[150px] !moe-whitespace-nowrap">
          <SvgIcon src={IconCalendarCheck} size={20} color="#3D414B" className="!moe-mr-[8px] !moe-align-top" />
          <Typography.Heading size="5" className="moe-inline-block">
            {dayjs(date).format('ddd')}, {business.formatDate(date)}
          </Typography.Heading>
        </div>
        {renderMainContent()}
        {renderSlotLabel()}
      </div>
      <div>
        {editable && (
          <Dropdown>
            <Dropdown.Trigger>
              <IconButton icon={<MinorMoreOutlined />} color="transparent" />
            </Dropdown.Trigger>
            <Dropdown.Menu
              onAction={(k) => {
                if (k === 'edit') {
                  openAddOverrideDrawerBySlot({
                    staffId,
                    defaultDate: date,
                    value: {
                      dates: [dayjs(date, DATE_FORMAT_EXCHANGE)],
                      value: data.value,
                    },
                    businessId,
                  });
                }
                if (k === 'delete') {
                  if (onDelete.isBusy()) {
                    return;
                  }
                  onDelete();
                }
              }}
            >
              <Dropdown.Item key="edit">Edit</Dropdown.Item>
              <Dropdown.Item key="delete">Delete</Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        )}
      </div>
    </div>
  );
}

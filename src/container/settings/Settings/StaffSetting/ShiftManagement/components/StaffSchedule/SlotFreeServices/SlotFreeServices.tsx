import { MinorCopyOutlined, MinorInfoOutlined } from '@moego/icons-react';
import { cn, Heading, IconButton, Overflow, Select, Tag, Tooltip } from '@moego/ui';
import React, { isValidElement, memo } from 'react';
import { selectServiceListWithCategory } from '../../../../../../../../store/service/service.selectors';
import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';

import { useSelector } from 'amos';
import { useLatestCallback } from '../../../../../../../../utils/hooks/useLatestCallback';
import { useModal } from '../../../../../../../../components/Modal/useModal';
import { CopyStaffListModal } from '../../CopyStaffListModal';
import { selectBusinessStaffs } from '../../../../../../../../store/staff/staff.selectors';

export interface SlotFreeServicesProps {
  serviceItemType?: ServiceItemType;
  className?: string;
  value: string[];
  onChange: (values: string[]) => void;
  onCopyConfirm?: (staffIdList: number[]) => void;
  staffId: number;
  isDisabled?: boolean;
}

export const SlotFreeServices = memo<SlotFreeServicesProps>((props) => {
  const {
    serviceItemType = ServiceItemType.GROOMING,
    className,
    value,
    onChange,
    onCopyConfirm,
    staffId,
    isDisabled,
  } = props;

  const [options, staffIdList] = useSelector(
    selectServiceListWithCategory([ServiceType.SERVICE, serviceItemType]),
    selectBusinessStaffs(),
  );

  const openCopyStaffListModal = useModal(CopyStaffListModal);

  const handleCopy = useLatestCallback(() => {
    openCopyStaffListModal({
      staffIdList: staffIdList.toArray().filter((id) => id !== staffId),
      title: 'Copy slot free services to',
      description:
        'The overlapping services settings will be copied to selected staff, overwriting their existing settings.',
      onConfirm: (staffIdList) => {
        onCopyConfirm?.(staffIdList);
      },
    });
  });

  return (
    <div className={cn('moe-flex moe-flex-col moe-gap-s', className)}>
      <div className="moe-flex moe-items-center moe-gap-xxs">
        <Heading size="5">Slot free services</Heading>
        <Tooltip content="Slot free services will not occupy slots.">
          <MinorInfoOutlined className="moe-text-[#828282]" />
        </Tooltip>
      </div>

      <div className="moe-flex moe-items-center moe-gap-xs moe-w-full">
        <Select.Multiple
          showSelectAll
          value={value}
          isDisabled={isDisabled}
          classNames={{
            base: 'moe-overflow-hidden',
            control: 'moe-overflow-hidden',
          }}
          tagClassNames={{
            base: cn('moe-max-w-[calc(100%_-_8px)]', {
              'moe-max-w-[calc(100%_-_40px)]': value.length > 1,
            }),
          }}
          onChange={onChange}
          renderValues={(values, _, renderItems) => {
            const itemsElement = renderItems(values);

            if (values.length > 0) {
              const validItemsElement = isValidElement(itemsElement) ? itemsElement : <>{itemsElement}</>;

              return (
                <Overflow
                  key={values.length}
                  className="moe-gap-xs moe-my-[-2px]"
                  renderRemaining={(count) => (
                    <Tag
                      className="moe-shrink-0 moe-whitespace-nowrap"
                      variant="filled"
                      color="neutral"
                      label={`+${count}`}
                    />
                  )}
                >
                  {validItemsElement?.props?.children}
                </Overflow>
              );
            }

            return itemsElement;
          }}
          enableSelectAllSearch
          mode="tag"
          className="moe-flex-1 moe-max-w-[678px]"
          items={options}
        />
        <IconButton isDisabled={isDisabled} onPress={handleCopy} icon={<MinorCopyOutlined />} color="transparent" />
      </div>
    </div>
  );
});

SlotFreeServices.displayName = 'SlotFreeServices';

import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { uniq } from 'lodash';
import React from 'react';
import { editingWorkingHourStaffIdBox } from '../../../../../../../../store/staffSchedule/staffSchedule.boxes';
import { DateOverrideType } from '../../../../../../../../store/staffSchedule/staffSchedule.types';
import { isNormal } from '../../../../../../../../store/utils/identifier';
import { EmptyDateOverride } from '../../EmptyDateOverride';
import { selectStaffScheduleSlotOverride } from '../../../../../../../../store/staffSchedule/staffSchedule.selectors';
import { OverrideItemBySlot } from './OverrideItemBySlot';

export interface OverridesProps {
  businessId: number;
  type: DateOverrideType;
  staffId?: number;
  editable?: boolean;
}
export function OverridesBySlot(props: OverridesProps) {
  const { businessId, type, staffId: staffIdProp, editable } = props;
  const [editingWorkingHourStaffId] = useSelector(editingWorkingHourStaffIdBox);
  const staffId = staffIdProp ?? editingWorkingHourStaffId;
  const [data] = useSelector(selectStaffScheduleSlotOverride(staffId));

  if (!isNormal(staffId)) return null;

  const k = type === DateOverrideType.Ongoing ? 'ongoing' : 'history';
  const workingSlotDate = data[k].map((v) => ({
    ...v,
    timeData: [
      {
        startTime: v.value.slotDailySetting.startTime,
        endTime: v.value.slotDailySetting.endTime,
      },
    ],
  }));
  const list = uniq(workingSlotDate).sort((a, b) => (dayjs(a.overrideDate) > dayjs(b.overrideDate) ? 1 : -1));

  return (
    <>
      {list.length > 0 ? (
        <div className="moe-border-b moe-border-solid moe-border-divider">
          {list.map((v, index) => {
            return (
              <OverrideItemBySlot
                className="moe-h-[40px]"
                key={index}
                type={type}
                data={v}
                businessId={businessId}
                editable={editable}
              />
            );
          })}
        </div>
      ) : (
        <EmptyDateOverride />
      )}
    </>
  );
}

import { Avatar, Checkbox, Heading, Modal, type ModalProps, Text } from '@moego/ui';
import React, { memo, useMemo, useState } from 'react';
import { store } from '../../../../../../provider';
import { staffMapBox } from '../../../../../../store/staff/staff.boxes';
import { selectStaffRole } from '../../../../../../store/business/role.selectors';

export interface CopyStaffListModalProps extends Omit<ModalProps, 'onConfirm'> {
  targetStaffId?: number;
  staffIdList: number[];
  onConfirm?: (staffIdList: number[]) => void;
  title?: string;
  description?: string;
}

export const CopyStaffListModal = memo<CopyStaffListModalProps>((props) => {
  const { targetStaffId, staffIdList, onConfirm, title: titleProp, description, ...rest } = props;

  const filteredStaffIdList = useMemo(() => {
    if (!targetStaffId) {
      return staffIdList;
    }
    return staffIdList.filter((id) => id !== targetStaffId);
  }, [staffIdList, targetStaffId]);
  const title = useMemo(() => {
    if (titleProp) {
      return titleProp;
    }
    if (!targetStaffId) {
      return 'Copy staff setting';
    }
    const targetStaff = store.select(staffMapBox.mustGetItem(targetStaffId));
    return `Copy ${targetStaff.firstName}’s setting to`;
  }, [targetStaffId, titleProp]);

  const [applyList, setApplyList] = useState<number[]>([]);

  const handleConfirm = () => {
    onConfirm?.(applyList);
  };

  return (
    <Modal size="s" isOpen confirmText="Apply" title={title} onConfirm={handleConfirm} {...rest}>
      {description && (
        <Text variant="regular" className="moe-text-primary moe-mb-s">
          {description}
        </Text>
      )}
      <div>
        {filteredStaffIdList.map((id) => {
          const staff = store.select(staffMapBox.mustGetItem(id));
          const role = store.select(selectStaffRole(id));

          return (
            <Checkbox
              key={id}
              isSelected={applyList.includes(id)}
              onChange={(v) => {
                if (v) {
                  setApplyList([...applyList, id]);
                } else {
                  setApplyList(applyList.filter((item) => item !== id));
                }
              }}
              classNames={{
                base: 'moe-border-divider moe-border-b moe-max-w-full',
                wrapper: 'moe-items-center moe-flex moe-h-[72px]',
              }}
            >
              <div className="moe-mr-xs">
                <Avatar.Staff color={staff.getColorCode()} name={staff.fullName()} size="s" src={staff.avatarPath} />
              </div>
              <div className="!moe-flex-1 !moe-min-w-0">
                <Heading size="5" className="moe-text-primary">
                  {staff.firstName}
                </Heading>
                <Text variant="caption" className="moe-mt-xxs moe-text-tertiary moe-truncate">
                  {role?.name}
                </Text>
              </div>
            </Checkbox>
          );
        })}
      </div>
    </Modal>
  );
});

CopyStaffListModal.displayName = 'CopyStaffListModal';

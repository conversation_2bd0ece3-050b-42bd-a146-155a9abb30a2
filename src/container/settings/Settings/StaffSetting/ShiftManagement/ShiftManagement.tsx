import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, Heading, Link, Spin, Text, toast } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { useAsync, useUnmount } from 'react-use';
import { Card } from '../../../../../components/Card/Card';
import { getBusinessDetail } from '../../../../../store/business/business.actions';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { setStaffShiftManagementNewDisappear } from '../../../../../store/common/common.actions';
import { getCompanyPreferenceSetting } from '../../../../../store/company/company.actions';
import { getAllSubscriptionPlans } from '../../../../../store/company/subscription.actions';
import { selectSubscriptionPlans } from '../../../../../store/company/subscription.selectors';
import { getOnlineBookingPreference } from '../../../../../store/onlineBooking/actions/private/onlineBookingPreference.actions';
import {
  mergeCACD,
  pushStaffServiceArea,
  putStaffWorkingHour,
  putStaffWorkingSlot,
  resetStaffSchedule,
  resetStaffServiceArea,
  setWorkingHourCurrentWeek,
} from '../../../../../store/staffSchedule/staffSchedule.actions';
import { getCompanyStaffList } from '../../../../../store/staff/staff.actions';
import {
  editingWorkingHourStaffIdBox,
  staffScheduleViewTypeBox,
} from '../../../../../store/staffSchedule/staffSchedule.boxes';
import { AvailabilityType, StaffWorkingHourViewType } from '../../../../../store/staffSchedule/staffSchedule.types';
import { type EnumValues } from '../../../../../store/utils/createEnum';
import { ID_ANONYMOUS, isNormal } from '../../../../../store/utils/identifier';
import { StaffScheduleListView } from './components/StaffSchedule/StaffScheduleListView';
import { SwitchStaffWorkingHourViewType } from './components/SwitchStaffWorkingHourViewType';
import { useInitShiftManagement } from './hooks/useInitShiftManagement';
import { useUnsavedConfirmGlobalV2 } from '../../../../../utils/hooks/useUnsavedConfirmGlobalV2';
import { useScheduleDirty } from './hooks/useScheduleDirty';
import { useAvailabilityType } from './hooks/useAvailabilityType';
import { AvailabilityTypeRadioGroup } from './components/AvailabilityType';
import { useEnableMultiPetBySlotFeature } from '../../../../Calendar/latest/components/SlotCalendar/hooks/useSlotCalendarFeature';
import { ShiftManagementTitle } from './components/ShiftManagementTitle';
import {
  getSyncStaffAvailabilityState,
  syncStaffAvailabilityState,
} from '../../../../../store/onlineBooking/actions/private/availabilityGrooming.actions';
import { syncWithStaffRegularWorkingHour } from '../../../../../store/onlineBooking/settings/availabilityGrooming.boxes';
import { selectOnlineBookingPreference } from '../../../../../store/onlineBooking/onlineBookingPreference.selectors';
import { CalendarView } from './components/CalendarView/CalendarView';
import { PATH_ONLINE_BOOKING_AVAILABILITY } from '../../../../../router/paths';
import { useUnsavedConfirmV2 } from '../../../../../utils/hooks/useUnsavedConfirmV2';
import { StaffScheduleDrawerProvider } from './components/StaffSchedule/StaffScheduleDrawerContext';
import { useLimitationPreload } from './components/Limitation/hooks/useLimitationPreload';
import { SMTestIds } from '../../../../../config/testIds/shiftManagement';
import { StaffManagementNoAuthTip } from './components/StaffManagementNoAuthTip';
import { usePricingPermissionAccess } from '../../../../../components/Pricing/pricing.hooks';
import { toastApi } from '../../../../../components/Toast/Toast';
import { OnlineBookingReporter } from '../../../../../utils/reportData/reporter/onlineBookingReporter';
import { useSerialCallback } from '@moego/tools';
import { useServiceArea } from './hooks/useServiceArea';
import { GroomingReportActionName } from '../../../../../utils/reportData/grooming/grooming';
import { reportData } from '../../../../../utils/tracker';

export const ShiftManagement = memo(() => {
  const dispatch = useDispatch();
  // AS TODO: 这个 business 需要明确一个上下文
  const [business, plans, viewType, staffId, obAvailabilitySync, obPreference] = useSelector(
    selectCurrentBusiness,
    selectSubscriptionPlans,
    staffScheduleViewTypeBox,
    editingWorkingHourStaffIdBox,
    syncWithStaffRegularWorkingHour,
    selectOnlineBookingPreference,
  );
  const {
    availabilityType,
    draftAvailabilityType,
    setDraftAvailabilityType,
    getAvailabilityTypeAsync,
    updateAvailabilityType,
    resetAvailabilityType,
  } = useAvailabilityType(viewType);
  const groomingReporter = useMemo(() => new OnlineBookingReporter(), []);

  const { access: hasBySlotAuth } = usePricingPermissionAccess('bookBySlot');
  const isEnableDirtyCheckAndUpdate = hasBySlotAuth || draftAvailabilityType.value === AvailabilityType.BY_TIME; // 目前在没有权限的情况下也可以切换 availableType，可能会影响 dirty check 结果，索性在无权限时不进行 dirty check，也不允许点击 update 更新客户数据
  const isEnableOBBySlot = useEnableMultiPetBySlotFeature();
  const isListViewType = viewType === StaffWorkingHourViewType.ListView;
  const isOBSettingDifferent = draftAvailabilityType.value !== obPreference.availableTimeType + 1;

  useInitShiftManagement();

  const { dirtyStaffIdList, clearDirtyStaff, isDirty } = useScheduleDirty({
    draftAvailabilityType: draftAvailabilityType.value,
    availabilityType,
    staffId,
    isEnable: isEnableDirtyCheckAndUpdate,
  });

  const sendMergeData = useSerialCallback(async () => {
    if (business.isMobileGrooming() && isNormal(business.id)) {
      await dispatch(mergeCACD());
    }
    if (!plans.size) {
      await dispatch(getAllSubscriptionPlans());
    }
  });

  // 手动点击触发 type change 时，type 为 changed
  const handleAvailabilityTypeChange = (v: EnumValues<typeof AvailabilityType>) => {
    setDraftAvailabilityType({
      value: v,
      type: 'changed',
    });
  };

  const updateAvailabilitySettings = async (params?: { showSuccessToast?: boolean }) => {
    const { showSuccessToast = true } = params || {};
    await updateAvailabilityType(`${business.id}`);
    if (!isListViewType) {
      showSuccessToast && toastApi.success('Shift Management settings have been saved');
      return;
    }

    if (draftAvailabilityType.value === AvailabilityType.BY_TIME) {
      await dispatch([putStaffWorkingHour(dirtyStaffIdList), pushStaffServiceArea(dirtyStaffIdList)]);
    } else {
      await dispatch(putStaffWorkingSlot(dirtyStaffIdList));
    }
    showSuccessToast && toastApi.success('Shift Management settings have been saved');

    clearDirtyStaff();
  };

  const handleStaffScheduleUpdate = useSerialCallback(async () => {
    const isAvailabilityTypeChanged = draftAvailabilityType.value !== availabilityType.current;
    reportData(GroomingReportActionName.ShiftManagementClickUpdateAvailabilitySettings, {
      availability_type: draftAvailabilityType.value === AvailabilityType.BY_TIME ? 'by time' : 'by slot',
    });

    return new Promise<void>((resolve) => {
      // 1. 当 ob 开了 sync，并且 availabilityType 变了
      // 2. mobile grooming 不会走这个逻辑，因为 mobile grooming 没有 by slot，isAvailabilityTypeChanged 一定是 false
      if (isEnableOBBySlot && obAvailabilitySync && isAvailabilityTypeChanged) {
        groomingReporter.reportGroomingByTimeToBySlotModal();
        AlertDialog.open({
          title: 'Online Booking sync',
          content: `This page's settings are synced with Online Booking. 
            If you save the availability settings here from 
            '${AvailabilityType.mapLabels[availabilityType.current]}' to 
            '${AvailabilityType.mapLabels[draftAvailabilityType.value]}', 
            the same change will be applied to Online Booking.`,
          onConfirm: () => {
            groomingReporter.reportGroomingByTimeToBySlotModalConfirm();
            updateAvailabilitySettings({ showSuccessToast: false }).then(() => {
              resolve();
              toast({
                type: 'success',
                title: 'Shift management and Online booking settings are saved',
              });
            });
          },
          onCancel: () => groomingReporter.reportGroomingByTimeToBySlotModalCancel(),
          onClose: () => {
            resolve();
          },
        });
      } else if (obAvailabilitySync && availabilityType.current === AvailabilityType.BY_SLOT && !hasBySlotAuth) {
        // 如果开启了 OB sync 则更改 ob by slot 的内容的时候会提示
        groomingReporter.reportGroomingWillWarnSyncSMModal();
        AlertDialog.open({
          title: 'Online Booking sync',
          content: (
            <Text variant="small">
              {`This page's settings syncs with Online Booking. Any changes made here, including availability, will also
              apply to Online Booking.`}
            </Text>
          ),
          onConfirm: () => {
            groomingReporter.reportGroomingWillWarnSyncSMModalConfirm();
            updateAvailabilitySettings({ showSuccessToast: false }).then(() => {
              resolve();
              toast({
                type: 'success',
                title: 'Shift management and Online booking settings are saved',
              });
            });
          },
          onCancel: () => groomingReporter.reportGroomingWillWarnSyncSMModalCancel(),
          onClose: () => {
            resolve();
          },
        });
      } else if (!obAvailabilitySync && availabilityType.current === AvailabilityType.BY_SLOT) {
        // 如果没有开启 OB sync 但是在 by slots 模式下，会提示是否要 sync
        groomingReporter.reportGroomingBySlotGuideSyncModal();
        AlertDialog.open({
          title: 'Online Booking sync',
          content: (
            <Text variant="small">
              Online Booking isn’t synced with this page. We recommend enabling Sync to keep both settings consistent
              and avoid future conflicts.
            </Text>
          ),
          confirmText: 'Save and sync',
          cancelText: 'Save without synced',
          onCancel: () => {
            groomingReporter.reportGroomingBySlotGuideSyncModalSaveWithoutSync();
            updateAvailabilitySettings().then(resolve);
          },
          onConfirm: () => {
            groomingReporter.reportGroomingBySlotGuideSyncModalSaveAndSync();
            Promise.all([
              updateAvailabilitySettings({ showSuccessToast: false }),
              dispatch(syncStaffAvailabilityState()),
            ]).then(() => {
              resolve();
              toast({
                type: 'success',
                title: 'Shift management and Online booking settings are saved',
              });
            });
          },
          onClose: () => {
            resolve();
          },
        });
      } else {
        updateAvailabilitySettings().then(resolve).catch(resolve);
      }
    });
  });

  // 重置回原来的数据
  const handleStaffScheduleReset = () => {
    resetAvailabilityType();
    clearDirtyStaff();
    dispatch([resetStaffSchedule(dirtyStaffIdList), resetStaffServiceArea(dirtyStaffIdList)]);
  };

  const onBeforeBusinessChange = useUnsavedConfirmV2(
    isDirty,
    undefined,
    handleStaffScheduleUpdate,
    handleStaffScheduleReset,
  );

  const handleViewTypeChange = useUnsavedConfirmV2(
    isDirty,
    undefined,
    () => {
      updateAvailabilitySettings();
    },
    handleStaffScheduleReset,
  );

  const onViewTypeChange = async (viewType: EnumValues<typeof StaffWorkingHourViewType>) => {
    await handleViewTypeChange();
    dispatch(staffScheduleViewTypeBox.setState(viewType));
  };

  const handleRouteToOB = () => {
    window.open(PATH_ONLINE_BOOKING_AVAILABILITY.build({ panel: 'Grooming' }), '_blank');
  };

  const limitationPreload = useLimitationPreload();
  const preload = useAsync(async () => {
    await Promise.all([
      getAvailabilityTypeAsync(`${business.id}`),
      sendMergeData(),
      dispatch([
        setStaffShiftManagementNewDisappear(),
        getBusinessDetail(),
        getOnlineBookingPreference(),
        getSyncStaffAvailabilityState(),
        getCompanyStaffList(),
        getCompanyPreferenceSetting(),
      ]),
    ]);
  }, [business.id]);

  const loading = preload.loading || limitationPreload.loading;

  useUnsavedConfirmGlobalV2({
    showConfirm: !loading && isDirty,
    modalProps: {
      content: 'Are you sure you want to save the changes?',
      onConfirm: handleStaffScheduleUpdate,
      onCancel: handleStaffScheduleReset,
    },
  });

  useUnmount(() => {
    dispatch(staffScheduleViewTypeBox.setState(StaffWorkingHourViewType.ListView));
    dispatch(editingWorkingHourStaffIdBox.setState(ID_ANONYMOUS));
    dispatch(setWorkingHourCurrentWeek());
  });

  const renderOBSyncAlert = () => {
    // 非白名单或者 OB 已经开启 sync，不显示 Alert
    if (!isEnableOBBySlot || obAvailabilitySync) {
      return null;
    }

    const description = isOBSettingDifferent ? (
      <>
        {' '}
        The availability type in Online booking is different, which may lead to setting conflicts in appointments. It’s
        recommended to change the type to the same in{' '}
        <Link className="moe-font-bold moe-cursor-pointer" onClick={handleRouteToOB}>
          {'Online booking > Settings > Availability'}
        </Link>{' '}
        by enabling Sync.
      </>
    ) : (
      <>
        Sync is off on the{' '}
        <Link className="moe-font-bold moe-cursor-pointer" onClick={handleRouteToOB}>
          {'Online Booking'}
        </Link>{' '}
        side. Turn it on to keep settings consistent and apply updates from here.
      </>
    );

    return <Alert isRounded color="warning" description={description} />;
  };

  useServiceArea(business.id);

  return (
    <Spin classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }} isLoading={loading}>
      <Card
        className="moe-mt-[-16px]"
        title={<ShiftManagementTitle onBeforeBusinessChange={onBeforeBusinessChange} />}
        headerClassName="!moe-px-none !moe-pb-[24px]"
        bodyClassName="!moe-p-none !moe-overflow-y-visible"
      >
        {isEnableOBBySlot && (
          <AvailabilityTypeRadioGroup
            className="moe-py-m"
            value={draftAvailabilityType.value}
            onChange={handleAvailabilityTypeChange}
          />
        )}

        {renderOBSyncAlert()}

        {/* Staff schedule */}
        <div className="moe-flex moe-justify-between moe-pt-m moe-pb-s moe-sticky moe-top-[-32px] moe-bg-white moe-z-10">
          <Heading size="4">Staff schedule</Heading>
          <SwitchStaffWorkingHourViewType value={viewType} onChange={onViewTypeChange} />
        </div>

        <StaffManagementNoAuthTip availabilityType={draftAvailabilityType.value} />

        {!loading && (
          <StaffScheduleDrawerProvider>
            {isListViewType ? (
              <StaffScheduleListView businessId={business.id} availabilityType={draftAvailabilityType} />
            ) : (
              <CalendarView businessId={business.id} availabilityType={draftAvailabilityType.value} />
            )}
          </StaffScheduleDrawerProvider>
        )}

        {isEnableDirtyCheckAndUpdate && !loading && isDirty ? (
          <div className="moe-sticky moe-bottom-[-40px]  moe-py-[20px] moe-bg-white moe-z-10 moe-flex moe-justify-center moe-gap-s">
            <Button
              variant="secondary"
              className="moe-min-w-[244px]"
              onPress={handleStaffScheduleReset}
              isDisabled={!isEnableDirtyCheckAndUpdate}
              data-testid={SMTestIds.SettingCancelAvailabilityBtn}
            >
              Cancel
            </Button>
            <Button
              isLoading={handleStaffScheduleUpdate.isBusy()}
              variant="primary"
              className="moe-min-w-[244px]"
              onPress={handleStaffScheduleUpdate}
              isDisabled={!isEnableDirtyCheckAndUpdate}
              data-testid={SMTestIds.SettingUpdateAvailabilityBtn}
            >
              Update availability settings
            </Button>
          </div>
        ) : null}
      </Card>
    </Spin>
  );
});

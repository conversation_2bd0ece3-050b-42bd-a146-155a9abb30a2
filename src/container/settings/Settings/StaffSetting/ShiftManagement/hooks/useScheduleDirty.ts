import { useSelector } from 'amos';
import type { EnumValues } from '../../../../../../store/utils/createEnum';
import { AvailabilityType } from '../../../../../../store/staffSchedule/staffSchedule.types';
import {
  staffScheduleServiceAreaMapBox,
  staffScheduleSlotFreeServicesMapBox,
  staffScheduleWorkingHourMapBox,
  staffScheduleWorkingSlotMapBox,
} from '../../../../../../store/staffSchedule/staffSchedule.boxes';
import { useEffect, useState, type RefObject } from 'react';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { isNormal } from '../../../../../../store/utils/identifier';

export interface ScheduleDirtyCheckOptions {
  staffId: number;
  availabilityType: RefObject<EnumValues<typeof AvailabilityType>>;
  draftAvailabilityType: EnumValues<typeof AvailabilityType>;
  /** 是否允许 dirty check */
  isEnable?: boolean;
}

/**
 * 计算 dirty staff list
 *
 * @returns 返回被改动过的 staffIdList
 */
export const useScheduleDirty = (options: ScheduleDirtyCheckOptions) => {
  const { staffId, draftAvailabilityType, availabilityType } = options;
  const [dirtyStaff, setDirtyStaff] = useState<{
    [key: EnumValues<typeof AvailabilityType>]: Set<number>;
  }>({
    [AvailabilityType.BY_TIME]: new Set(),
    [AvailabilityType.BY_SLOT]: new Set(),
  });

  const isDirty = useScheduleIsDirty(options);

  const clearDirtyStaff = () => {
    setDirtyStaff({
      [AvailabilityType.BY_TIME]: new Set(),
      [AvailabilityType.BY_SLOT]: new Set(),
    });
  };

  useEffect(() => {
    setDirtyStaff((prevDirtyStaff) => {
      const prevDirtyStaffIdList = prevDirtyStaff[draftAvailabilityType];
      const nextStaffIdList = new Set(prevDirtyStaffIdList);
      if (isDirty) {
        nextStaffIdList.add(staffId);
      } else {
        nextStaffIdList.delete(staffId);
      }

      return {
        ...prevDirtyStaff,
        [draftAvailabilityType]: nextStaffIdList,
      };
    });
  }, [staffId, isDirty, draftAvailabilityType]);

  const dirtyStaffIdList = Array.from(dirtyStaff[draftAvailabilityType]);
  const isAvailabilityTypeDirty = draftAvailabilityType !== availabilityType.current;

  return {
    dirtyStaffIdList,
    clearDirtyStaff,
    isDirty: dirtyStaffIdList.length > 0 || isAvailabilityTypeDirty,
  };
};

export const useScheduleIsDirty = (options: ScheduleDirtyCheckOptions) => {
  const { staffId, draftAvailabilityType, availabilityType, isEnable = true } = options;

  const isWorkingHourDirty = useWorkingHourIsDirty({ staffId });
  const isWorkingSlotDirty = useWorkingSlotIsDirty({ staffId });
  const isSlotFreeServicesDirty = useSlotFreeServicesIsDirty({ staffId });

  if (!isEnable || !isNormal(staffId)) {
    return false;
  }

  if (availabilityType.current !== draftAvailabilityType) {
    return true;
  }

  return draftAvailabilityType === AvailabilityType.BY_TIME
    ? isWorkingHourDirty
    : isWorkingSlotDirty || isSlotFreeServicesDirty;
};

const useWorkingHourIsDirty = ({ staffId }: { staffId: number }) => {
  const [staffScheduleWorkingHour, serviceArea, business] = useSelector(
    staffScheduleWorkingHourMapBox.mustGetItem(staffId),
    staffScheduleServiceAreaMapBox.mustGetItem(staffId),
    selectCurrentBusiness,
  );

  if (business.isMobileGrooming()) {
    return staffScheduleWorkingHour.isDirty() || serviceArea.isDirty();
  }

  return staffScheduleWorkingHour.isDirty();
};

const useWorkingSlotIsDirty = ({ staffId }: { staffId: number }) => {
  const [staffScheduleWorkingSlot] = useSelector(staffScheduleWorkingSlotMapBox.mustGetItem(staffId));

  return staffScheduleWorkingSlot.isDirty();
};

export const useSlotFreeServicesIsDirty = ({ staffId }: { staffId: number }) => {
  const [staffScheduleSlotFreeServices] = useSelector(staffScheduleSlotFreeServicesMapBox.mustGetItem(staffId));

  return staffScheduleSlotFreeServices.isDirty();
};

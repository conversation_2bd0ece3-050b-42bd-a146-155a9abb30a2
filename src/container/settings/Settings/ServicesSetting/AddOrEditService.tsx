import { ServiceModelSource } from '@moego/api-web/moego/models/offering/v1/service_models';
import { MinorCloseOutlined } from '@moego/icons-react';
import { AlertDialog, Button, Heading, IconButton, Spin, Tag, useForm, useFormState } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect } from 'react';
import { useHistory } from 'react-router';
import { WithPermission } from '../../../../components/GuardRoute/WithPermission';
import { toastApi } from '../../../../components/Toast/Toast';
import { ScrollerProvider } from '../../../../layout/components/ScrollerProvider';
import { PATH_ADD_OR_EDIT_SERVICE, PATH_SERVICE_SETTING } from '../../../../router/paths';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { getServiceEditableDetail } from '../../../../store/service/actions/private/service.actions';
import { getCompanyFullServiceInfoList } from '../../../../store/service/actions/public/service.actions';
import { ServiceType } from '../../../../store/service/category.boxes';
import { getCompanyStaffList } from '../../../../store/staff/staff.actions';
import { useRouteQueryV2, useRouteState } from '../../../../utils/RoutePath';
import { abortNavigation } from '../../../../utils/abortNavigation';
import { useBizIdReadyEffect } from '../../../../utils/hooks/useBizIdReadyEffect';
import { useBool } from '../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useScrollToSection } from '../../../../utils/hooks/useScrollToSection';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { useUnsavedConfirmGlobalV2 } from '../../../../utils/hooks/useUnsavedConfirmGlobalV2';
import { ServicesSettingContext } from './ServicesSettingContext';
import { AddOrEditServiceForm } from './components/AddOrEditServiceForm/AddOrEditServiceForm';
import { useApplyUpcomingModal } from './hooks/useApplyUpcomingModal';
import { useServiceRouteParams } from './hooks/useServiceRouteParams';
import { useServiceTitleConfig } from './hooks/useServiceTitleConfig';
import { type ServiceSettingForm, ServicesNav } from './types';
import { shouldShowApplyUpcomingModal } from './utils/shouldShowApplyUpcomingModal';
import { useSaveService } from './hooks/useSaveService';
import { useInitServiceSettingForm } from './hooks/useInitServiceSettingForm';
import { useGetService } from '../../../../utils/service/useGetService';

export const AddOrEditService = memo(function AddOrEditService() {
  const history = useHistory();
  const { serviceType, serviceItemType, isGroupClass } = useServiceRouteParams();
  const { isInactive, selectedBusinessId } = useRouteState(PATH_ADD_OR_EDIT_SERVICE) || {};
  const { serviceId = '', categoryId = '', isDuplicate, scrollToSection } = useRouteQueryV2(PATH_ADD_OR_EDIT_SERVICE);
  const [permissions] = useSelector(selectCurrentPermissions);

  const navType = serviceType === ServiceType.Service ? ServicesNav.Services : ServicesNav.Addons;
  const { title: serviceTitle, singularTitle } = useServiceTitleConfig(navType, serviceItemType);

  const isEditMode = !!serviceId && !isDuplicate;
  const saveService = useSaveService({
    serviceId,
    careTypeId: '',
    isEditMode,
    singularTitle,
    serviceType,
    serviceItemType,
  });
  const checkApplyUpcoming = useApplyUpcomingModal(serviceType, serviceItemType);
  const form = useForm<ServiceSettingForm>();
  const dispatch = useDispatch();
  const dedicatedStaffEditable = useBool();
  const clickedSubmit = useBool();
  const { isValid, isDirty } = useFormState({ control: form.control });
  const title = isEditMode ? `Edit ${singularTitle}` : `Add ${singularTitle}`;
  const service = useGetService(serviceId);
  const serviceSettingForm = useInitServiceSettingForm(serviceId, {
    serviceType,
    serviceItemType,
    categoryId,
    isDuplicate: !!isDuplicate,
  });

  const saveDoubleConfirm = async () => {
    return new Promise<void>((resolve, reject) => {
      AlertDialog.open({
        title: 'Confirm before saving',
        content: 'You are editing an existing service. Changes will only affect new and unenrolled upcoming sessions.',
        onConfirm: () => {
          resolve();
        },
        onCancel: reject,
        onClose: reject,
      });
    });
  };

  const handleSave = useSerialCallback(async () => {
    clickedSubmit.open();

    return form.handleSubmit(async (data) => {
      let applyUpcomingAppt = undefined;
      try {
        const isKeyInfoDirty = isEditMode && shouldShowApplyUpcomingModal(serviceSettingForm, data);
        if (isKeyInfoDirty) {
          if (isGroupClass) {
            await saveDoubleConfirm();
          } else {
            // 判断用户的改动是否需要应用到未来的预约
            const result = await checkApplyUpcoming();
            if (result === undefined) {
              return;
            }
            applyUpcomingAppt = result;
          }
        }

        const res = await saveService(data, applyUpcomingAppt);
        if (res) {
          form.reset(undefined, { keepValues: true, keepDirty: false, keepDefaultValues: false });
          // 等一下 toast 动画
          setTimeout(handleGoBack, 100);
        }
      } catch {
        clickedSubmit.close();
      }
    })();
  });

  const getData = useSerialCallback(async () => {
    await dispatch(getCompanyStaffList());
    if (!serviceId) {
      // 新增时默认可编辑
      dedicatedStaffEditable.open();
      return;
    }
    await dispatch(
      getCompanyFullServiceInfoList({
        serviceType,
        serviceItemType,
        inactive: !!isInactive,
        businessIds: selectedBusinessId ? [selectedBusinessId] : undefined,
      }),
    );
    const { requiredDedicatedStaffEditable } = await dispatch(getServiceEditableDetail(serviceId));
    if (requiredDedicatedStaffEditable) {
      dedicatedStaffEditable.open();
    } else {
      dedicatedStaffEditable.close();
    }
  });

  useBizIdReadyEffect(() => {
    getData();
  }, [serviceType, isInactive, selectedBusinessId]);

  useEffect(() => {
    if (serviceSettingForm) {
      form.reset(serviceSettingForm);
    }
  }, [serviceSettingForm]);

  const handleDoubleConfirm = useLatestCallback(async () => {
    if (!isValid) {
      // 触发 error
      await form.handleSubmit(() => {})();
      toastApi.error(`${singularTitle} save failed. Please fill in the required fields.`);
      abortNavigation();
    } else {
      await handleSave();
    }
  });

  const handleGoBack = useLatestCallback(() => {
    const panel = serviceType === ServiceType.Service ? ServicesNav.Services : ServicesNav.Addons;
    history.push(
      PATH_SERVICE_SETTING.stated({ selectedBusinessId, isInactive }, { panel, childPanel: `${serviceItemType}` }),
    );
  });

  const handleClose = useLatestCallback(() => {
    handleGoBack();
  });

  useUnsavedConfirmGlobalV2({
    showConfirm: isDirty,
    modalProps: {
      title: `${serviceTitle} has unsaved changes`,
      content: 'Would you like to save your changes before exiting?',
      onConfirm: handleDoubleConfirm,
      onCancel: handleGoBack,
      cancelText: 'Discard changes',
      confirmText: 'Save',
    },
  });

  useScrollToSection(scrollToSection);

  return (
    <ScrollerProvider
      style={{
        maxHeight: '100vh',
        padding: 0,
      }}
    >
      <ServicesSettingContext.Provider
        value={{
          form,
          isEdit: isEditMode,
          serviceItemType,
          serviceType,
          requiredDedicatedStaffEditable: dedicatedStaffEditable.value,
          clickedSubmit: clickedSubmit.value,
          isDisabledForRemains: !permissions.has(isEditMode ? 'updateService' : 'addService'),
          isDisabledForDuration: !permissions.has(isEditMode ? 'updateServiceDuration' : 'addService'),
          isDisabledForTax: !permissions.has(isEditMode ? 'updateServicePriceAndTax' : 'addService'),
          isDisabledForPetWeight: !permissions.has(isEditMode ? 'managePetWeightRange' : 'addService'),
          isDisabledForPetType: !permissions.has(isEditMode ? 'managePetTypeAndBreed' : 'addService'),
        }}
      >
        <div className="moe-pb-[16px] moe-px-[24px] moe-w-full moe-font-manrope">
          <div className="moe-flex moe-w-full moe-h-[72px] moe-justify-between moe-items-center moe-sticky moe-top-[0px] moe-z-[1] moe-bg-white">
            <IconButton icon={<MinorCloseOutlined />} onPress={handleClose} color="transparent" size="xl" />
            <WithPermission
              conditionOperator="or"
              permissions={
                isEditMode ? ['updateService', 'updateServiceDuration', 'updateServicePriceAndTax'] : ['addService']
              }
            >
              <Button onPress={handleSave} isLoading={handleSave.isBusy()} size="l">
                Save
              </Button>
            </WithPermission>
          </div>
          <div className="moe-flex moe-justify-center moe-mt-[16px] [@media(min-width:1450px)]:moe-grid [@media(min-width:1450px)]:moe-grid-cols-12 [@media(min-width:1450px)]:moe-gap-x-[24px]">
            <div className="[@media(max-width:1450px)]:moe-w-[700px] [@media(min-width:1450px)]:moe-col-start-4 [@media(min-width:1450px)]:moe-col-span-6">
              <Heading size="2" className="moe-mb-xl moe-flex moe-items-center">
                {title}
                {service.source === ServiceModelSource.ENTERPRISE_HUB && (
                  <Tag className="moe-ml-[4px]" color="discover" label="Corporate" />
                )}
              </Heading>
              <Spin
                isLoading={getData.isBusy()}
                classNames={{
                  base: 'moe-w-full moe-h-full',
                  container: 'moe-w-full moe-h-full',
                  iconContainer: 'moe-top-[20%]',
                }}
              >
                <AddOrEditServiceForm />
              </Spin>
            </div>
          </div>
        </div>
      </ServicesSettingContext.Provider>
    </ScrollerProvider>
  );
});

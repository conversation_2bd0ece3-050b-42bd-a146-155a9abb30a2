import { type CompanyServiceView } from '../../../../../store/service/service.types';
import { truly } from '../../../../../store/utils/utils';
import { AddToCommissionBaseMap, CanTipMap, RequireDedicatedLodgingMap, type ServiceSettingForm } from '../types';
import { ColorCodeMap, type TransformedServiceRecord } from './serviceOverrideLocationHandler';
import { omit, pick } from 'lodash';
import {
  ServiceItemType,
  ServicePriceUnit,
  type ServiceType,
} from '@moego/api-web/moego/models/offering/v1/service_enum';
import { isNormal } from '../../../../../store/utils/identifier';
import { ServiceModelSource } from '@moego/api-web/moego/models/offering/v1/service_models';
import { formatImageForUpload } from './formatImageForUpload';

export interface TransformToServiceModelParams {
  formData: ServiceSettingForm;
  careTypeId: string;
  serviceId?: string;
}

export const transformToServiceModel = (params: TransformToServiceModelParams): CompanyServiceView => {
  const { formData, serviceId = '', careTypeId } = params;
  const { imgList, categoryId = '' } = formData;

  return {
    ...pick(formData, ['name', 'description', 'colorCode', 'isActive', 'isAllBusiness', 'availableBusinessIdList']),
    serviceId,
    categoryId,
    images: imgList.map((item) => item.url).filter(truly),
    careTypeId,
    source: ServiceModelSource.MOEGO_PLATFORM,
    attributes: {
      ...pick(formData, [
        'duration',
        'maxDuration',
        'autoRolloverRule',
        'lodgingFilter',
        'customizedLodgings',
        'petSizeFilter',
        'weightRange',
        'petCodeFilter',
        'additionalServiceRule',
        'numSessions',
        'durationSessionMin',
        'capacity',
        'isRequirePrerequisiteClass',
        'prerequisiteClassIds',
        'availableForAllStaff',
        'availableStaffIdList',
        'isEvaluationRequired',
        'isEvaluationRequiredForOb',
        'evaluationId',
        'bundleServiceIds',
      ]),
    },
    pricing: {
      price: formData.price!,
      priceUnit: formData.priceUnit,
      taxId: formData.taxId!,
    },
  };
};

/**
 * transform existing service model to form model, for editing
 */
export const transformToFormModel = (service: CompanyServiceView): ServiceSettingForm => {
  return {
    ...omit(service, ['attributes', 'pricing']),
    ...service.attributes,
    ...service.pricing,
    imgList: service.images?.map(formatImageForUpload),
    serviceFilter: false,
    serviceFilterList: [],
    requireDedicatedStaff: false,
  };
};

export interface GetDefaultServiceSettingFormParams {
  service: CompanyServiceView;
  categoryId: string;
  serviceItemType: ServiceItemType;
}

/**
 * generate default service setting form, for creating new service
 */
export const getDefaultServiceSettingForm = (params: GetDefaultServiceSettingFormParams): ServiceSettingForm => {
  const { service, categoryId, serviceItemType } = params;

  const priceUnit =
    serviceItemType === ServiceItemType.BOARDING ? ServicePriceUnit.PER_NIGHT : ServicePriceUnit.PER_SESSION;
  const isBoardingOrDaycare =
    serviceItemType === ServiceItemType.BOARDING || serviceItemType === ServiceItemType.DAYCARE;

  return {
    ...omit(service, 'images', 'attributes', 'pricing'),
    imgList: [],
    categoryId: isNormal(categoryId) ? categoryId : undefined,
    colorCode: ColorCodeMap[serviceItemType] ?? '#000000',
    ...service.attributes,
    ...service.pricing,
    duration: undefined,
    numSessions: undefined,
    durationSessionMin: undefined,
    capacity: undefined,
    petCodeFilter: isBoardingOrDaycare
      ? {
          isAllPetCode: true,
          isWhiteList: true,
          petCodeIds: [],
        }
      : undefined,
    price: undefined,
    taxId: undefined,
    priceUnit,
    serviceFilter: false,
    serviceFilterList: [],
    requireDedicatedStaff: false,
  };
};

export interface TransformServiceModelToLegacyParams {
  service: ServiceSettingForm;
  serviceItemType: ServiceItemType;
  serviceType: ServiceType;
  applyUpcomingAppt?: boolean;
}

/**
 * transform service model to legacy service model, for compatibility with old service api
 */
export const transformServiceModelToLegacy = (
  params: TransformServiceModelToLegacyParams,
): Partial<TransformedServiceRecord> => {
  const { service, applyUpcomingAppt, serviceItemType, serviceType } = params;
  const type = `${serviceType}-${serviceItemType}`;
  const { categoryId, isActive, isAllBusiness, ...rest } = service;

  const customizedBreed =
    service.customizedBreed?.map((item) => ({
      ...item,
      isAll: !!item.isAll,
      petTypeId: Number(item.petTypeId),
    })) ?? [];

  const customizedCoat = service.customizedCoat?.map(Number) ?? [];
  const breedFilter = Number(service.breedFilter);
  const coatFilter = Number(service.coatFilter);
  const weightFilter = Number(service.weightFilter);

  return {
    ...rest,
    customizedBreed,
    customizedCoat,
    breedFilter,
    coatFilter,
    weightFilter,
    taxId: Number(service.taxId),
    categoryId: isNormal(categoryId) ? Number(categoryId) : 0,
    applyUpcomingAppt,
    requireDedicatedLodging: RequireDedicatedLodgingMap[type],
    canTip: CanTipMap[type],
    addToCommissionBase: AddToCommissionBaseMap[type],
    type: serviceType,
    serviceItemType,
    inactive: Number(!isActive),
    isAllLocation: Number(isAllBusiness),
    isAllStaff: 1,
  };
};

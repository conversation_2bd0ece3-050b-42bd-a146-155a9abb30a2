import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect } from 'react';
import { PATH_ADD_OR_EDIT_SERVICE, PATH_SERVICE_SETTING } from '../../../../../router/paths';
import { getRoleList } from '../../../../../store/business/role.actions';
import { getTaxList } from '../../../../../store/business/tax.actions';
import { getLodgingTypeList } from '../../../../../store/lodging/actions/public/lodgingType.actions';
import { getPetSizeList } from '../../../../../store/onlineBooking/actions/private/petSize.actions';
import { getPetHairLengthList } from '../../../../../store/pet/petHairLength.actions';
import { getPetTypeList } from '../../../../../store/pet/petType.actions';
import { selectSceneCareType } from '../../../../../store/careType/careType.selectors';
import { ServiceType } from '../../../../../store/service/category.boxes';
import { Scene } from '../../../../../store/service/scene.enum';
import { useRouteParams } from '../../../../../utils/RoutePath';
import { useBizIdReadyEffect } from '../../../../../utils/hooks/useBizIdReadyEffect';
import { AddOrEditService } from '../AddOrEditService';
import { useHistory } from 'react-router';

/**
 * add or edit service 全屏弹窗变成页面
 */
export const AddOrEditServicePortal = memo(function AddOrEditServicePortal() {
  const dispatch = useDispatch();
  const { serviceType, serviceItemType = ServiceItemType.GROOMING } = useRouteParams(PATH_ADD_OR_EDIT_SERVICE) || {};
  const [enableLodgingCareTypes] = useSelector(selectSceneCareType(Scene.EnableLodging));
  const history = useHistory();

  const isLegacyPricingRuleRoute = serviceType && !isNaN(+serviceType) && +serviceType < 0;

  useBizIdReadyEffect(() => {
    dispatch([getTaxList(), getPetHairLengthList(), getPetTypeList(), getPetSizeList(), getRoleList()]);

    if (+serviceType === ServiceType.Service && enableLodgingCareTypes.includes(+serviceItemType)) {
      dispatch(getLodgingTypeList());
    }
  }, []);

  useEffect(() => {
    // Compatible with the service charge and surcharge (BD) routes, which had been removed from addOrEditService for clarification,
    if (isLegacyPricingRuleRoute) {
      history.goBackWithFallback(PATH_SERVICE_SETTING.path);
    }
  }, [isLegacyPricingRuleRoute]);

  if (isLegacyPricingRuleRoute) {
    return null;
  }

  return <AddOrEditService />;
});

import { isEqual, sortBy } from 'lodash';
import { getValidLocationStaffOverrideList } from './serviceOverrideLocationHandler';
import { type ServiceSettingForm } from '../types';

export const shouldShowApplyUpcomingModal = (source: ServiceSettingForm, target: ServiceSettingForm) => {
  const isPriceChanged = source.price !== target.price;
  const isDurationChanged = source.duration !== target.duration;
  const isPriceUnitChanged = source.priceUnit !== target.priceUnit;
  const isTaxIdChanged = source.taxId !== target.taxId;
  const isMaxDurationChanged = source.maxDuration !== target.maxDuration;
  const isNumOfSessionsChanged = source.numSessions !== target.numSessions;
  const isCapacityChanged = source.capacity !== target.capacity;
  const isSessionDurationChanged = source.durationSessionMin !== target.durationSessionMin;

  const sourceValidBizStaffOverrideList = getValidLocationStaffOverrideList(source.locationStaffOverrideList);
  const targetValidBizStaffOverrideList = getValidLocationStaffOverrideList(target.locationStaffOverrideList);

  const isBizStaffOverrideListChanged = !isEqual(
    sortBy(sourceValidBizStaffOverrideList, 'businessId'),
    sortBy(targetValidBizStaffOverrideList, 'businessId'),
  );

  return (
    isPriceChanged ||
    isDurationChanged ||
    isPriceUnitChanged ||
    isTaxIdChanged ||
    isMaxDurationChanged ||
    isNumOfSessionsChanged ||
    isCapacityChanged ||
    isSessionDurationChanged ||
    isBizStaffOverrideListChanged
  );
};

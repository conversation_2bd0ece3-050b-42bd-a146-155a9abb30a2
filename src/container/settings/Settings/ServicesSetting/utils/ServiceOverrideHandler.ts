import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type LocationStaffOverrideRule } from '@moego/api-web/moego/models/offering/v1/service_models';
import { merge } from 'lodash';
import { isUndefinedOrNullOrEmptyString } from '../../../../../utils/common';
import { type LocationOverrideList, type TransformedServiceRecord } from './serviceOverrideLocationHandler';

const ALLOW_SBS_SERVICE_TYPE = [ServiceType.SERVICE];
const ALLOW_SBS_SERVICE_ITEM_TYPE = [ServiceItemType.GROOMING];

export class ServiceOverrideFactory {
  static create(serviceType: ServiceType, serviceItemType: ServiceItemType) {
    // 目前 service by staff 只支持 grooming service
    const isServiceByStaff =
      ALLOW_SBS_SERVICE_TYPE.includes(serviceType) && ALLOW_SBS_SERVICE_ITEM_TYPE.includes(serviceItemType);
    if (isServiceByStaff) {
      return new ServiceByStaffOverrideHandler();
    }
    return new NormalServiceOverrideHandler();
  }
}

/**
 * 处理 service 格式化参数的工具类
 *
 * @abstract
 * @class ServiceOverrideHandler
 */
abstract class ServiceOverrideHandler {
  /**
   * 根据不同的 service 类型格式化 override 参数
   *
   * @abstract
   * @memberof ServiceOverrideHandler
   */
  public abstract formatServiceOverride: (data: Partial<TransformedServiceRecord>) => void;

  /**
   * 处理是否选择 all location 的数据如果不是则过滤掉不需要的数据
   *
   * @protected
   * @param {LocationStaffOverrideRule[]} staffOverrideList
   * @param {Partial<TransformedServiceRecord>} data
   * @return {*}  {LocationStaffOverrideRule[]}
   * @memberof ServiceOverrideHandler
   */
  protected handleAvailableLocation(
    staffOverrideList: LocationStaffOverrideRule[],
    data: Partial<TransformedServiceRecord>,
  ): LocationStaffOverrideRule[] {
    const { isAllLocation, availableBusinessIdList = [] } = data;
    let result: LocationStaffOverrideRule[] = merge([], staffOverrideList);
    if (isAllLocation) {
      data.availableBusinessIdList = [];
    } else {
      result = result.filter((item) => availableBusinessIdList.includes(String(item.locationOverride.businessId)));
    }
    return result;
  }

  /**
   * 处理是否选择 all staff 的数据如果不是则过滤掉不需要的数据
   *
   * @protected
   * @param {LocationStaffOverrideRule[]} staffOverrideList
   * @param {Partial<TransformedServiceRecord>} data
   * @return {*}  {LocationStaffOverrideRule[]}
   * @memberof ServiceOverrideHandler
   */
  protected handleAvailableStaff(
    staffOverrideList: LocationStaffOverrideRule[],
    data: Partial<TransformedServiceRecord>,
  ): LocationStaffOverrideRule[] {
    const { availableForAllStaff, availableStaffIdList = [] } = data;
    let result: LocationStaffOverrideRule[] = merge([], staffOverrideList);
    if (availableForAllStaff) {
      data.availableStaffIdList = [];
    } else {
      result = result.map(({ staffOverrideList, ...rest }) => ({
        ...rest,
        staffOverrideList: staffOverrideList.filter((item) => availableStaffIdList.includes(item.staffId)),
      }));
    }
    return result;
  }

  /**
   * 处理无效的输入
   *
   * @protected
   * @param {LocationStaffOverrideRule[]} staffOverrideList
   * @return {*}
   * @memberof ServiceOverrideHandler
   */
  protected handleInvalidInput(staffOverrideList: LocationStaffOverrideRule[]) {
    return staffOverrideList.map(({ locationOverride, staffOverrideList }) => ({
      locationOverride: {
        businessId: locationOverride.businessId || undefined,
        taxId: isUndefinedOrNullOrEmptyString(locationOverride.taxId) ? undefined : locationOverride.taxId,
        price: isUndefinedOrNullOrEmptyString(locationOverride.price) ? undefined : Number(locationOverride.price),
        duration: isUndefinedOrNullOrEmptyString(locationOverride.duration)
          ? undefined
          : Number(locationOverride.duration),
        maxDuration: isUndefinedOrNullOrEmptyString(locationOverride.maxDuration)
          ? undefined
          : Number(locationOverride.maxDuration),
      },
      staffOverrideList: (staffOverrideList ?? []).map((staffOverride) => ({
        staffId: staffOverride.staffId,
        price: isUndefinedOrNullOrEmptyString(staffOverride.price) ? undefined : Number(staffOverride.price),
        duration: isUndefinedOrNullOrEmptyString(staffOverride.duration) ? undefined : Number(staffOverride.duration),
      })),
    })) as LocationStaffOverrideRule[];
  }

  /**
   * 兼容一下已经弃用的输入格式
   *
   * @protected
   * @param {LocationStaffOverrideRule[]} staffOverrideList
   * @return {*}  {LocationOverrideList}
   * @memberof ServiceOverrideHandler
   */
  protected handleDeprecatedInput(staffOverrideList: LocationStaffOverrideRule[]): LocationOverrideList {
    return staffOverrideList.map((item) => ({
      businessId: Number(item.locationOverride.businessId),
      price: isUndefinedOrNullOrEmptyString(item.locationOverride.price)
        ? undefined
        : Number(item.locationOverride.price),
      duration: isUndefinedOrNullOrEmptyString(item.locationOverride.duration)
        ? undefined
        : Number(item.locationOverride.duration),
      taxId: isUndefinedOrNullOrEmptyString(item.locationOverride.taxId)
        ? undefined
        : Number(item.locationOverride.taxId),
    }));
  }
}

/**
 * 单独处理 service by staff 类型的 handler 类
 *
 * @class ServiceByStaffOverrideHandler
 * @extends {ServiceOverrideHandler}
 */
class ServiceByStaffOverrideHandler extends ServiceOverrideHandler {
  public formatServiceOverride = (data: Partial<TransformedServiceRecord>) => {
    const initialList = data.locationStaffOverrideList ?? [];
    //  Clearly chain the handling functions
    const listAfterLocationHandling = this.handleAvailableLocation(initialList, data);
    const listAfterStaffHandling = this.handleAvailableStaff(listAfterLocationHandling, data);
    const finalList = this.handleInvalidInput(listAfterStaffHandling);
    const deprecatedList = this.handleDeprecatedInput(finalList);

    return {
      locationStaffOverrideList: finalList, // 覆盖新处理的列表
      locationOverrideList: deprecatedList, // 添加新的列表
    };
  };
}

/**
 * 常规处理 override 参数的 handler
 *
 * @class NormalServiceOverrideHandler
 * @extends {ServiceOverrideHandler}
 */
class NormalServiceOverrideHandler extends ServiceOverrideHandler {
  public formatServiceOverride = (data: Partial<TransformedServiceRecord>) => {
    const initialList = data.locationStaffOverrideList ?? [];

    //  Clearly chain the handling functions
    const listAfterLocationHandling = this.handleAvailableLocation(initialList, data);
    const finalList = this.handleInvalidInput(listAfterLocationHandling);
    const deprecatedList = this.handleDeprecatedInput(finalList);

    return {
      locationStaffOverrideList: finalList, // 覆盖新处理的列表
      locationOverrideList: deprecatedList, // 添加新的列表
    };
  };
}

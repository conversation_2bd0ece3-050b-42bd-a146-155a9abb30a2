import { UploadStatus } from '@moego/ui';
import { generateUID } from '../../../../../components/Upload/MoeGoUIUpload';

/**
 * Convert image URL to a file object suitable for the upload component
 * @param {string} image - Image URL
 * @param {number} index - Index in the array
 * @returns {{url: string, uid: string, status: string}} File object
 */

export const formatImageForUpload = (image: string, index: number) => ({
  url: image,
  uid: generateUID(index),
  status: UploadStatus.success,
});

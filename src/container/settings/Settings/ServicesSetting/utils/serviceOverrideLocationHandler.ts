import { ServiceItemType, ServicePriceUnit } from '@moego/api-web/moego/models/offering/v1/service_enum';
import {
  type LocationOverrideRule,
  type LocationStaffOverrideRule,
  type StaffOverrideRule,
} from '@moego/api-web/moego/models/offering/v1/service_models';
import {
  ServiceChargeApplyType,
  ServiceChargeAutoApplyCondition,
  ServiceChargeAutoApplyStatus,
  type ServiceChargeLocationOverride,
} from '@moego/api-web/moego/models/order/v1/service_charge_model';
import { type DeepPartial } from 'utility-types';
import { ServiceType } from '../../../../../store/service/category.boxes';
import { type ServiceChargeRecord, type ServiceRecord } from '../../../../../store/service/service.boxes';
import { type RecordProps } from '../../../../../store/utils/RecordMap';
import { type EnumValues } from '../../../../../store/utils/createEnum';
import { isNormal } from '../../../../../store/utils/identifier';
import { isUndefinedOrNull, isUndefinedOrNullOrEmptyString } from '../../../../../utils/common';
import { type ServiceSettingForm } from '../types';
import { formatImageForUpload } from './formatImageForUpload';

export type LocationOverrideList = RecordProps<ServiceRecord>['locationOverrideList'];

export type ServiceChargeLocationOverrideList = RecordProps<ServiceChargeRecord>['locationOverrideList'];

export type TransformedServiceRecord = RecordProps<ServiceRecord> & {
  /**
   * 这个是后端不存在的字段，只是为了方便前端处理
    这个字段代表着有具体 override 含义的列表，（有写 price 或者 tax 或者 duration），如果上述三个都没写，哪怕在 locationOverrideList 列表中，也不会被认为是有效的 override，前端界面一开始将不会展示
  */
};

export type TransformedServiceChargeRecord = RecordProps<ServiceChargeRecord> & {
  /**
   * 这个是后端不存在的字段，只是为了方便前端处理
    这个字段代表着有具体 override 含义的列表，（有写 price 或者 tax)，如果都没写，哪怕在 locationOverrideList 列表中，也不会被认为是有效的 override，前端界面一开始将不会展示
  */
  draftLocationOverrideList: ServiceChargeLocationOverrideList;
  applyUpcomingAppt: boolean;
};

export const getValidServiceChargeOverrideLocationList = (
  locationOverrideList?: DeepPartial<ServiceChargeLocationOverrideList>,
) => {
  return locationOverrideList?.filter(isValidServiceChargeOverrideLocation).map((item) => {
    return {
      businessId: item.businessId || undefined,
      taxId: isUndefinedOrNull(item.taxId) ? undefined : item.taxId,
      price: isUndefinedOrNull(item.price) ? undefined : `${item.price}`,
    };
  }) as ServiceChargeLocationOverrideList;
};

export const getValidLocationStaffOverrideList = (
  locationStaffOverrideList?: DeepPartial<LocationStaffOverrideRule[]>,
) => {
  return (
    ((locationStaffOverrideList ?? []) as LocationStaffOverrideRule[])
      ?.filter(isValidLocationStaffOverride)
      .map(({ locationOverride, staffOverrideList }) => ({
        locationOverride: isValidServiceOverrideLocation(locationOverride)
          ? {
              businessId: locationOverride.businessId || undefined,
              taxId: isUndefinedOrNull(locationOverride.taxId) ? undefined : locationOverride.taxId,
              price: isUndefinedOrNull(locationOverride.price) ? undefined : Number(locationOverride.price),
              duration: isUndefinedOrNull(locationOverride.duration) ? undefined : Number(locationOverride.duration),
              maxDuration: isUndefinedOrNull(locationOverride.maxDuration)
                ? undefined
                : Number(locationOverride.maxDuration),
            }
          : {
              businessId: locationOverride.businessId,
            },
        staffOverrideList: (staffOverrideList ?? [])
          ?.filter(isValidServiceStaffOverride)
          .map((staffOverride) => ({
            staffId: staffOverride.staffId,
            price: isUndefinedOrNullOrEmptyString(staffOverride.price) ? undefined : Number(staffOverride.price),
            duration: isUndefinedOrNullOrEmptyString(staffOverride.duration)
              ? undefined
              : Number(staffOverride.duration),
          }))
          .filter(isValidServiceStaffOverride),
      })) as LocationStaffOverrideRule[]
  ).filter(isValidLocationStaffOverride);
};

export const isValidLocationStaffOverride = (locationStaffOverride: LocationStaffOverrideRule) => {
  const { locationOverride, staffOverrideList } = locationStaffOverride;
  return isValidServiceOverrideLocation(locationOverride) || staffOverrideList?.some(isValidServiceStaffOverride);
};

/**
 * 判断是否是合法的 location override
 * 如果关键字段都是空的，那么就不用展示这个 location override
 */
export const isValidServiceOverrideLocation = (
  locationOverride:
    | DeepPartial<LocationOverrideList[number]>
    | DeepPartial<ServiceChargeLocationOverride>
    | DeepPartial<LocationOverrideRule>,
) => {
  const { taxId, price, duration, maxDuration } = (locationOverride as DeepPartial<LocationOverrideList[number]>) || {};

  return !(
    !isNormal(taxId) &&
    isUndefinedOrNull(price) &&
    isUndefinedOrNull(duration) &&
    isUndefinedOrNull(maxDuration)
  );
};

export const isValidServiceStaffOverride = (staffOverride: StaffOverrideRule) => {
  const { staffId, price, duration } = staffOverride;
  return isNormal(staffId) && (!isUndefinedOrNull(price) || !isUndefinedOrNull(duration));
};

/**
 * 判断是否是合法的 service charge location override
 * 如果关键字段都是空的，那么就不用展示这个 location override
 */
export const isValidServiceChargeOverrideLocation = (
  locationOverride: DeepPartial<ServiceChargeLocationOverrideList[0]>,
): boolean => {
  const { taxId, price } = locationOverride || {};

  return !(isUndefinedOrNull(taxId) && isUndefinedOrNull(price));
};

export const ColorCodeMap = {
  [ServiceItemType.BOARDING]: '#7AC5FF',
  [ServiceItemType.DAYCARE]: '#FFB865',
  [ServiceItemType.GROOMING]: '#000000',
} as Record<ServiceItemType, string>;

/**
 * 转换成需要展示 override list 的 location list 列表
 * 因为如果 price、taxId、duration 都为空（null、undefined）的话，是不需要展示成 location list 的。
 */
export const transformServiceLocalList = (
  service?: RecordProps<ServiceRecord>,
  serviceType: EnumValues<typeof ServiceType> = ServiceType.Service,
  serviceItemType: ServiceItemType = ServiceItemType.GROOMING,
  categoryId: string = '',
  isDuplicate = false,
): ServiceSettingForm => {
  if (!isNormal(service?.serviceId)) {
    // create flow
    const isBoardingOrDaycare =
      serviceType === ServiceType.Service &&
      (serviceItemType === ServiceItemType.BOARDING || serviceItemType === ServiceItemType.DAYCARE);

    const priceUnit =
      serviceItemType === ServiceItemType.BOARDING ? ServicePriceUnit.PER_NIGHT : ServicePriceUnit.PER_SESSION;

    return {
      name: '',
      imgList: [],
      description: '',
      taxId: undefined,
      isActive: true,
      isAllBusiness: true,
      availableBusinessIdList: [],
      availableForAllStaff: true,
      breedFilter: false,
      weightFilter: false,
      coatFilter: false,
      categoryId: isNormal(categoryId) ? categoryId : undefined,
      colorCode: ColorCodeMap[serviceItemType] || '#000000',
      serviceFilter: false,
      serviceFilterList: [],
      requireDedicatedStaff: false,
      petSizeFilter: false,
      lodgingFilter: serviceItemType === ServiceItemType.BOARDING,
      customizedLodgings: [],
      priceUnit,
      autoRolloverRule: {
        enabled: false,
        afterMinute: 0,
        targetServiceId: '0',
      },
      petCodeFilter: isBoardingOrDaycare
        ? {
            isAllPetCode: true,
            isWhiteList: true,
            petCodeIds: [],
          }
        : undefined,
      isRequirePrerequisiteClass: false,
      prerequisiteClassIds: [],
      isEvaluationRequired: false,
      isEvaluationRequiredForOb: false,
      additionalServiceRule: {
        enable: false,
        minStayLength: 1,
        applyRules: [],
      },
    };
  }
  const {
    inactive,
    isAllLocation,
    images,
    taxId,
    breedFilter,
    customizedBreed,
    weightFilter,
    coatFilter,
    customizedCoat,
    locationStaffOverrideList,
    customizedPetSizes,
  } = service;

  return {
    ...service,
    name: isDuplicate ? `${service.name} (copy)` : service.name,
    categoryId: isNormal(categoryId)
      ? categoryId
      : isNormal(service.categoryId)
        ? String(service.categoryId)
        : undefined,
    isActive: !inactive,
    isAllBusiness: !!isAllLocation,
    imgList: images.map(formatImageForUpload),
    locationStaffOverrideList: getValidLocationStaffOverrideList(locationStaffOverrideList),
    breedFilter: !!breedFilter,
    customizedBreed: customizedBreed.map((item) => ({
      ...item,
      petTypeId: String(item.petTypeId),
    })),
    customizedPetSizes: customizedPetSizes?.map(String) ?? [],
    weightFilter: !!weightFilter,
    coatFilter: !!coatFilter,
    customizedCoat: customizedCoat.map(String),
    taxId: String(taxId),
    evaluationId: isNormal(service.evaluationId) ? service.evaluationId : undefined,
  };
};

export const transformServiceChargeLocalOverrideList = (
  service?: RecordProps<ServiceChargeRecord>,
  isDuplicate = false,
): Partial<TransformedServiceChargeRecord> => {
  if (!service) {
    return {
      name: '',
      price: undefined,
      taxId: undefined,
      isActive: true,
      isAllLocation: true,
      autoApplyStatus: ServiceChargeAutoApplyStatus.AUTO_APPLY_DISABLED,
      autoApplyCondition: ServiceChargeAutoApplyCondition.UNSPECIFIED,
      autoApplyTime: 0,
      applyType: ServiceChargeApplyType.PER_APPOINTMENT,
    };
  }

  const { locationOverrideList } = service;
  const draftLocationOverrideList = getValidServiceChargeOverrideLocationList(locationOverrideList);

  return {
    ...service,
    name: isDuplicate ? `${service.name} (copy)` : service.name,
    draftLocationOverrideList,
  };
};

/**
 * 将前端操作的 override list 拼接起来给后端；
 * draftOverrideList 是只记录合法的 override list，不包含无效的 override list(无效的是指，只有 biz id，其他的 price、taxId、duration 都为空)
 * overrideList 是记录所有的 override list，包含无效的 override list
 * 所以最终以 overrideList 为准
 */
export const getLatestServiceLocationOverrideList = (
  overrideList?: DeepPartial<LocationOverrideList>,
  draftOverrideList?: DeepPartial<LocationOverrideList>,
) => {
  if (!overrideList && !draftOverrideList) {
    return undefined;
  }

  return overrideList?.map((item) => {
    const validItem = draftOverrideList?.find((validItem) => validItem.businessId === item.businessId);
    if (validItem) {
      return {
        ...validItem,
        price: isUndefinedOrNullOrEmptyString(validItem.price) ? undefined : Number(validItem.price),
        duration: isUndefinedOrNullOrEmptyString(validItem.duration) ? undefined : Number(validItem.duration),
      };
    }
    // 如果没有 validItem，意味着用户在界面上删掉了这个 override。那么就直接返回 biz id 即可
    return {
      businessId: item.businessId,
    };
  });
};

/**
 * service charge 的 location override list 操作，效果同 service override  list
 */
export const getLatestServiceChargeLocationOverrideList = (
  overrideList?: DeepPartial<ServiceChargeLocationOverrideList>,
  draftOverrideList?: DeepPartial<ServiceChargeLocationOverrideList>,
) => {
  if (!overrideList && !draftOverrideList) {
    return undefined;
  }

  return overrideList?.map((item) => {
    const validItem = draftOverrideList?.find((validItem) => validItem.businessId === item.businessId);
    if (validItem) {
      return {
        ...validItem,
        price: isUndefinedOrNullOrEmptyString(validItem.price) ? undefined : Number(validItem.price),
      };
    }
    // 如果没有 validItem，意味着用户在界面上删掉了这个 override。那么就直接返回 biz id 即可
    return {
      businessId: item.businessId,
    };
  });
};

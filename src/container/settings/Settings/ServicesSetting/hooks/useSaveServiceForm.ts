import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type AutoRolloverRule } from '@moego/api-web/moego/models/offering/v1/service_models';
import { useDispatch, useSelector, useStore } from 'amos';
import { cloneDeep, upperFirst } from 'lodash';
import { toastApi } from '../../../../../components/Toast/Toast';
import { selectBDFeatureEnable } from '../../../../../store/company/company.selectors';
import { petBreedMapBox } from '../../../../../store/pet/petBreed.boxes';
import { selectPetBreeds } from '../../../../../store/pet/petBreed.selectors';
import { createService, updateService } from '../../../../../store/service/actions/private/service.actions';
import { getBusinessBasicServiceInfoList } from '../../../../../store/service/actions/public/service.actions';
import { type ServiceRecord } from '../../../../../store/service/service.boxes';
import { type PartialRequired } from '../../../../../store/utils/RecordMap';
import { isUndefinedOrNullOrEmptyString } from '../../../../../utils/common';
import { ServiceOverrideFactory } from '../utils/ServiceOverrideHandler';
import { type TransformedServiceRecord } from '../utils/serviceOverrideLocationHandler';

export const useSaveServiceForm = (
  isEditMode: boolean,
  serviceTypeName: string,
  serviceType: number,
  serviceItemType: number,
) => {
  const store = useStore();
  const dispatch = useDispatch();
  const [enableBD] = useSelector(selectBDFeatureEnable);

  return async (source: Partial<TransformedServiceRecord>): Promise<boolean> => {
    const data = cloneDeep(source);
    data.serviceItemType = serviceItemType;
    const isDaycareService = serviceType === ServiceType.SERVICE && serviceItemType === ServiceItemType.DAYCARE;
    const isBoardingService = serviceType === ServiceType.SERVICE && serviceItemType === ServiceItemType.BOARDING;
    const { price, categoryId, weightFilter, weightRange, breedFilter, customizedBreed } = data;

    if (!isUndefinedOrNullOrEmptyString(price)) {
      data.price = Number(price);
    }

    if (isUndefinedOrNullOrEmptyString(categoryId)) {
      data.categoryId = 0; // 说明没选 category
    }

    const serviceOverrideHandler = ServiceOverrideFactory.create(serviceType, serviceItemType);
    const { locationStaffOverrideList, locationOverrideList } = serviceOverrideHandler.formatServiceOverride(data);
    data.locationStaffOverrideList = locationStaffOverrideList;
    data.locationOverrideList = locationOverrideList;

    if (weightFilter) {
      data.weightRange = weightRange?.map((item) => {
        return isUndefinedOrNullOrEmptyString(item) ? 0 : Number(item);
      });
    }

    if (!breedFilter) {
      data.customizedBreed = [];
    }

    if (breedFilter) {
      const validBreedList = customizedBreed?.filter((item) => {
        if (isUndefinedOrNullOrEmptyString(item.petTypeId)) {
          return false;
        }
        /* eslint-disable sonarjs/prefer-single-boolean-return */
        if (!item.isAll && item.breeds?.length === 0) {
          return false;
        }
        return true;
      });
      if (validBreedList?.length === 0) {
        toastApi.error('Please select at least one pet breed');
        return false;
      }
      data.customizedBreed = validBreedList?.map((item) => {
        if (!item.isAll) {
          return item;
        }
        const breedMap = store.select(petBreedMapBox);
        const allBreed = store.select(selectPetBreeds(item.petTypeId));
        return {
          ...item,
          breeds: allBreed
            .toJSON()
            .map((id) => {
              const item = breedMap.mustGetItem(id);
              return item.name;
            })
            .filter(Boolean),
        };
      });
    }

    if (!data.coatFilter) {
      data.customizedCoat = [];
    }

    data.images = data.imgList?.map((photo) => photo.url!);

    if (!enableBD) {
      // require staff 的控制能力只对白名单用户开放。其余用户默认为true
      data.requireDedicatedStaff = true;
    }

    // 非 daycare 不需要这个字段
    if (!isDaycareService) {
      delete data.autoRolloverRule;
    } else if (!data.autoRolloverRule?.enabled) {
      // 如果不开启 autoRolloverRule，其他属性就清理掉，后端不 care
      data.autoRolloverRule = {
        enabled: false,
      } as AutoRolloverRule;
    }

    if (isDaycareService || isBoardingService) {
      if (data.petCodeFilter?.isAllPetCode) {
        data.petCodeFilter.petCodeIds = [];
      }
    }

    // 只有 boarding service 才需要这个字段
    if (!isBoardingService) {
      if (data.additionalServiceRule) {
        delete data.additionalServiceRule;
      }
    }

    if (!isEditMode) {
      // add 或者 copy 的情况，就不应该有 serviceId
      delete data.serviceId;
      await dispatch(createService(data));
      toastApi.success(`New ${serviceTypeName} added!`);
      return true;
    } else {
      await dispatch(
        updateService(
          data as PartialRequired<ServiceRecord, 'serviceId' | 'type' | 'serviceItemType'>,
          data?.applyUpcomingAppt,
        ),
      );
      toastApi.success(`${upperFirst(serviceTypeName)} updated!`);
      // 更新完后重新拉取一次 biz 数据，避免消费端用的数据还是旧的
      dispatch(
        getBusinessBasicServiceInfoList({
          serviceType,
          inactive: Boolean(data.inactive),
        }),
      );
      return true;
    }
  };
};

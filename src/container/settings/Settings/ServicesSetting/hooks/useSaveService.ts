import { type ServiceItemType, type ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useSaveServiceForm } from './useSaveServiceForm';
import { useEnableFulfillmentFlow } from '../../../../../utils/hooks/useEnableFulfillmentFlow';
import { type ServiceSettingForm } from '../types';
import { transformServiceModelToLegacy, transformToServiceModel } from '../utils/AddOrEditServiceFormTransformer';
import {
  createCompanyService,
  updateCompanyService,
} from '../../../../../store/service/actions/private/companyService.actions';
import { useDispatch } from 'amos';
import { toastApi } from '../../../../../components/Toast/Toast';
import { upperFirst } from 'lodash';

export interface SaveServiceParams {
  isEditMode: boolean;
  singularTitle: string;
  serviceType: ServiceType;
  serviceItemType: ServiceItemType;
  careTypeId: string;
  serviceId?: string;
}

export const useSaveService = (params: SaveServiceParams) => {
  const { isEditMode, singularTitle, serviceType, serviceItemType, serviceId, careTypeId } = params;

  const dispatch = useDispatch();
  const enableFulfillmentFlow = useEnableFulfillmentFlow();
  const saveServiceForm = useSaveServiceForm(isEditMode, singularTitle, serviceType, serviceItemType);

  return async (formData: ServiceSettingForm, applyUpcomingAppt?: boolean) => {
    const serviceModelData = transformToServiceModel({ formData, serviceId, careTypeId });

    if (!enableFulfillmentFlow) {
      const data = transformServiceModelToLegacy({
        service: formData,
        serviceItemType,
        serviceType,
        applyUpcomingAppt,
      });
      return await saveServiceForm(data);
    }

    if (isEditMode) {
      await dispatch(updateCompanyService(serviceModelData, applyUpcomingAppt));
      toastApi.success(`${upperFirst(singularTitle)} updated!`);
    } else {
      await dispatch(createCompanyService(serviceModelData));
      toastApi.success(`New ${singularTitle} added!`);
    }
  };
};

import { type ServiceItemType, type ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type ServiceSettingForm } from '../types';
import { useGetService } from '../../../../../utils/service/useGetService';
import { useEnableFulfillmentFlow } from '../../../../../utils/hooks/useEnableFulfillmentFlow';
import { transformServiceLocalList } from '../utils/serviceOverrideLocationHandler';
import { type ServiceRecord } from '../../../../../store/service/service.boxes';
import { type RecordProps } from '../../../../../store/utils/RecordMap';
import { getDefaultServiceSettingForm, transformToFormModel } from '../utils/AddOrEditServiceFormTransformer';
import { type CompanyServiceView } from '../../../../../store/service/service.types';
import { useMemo } from 'react';
import { isNormal } from '../../../../../store/utils/identifier';

export interface InitServiceSettingFormOptions {
  serviceType: ServiceType;
  serviceItemType: ServiceItemType;
  categoryId: string;
  isDuplicate: boolean;
}

export const useInitServiceSettingForm = (
  serviceId: string,
  options: InitServiceSettingFormOptions,
): ServiceSettingForm => {
  const { serviceType, serviceItemType, categoryId, isDuplicate } = options;

  const service = useGetService(serviceId);
  const enableFulfillmentFlow = useEnableFulfillmentFlow();

  return useMemo(() => {
    if (!enableFulfillmentFlow) {
      return transformServiceLocalList(
        service.toJSON() as RecordProps<ServiceRecord>,
        serviceType,
        serviceItemType,
        categoryId,
        isDuplicate,
      );
    }
    if (!isNormal(serviceId)) {
      return getDefaultServiceSettingForm({
        service: service.toJSON() as CompanyServiceView,
        categoryId,
        serviceItemType,
      });
    }
    return transformToFormModel(service.toJSON() as CompanyServiceView);
  }, [service, enableFulfillmentFlow, serviceType, serviceItemType, categoryId, isDuplicate]);
};

import { useSelector } from 'amos';
import { UN_CATEGORY_ID } from '../../../../../store/service/category.boxes';
import { selectBusinessServiceCategories } from '../../../../../store/service/category.selectors';
import { type ServiceItemType, type ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { currentAccountIdBox } from '../../../../../store/account/account.boxes';
import { useEnableFulfillmentFlow } from '../../../../../utils/hooks/useEnableFulfillmentFlow';
import { useMemo } from 'react';
import { ALL_LOCATIONS } from '../../../../../components/Business/SingleLocationSelector';
import { selectGroupedCompanyServices } from '../../../../../store/service/companyService.selectors';
import { selectInactiveCompanyServices } from '../../../../../store/service/inactiveCompanyService.selectors';
import { selectInactiveCategoryServices, selectServiceList } from '../../../../../store/service/service.selectors';
import { isNormal } from '../../../../../store/utils/identifier';

export interface UseServiceCategoryListParams {
  serviceType: ServiceType;
  serviceItemType: ServiceItemType;
  selectedBusinessId: string;
  careTypeId: string;
  isActive: boolean;
}

export const useServiceCategoryList = (params: UseServiceCategoryListParams) => {
  const { serviceType, serviceItemType, selectedBusinessId, careTypeId, isActive } = params;
  const enableFulfillmentFlow = useEnableFulfillmentFlow();
  const [currentAccountId] = useSelector(currentAccountIdBox);
  const businessIdForSelector = selectedBusinessId === ALL_LOCATIONS ? '' : selectedBusinessId;
  const careTypeIds = useMemo<string[]>(() => {
    return isNormal(careTypeId) ? [careTypeId] : [];
  }, [careTypeId]);

  const type = `${serviceType}-${serviceItemType}`;

  const [categories, legacyActiveServiceList, legacyInactiveServiceList, groupedList, inactiveServiceList] =
    useSelector(
      selectBusinessServiceCategories(type, currentAccountId),
      selectServiceList(type, currentAccountId),
      selectInactiveCategoryServices(type, currentAccountId),
      selectGroupedCompanyServices(businessIdForSelector, careTypeIds),
      selectInactiveCompanyServices(businessIdForSelector, careTypeIds),
    );

  const categoryIdList = useMemo(() => {
    if (!enableFulfillmentFlow) {
      // uncategory first
      return [UN_CATEGORY_ID].concat(categories.toJSON()).map(String);
    }

    return groupedList.map(([categoryId]) => categoryId);
  }, [enableFulfillmentFlow, categories, groupedList]);

  const serviceList = useMemo(() => {
    if (!enableFulfillmentFlow) {
      return isActive ? legacyActiveServiceList.toArray() : legacyInactiveServiceList.toArray();
    }
    return isActive ? groupedList.flatMap(([, serviceIdList]) => serviceIdList) : inactiveServiceList.toArray();
  }, [
    enableFulfillmentFlow,
    isActive,
    groupedList,
    inactiveServiceList,
    legacyActiveServiceList,
    legacyInactiveServiceList,
  ]);

  return {
    serviceList,
    categoryIdList,
  };
};

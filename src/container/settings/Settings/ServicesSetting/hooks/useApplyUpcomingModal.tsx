import { type ServiceItemType, type ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import React from 'react';
import { useAsyncCallback } from '../../../../../utils/hooks/useAsyncCallback';
import { useFloatableHost } from '../../../../../utils/hooks/useFloatableHost';
import { type ActionType, ApplyToUpcomingModal } from '../components/ApplyToUpcomingModal';

type ApplyUpcomingModalProps = {
  action: ActionType;
  title?: string;
  contentTitle?: string;
  confirmText?: string;
  confirmColor?: 'brand' | 'danger';
};

export const useApplyUpcomingModal = (serviceType: ServiceType, serviceItemType: ServiceItemType) => {
  const { mountModal } = useFloatableHost<boolean>();

  const applyUpcomingModal = useAsyncCallback((props?: ApplyUpcomingModalProps) => {
    const { promise, closeFloatable: closeModal } = mountModal(
      <ApplyToUpcomingModal
        {...props}
        serviceType={serviceType}
        serviceItemType={serviceItemType}
        onClose={() => {
          closeModal();
        }}
        onConfirm={(val: boolean) => {
          closeModal(val);
        }}
      />,
    );
    return promise;
  });

  return applyUpcomingModal;
};

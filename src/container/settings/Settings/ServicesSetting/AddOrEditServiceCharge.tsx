import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { SurchargeType } from '@moego/api-web/moego/models/order/v1/service_charge_enums';
import { ServiceChargeAutoApplyStatus } from '@moego/api-web/moego/models/order/v1/service_charge_model';
import { MinorCloseOutlined } from '@moego/icons-react';
import { Button, Heading, IconButton, useForm, useFormState } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { useHistory } from 'react-router';
import { toastApi } from '../../../../components/Toast/Toast';
import { ScrollerProvider } from '../../../../layout/components/ScrollerProvider';
import { PATH_ADD_OR_EDIT_SERVICE_CHARGE, PATH_SERVICE_SETTING } from '../../../../router/paths';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { selectBDFeatureEnable } from '../../../../store/company/company.selectors';
import {
  addCompanyServiceCharge,
  getCompanyServiceChargeList,
  updateCompanyServiceCharge,
} from '../../../../store/service/actions/private/serviceCharge.actions';
import { type ServiceChargeRecord, serviceChargeMapBox } from '../../../../store/service/service.boxes';
import { type PartialRequiredProps } from '../../../../store/utils/RecordMap';
import { useRouteQueryV2, useRouteState } from '../../../../utils/RoutePath';
import { abortNavigation } from '../../../../utils/abortNavigation';
import { isUndefinedOrNullOrEmptyString } from '../../../../utils/common';
import { useBizIdReadyEffect } from '../../../../utils/hooks/useBizIdReadyEffect';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { useUnsavedConfirmGlobalV2 } from '../../../../utils/hooks/useUnsavedConfirmGlobalV2';
import { type ApplyUpcomingModalConfig, ServicesSettingContext } from './ServicesSettingContext';
import { AddOrEditServiceChargeForm } from './components/AddOrEditServiceChargeForm/AddOrEditServiceChargeForm';
import { useApplyUpcomingModal } from './hooks/useApplyUpcomingModal';
import {
  type TransformedServiceChargeRecord,
  getLatestServiceChargeLocationOverrideList,
  transformServiceChargeLocalOverrideList,
} from './utils/serviceOverrideLocationHandler';
import { getTaxList } from '../../../../store/business/tax.actions';
import { ServicesNav } from './types';
import { isNormal } from '../../../../store/utils/identifier';

export const AddOrEditServiceCharge = memo(function AddOrEditServiceCharge() {
  const history = useHistory();
  const form = useForm<Partial<TransformedServiceChargeRecord>>({
    mode: 'onBlur',
  });
  const [serviceChargeMap, isBD, permissions] = useSelector(
    serviceChargeMapBox,
    selectBDFeatureEnable,
    selectCurrentPermissions,
  );
  const { isActive, selectedBusinessId } = useRouteState(PATH_ADD_OR_EDIT_SERVICE_CHARGE) || {};
  const { id, isDuplicate } = useRouteQueryV2(PATH_ADD_OR_EDIT_SERVICE_CHARGE);
  const data = id ? serviceChargeMap.mustGetItem(id) : undefined;
  const dispatch = useDispatch();
  const checkApplyUpcoming = useApplyUpcomingModal(ServiceType.UNSPECIFIED, ServiceItemType.UNSPECIFIED);
  const [applyUpcomingConfig, setApplyUpcomingConfig] = useState<ApplyUpcomingModalConfig>();
  const { isValid, dirtyFields } = useFormState({ control: form.control });

  const isEdit = !!id && !isDuplicate;

  const title = isEdit ? `Edit service charge` : `Add service charge`;

  const service = useMemo(() => {
    return transformServiceChargeLocalOverrideList(data?.toJSON(), !!isDuplicate);
  }, [data, isDuplicate]);

  const handleSave = useSerialCallback(async () => {
    return form.handleSubmit(async (data) => {
      // 判断用户的改动是否需要应用到未来的预约
      if (applyUpcomingConfig?.visible) {
        const result = await checkApplyUpcoming({ action: applyUpcomingConfig.action! });
        if (result === undefined) {
          return;
        }
        data.applyUpcomingAppt = result;
      }

      if (!isUndefinedOrNullOrEmptyString(data.price)) {
        data.price = Number(data.price);
      }

      // locationOverrideList里的对象可能除businessId不带其他有效信息，无需过滤掉，后端需要使用
      if (data.isAllLocation && data.draftLocationOverrideList) {
        data.locationOverrideList = data.draftLocationOverrideList.map((item) => {
          return {
            ...item,
            price: isUndefinedOrNullOrEmptyString(item.price) ? undefined : Number(item.price),
          };
        });
      }
      if (!data.isAllLocation) {
        data.locationOverrideList = getLatestServiceChargeLocationOverrideList(
          data.locationOverrideList,
          data.draftLocationOverrideList,
        );
      }

      if (!data.applyUpcomingAppt) {
        data.applyUpcomingAppt = false;
      }

      // 非bd用户不使用这两个参数
      if (!isBD) {
        data.autoApplyCondition = undefined;
        data.autoApplyTime = undefined;
      }

      // 如果 autoApplyCondition 不存在或者是默认值 0，说明用户并没有选这个选项
      // 后端不允许传 0，所以这里需要置空
      if (!data.autoApplyCondition) {
        data.autoApplyCondition = undefined;
      }

      // 对于非 BD 用户，autoApplyStatus 为 AUTO_APPLY_ENABLED 时，serviceItemTypes 需要添加上所需 careTypes 后端没法区分是否是 BD 用户
      if (data.autoApplyStatus === ServiceChargeAutoApplyStatus.AUTO_APPLY_ENABLED && !isBD) {
        data.serviceItemTypes = [ServiceItemType.GROOMING];
      }

      if (!isEdit) {
        // add 或者 copy 的情况，就不应该有 Id
        delete data.id;
        await dispatch(
          addCompanyServiceCharge(
            { ...data, surchargeType: SurchargeType.CUSTOM_FEE } as ServiceChargeRecord,
            isActive,
          ),
        );
        toastApi.success('New service charge added!');
      } else {
        await dispatch(
          updateCompanyServiceCharge(
            { ...data, surchargeType: SurchargeType.CUSTOM_FEE } as PartialRequiredProps<ServiceChargeRecord, 'id'>,
            isActive,
          ),
        );
        toastApi.success('Service charge updated!');
      }
      // 置空 dirty 态
      form.reset(undefined, { keepValues: true, keepDirty: false, keepDefaultValues: false });
      setTimeout(() => {
        handleGoBack();
        // 等一下 toast 动画
      }, 100);
    })();
  });

  useEffect(() => {
    if (service) {
      form.reset(service);
    }
  }, [service]);

  const handleGoBack = useLatestCallback(() => {
    const businessId = isNormal(selectedBusinessId) ? Number(selectedBusinessId) : undefined;
    history.push(
      PATH_SERVICE_SETTING.stated(
        { selectedBusinessId: businessId, isInactive: !isActive },
        { panel: ServicesNav.ServiceCharges },
      ),
    );
  });

  const handleDoubleConfirm = useLatestCallback(async () => {
    if (!isValid) {
      // 触发 error
      await form.handleSubmit(() => {})();
      toastApi.error(`Service charge save failed. Please fill in the required fields.`);
      abortNavigation();
    } else {
      await handleSave();
    }
  });

  const handleClose = useLatestCallback(() => {
    handleGoBack();
  });

  const getData = useLatestCallback(async () => {
    await Promise.all([
      dispatch(getTaxList()),
      dispatch(
        getCompanyServiceChargeList({
          isActive,
          businessIds: selectedBusinessId ? [`${selectedBusinessId}`] : [],
          surchargeType: SurchargeType.CUSTOM_FEE,
        }),
      ),
    ]);
  });

  useBizIdReadyEffect(() => {
    getData();
  }, [isActive, selectedBusinessId]);

  // 原 isDirty 字段在不知道哪个地方被手动污染了，导致无法正常判断是否需要弹窗。
  // 这里使用 dirtyFields 来判断是否需要弹窗
  const isNotDirty = !dirtyFields || Object.keys(form.formState.dirtyFields).length === 0;
  useUnsavedConfirmGlobalV2({
    showConfirm: !isNotDirty,
    modalProps: {
      title: `Service charge has unsaved changes`,
      content: 'Would you like to save your changes before exiting?',
      onConfirm: handleDoubleConfirm,
      onCancel: handleGoBack,
      cancelText: 'Discard changes',
      confirmText: 'Save',
    },
  });

  return (
    <ScrollerProvider
      style={{
        maxHeight: '100vh',
        padding: 0,
      }}
    >
      <ServicesSettingContext.Provider
        value={{
          serviceItemType: ServiceItemType.UNSPECIFIED,
          serviceType: ServiceType.UNSPECIFIED,
          setApplyUpcomingModalConfig: setApplyUpcomingConfig,
          isDisabledForRemains: !permissions.has(isEdit ? 'updateService' : 'addService'),
          isDisabledForDuration: !permissions.has(isEdit ? 'updateServiceDuration' : 'addService'),
          isDisabledForTax: !permissions.has(isEdit ? 'updateServicePriceAndTax' : 'addService'),
        }}
      >
        <div className="moe-pb-[16px] moe-px-[24px] moe-w-full moe-font-manrope">
          <div className="moe-flex moe-w-full moe-h-[72px] moe-justify-between moe-items-center moe-sticky moe-top-[0px] moe-z-[1] moe-bg-white">
            <IconButton icon={<MinorCloseOutlined />} onPress={handleClose} color="transparent" size="xl" />
            <Button onPress={handleSave} isLoading={handleSave.isBusy()} size="l">
              Save
            </Button>
          </div>
          <div className="moe-flex moe-justify-center moe-mt-[16px] [@media(min-width:1450px)]:moe-grid [@media(min-width:1450px)]:moe-grid-cols-12 [@media(min-width:1450px)]:moe-gap-x-[24px]">
            <div className="[@media(max-width:1450px)]:moe-w-[670px] [@media(min-width:1450px)]:moe-col-start-4 [@media(min-width:1450px)]:moe-col-span-6">
              <Heading size="2" className="moe-mb-xl">
                {title}
              </Heading>
              <AddOrEditServiceChargeForm form={form} isEdit={isEdit} />
            </div>
          </div>
        </div>
      </ServicesSettingContext.Provider>
    </ScrollerProvider>
  );
});

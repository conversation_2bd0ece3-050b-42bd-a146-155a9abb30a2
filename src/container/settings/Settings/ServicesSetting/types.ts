import { ServiceItemType, type ServicePriceUnit } from '@moego/api-web/moego/models/offering/v1/service_enum';
import {
  AddonItemType,
  EVALUATION_TYPE,
  PRICING_RULE_TYPE,
  SERVICE_CHARGE_TYPE,
  ServiceType,
} from '../../../../store/service/category.boxes';
import { createEnum } from '../../../../store/utils/createEnum';
import { type SettingsLeftNavItem } from '../types';
import { type PricingRuleTypes } from './components/PricingRules/PricingRules.enum';
import { type CustomizeCareTypeView } from '@moego/api-web/moego/models/offering/v1/customize_care_type_model';
import { type CompanyServiceView } from '../../../../store/service/service.types';
import { type UploadItem } from '@moego/ui';
import { type ServiceFilter } from '@moego/api-web/moego/models/offering/v1/service_models';

export type ServiceSettingForm = Omit<
  CompanyServiceView,
  'serviceId' | 'images' | 'categoryId' | 'careTypeId' | 'sort' | 'source' | 'pricing' | 'attributes'
> &
  CompanyServiceView['attributes'] & {
    imgList: UploadItem[];
    categoryId?: string;
    price?: number;
    priceUnit: ServicePriceUnit;
    taxId?: string;
    // 以下是兼容旧版保留字段，重构全量之后可以删掉
    serviceFilter: boolean;
    serviceFilterList: ServiceFilter[];
    requireDedicatedStaff: boolean;
  };

export enum ServicesNav {
  Services = 'services',
  Addons = 'addons',
  ServiceCharges = 'serviceCharges',
  Evaluation = 'evaluation',
  PricingRules = 'pricingRules',
}
export type ServicesChildNav = `${ServiceItemType}` | `${PricingRuleTypes}`;

export type ServiceSettingsLeftNav = ServicesNav | ServicesChildNav;

export const ServicesNavType = createEnum<string, ServicesNav, { title: string; singularTitle: string }>({
  Service: [
    ServicesNav.Services,
    {
      title: 'Services',
      singularTitle: 'service',
    },
  ],
  Addons: [
    ServicesNav.Addons,
    {
      title: 'Add-ons',
      singularTitle: 'add-on',
    },
  ],
  ServiceCharge: [
    ServicesNav.ServiceCharges,
    {
      title: 'Service charges',
      singularTitle: 'service charge',
    },
  ],
  Evaluation: [
    ServicesNav.Evaluation,
    {
      title: 'Evaluation',
      singularTitle: 'evaluation',
    },
  ],
  PricingRules: [
    ServicesNav.PricingRules,
    {
      title: 'Pricing rules',
      singularTitle: 'pricing rule',
    },
  ],
});

export const getSettingServicesNavItem = (serviceNav: ServicesNav) => {
  return {
    id: serviceNav,
    title: ServicesNavType.mapLabels[serviceNav].title,
    path: serviceNav,
  };
};

export const getCareTypesLeftNav = (
  careTypeList: CustomizeCareTypeView[],
): SettingsLeftNavItem<ServiceSettingsLeftNav> => {
  return careTypeList.length > 1
    ? {
        title: ServicesNavType.mapLabels[ServicesNav.Services].title,
        id: ServicesNav.Services,
        childList: careTypeList.map(({ serviceItemType, name }) => {
          const careTypeStr = String(serviceItemType) as ServicesChildNav;
          return {
            id: careTypeStr,
            title: name,
            path: careTypeStr,
          };
        }),
      }
    : getSettingServicesNavItem(ServicesNav.Services);
};

export const serviceTypeMap: Record<ServicesNav, number> = {
  [ServicesNav.Services]: ServiceType.Service,
  [ServicesNav.Addons]: ServiceType.Addon,
  [ServicesNav.ServiceCharges]: SERVICE_CHARGE_TYPE,
  [ServicesNav.Evaluation]: EVALUATION_TYPE,
  [ServicesNav.PricingRules]: PRICING_RULE_TYPE,
};

export const ColumnType = createEnum<string, number, string>({
  Services: [1, 'Services'],
  Price: [2, 'Price'],
  Duration: [3, 'Duration'],
  TypeAndBreed: [4, 'Type & Breed'],
  PetSize: [5, 'Weight'],
  CoatType: [6, 'Coat Type'],
  LodgingTypes: [7, 'Eligible lodging type'],
  Action: [8, ''],
  ApplicableServices: [9, 'Applicable Services'],
  Tax: [10, 'Tax'],
  MaxDuration: [11, 'Max Duration'],
  PrerequisiteClass: [12, 'Prerequisite class'],
});

export const ServiceColumns = [
  {
    key: ColumnType.Services,
  },
  {
    key: ColumnType.Price,
  },
  {
    key: ColumnType.Duration,
  },
  {
    key: ColumnType.TypeAndBreed,
  },
  {
    key: ColumnType.PetSize,
  },
  {
    key: ColumnType.CoatType,
  },
  {
    key: ColumnType.Action,
  },
];

export const GroomingColumns = [
  { key: ColumnType.Services },
  { key: ColumnType.Price },
  { key: ColumnType.Tax },
  { key: ColumnType.Duration },
  { key: ColumnType.TypeAndBreed },
  { key: ColumnType.PetSize },
  { key: ColumnType.CoatType },
  { key: ColumnType.Action },
];

export const AddonColumns = [
  { key: ColumnType.Services },
  { key: ColumnType.Price },
  { key: ColumnType.Tax },
  { key: ColumnType.Duration },
  { key: ColumnType.ApplicableServices },
  { key: ColumnType.TypeAndBreed },
  { key: ColumnType.PetSize },
  { key: ColumnType.CoatType },
  { key: ColumnType.Action },
];

export const BoardingColumns = [
  { key: ColumnType.Services },
  { key: ColumnType.Price },
  { key: ColumnType.Tax },
  { key: ColumnType.TypeAndBreed },
  { key: ColumnType.PetSize },
  { key: ColumnType.LodgingTypes },
  { key: ColumnType.Action },
];

export const DaycareColumns = [
  { key: ColumnType.Services },
  { key: ColumnType.Price },
  { key: ColumnType.Tax },
  { key: ColumnType.MaxDuration },
  { key: ColumnType.TypeAndBreed },
  { key: ColumnType.PetSize },
  { key: ColumnType.LodgingTypes },
  { key: ColumnType.Action },
];

export const DogWalkingColumns = [
  { key: ColumnType.Services, width: 300 },
  { key: ColumnType.Price, width: 216 },
  { key: ColumnType.Tax, width: 216 },
  { key: ColumnType.Duration, width: 216 },
  { key: ColumnType.Action, width: 180 },
];

export const GroupClassColumns = [
  { key: ColumnType.Services },
  { key: ColumnType.Price },
  { key: ColumnType.Tax },
  { key: ColumnType.PrerequisiteClass },
  { key: ColumnType.Action },
];

const AllServiceType = {
  Grooming: `${ServiceType.Service}-${ServiceItemType.GROOMING}`,
  Boarding: `${ServiceType.Service}-${ServiceItemType.BOARDING}`,
  Daycare: `${ServiceType.Service}-${ServiceItemType.DAYCARE}`,
  DogWalking: `${ServiceType.Service}-${ServiceItemType.DOG_WALKING}`,
  GroupClass: `${ServiceType.Service}-${ServiceItemType.GROUP_CLASS}`,
  AddOn: `${ServiceType.Addon}-${AddonItemType.serviceAddon}`,
};

export const ColumnsMap = {
  [AllServiceType.Boarding]: BoardingColumns,
  [AllServiceType.Daycare]: DaycareColumns,
  [AllServiceType.Grooming]: GroomingColumns,
  [AllServiceType.DogWalking]: DogWalkingColumns,
  [AllServiceType.GroupClass]: GroupClassColumns,
  [AllServiceType.AddOn]: AddonColumns,
};

export const RequireDedicatedLodgingMap = {
  [AllServiceType.Grooming]: false,
  [AllServiceType.Boarding]: true,
  [AllServiceType.Daycare]: false,
  [AllServiceType.DogWalking]: false,
  [AllServiceType.AddOn]: false,
};

export const CanTipMap = {
  [AllServiceType.Grooming]: true,
  [AllServiceType.Boarding]: false,
  [AllServiceType.Daycare]: false,
  [AllServiceType.DogWalking]: true,
  [AllServiceType.AddOn]: false,
};

export const AddToCommissionBaseMap = {
  [AllServiceType.Grooming]: true,
  [AllServiceType.Boarding]: false,
  [AllServiceType.Daycare]: false,
  [AllServiceType.DogWalking]: true,
  [AllServiceType.AddOn]: false,
};

export const SetupEvaluationEnum = createEnum({
  Setup: ['Setup', 'Customize the evaluation with its price, duration, and settings.'],
  Scheduling: ['Scheduling for new pets', 'Accessible to both staff members and customers.'],
  Recording: ['Recording test result', 'Record the test results to inform future booking decisions.'],
});

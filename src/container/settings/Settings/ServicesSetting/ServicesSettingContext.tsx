import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type useForm } from '@moego/ui';
import { createContext, useContext } from 'react';
import { type ServiceSettingForm, ServicesNav } from './types';

export interface ServicesSettingContextProps {
  navType?: ServicesNav;
  serviceType: number;
  serviceItemType: ServiceItemType;
  requiredDedicatedStaffEditable?: boolean;
  clickedSubmit?: boolean;
  isEdit?: boolean;
  form?: ReturnType<typeof useForm<ServiceSettingForm>>;
  setApplyUpcomingModalConfig?: (config: ApplyUpcomingModalConfig) => void;
  isDisabledForTax?: boolean;
  isDisabledForDuration?: boolean;
  isDisabledForRemains?: boolean;
  isDisabledForPetWeight?: boolean;
  isDisabledForPetType?: boolean;
  childPanel?: string;
}

export interface ApplyUpcomingModalConfig {
  visible: boolean;
  action?: 'update' | 'delete';
}

export const ServicesSettingContext = createContext<ServicesSettingContextProps>({
  navType: ServicesNav.Services,
  serviceType: 0,
  serviceItemType: 0,
  clickedSubmit: false,
  setApplyUpcomingModalConfig: () => {},
});

export const useServiceSettingContext = () => useContext(ServicesSettingContext);

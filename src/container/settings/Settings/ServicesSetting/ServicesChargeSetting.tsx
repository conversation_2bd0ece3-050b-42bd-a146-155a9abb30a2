import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { SurchargeType } from '@moego/api-web/moego/models/order/v1/service_charge_enums';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, But<PERSON> } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { useHistory } from 'react-router';
import { ALL_LOCATIONS, type ALL_LOCATIONS_TYPE } from '../../../../components/Business/SingleLocationSelector';
import { Condition } from '../../../../components/Condition';
import { WithPermission } from '../../../../components/GuardRoute/WithPermission';
import { toastApi } from '../../../../components/Toast/Toast';
import { useNewAccountStructure } from '../../../../components/WithFeature/useNewAccountStructure';
import {
  type AddOrEditServiceChargeQuery,
  PATH_ADD_OR_EDIT_SERVICE_CHARGE,
  PATH_SERVICE_SETTING,
} from '../../../../router/paths';
import { currentAccountIdBox } from '../../../../store/account/account.boxes';
import { selectAllLocationIdList } from '../../../../store/business/location.selectors';
import { getTaxList } from '../../../../store/business/tax.actions';
import { selectPricingPermission } from '../../../../store/company/company.selectors';
import { getPetHairLengthList } from '../../../../store/pet/petHairLength.actions';
import { getPetTypeList } from '../../../../store/pet/petType.actions';
import {
  getCompanyServiceChargeList,
  removeServiceCharge,
} from '../../../../store/service/actions/private/serviceCharge.actions';
import { type ServiceChargeRecord } from '../../../../store/service/service.boxes';
import { useRouteState } from '../../../../utils/RoutePath';
import { useCancelableCallback } from '../../../../utils/hooks/useCancelableCallback';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { ToggleValue } from '../components/Selector/ToggleSelector';
import { SimpleUpgradeBanner } from '../components/SimpleUpgradeBanner';
import { ServicesSettingBox } from './ServicesSettingBox';
import { useServiceSettingContext } from './ServicesSettingContext';
import { ServiceChargeTable } from './components/ServiceChargeTable';
import { useApplyUpcomingModal } from './hooks/useApplyUpcomingModal';
import { useServiceTitleConfig } from './hooks/useServiceTitleConfig';
import { useServiceChargeSortDrawer } from './components/ServiceChargeSortDrawer';
import { isNormal } from '../../../../store/utils/identifier';

export const ServicesChargeSetting = memo(() => {
  const history = useHistory();
  const dispatch = useDispatch();
  const { selectedBusinessId: statedSelectedBusinessId, isInactive: statedInactive } =
    useRouteState(PATH_SERVICE_SETTING) || {};
  const { navType } = useServiceSettingContext();
  const [activeType, setActiveType] = useState<ToggleValue>(statedInactive ? ToggleValue.Inactive : ToggleValue.Active);
  const isActive = activeType === ToggleValue.Active;
  const [currentAccountId] = useSelector(currentAccountIdBox);
  const checkApplyUpcoming = useApplyUpcomingModal(ServiceType.UNSPECIFIED, ServiceItemType.UNSPECIFIED);

  const [businessIdList, pricingPermissions] = useSelector(selectAllLocationIdList(), selectPricingPermission());

  const hasServiceChargePermission = pricingPermissions.enable.has('serviceCharge');

  const { singularTitle } = useServiceTitleConfig(navType);

  const [selectedBusiness, setSelectedBusiness] = useState<string | ALL_LOCATIONS_TYPE>(
    statedSelectedBusinessId ? `${statedSelectedBusinessId}` : ALL_LOCATIONS,
  );

  const selectedBusinessIds = useMemo(() => {
    if (selectedBusiness === ALL_LOCATIONS) {
      return undefined;
    }
    return [Number(selectedBusiness)];
  }, [businessIdList, selectedBusiness]);

  const openServiceChargeSortModal = useServiceChargeSortDrawer();

  const isAllLocation = selectedBusiness === ALL_LOCATIONS;

  const getData = useCancelableCallback(async (token) => {
    await dispatch(
      getCompanyServiceChargeList(
        { isActive, businessIds: (selectedBusinessIds || []).map(String), surchargeType: SurchargeType.CUSTOM_FEE },
        token,
      ),
    );
  });

  const loading = getData.isBusy();

  useEffect(() => {
    getData();
  }, [navType, activeType, selectedBusinessIds]);

  useEffect(() => {
    dispatch(getTaxList());
    dispatch(getPetHairLengthList());
    dispatch(getPetTypeList());
  }, []);

  const handleOpenDeleteModal = useLatestCallback(async (service: ServiceChargeRecord) => {
    let result;
    // 删除service charge：status为active时，才出弹窗
    if (service.isActive) {
      result = await checkApplyUpcoming({
        action: 'delete',
        title: 'Delete service charge',
        confirmColor: 'danger',
        confirmText: 'Delete',
      });
      if (result === undefined) {
        return;
      }
    } else {
      AlertDialog.open({
        variant: 'danger',
        size: 's',
        title: `Delete ${singularTitle}`,
        content: ` Are you sure to delete this ${singularTitle}? This action cannot be reversed.`,
        confirmText: 'Delete',
        onConfirm: () => handleDelete(service),
      });
      return;
    }

    handleDelete(service, result);
  });

  const handleDelete = useLatestCallback(async (serviceCharge: ServiceChargeRecord, applyUpcomingAppt?: boolean) => {
    await dispatch(removeServiceCharge(serviceCharge.id, currentAccountId, applyUpcomingAppt));

    toastApi.success(`Delete ${singularTitle} successfully`);
  });

  const goEditOrAdd = (params: AddOrEditServiceChargeQuery) => {
    const businessId = isNormal(selectedBusinessIds?.[0]) ? String(selectedBusinessIds[0]) : undefined;

    history.push(
      PATH_ADD_OR_EDIT_SERVICE_CHARGE.fully(
        {},
        { selectedBusinessId: businessId, isActive }, // 这个不好直接在 URL 上展示，就放到 state 里
        { ...params },
      ),
    );
  };

  const handleAdd = useLatestCallback(() => {
    goEditOrAdd({});
  });

  const handleEditService = useLatestCallback((service: ServiceChargeRecord) => {
    goEditOrAdd({ id: service.id });
  });

  const handleDuplicate = useLatestCallback((service: ServiceChargeRecord) => {
    goEditOrAdd({ id: service.id, isDuplicate: '1' });
  });

  const handleSort = () => {
    openServiceChargeSortModal({
      isInactive: !isActive,
    });
  };

  const { isSingleLocation, hasMultipleLocation } = useNewAccountStructure('all');

  const showSort = isSingleLocation || (hasMultipleLocation && isAllLocation);

  const titleBottomBlock = (
    <SimpleUpgradeBanner
      permission="serviceCharge"
      /* @text-lint ignore */
      title="Upgrade to manage additional charges with simplicity."
      className="moe-mb-[32px]"
    />
  );

  const tableBlock = (
    <ServiceChargeTable
      onDelete={handleOpenDeleteModal}
      onEdit={handleEditService}
      onDuplicate={handleDuplicate}
      isAllLocation={isAllLocation}
      isInactive={!isActive}
      onAdd={handleAdd}
    />
  );

  const headerRightBlock = (
    <div className="moe-flex moe-gap-x-[8px]">
      <Condition if={showSort}>
        <Button variant="secondary" onPress={handleSort} isDisabled={!hasServiceChargePermission}>
          Sort service charges
        </Button>
      </Condition>
      <WithPermission permissions={'addService'}>
        <Button onPress={() => handleAdd()} isDisabled={!hasServiceChargePermission}>
          Add new {singularTitle}
        </Button>
      </WithPermission>
    </div>
  );

  if (!navType) {
    return null;
  }

  return (
    <ServicesSettingBox
      titleBottomBlock={titleBottomBlock}
      tableBlock={tableBlock}
      headerRightBlock={headerRightBlock}
      loading={loading}
      selectedBusiness={selectedBusiness}
      onSelectBusiness={setSelectedBusiness}
      onSelectActiveType={setActiveType}
      activeType={activeType}
      showBusinessSelector={true}
      showCareTypeSelector={false}
    />
  );
});

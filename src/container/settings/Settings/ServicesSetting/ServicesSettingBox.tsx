import { LegacySelect as Select, Spin, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import {
  type ALL_LOCATIONS_TYPE,
  SingleLocationSelector,
} from '../../../../components/Business/SingleLocationSelector';
import { Condition } from '../../../../components/Condition';
import { WithMultiLocation } from '../../../../components/WithFeature/WithMultiLocation';
import { selectSceneCareTypeAsOptions } from '../../../../store/careType/careType.selectors';
import { AllCareTypeValue, Scene, type ServiceItemTypeWithAllOption } from '../../../../store/service/scene.enum';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { ToggleSelector, type ToggleValue } from '../components/Selector/ToggleSelector';
import { SettingTitle } from '../components/SettingTitle';
import { useServiceSettingContext } from './ServicesSettingContext';
import { ServicesTitle } from './components/ServicesTitle';

export interface ServicesBoxProps {
  titleBottomBlock?: React.ReactNode;
  tableBlock?: React.ReactNode;
  globalBlock?: React.ReactNode;
  headerRightBlock?: React.ReactNode;
  loading?: boolean;
  selectedBusiness?: string;
  onSelectBusiness?: (businessId: string | ALL_LOCATIONS_TYPE) => void;
  onSelectActiveType: (activeType: ToggleValue) => void;
  activeType: ToggleValue;
  showCareTypeSelector: boolean;
  showBusinessSelector: boolean;
  toolbarClassName?: string;
  onCareTypeChange?: (value: ServiceItemTypeWithAllOption) => void;
  showBackButton?: boolean;
}

export const ServicesSettingBox = memo(function ServicesInfo(props: ServicesBoxProps) {
  const {
    titleBottomBlock,
    tableBlock,
    globalBlock,
    headerRightBlock,
    loading,
    selectedBusiness,
    onSelectBusiness,
    onSelectActiveType,
    activeType,
    showBusinessSelector,
    showCareTypeSelector,
    toolbarClassName,
    onCareTypeChange,
    showBackButton,
  } = props;
  const { navType, serviceItemType, childPanel } = useServiceSettingContext();
  const [sceneCareTypeOptions] = useSelector(selectSceneCareTypeAsOptions(Scene.AutoMessageUpdates));

  const handleRenderLabel = useLatestCallback((_value: string, label: string) => {
    return (
      <div className="moe-truncate moe-max-w-[250px] moe-text-tertiary">
        <span className="moe-text-primary">{label}</span>
      </div>
    );
  });

  if (!navType) {
    return null;
  }

  return (
    <div className="moe-w-full moe-flex moe-flex-col moe-mb-[100px]">
      <SettingTitle
        headerRight={headerRightBlock}
        title={
          <ServicesTitle
            navType={navType}
            serviceItemType={serviceItemType}
            childPanel={childPanel}
            showBackButton={showBackButton}
          />
        }
      />
      {titleBottomBlock}
      <div className={cn('moe-flex moe-gap-x-[16px] moe-mb-[16px]', toolbarClassName)}>
        <Condition if={showBusinessSelector}>
          <WithMultiLocation scene="all">
            <SingleLocationSelector
              value={selectedBusiness}
              onChange={onSelectBusiness}
              className="moe-w-[300px]"
              showAll
              scene="all"
              allLocationsLabel={handleRenderLabel('', 'All businesses')}
              customLabel={handleRenderLabel}
            />
          </WithMultiLocation>
        </Condition>
        <Condition if={showCareTypeSelector}>
          <Select
            defaultValue={AllCareTypeValue}
            options={sceneCareTypeOptions}
            className="moe-w-[300px]"
            isClearable
            isSearchable
            onChange={onCareTypeChange}
          />
        </Condition>
        <ToggleSelector value={activeType} onChange={onSelectActiveType} className="moe-w-[300px]" />
      </div>
      <Spin
        isLoading={loading}
        classNames={{
          base: 'moe-w-full moe-h-full',
          container: 'moe-w-full moe-h-full',
          iconContainer: '!moe-top-[200px]',
        }}
      >
        <div className="moe-flex moe-flex-col moe-gap-y-[40px]">{tableBlock}</div>
      </Spin>
      {globalBlock}
    </div>
  );
});

import { AlertDialog, Empty } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { useHistory } from 'react-router';
import { ALL_LOCATIONS, type ALL_LOCATIONS_TYPE } from '../../../../components/Business/SingleLocationSelector';
import { Switch } from '../../../../components/SwitchCase';
import { toastApi } from '../../../../components/Toast/Toast';
import { useNewAccountStructure } from '../../../../components/WithFeature/useNewAccountStructure';
import { type AddOrEditServiceQuery, PATH_ADD_OR_EDIT_SERVICE, PATH_SERVICE_SETTING } from '../../../../router/paths';
import { selectAllLocationIdList } from '../../../../store/business/location.selectors';
import { getTaxList } from '../../../../store/business/tax.actions';
import { getLodgingTypeList } from '../../../../store/lodging/actions/public/lodgingType.actions';
import { getPetSizeList } from '../../../../store/onlineBooking/actions/private/petSize.actions';
import { getPetHairLengthList } from '../../../../store/pet/petHairLength.actions';
import { getPetTypeList } from '../../../../store/pet/petType.actions';
import { type ServiceRecord } from '../../../../store/service/service.boxes';
import { useRouteState } from '../../../../utils/RoutePath';
import { useCancelableCallback } from '../../../../utils/hooks/useCancelableCallback';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { ToggleValue } from '../components/Selector/ToggleSelector';
import { ServicesSettingBox } from './ServicesSettingBox';
import { useServiceSettingContext } from './ServicesSettingContext';
import { CareTypeServiceBanner } from './components/CareTypeServiceBanner/CareTypeServiceBanner';
import { ServiceTableList } from './components/ServiceTableList';
import { useServiceTitleConfig } from './hooks/useServiceTitleConfig';
import { getCompanyServiceList } from '../../../../store/service/actions/public/companyService.actions';
import { useServiceCategoryList } from './hooks/useServiceCategoryList';
import { deleteCompanyService } from '../../../../store/service/actions/private/companyService.actions';
import { useMount } from 'react-use';
import { useServiceSortDrawer } from './components/ServiceSortDrawer';
import { ServiceListHeaderRight } from './components/Service/ServiceListHeaderRight';

export const ServicesCommonSetting = memo(function ServicesInfo() {
  const history = useHistory();
  const dispatch = useDispatch();
  const { selectedBusinessId: statedSelectedBusinessId, isInactive: statedInactive } =
    useRouteState(PATH_SERVICE_SETTING) || {};
  const { serviceItemType, serviceType, navType } = useServiceSettingContext();
  const [activeType, setActiveType] = useState<ToggleValue>(statedInactive ? ToggleValue.Inactive : ToggleValue.Active);
  const isActive = activeType === ToggleValue.Active;
  const [businessIdList] = useSelector(selectAllLocationIdList());

  const { singularTitle } = useServiceTitleConfig(navType, serviceItemType);
  const [selectedBusiness, setSelectedBusiness] = useState<string | ALL_LOCATIONS_TYPE>(
    statedSelectedBusinessId ? `${statedSelectedBusinessId}` : ALL_LOCATIONS,
  );

  const { serviceList, categoryIdList } = useServiceCategoryList({
    serviceType,
    serviceItemType,
    selectedBusinessId: selectedBusiness,
    careTypeId: '',
    isActive,
  });

  const openServiceSortDrawer = useServiceSortDrawer();

  const isAllLocation = selectedBusiness === ALL_LOCATIONS;
  const selectedBusinessIds = useMemo(() => {
    if (selectedBusiness === ALL_LOCATIONS) {
      return undefined;
    }
    return [Number(selectedBusiness)];
  }, [businessIdList, selectedBusiness]);

  const getData = useCancelableCallback(async () => {
    await dispatch(
      getCompanyServiceList({
        serviceType,
        serviceItemType,
        inactive: !isActive,
        businessIds: selectedBusinessIds,
      }),
    );
  });

  const loading = getData.isBusy();

  useEffect(() => {
    getData();
  }, [navType, activeType, selectedBusinessIds, serviceItemType]);

  useMount(() => {
    dispatch([getTaxList(), getPetHairLengthList(), getPetTypeList(), getLodgingTypeList(), getPetSizeList()]);
  });

  const handleOpenDeleteModal = useLatestCallback((service: ServiceRecord) => {
    AlertDialog.open({
      variant: 'danger',
      size: 's',
      title: `Delete ${singularTitle}`,
      content: ` Are you sure to delete this ${singularTitle}? This action cannot be reversed.`,
      confirmText: 'Delete',
      onConfirm: () => handleDelete(service),
    });
  });

  const handleDelete = useLatestCallback(async (service: ServiceRecord) => {
    await dispatch(deleteCompanyService(String(service.serviceId), serviceType, serviceItemType));
    toastApi.success(`Delete ${singularTitle} successfully`);
  });

  const handleSort = useLatestCallback((_, categoryId?: string) => {
    openServiceSortDrawer({
      categoryId,
      serviceType,
      serviceItemType,
      isInactive: !isActive,
      typeName: singularTitle,
    });
  });

  const goEditOrAdd = (params: AddOrEditServiceQuery) => {
    history.push(
      PATH_ADD_OR_EDIT_SERVICE.fully(
        { serviceType: `${serviceType}`, serviceItemType: `${serviceItemType}` },
        { selectedBusinessId: selectedBusinessIds?.[0], isInactive: !isActive }, // 这个不好直接在 URL 上展示，就放到 state 里
        { ...params },
      ),
    );
  };

  const handleAdd = useLatestCallback((categoryId: string = '') => {
    goEditOrAdd({ categoryId });
  });

  const handleEditService = useLatestCallback((service: ServiceRecord) => {
    goEditOrAdd({ categoryId: `${service.categoryId}`, serviceId: `${service.serviceId}` });
  });

  const handleDuplicate = useLatestCallback((service: ServiceRecord) => {
    goEditOrAdd({
      categoryId: `${service.categoryId}`,
      serviceId: `${service.serviceId}`,
      isDuplicate: '1',
    });
  });

  const { isSingleLocation } = useNewAccountStructure('all');

  const tableBlock = (
    <Switch>
      <Switch.Case if={!serviceList?.length}>
        <Empty
          title=""
          className=" moe-bg-white moe-mt-[112px]"
          description={`${isActive ? 'No' : 'No inactive'} ${singularTitle} available.`}
        />
      </Switch.Case>
      <Switch.Case else>
        <ServiceTableList
          type={serviceType}
          categoryId=""
          onDelete={handleOpenDeleteModal}
          onEdit={handleEditService}
          onDuplicate={handleDuplicate}
          onAdd={handleAdd}
          onSort={handleSort}
          isAllLocation={isAllLocation}
          selectedBusinessId={selectedBusinessIds?.[0]}
          isSingleLocation={isSingleLocation}
          categoryIdList={categoryIdList}
          listVisible={isActive}
        />
      </Switch.Case>
    </Switch>
  );

  return (
    <>
      <ServicesSettingBox
        tableBlock={tableBlock}
        titleBottomBlock={<CareTypeServiceBanner />}
        headerRightBlock={
          <ServiceListHeaderRight
            serviceType={serviceType}
            serviceItemType={serviceItemType}
            singularTitle={singularTitle}
            onAdd={handleAdd}
          />
        }
        loading={loading}
        selectedBusiness={selectedBusiness}
        onSelectBusiness={setSelectedBusiness}
        onSelectActiveType={setActiveType}
        activeType={activeType}
        showBusinessSelector={true}
        showCareTypeSelector={false}
      />
    </>
  );
});

import { Drawer } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useRef } from 'react';
import { toast<PERSON><PERSON> } from '../../../../../components/Toast/Toast';
import { type OpenApiDefinitions } from '../../../../../openApi/schema';
import { currentAccountIdBox } from '../../../../../store/account/account.boxes';
import { sortCompanyCategoryList } from '../../../../../store/service/actions/private/category.actions';
import { selectBusinessServiceCategories } from '../../../../../store/service/category.selectors';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { CategoryForm, type CategoryFormRef } from './CategoryForm';
import { getCompanyServiceList } from '../../../../../store/service/actions/public/companyService.actions';
import { useModal } from '../../../../../components/Modal/useModal';
import { type ServiceItemType, type ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';

export interface CategoryDrawerProps {
  serviceType: ServiceType;
  serviceItemType: ServiceItemType;
  onClose: () => void;
}

export const CategoryDrawer = memo(function CategoryDrawer(props: CategoryDrawerProps) {
  const dispatch = useDispatch();
  const { onClose, serviceType, serviceItemType } = props;
  const [currentAccountId] = useSelector(currentAccountIdBox);
  const [categories] = useSelector(
    selectBusinessServiceCategories(`${serviceType}-${serviceItemType}`, currentAccountId),
  );

  const formRef = useRef<CategoryFormRef>(null);

  const handleSave = useSerialCallback(async () => {
    const data =
      formRef.current?.getData() as OpenApiDefinitions['grooming']['com.moego.server.grooming.service.dto.ServiceCategoryUpdateDto'][];
    if (data) {
      // delete categoryId < 0
      data.forEach((item) => {
        if (item?.categoryId && item.categoryId < 0) {
          delete item?.categoryId;
        }
      });
      const deletedCategoryIdList = categories.filter((id) => !data.find((item) => item.categoryId === id));
      await dispatch(sortCompanyCategoryList(data, serviceType, serviceItemType));
      // if some categories have been deleted , then get the latest service list
      if (deletedCategoryIdList.size) {
        await dispatch(
          getCompanyServiceList({
            serviceType,
            serviceItemType,
          }),
        );
      }

      toastApi.success('Category list saved');
    }
  });

  return (
    <Drawer
      isOpen
      isBlockScroll
      isDismissable
      showCloseButton
      onClose={onClose}
      onConfirm={handleSave}
      size="m"
      title={'Edit category'}
      confirmText="Save"
    >
      <CategoryForm serviceType={serviceType} serviceItemType={serviceItemType} ref={formRef} />
    </Drawer>
  );
});

export const useCategoryDrawer = () => useModal(CategoryDrawer);

import { MajorPlusOutlined } from '@moego/icons-react';
import { But<PERSON>, Empty, Heading } from '@moego/ui';
import { useSelector } from 'amos';
import { cloneDeep } from 'lodash';
import React, { useImperativeHandle } from 'react';
import { Condition } from '../../../../../components/Condition';
import { WithPermission } from '../../../../../components/GuardRoute/WithPermission';
import { currentAccountIdBox } from '../../../../../store/account/account.boxes';
import { selectCurrentPermissions } from '../../../../../store/business/role.selectors';
import { type ServiceCategoryRecord, serviceCategoryMapBox } from '../../../../../store/service/category.boxes';
import { selectBusinessServiceCategories } from '../../../../../store/service/category.selectors';
import { type RecordProps } from '../../../../../store/utils/RecordMap';
import { useMergedState } from '../../../../../utils/hooks/useMergedState';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { memoForwardRef } from '../../../../../utils/react';
import { useMoveList } from '../../components/DraggableList/utils';
import { CategoryFormRow } from './CategoryFormRow';
import { type ServiceItemType, type ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { DndProvider } from 'react-dnd';
import { dndManager } from '../../../../../utils/dndManager';

export interface CategoryFormProps {
  serviceType: ServiceType;
  serviceItemType: ServiceItemType;
}

export interface CategoryFormRef {
  getData(): RecordProps<ServiceCategoryRecord>[];
}

export const CategoryForm = memoForwardRef<CategoryFormRef, CategoryFormProps>(function CategoryForm(
  props: CategoryFormProps,
  ref,
) {
  const { serviceType, serviceItemType } = props;
  const [currentAccountId, permissions] = useSelector(currentAccountIdBox, selectCurrentPermissions);
  const type = `${serviceType}-${serviceItemType}`;
  const [categories, categoryMap] = useSelector(
    selectBusinessServiceCategories(type, currentAccountId),
    serviceCategoryMapBox,
  );
  const [localCategoryList, setLocalCategoryList] = useMergedState([categories], () =>
    categories.toJSON().map((id) => categoryMap.mustGetItem(id).toJSON()),
  );

  const handleMove = useMoveList(localCategoryList, setLocalCategoryList);

  const handleDrop = useSerialCallback(async () => {
    return;
  });

  const handleDelete = useSerialCallback(async (id: number) => {
    setLocalCategoryList((prev) => {
      return prev.filter((item) => item.categoryId !== id);
    });
  });
  const handleAddCategory = useSerialCallback(async () => {
    const fakeId = -Math.random();
    const newCategory = categoryMap.mustGetItem(fakeId).toJSON();
    newCategory.categoryId = fakeId;
    newCategory.type = serviceType;
    newCategory.serviceItemType = serviceItemType;
    setLocalCategoryList((prev) => {
      return [newCategory].concat(prev);
    });
  });

  useImperativeHandle(ref, () => ({
    getData() {
      return cloneDeep(localCategoryList);
    },
  }));

  const handleEdit = useSerialCallback(async (item: RecordProps<ServiceCategoryRecord>) => {
    // update item in local list
    setLocalCategoryList((prev) => {
      return prev.map((i) => {
        if (i.categoryId === item.categoryId) {
          return item;
        }
        return i;
      });
    });
  });

  const hasCategory = localCategoryList.length > 0;

  return (
    <div className="moe-flex moe-flex-col moe-h-[100%] moe-font-manrope">
      <div className="moe-flex moe-justify-between moe-mb-[24px] moe-items-center">
        <Heading size="4">Category list</Heading>
        <WithPermission permissions="editCategory">
          <Button variant="tertiary-legacy" icon={<MajorPlusOutlined />} onPress={handleAddCategory} align="end">
            Add new category
          </Button>
        </WithPermission>
      </div>
      <Condition if={hasCategory}>
        <DndProvider manager={dndManager}>
          <div className="moe-flex moe-flex-col moe-gap-y-[16px] moe-flex-1 moe-overflow-y-auto">
            {localCategoryList.map((item, index) => {
              return (
                <CategoryFormRow<typeof item>
                  id={item.categoryId}
                  key={item.categoryId}
                  item={item}
                  index={index}
                  onDelete={handleDelete}
                  onDrop={handleDrop}
                  onEdit={handleEdit}
                  onMove={handleMove}
                  valueKey="name"
                  disabled={!permissions.has('editCategory')}
                />
              );
            })}
          </div>
        </DndProvider>
      </Condition>
      <Condition if={!hasCategory}>
        <div className="moe-flex-1 moe-flex moe-flex-col moe-justify-center moe-items-center">
          <Empty />
        </div>
      </Condition>
    </div>
  );
});

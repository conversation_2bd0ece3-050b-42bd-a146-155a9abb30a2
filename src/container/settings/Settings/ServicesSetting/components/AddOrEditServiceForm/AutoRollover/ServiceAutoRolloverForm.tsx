import { Form, Input, LegacySelect as Select, Switch, Text, useWatch } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { PATH_ADD_OR_EDIT_SERVICE } from '../../../../../../../router/paths';
import { currentAccountIdBox } from '../../../../../../../store/account/account.boxes';
import { getCompanyFullServiceInfoList } from '../../../../../../../store/service/actions/public/service.actions';
import { selectBusinessServiceCategories } from '../../../../../../../store/service/category.selectors';
import { companyServiceMapBox } from '../../../../../../../store/service/service.boxes';
import { selectCompanyServiceIdListWithoutCategory } from '../../../../../../../store/service/service.selectors';
import { isNormal } from '../../../../../../../store/utils/identifier';
import { useRouteQueryV2 } from '../../../../../../../utils/RoutePath';
import { withPl } from '../../../../../../../utils/calculator';
import { useBizIdReadyEffect } from '../../../../../../../utils/hooks/useBizIdReadyEffect';
import { useSerialCallback } from '../../../../../../../utils/hooks/useSerialCallback';
import { useServiceSettingContext } from '../../../ServicesSettingContext';
import { hours2Minutes } from '../../../utils/inputTransformer';

export interface ServiceAutoRolloverFormProps {}

export const MAX_AUTO_ROLLOVER_TIME = 60;

export const ServiceAutoRolloverForm = memo(function ServiceRolloverForm(_props: ServiceAutoRolloverFormProps) {
  const dispatch = useDispatch();
  const { form, isEdit, serviceType, serviceItemType } = useServiceSettingContext();
  const { serviceId } = useRouteQueryV2(PATH_ADD_OR_EDIT_SERVICE);
  const [currentAccountId] = useSelector(currentAccountIdBox);
  const [categories, serviceMap] = useSelector(
    selectBusinessServiceCategories(`${serviceType}-${serviceItemType}`, currentAccountId),
    companyServiceMapBox,
  );
  const categoryIdList = [-serviceType].concat(categories.toArray());

  const [daycareServiceIdList] = useSelector(
    selectCompanyServiceIdListWithoutCategory(categoryIdList, serviceType, serviceItemType, true),
  );

  const enableAutoRollover = useWatch({
    control: form?.control,
    name: 'autoRolloverRule.enabled',
  });

  const daycareServiceList = useMemo(() => {
    return daycareServiceIdList
      .map((id) => {
        const service = serviceMap.mustGetItem(id);
        return {
          value: `${id}`,
          label: service.name,
        };
      })
      .filter((item) => {
        // 编辑模式下，需要排除自身
        if (isEdit) {
          return item.value !== serviceId;
        }
        return true;
      });
  }, [daycareServiceIdList.join(','), serviceMap, serviceId, isEdit]);

  const getData = useSerialCallback(async () => {
    await dispatch(
      getCompanyFullServiceInfoList({
        serviceType,
        serviceItemType,
      }),
    );
  });

  useBizIdReadyEffect(() => {
    getData();
  }, []);

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-m">
      <Form.Item name="autoRolloverRule.enabled">
        <Switch isSelected={enableAutoRollover}>Enable service auto-rollover</Switch>
      </Form.Item>
      <Condition if={enableAutoRollover}>
        <div className="moe-flex">
          <Form.Item
            name="autoRolloverRule.afterMinute"
            label="Auto-rollover time"
            rules={{
              required: 'Auto-rollover time is required',
              validate(value) {
                if (+value > MAX_AUTO_ROLLOVER_TIME) {
                  return `Up to ${MAX_AUTO_ROLLOVER_TIME} minutes`;
                }
                return true;
              },
            }}
          >
            <Input.Number
              isRequired
              placeholder="Enter"
              suffix="min"
              className="moe-w-[185px]"
              minValue={0}
              precision={0}
              helpTextClassNames={{ error: ['moe-w-[300px]'] }}
            />
          </Form.Item>
          <Text variant="regular-short" className="moe-flex-1 moe-pt-[32px] moe-ml-xs">
            {/* @text-lint ignore */}
            after max stay duration
          </Text>
        </div>

        <Form.Item
          name="autoRolloverRule.targetServiceId"
          label="Roll over to the following service"
          rules={{
            required: 'Service is required',
            validate: (value) => {
              // 避免 service id 不存在导致问题
              if (daycareServiceList.some((item) => item.value === value)) {
                return true;
              }
              return 'Service is required';
            },
          }}
          transformer={{
            input(value) {
              // 不这样处理的话，placeholder 出不来
              if (!isNormal(value as string)) {
                return undefined;
              }
              return value;
            },
          }}
        >
          <Select
            isRequired
            options={daycareServiceList}
            placeholder="Select service"
            classNames={{
              menuList: 'moe-max-h-[300px]',
            }}
            renderItem={({ data: { value } }) => {
              const service = serviceMap.mustGetItem(Number(value));
              const label = withPl(+(hours2Minutes.input?.(service.maxDuration) || 0), 'hour');
              return (
                <div className="moe-flex moe-w-full">
                  <div className="moe-flex-1 moe-truncate moe-font-bold moe-text-base">{service.name}</div>
                  <Text variant="small" className="moe-text-tertiary">
                    {label}
                  </Text>
                </div>
              );
            }}
            isSearchable
            noOptionsMessage={() =>
              'No available service, please set more daycare services before turning on auto-rollover.'
            }
            formatOptionLabel={(opt: { label: string; value: string }) => opt?.label || ''}
          ></Select>
        </Form.Item>
      </Condition>
    </div>
  );
});

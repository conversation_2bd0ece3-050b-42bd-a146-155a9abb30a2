import React, { memo, useMemo, useState } from 'react';
import { type DateType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Select, Form, useWatch, Tabs } from '@moego/ui';
import { type ServicesSettingContextProps } from '../../../../ServicesSettingContext';
import { AllServicesItemType } from '../../../../../../../../store/service/category.boxes';
import { useSelector } from 'amos';
import { companyServiceMapBox } from '../../../../../../../../store/service/service.boxes';
import { selectCompanyCareTypeNameMap } from '../../../../../../../../store/careType/careType.selectors';

export interface SelectOption {
  label: string;
  options: {
    label: string;
    value: string;
  }[];
}

export interface BoardingBundleServiceSelectorProps {
  index: number;
  form?: ServicesSettingContextProps['form'];
  groomingServiceOptions: SelectOption[];
  addOnOptions: SelectOption[];
}

export const BoardingBundleServiceSelector = memo(function BoardingBundleServiceSelector(
  props: BoardingBundleServiceSelectorProps,
) {
  const { index, form, groomingServiceOptions, addOnOptions } = props;
  const [boardingBundleServiceAndAddonList = []] = useWatch({
    control: form?.control,
    name: ['additionalServiceRule.applyRules'],
  });
  const [companyServiceMap, companyCareTypeNameMap] = useSelector(companyServiceMapBox, selectCompanyCareTypeNameMap);
  const [currentTab, setCurrentTab] = useState<string>(AllServicesItemType.grooming);

  const selectOptions = useMemo(() => {
    if (currentTab === AllServicesItemType.grooming) {
      return groomingServiceOptions;
    }
    return addOnOptions;
  }, [addOnOptions, groomingServiceOptions, currentTab]);

  const disabledKeys = boardingBundleServiceAndAddonList.map((item) => item.serviceId).filter(Boolean);

  const ServiceMenuTab = [
    { label: companyCareTypeNameMap.Grooming, value: AllServicesItemType.grooming },
    { label: `${companyCareTypeNameMap.Boarding} add-ons`, value: AllServicesItemType.serviceAddon },
  ];

  return (
    <Form.Item
      name={`additionalServiceRule.applyRules[${index}].serviceId`}
      label={index === 0 ? 'Select service & add-on' : undefined}
      rules={{
        validate: (value) => {
          if (value) {
            return true;
          }
          return 'Please select a service or add-on';
        },
      }}
    >
      <Select
        isRequired
        classNames={{
          overlay: 'moe-w-[400px]',
        }}
        items={selectOptions}
        disabledKeys={disabledKeys}
        onChange={() => {
          // 要置空，所以这里要用 null，undefined 会不受控
          form?.setValue(`additionalServiceRule.applyRules.${index}.dateType`, null as unknown as DateType, {
            shouldDirty: true,
          });
          form?.setValue(`additionalServiceRule.applyRules.${index}.quantityPerDay`, 1, {
            shouldDirty: true,
          });
        }}
        formatOptionLabel={(option) => {
          // 用户切换 tab 后，上一个 tab 的 options 会被清理。
          // 如果 select 中的 value 是上一个 tab 中的数据，这里需要手动显示上一个 tab 中的 label。
          const name = option?.value?.label || companyServiceMap.mustGetItem(+option.key as number).name;
          return name;
        }}
        renderMenu={(menu) => {
          return (
            <>
              <Tabs
                selectedKey={currentTab}
                onChange={(key) => {
                  setCurrentTab(key as string);
                }}
                classNames={{
                  base: ['moe-w-full moe-px-spacing-xs moe-pt-spacing-xs'],
                  tabList: ['moe-mx-xs moe-w-[calc(100%_-_16px)] moe-overflow-x-auto'],
                  panel: ['moe-h-0 moe-pt-0'],
                }}
              >
                {ServiceMenuTab.map((item) => {
                  return <Tabs.Item label={item.label} key={item.value}></Tabs.Item>;
                })}
              </Tabs>
              {menu}
            </>
          );
        }}
      />
    </Form.Item>
  );
});

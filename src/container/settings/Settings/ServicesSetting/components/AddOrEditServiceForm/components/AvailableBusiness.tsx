import { Form, useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { WithMultiLocation } from '../../../../../../../components/WithFeature/WithMultiLocation';
import { selectAllLocationIdList } from '../../../../../../../store/business/location.selectors';
import { ServiceType } from '../../../../../../../store/service/category.boxes';
import { MultiLocationSelector } from '../../../../components/Selector/MultiLocationSelector';
import { useServiceSettingContext } from '../../../ServicesSettingContext';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';

export const AvailableBusiness = memo(() => {
  const { form, serviceType, serviceItemType, isDisabledForRemains } = useServiceSettingContext();

  const [allLocationIdList] = useSelector(selectAllLocationIdList);

  const [isAllLocation, locationStaffOverrideList] = useWatch({
    control: form?.control,
    name: ['isAllBusiness', 'locationStaffOverrideList'],
  });

  const isAllowServiceByStaff = serviceItemType === ServiceItemType.GROOMING && serviceType === ServiceType.Service;

  return (
    <WithMultiLocation scene="all">
      <Form.Item<string[], string[]>
        name="availableBusinessIdList"
        label="Business"
        rules={{
          validate: (value) => {
            if (!isAllLocation) {
              if (value?.length === 0) {
                return 'Business is required';
              }
            }
            return true;
          },
        }}
        transformer={{
          input(val) {
            if (isAllLocation) {
              return allLocationIdList.toJSON().map((item) => `${item}`);
            } else {
              return val;
            }
          },

          output(locationIdList) {
            const isAllBusiness =
              !!allLocationIdList.size && allLocationIdList.every((item) => locationIdList.includes(item));
            form?.setValue('isAllBusiness', isAllBusiness, { shouldDirty: true });

            if (isAllowServiceByStaff) {
              // 如果是 service by staff 则不再需要处理其他数据了
              return locationIdList;
            }

            // 用户勾选 location 的情况下，可能会去掉一部分 location，这个时候需要把去掉的 location
            if (!isAllBusiness) {
              const nextLocationStaffOverrideList =
                locationStaffOverrideList?.filter((item) => {
                  return locationIdList.includes(item.locationOverride.businessId);
                }) ?? [];
              form?.setValue('locationStaffOverrideList', nextLocationStaffOverrideList, {
                shouldDirty: true,
              });
            }
            return locationIdList;
          },
        }}
      >
        <MultiLocationSelector
          scene="all"
          menuListClassName="moe-max-h-[300px]"
          isRequired
          showSelectAll
          footer={null}
          isDisabled={() => {
            return isDisabledForRemains || false;
          }}
        />
      </Form.Item>
    </WithMultiLocation>
  );
});

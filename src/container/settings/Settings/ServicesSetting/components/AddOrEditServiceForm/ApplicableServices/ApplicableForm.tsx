import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { booleanToStringify, Form, Radio, RadioGroup, useWatch } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { selectBDFeatureEnable } from '../../../../../../../store/company/company.selectors';
import { getAllCompanyFullServiceInfoList } from '../../../../../../../store/service/actions/public/service.actions';
import { useCancelableCallback } from '../../../../../../../utils/hooks/useCancelableCallback';
import { useServiceSettingContext } from '../../../ServicesSettingContext';
import { ServicesSelector } from './ServicesSelector';
import { ServicesCheckboxGroup } from './ServicesCheckboxGroup';

export const ApplicableForm = memo(() => {
  const dispatch = useDispatch();
  const { form, isDisabledForRemains } = useServiceSettingContext();
  const [enableBD] = useSelector(selectBDFeatureEnable);
  const [serviceFilter] = useWatch({
    control: form?.control,
    name: ['serviceFilter'],
  });

  const getData = useCancelableCallback(async () => {
    await dispatch(
      getAllCompanyFullServiceInfoList({
        withAddon: false,
      }),
    );
  });

  useEffect(() => {
    getData();
  }, []);

  if (getData.isBusy()) {
    return null;
  }

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-[16px]">
      <Form.Item name="serviceFilter" transformer={booleanToStringify}>
        <RadioGroup
          isDisabled={isDisabledForRemains}
          isRequired
          label="Select which services can be applied to"
          onChange={(radioValue: string) => {
            const isFilter = booleanToStringify.output?.(radioValue);
            if (!isFilter) {
              form?.setValue('serviceFilterList', []);
            }
          }}
        >
          <Radio value={`false`} className="moe">
            All services (including new services)
          </Radio>
          <Radio value={`true`}>Specific services</Radio>
        </RadioGroup>
      </Form.Item>
      <Condition if={serviceFilter && enableBD}>
        <Form.Item
          name="serviceFilterList"
          aria-label="Applicable services list"
          rules={{
            validate: (value: number[]) => {
              if (!value?.length) {
                return 'Please select at least one service';
              }
              return true;
            },
          }}
        >
          <ServicesCheckboxGroup />
        </Form.Item>
      </Condition>
      <Condition if={serviceFilter && !enableBD}>
        <ServicesSelector serviceItemType={ServiceItemType.GROOMING} />
      </Condition>
    </div>
  );
});

import { Form, type OptionValue, LegacySelect as Select, Switch, useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useEffect, useMemo } from 'react';
import { currentAccountIdBox } from '../../../../../../../store/account/account.boxes';
import { ServiceType, UN_CATEGORY_ID } from '../../../../../../../store/service/category.boxes';
import {
  selectBusinessServiceCategories,
  selectServicesByCategoriesId,
} from '../../../../../../../store/service/category.selectors';
import { useServiceSettingContext } from '../../../ServicesSettingContext';
import { withPl } from '../../../../../../../utils/calculator';

export interface ServicesSelectorProps {
  serviceItemType: number;
}

export const ServicesSelector = memo((props: ServicesSelectorProps) => {
  const { serviceItemType } = props;
  const { form } = useServiceSettingContext();
  const [currentAccountId] = useSelector(currentAccountIdBox);
  const [categories] = useSelector(
    selectBusinessServiceCategories(`${ServiceType.Service}-${serviceItemType}`, currentAccountId),
  );

  // uncategory 的放前面
  const categoryIdList = [UN_CATEGORY_ID].concat(categories.toJSON());
  const [{ serviceGroupList: originServiceGroupList }] = useSelector(
    selectServicesByCategoriesId(ServiceType.Service, serviceItemType, categoryIdList),
  );

  const serviceGroupList = useMemo(() => {
    return originServiceGroupList.map((item) => {
      return {
        ...item,
        options: item.options.map((option) => {
          return {
            ...option,
            // 避免 select 组件里出现 description
            description: '',
          };
        }),
      };
    });
  }, [originServiceGroupList]);

  const [serviceFilterList] = useWatch({
    control: form?.control,
    name: ['serviceFilterList'],
  });

  const index = serviceFilterList.findIndex((item) => item.serviceItemType === serviceItemType);
  // grooming only business should use 0 as index, because it doesn't need to select care type
  const indexInServiceFilterList = index > -1 ? index : 0;

  const servicesIdList = useMemo(() => {
    const list: OptionValue[] = [];
    serviceGroupList.forEach((item) => item?.options.forEach(({ value }) => list.push(String(value))));
    return list;
  }, [serviceGroupList]);

  const { availableForAllServices: isSelectedAll, availableServiceIdList = [] } =
    serviceFilterList[indexInServiceFilterList] || {};

  const calcValues = useMemo(() => {
    if (isSelectedAll) {
      return servicesIdList;
    } else {
      return availableServiceIdList;
    }
  }, [availableServiceIdList, isSelectedAll, servicesIdList]);

  const transformServices = useMemo(() => {
    return serviceGroupList.map((group) => {
      return {
        label: group.label,
        options: group.options.map(({ value, label }) => {
          return {
            value: String(value),
            label,
            isDisabled: isSelectedAll,
          };
        }),
      };
    });
  }, [serviceGroupList, isSelectedAll]);

  const handleSelectChange = (serviceIdList: string[]) => {
    form?.setValue(`serviceFilterList.${indexInServiceFilterList}`, {
      availableForAllServices: false,
      availableServiceIdList: serviceIdList,
      serviceItemType,
    });
  };

  const handleSwitchChange = (value: boolean) => {
    form?.setValue(
      `serviceFilterList.${indexInServiceFilterList}`,
      {
        availableForAllServices: value,
        availableServiceIdList: [],
        serviceItemType,
      },
      { shouldDirty: true },
    );
  };

  const handleRenderMultipleValues = () => {
    if (isSelectedAll) {
      return <div>All selected</div>;
    }

    if (calcValues?.length) {
      return <div>{withPl(calcValues.length, 'service')}</div>;
    }

    return undefined;
  };

  useEffect(() => {
    // 初始化如果当前服务类型没有选择的服务，则默认选择所有服务
    if (!availableServiceIdList?.length) {
      handleSwitchChange(true);
    }
  }, []);

  return (
    <Form.Item
      rules={{
        validate: (value) => isSelectedAll || value?.length > 0 || 'Please select at least one service type',
      }}
      name={`serviceFilterList.${indexInServiceFilterList}.availableServiceIdList`}
      transformer={{
        input() {
          if (isSelectedAll) {
            return servicesIdList;
          }
          return calcValues;
        },
      }}
    >
      <Select
        className="moe-ml-[28px]"
        isMultiple
        options={transformServices}
        multipleMode={isSelectedAll ? 'value' : 'count'}
        renderMultipleValues={handleRenderMultipleValues}
        onChange={handleSelectChange}
        footer={
          <Switch isSelected={isSelectedAll} onChange={handleSwitchChange} className="moe-p-[6px]">
            All services (including new services)
          </Switch>
        }
      />
    </Form.Item>
  );
});

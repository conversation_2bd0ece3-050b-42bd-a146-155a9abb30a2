import { type ServiceFilter } from '@moego/api-web/moego/models/offering/v1/service_models';
import { Checkbox, CheckboxGroup, type FormFieldSharedProps, type CheckboxGroupRefConfig } from '@moego/ui';
import { useSelector } from 'amos';
import React, { forwardRef, Fragment, useMemo } from 'react';
import {
  selectCompanyCareTypeNameMap,
  selectSceneCareType,
} from '../../../../../../../store/careType/careType.selectors';
import { Scene } from '../../../../../../../store/service/scene.enum';
import { Condition } from '../../../../../../../components/Condition';
import { ServicesSelector } from './ServicesSelector';
import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useServiceSettingContext } from '../../../ServicesSettingContext';

export interface ServicesCheckboxGroupProps extends FormFieldSharedProps {
  value?: ServiceFilter[];
  onChange?: (value: ServiceFilter[]) => void;
}

export const ServicesCheckboxGroup = forwardRef<CheckboxGroupRefConfig, ServicesCheckboxGroupProps>(
  ({ value = [], onChange, ...restFormFields }, ref) => {
    const { form } = useServiceSettingContext();

    const [companyCareTypeNameMap, enableAddonCareTypes] = useSelector(
      selectCompanyCareTypeNameMap,
      selectSceneCareType(Scene.EnableAddons),
    );

    const checkboxGroupValue = useMemo(() => value.map((item) => item.serviceItemType), [value]);

    const handleChange = (careTypeList: ServiceItemType[]) => {
      // compare with current value, remove the careType that is not in the value, add the careType that is only in the careTypeList
      const currentValue = form?.getValues('serviceFilterList') ?? [];

      const currentValueSet = new Set(currentValue.map((item) => item.serviceItemType));
      const nextServiceFilterList: ServiceFilter[] = careTypeList.map((careType) => {
        if (currentValueSet.has(careType)) {
          return currentValue.find((item) => item.serviceItemType === careType)!;
        } else {
          return {
            serviceItemType: careType,
            availableForAllServices: true,
            availableServiceIdList: [],
          };
        }
      });
      onChange?.(nextServiceFilterList);
    };

    return (
      <CheckboxGroup
        ref={ref}
        className="moe-ml-[28px]"
        aria-label="Applicable services list"
        value={checkboxGroupValue}
        onChange={handleChange}
        {...restFormFields}
      >
        {enableAddonCareTypes.map((careType) => (
          <Fragment key={careType}>
            <Checkbox value={careType}>{companyCareTypeNameMap.getName(careType as ServiceItemType)} services</Checkbox>
            <Condition if={value.some((item) => item.serviceItemType === careType)}>
              <ServicesSelector serviceItemType={careType} />
            </Condition>
          </Fragment>
        ))}
      </CheckboxGroup>
    );
  },
);

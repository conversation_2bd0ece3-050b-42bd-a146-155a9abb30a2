import { Form, LegacySelect as Select } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { currentAccountIdBox } from '../../../../../../../store/account/account.boxes';
import { serviceCategoryMapBox } from '../../../../../../../store/service/category.boxes';
import { selectBusinessServiceCategories } from '../../../../../../../store/service/category.selectors';
import { useServiceSettingContext } from '../../../ServicesSettingContext';

export const Category = memo(() => {
  const { serviceType, serviceItemType, isDisabledForRemains } = useServiceSettingContext();

  const [accountId] = useSelector(currentAccountIdBox);
  const [categoryIds, categoryMap] = useSelector(
    selectBusinessServiceCategories(`${serviceType}-${serviceItemType}`, accountId),
    serviceCategoryMapBox,
  );
  // 直接用 form.watch 有时会失效
  const categoryList = useMemo(() => {
    return categoryIds.toJSON().map((id) => {
      const category = categoryMap.mustGetItem(id);
      return {
        label: category.name,
        value: String(category.categoryId),
      };
    });
  }, [categoryIds, categoryMap]);

  return (
    <Form.Item name="categoryId" label="Category">
      <Select
        isDisabled={isDisabledForRemains}
        options={categoryList}
        placeholder="Select a category"
        isClearable
      ></Select>
    </Form.Item>
  );
});

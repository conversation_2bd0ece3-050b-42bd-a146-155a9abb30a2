import { Form, Radio, RadioGroup, booleanToStringify } from '@moego/ui';
import React, { memo } from 'react';
import { useServiceSettingContext } from '../../../ServicesSettingContext';

export const Status = memo(() => {
  const { isDisabledForRemains } = useServiceSettingContext();
  return (
    <Form.Item name="isActive" label="Status" transformer={booleanToStringify}>
      <RadioGroup isDisabled={isDisabledForRemains} isRequired orientation="horizontal">
        <Radio value={`true`}>Active</Radio>
        <Radio value={`false`}>Inactive</Radio>
      </RadioGroup>
    </Form.Item>
  );
});

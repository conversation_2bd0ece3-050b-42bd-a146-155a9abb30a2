import { useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { selectAllLodgingTypes } from '../../../../../../../store/lodging/lodgingType.selectors';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { useServiceSettingContext } from '../../../ServicesSettingContext';
import { SelectLodging } from '../../SelectLodging';
export interface LodgingFormProps {}

export const LodgingForm = memo((_props: LodgingFormProps) => {
  const { form, isDisabledForRemains } = useServiceSettingContext();
  const [lodgingTypeList = []] = useSelector(selectAllLodgingTypes);
  const [customizedLodgings, lodgingFilter] = useWatch({
    control: form?.control,
    name: ['customizedLodgings', 'lodgingFilter'],
  });

  const isSelectedAll = !lodgingFilter;

  const availableLodgingOption = lodgingTypeList.map((item) => {
    const { name, id } = item || {};
    return {
      label: name,
      value: id,
      isDisabled: isSelectedAll,
    };
  });

  const handleSelectAllChange = useLatestCallback((value) => {
    form?.setValue('lodgingFilter', !value, {
      shouldDirty: true,
    });
    !value &&
      form?.setValue('customizedLodgings', [], {
        shouldDirty: true,
      });
  });

  const handleChange = (value: string[] | undefined) => {
    form?.setValue('customizedLodgings', value || [], { shouldDirty: true });
    form?.setValue('lodgingFilter', true, { shouldDirty: true });
  };

  return (
    <SelectLodging
      lodgingsName="customizedLodgings"
      onSelectChange={handleChange}
      onSelectAllChange={handleSelectAllChange}
      customizedLodgings={customizedLodgings}
      lodgingFilter={lodgingFilter}
      availableLodgingOption={availableLodgingOption}
      isDisabled={isDisabledForRemains}
    />
  );
});

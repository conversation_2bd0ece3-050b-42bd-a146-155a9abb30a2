import { Form, LegacySelect as Select } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { taxMapBox } from '../../../../../../../store/business/tax.boxes';
import { selectBusinessTaxes } from '../../../../../../../store/business/tax.selectors';
import { useServiceSettingContext } from '../../../ServicesSettingContext';

export interface TaxProps {}

export const Tax = memo<TaxProps>(() => {
  const { isDisabledForTax } = useServiceSettingContext();
  const [taxIdList, taxMap] = useSelector(selectBusinessTaxes, taxMapBox);

  const taxOptionList = useMemo(() => {
    return taxIdList.toArray().map((id) => {
      const tax = taxMap.mustGetItem(id);
      return {
        label: `${tax.taxName} (${tax.taxRate}%)`,
        value: String(tax.id),
      };
    });
  }, [taxIdList, taxMap]);

  return (
    <Form.Item
      name="taxId"
      label="Tax"
      rules={{
        required: 'Tax is required',
        validate: (value) => {
          if (taxOptionList.some((item) => item.value === value)) {
            return true;
          }
          return 'Tax is required';
        },
      }}
    >
      <Select
        isDisabled={isDisabledForTax}
        options={taxOptionList}
        isRequired
        formatOptionLabel={(opt: { label: string; value: string }) => opt?.label || ''}
      />
    </Form.Item>
  );
});

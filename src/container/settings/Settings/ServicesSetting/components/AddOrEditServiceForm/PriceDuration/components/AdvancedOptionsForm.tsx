import {
  type LocationOverrideRule,
  type LocationStaffOverrideRule,
  type StaffOverrideRule,
} from '@moego/api-web/moego/models/offering/v1/service_models';
import { BusinessType } from '@moego/api-web/moego/models/organization/v1/location_enums';
import { SvgIcon } from '@moego/business-components';
import { MajorCrown, MinorChevronDownOutlined, MinorInfoOutlined } from '@moego/icons-react';
import { Avatar, Button, Condition, Heading, Markup, Tag, Text, Tooltip, cn, useWatch } from '@moego/ui';
import { useSelector, useStore } from 'amos';
import { Collapse } from 'antd';
import { type List as ImmutableList } from 'immutable';
import { intersection, isUndefined, merge, remove } from 'lodash';
import React, { type FC, Fragment, useMemo } from 'react';
import { useLatest } from 'react-use';
import { useDeepCompareMemoize } from 'use-deep-compare-effect';
import { type DeepPartial } from 'utility-types';
import SvgIconMobileSvg from '../../../../../../../../assets/svg/icon-mobile.svg';
import SvgIconSalonSvg from '../../../../../../../../assets/svg/icon-salon.svg';
import { WithPricingEnableUpgrade } from '../../../../../../../../components/Pricing/WithPricingComponents';
import { Full } from '../../../../../../../../components/Style/Style';
import { Switch } from '../../../../../../../../components/SwitchCase';
import { WithMultiLocation } from '../../../../../../../../components/WithFeature/WithMultiLocation';
import { useNewAccountStructure } from '../../../../../../../../components/WithFeature/useNewAccountStructure';
import { RE_INPUT_AMOUNT, RE_INPUT_NUMBER } from '../../../../../../../../components/form/NumberInput';
import { NumberInputV2 } from '../../../../../../../../components/form/NumberInputV2';
import { selectCurrentBusiness } from '../../../../../../../../store/business/business.selectors';
import { type LocationRecord } from '../../../../../../../../store/business/location.boxes';
import { selectLocationDetailByBusiness } from '../../../../../../../../store/business/location.selectors';
import { selectPricingPermission } from '../../../../../../../../store/company/company.selectors';
import { selectBusinessStaffs, selectStaff } from '../../../../../../../../store/staff/staff.selectors';
import { isNormal } from '../../../../../../../../store/utils/identifier';
import { isUndefinedOrNullOrEmptyString } from '../../../../../../../../utils/common';
import { useBool } from '../../../../../../../../utils/hooks/useBool';
import { useMergedState } from '../../../../../../../../utils/hooks/useMergedState';
import { getStaffColorCode } from '../../../../../../../../utils/utils';
import { MultiLocationSelector } from '../../../../../components/Selector/MultiLocationSelector';
import { MultiRoleStaffsSelector } from '../../../../../components/Selector/MultiStaffsSelector/MultiRoleStaffsSelector';
import { useServiceSettingContext } from '../../../../ServicesSettingContext';
import { numberToStringV2, stringToNumberV2 } from '../../../../utils/inputTransformer';
import {
  isValidServiceOverrideLocation,
  isValidServiceStaffOverride,
} from '../../../../utils/serviceOverrideLocationHandler';
import { CollapseView } from './AdvancedOptionsForm.style';
import { TaxSelector } from './TaxSelector';

interface Props {
  availableLocationIdList: number[];
  availableStaffIdList: number[];
}

type Id = string | number;
type LocationOverrideKeys = keyof Omit<LocationOverrideRule, 'businessId'>;
type StaffOverrideKeys = keyof Omit<StaffOverrideRule, 'staffId'>;
type ArrowDegConfig = {
  activeDeg: number;
  defaultDeg: number;
};

/**
 * common params
 * ------------------------------
 */
const { Panel } = Collapse;
const NotFoundIndex = -1;
const FormNameCls = 'moe-py-s moe-px-8px-100 moe-flex moe-min-w-[200px] moe-pl-[28px] moe-relative moe-items-center';
const FormItemCls = 'moe-p-8px-100 moe-flex-1';
const FormItemNameCls = 'moe-text-primary moe-max-w-[120px]';
const ServiceByStaffPricingPermissionKey = 'servicePriceAndDurationByStaff';
const AdvanceCollapseArrowDegConfig: ArrowDegConfig = { activeDeg: -180, defaultDeg: 0 };
const LocationCollapseArrowDegConfig: ArrowDegConfig = { activeDeg: 0, defaultDeg: -90 };

export const AdvancedOptionsForm: FC<Props> = ({ availableLocationIdList, availableStaffIdList }) => {
  const store = useStore();
  const isActive = useBool();
  const { form } = useServiceSettingContext();
  const { hasMultipleLocation } = useNewAccountStructure('all');
  const [business, pricingPermission] = useSelector(selectCurrentBusiness, selectPricingPermission);
  const hasAuth = pricingPermission.enable.has(ServiceByStaffPricingPermissionKey);
  /**
   * location filter 所需数据
   * ------------------------------
   * @param availableLocationIdList 最新的 available location 列表
   * @param displayLocationIdList filter 之后显示的 location 列表 每当 available location 变化的时候重新全选
   */
  const availableLocationIdListRef = useLatest(availableLocationIdList);
  const [displayLocationIdList, setDisplayLocationIdList] = useMergedState<string[]>(
    [useDeepCompareMemoize(availableLocationIdListRef.current)],
    () => availableLocationIdListRef.current.map((item) => item.toString()),
  );
  const [locationOverrideActiveKeys, setLocationOverrideActiveKeys] = useMergedState<string[]>(
    [useDeepCompareMemoize(availableLocationIdListRef.current)],
    () => [availableLocationIdListRef.current?.[0]?.toString()],
  );

  /**
   * staff override 表单数据
   * ------------------------------
   */
  const [price, taxId, duration, sourceLocationStaffOverrideList] = useWatch({
    control: form?.control,
    name: ['price', 'taxId', 'duration', 'locationStaffOverrideList'],
  });
  const pricePlaceholder = numberToStringV2.input?.(price);
  const durationPlaceholder = numberToStringV2.input?.(duration);
  const locationStaffOverrideList = sourceLocationStaffOverrideList ?? [];

  /**
   * staff filter 表头数据
   * ------------------------------
   * @param availableStaffIdList 最新的 available staff 列表
   * @param availableStaffIds 计算出 staff filter 的可选项
   * @param displayStaffIdList 筛选出来的 staff 列表
   * @param isDisplayAllStaff available staffs 是否全部展示
   */
  const availableStaffIdListRef = useLatest(availableStaffIdList);
  const availableStaffIds = useMemo(() => {
    const currentLocationsStaffList = displayLocationIdList
      .map((locationId) => store.select(selectBusinessStaffs(Number(locationId))).toArray())
      .flat();
    return intersection(availableStaffIdListRef.current, currentLocationsStaffList);
  }, [useDeepCompareMemoize(availableStaffIdListRef.current), displayLocationIdList]);
  const [displayStaffIdList, setDisplayStaffIdList] = useMergedState<number[]>(
    [availableStaffIds],
    () => availableStaffIds,
  );
  const isDisplayAllStaff = displayStaffIdList.length === availableStaffIds.length;

  const getLocationOverrideIndex = (locationId: Id) => {
    return locationStaffOverrideList.findIndex((item) => item.locationOverride.businessId === locationId.toString());
  };

  const getLocationStaffOverrideIndex = (locationId: Id, staffId: Id) => {
    const locationOverrideIndex = getLocationOverrideIndex(locationId);
    if (locationOverrideIndex === NotFoundIndex) {
      return [NotFoundIndex, NotFoundIndex];
    }
    const staffOverrideIndex =
      locationStaffOverrideList?.[locationOverrideIndex]?.staffOverrideList?.findIndex(
        (item) => item.staffId === staffId.toString(),
      ) ?? NotFoundIndex;
    return [locationOverrideIndex, staffOverrideIndex];
  };

  const createLocationStaffOverride = (locationId: Id): LocationStaffOverrideRule => {
    return {
      locationOverride: { businessId: `${locationId}` },
      staffOverrideList: [],
    };
  };

  /**
   * - 重置 filter
   * 重置 filter 到 all location & all staff 的状态
   * 因为 staff filter 是依赖于 location 进行二次计算的这里只需要重置 location 为 all 即可
   *
   * - 重置 override 表单
   * 清空 location staff override 表单的值这里注意不可置为空数组而是要保留 id 其他值剔除
   */
  const handleResetAll = () => {
    setLocationOverrideActiveKeys([]);
    setDisplayLocationIdList(availableLocationIdListRef.current.map((item) => item.toString()));
    form?.setValue(
      'locationStaffOverrideList',
      locationStaffOverrideList.map(({ locationOverride, staffOverrideList }) => ({
        locationOverride: { businessId: locationOverride.businessId },
        staffOverrideList: staffOverrideList.map((staffOverride) => ({
          staffId: staffOverride.staffId,
        })),
      })),
      { shouldDirty: true },
    );
  };

  /**
   * 过滤 location 表头
   * @param locationList
   * @returns
   */
  const handleFilterAvailableLocation = (locationList: ImmutableList<LocationRecord>) => {
    return locationList.filter((location) => availableLocationIdListRef.current?.includes(Number(location.id)));
  };

  /**
   * 修改 location 级别的 override 值
   *
   * @param {OverrideId} locationId
   * @param {LocationOverrideKeys} key
   * @param {any} value
   */
  const handleLocationOverrideChange = (locationId: Id, key: LocationOverrideKeys, value: any) => {
    const rule = { [key]: value };
    const overrideIndex = getLocationOverrideIndex(locationId);

    const createAndInsertNewLocationOverride = () => {
      const { locationOverride: emptyLocationOverride, staffOverrideList } = createLocationStaffOverride(locationId);
      const locationOverride = merge(emptyLocationOverride, rule);
      const newLocationStaffOverrideList = [...locationStaffOverrideList, { locationOverride, staffOverrideList }];
      form?.setValue('locationStaffOverrideList', newLocationStaffOverrideList, { shouldDirty: true });
    };

    const updateLocationOverride = () => {
      const { locationOverride: oldLocationOverride, staffOverrideList } = locationStaffOverrideList[overrideIndex];
      const locationOverride = merge(oldLocationOverride, rule);
      // 校验更新后的 override 是否合法 合法才更新 如果不合法则删除
      const isValidRow =
        isValidLocationOverrideRow(locationOverride) || staffOverrideList?.some(isValidStaffOverrideRow);
      if (isValidRow) {
        form?.setValue(
          `locationStaffOverrideList.${overrideIndex}`,
          { locationOverride, staffOverrideList },
          { shouldDirty: true },
        );
      } else {
        const newLocationStaffOverrideList = remove(locationStaffOverrideList, (_, index) => index !== overrideIndex);
        form?.setValue('locationStaffOverrideList', newLocationStaffOverrideList, { shouldDirty: true });
      }
    };

    if (overrideIndex === NotFoundIndex) {
      return createAndInsertNewLocationOverride();
    }
    return updateLocationOverride();
  };

  /**
   *  修改 staff 级别的 override 值拥有 location 级别的 override
   *  如果有 staff 对应的 override 直接修改数值
   *  如果没有 staff 对应的 override 则新增 staff 对应的 override
   *
   * @param {Id} locationId
   * @param {Id} staffId
   * @param {StaffOverrideKeys} key
   * @param {*} value
   */
  const handleLocationStaffOverrideChange = (locationId: Id, staffId: Id, key: StaffOverrideKeys, value: any) => {
    const [locationIndex, staffIndex] = getLocationStaffOverrideIndex(locationId, staffId);

    const updateStaffOverride = () => {
      const staffOverrideList = locationStaffOverrideList[locationIndex].staffOverrideList;
      const newStaffOverride = { ...staffOverrideList[staffIndex], [key]: value };
      // 校验更新后的 override 是否合法 合法才更新 如果不合法则删除
      if (isValidStaffOverrideRow(newStaffOverride)) {
        staffOverrideList[staffIndex] = newStaffOverride;
        form?.setValue(`locationStaffOverrideList.${locationIndex}.staffOverrideList`, staffOverrideList, {
          shouldDirty: true,
        });
      } else {
        const newStaffOverrideList = remove(staffOverrideList, (_, index) => index !== staffIndex);
        form?.setValue(`locationStaffOverrideList.${locationIndex}.staffOverrideList`, newStaffOverrideList, {
          shouldDirty: true,
        });
      }
    };

    const createAndInsertNewStaffOverride = () => {
      const rule = { staffId: staffId.toString(), [key]: value };
      const newStaffOverrideList = locationStaffOverrideList[locationIndex].staffOverrideList.concat(rule);
      form?.setValue(`locationStaffOverrideList.${locationIndex}.staffOverrideList`, newStaffOverrideList, {
        shouldDirty: true,
      });
    };

    const createAndInsertLocationStaffOverride = () => {
      const { locationOverride } = createLocationStaffOverride(locationId);
      const rule = { staffId: staffId.toString(), [key]: value };
      const newLocationStaffOverrideList = [
        ...locationStaffOverrideList,
        { locationOverride, staffOverrideList: [rule] },
      ];
      form?.setValue('locationStaffOverrideList', newLocationStaffOverrideList, { shouldDirty: true });
    };

    if (locationIndex !== NotFoundIndex) {
      if (staffIndex !== NotFoundIndex) {
        return updateStaffOverride();
      } else {
        return createAndInsertNewStaffOverride();
      }
    }
    return createAndInsertLocationStaffOverride();
  };

  const handleToggleLocationOverridePanel = (locationId: string) => {
    setLocationOverrideActiveKeys((prevActiveKeys) => {
      if (prevActiveKeys.includes(locationId)) {
        return prevActiveKeys.filter((key) => key !== locationId);
      }
      return prevActiveKeys.concat(locationId);
    });
  };

  const renderAdvancePanelHeader = () => (
    <div onClick={isActive.toggle} className="moe-flex moe-gap-xxs">
      <Markup variant="small" className="moe-text-primary">
        {hasMultipleLocation ? 'Advanced options for business & staff' : 'Advanced options for staff'}
      </Markup>
      {renderOverrideRowNumber()}
      {renderActiveArrow(isActive.value, AdvanceCollapseArrowDegConfig)}
      <Tooltip
        content={
          <Fragment>
            <p>
              When creating a new appointment or book again, the price and duration will be determined in the following
              order of priority:
            </p>
            <p>1. Last finished appointment&apos;s price & duration (if book again)</p>
            <p>2. Saved price & duration for pet</p>
            <p>3. Saved price & duration for staff</p>
            <p>4. Price & duration in service settings</p>
          </Fragment>
        }
      >
        <MinorInfoOutlined className="moe-text-icon-tertiary" />
      </Tooltip>
    </div>
  );
  const renderNoAuthTip = () => (
    <Condition if={!hasAuth}>
      <WithPricingEnableUpgrade permission={ServiceByStaffPricingPermissionKey}>
        {(onCapture) => (
          <div className="moe-flex moe-items-center moe-my-8px-150 moe-p-s moe-rounded-m moe-bg-brand-subtle">
            <MajorCrown />
            <Text variant="regular" className="moe-ml-xs moe-text-primary">
              Upgrade your plan to customize price & duration for staff
            </Text>
            <Full />
            <Button size="s" onPress={() => onCapture?.()}>
              Upgrade
            </Button>
          </div>
        )}
      </WithPricingEnableUpgrade>
    </Condition>
  );
  const renderOverrideRowNumber = () => {
    const isDisplayLocation = (locationOverride: LocationOverrideRule) =>
      displayLocationIdList.includes(locationOverride.businessId);
    const isDisplayStaff = (staffOverride: StaffOverrideRule) =>
      displayStaffIdList.includes(Number(staffOverride.staffId));
    let overrideRowNumber = 0;
    locationStaffOverrideList.forEach(({ locationOverride, staffOverrideList }) => {
      if (isDisplayLocation(locationOverride)) {
        if (isValidServiceOverrideLocation(locationOverride)) {
          overrideRowNumber++;
        }
        staffOverrideList.forEach((staffOverride) => {
          if (isDisplayStaff(staffOverride) && isValidServiceStaffOverride(staffOverride)) {
            overrideRowNumber++;
          }
        });
      }
    });
    return (
      <Condition if={overrideRowNumber > 0}>
        <Tag
          color="neutral"
          variant="filled"
          isBordered={false}
          classNames={{
            base: 'moe-rounded-[10px] moe-h-[20px] moe-min-w-[20px]',
          }}
          label={
            <Text variant="caption" className="moe-text-tertiary">
              {overrideRowNumber}
            </Text>
          }
        />
      </Condition>
    );
  };
  /**
   * 这里的 selector 只是前端对 available 数据进行一层过滤
   * 渲染过滤后的表单内容，但是并不会修改真实的表单内容
   *
   * @returns
   */
  const renderOverrideFormFilter = () => (
    <div className="moe-w-full moe-flex moe-justify-between moe-py-8px-100">
      <div className="moe-flex moe-gap-s">
        <WithMultiLocation scene="all">
          <MultiLocationSelector
            scene="all"
            value={displayLocationIdList}
            onChange={setDisplayLocationIdList}
            filterOptions={handleFilterAvailableLocation}
            isDisabled={() => false}
            multipleMode="count"
            className="moe-w-[280px]"
            showSelectAll
            footer={null}
            renderMultipleValues={(values, isSelectedAll) =>
              isSelectedAll
                ? 'All businesses'
                : values.length === 1
                  ? `${values[0].label}`
                  : `${values.length} businesses`
            }
          />
        </WithMultiLocation>
        <MultiRoleStaffsSelector
          multipleMode="count"
          value={displayStaffIdList}
          onChange={setDisplayStaffIdList}
          availableStaffIds={availableStaffIds}
          classNames={{ menuPortal: '!moe-w-[440px]', selectAll: 'moe-py-none' }}
          renderMultipleValues={(staffs) =>
            isDisplayAllStaff ? 'All staff' : staffs.length === 1 ? `${staffs[0].label}` : `${staffs.length} staff`
          }
        />
      </div>
      <Button onPress={handleResetAll} variant="tertiary">
        Reset all
      </Button>
    </div>
  );
  const renderOverrideFormHeader = () => {
    const formNameLabel = hasMultipleLocation ? 'Business & Staff' : 'Staff';
    const formColNames = hasMultipleLocation ? ['Price', 'Tax', 'Duration'] : ['Price', 'Duration'];
    return (
      <div className={cn('moe-w-full moe-flex', 'moe-border-divider moe-border-b')}>
        <div className={cn(FormNameCls, { '!moe-pl-8px-100': !hasMultipleLocation })}>
          <Heading size="6" className="moe-text-secondary">
            {formNameLabel}
          </Heading>
        </div>
        <div className="moe-w-full moe-flex">
          {formColNames.map((title) => (
            <div key={title} className={cn(FormItemCls, 'moe-py-s')}>
              <Heading size="6" className="moe-text-secondary">
                {title}
              </Heading>
            </div>
          ))}
        </div>
      </div>
    );
  };
  const renderOverrideFormBody = () => (
    <Switch>
      <Switch.Case if={hasMultipleLocation}>
        <Collapse bordered={false} expandIcon={() => null} activeKey={locationOverrideActiveKeys}>
          {displayLocationIdList.map((locationId, index) => (
            <Panel key={locationId} header={renderLocationOverride(locationId, index === 0)}>
              {renderLocationStaffOverrideList(locationId)}
            </Panel>
          ))}
        </Collapse>
      </Switch.Case>
      <Switch.Case else>{renderLocationStaffOverrideList(displayLocationIdList[0])}</Switch.Case>
    </Switch>
  );
  const renderLocationOverride = (locationId: string, isDisableBorder = false) => {
    const isCurrentLocationOverrideCollapseOpen = locationOverrideActiveKeys.includes(locationId);
    const location = store.select(selectLocationDetailByBusiness(Number(locationId)));
    const overrideIndex = getLocationOverrideIndex(locationId);
    const taxRealValue = stringToNumberV2.input(
      form?.watch(`locationStaffOverrideList.${overrideIndex}.locationOverride.taxId`),
    );
    const taxValue: any = isUndefined(taxRealValue) ? null : taxRealValue;
    return (
      <div className={cn('moe-flex', { 'moe-border-divider moe-border-t': !isDisableBorder })}>
        <div className={FormNameCls} onClick={() => handleToggleLocationOverridePanel(locationId)}>
          <div className="moe-flex moe-items-center moe-absolute moe-top-1/2 moe-left-8px-100 -moe-translate-y-1/2">
            {renderActiveArrow(isCurrentLocationOverrideCollapseOpen, LocationCollapseArrowDegConfig)}
          </div>
          {renderLocationIcon(location.businessType)}
          <Text className={FormItemNameCls} variant="small" ellipsis={{ tooltip: { content: location.name } }}>
            {location.name}
          </Text>
        </div>
        <div className={FormItemCls}>
          <NumberInputV2
            inputFormat={RE_INPUT_AMOUNT}
            placeholder={pricePlaceholder}
            prefix={renderPricePrefix()}
            onChange={(value) => handleLocationOverrideChange(location.id, 'price', value)}
            value={numberToStringV2.input?.(
              form?.watch(`locationStaffOverrideList.${overrideIndex}.locationOverride.price`),
            )}
          />
        </div>
        <div className={FormItemCls}>
          <TaxSelector
            placeholderTaxId={Number(taxId)}
            placeholder=""
            value={taxValue}
            onChange={(value) => handleLocationOverrideChange(location.id, 'taxId', value)}
          />
        </div>
        <div className={FormItemCls}>
          <NumberInputV2
            maxLength={4}
            suffix="mins"
            inputFormat={RE_INPUT_NUMBER}
            placeholder={durationPlaceholder}
            onChange={(value) => handleLocationOverrideChange(location.id, 'duration', value)}
            value={numberToStringV2.input?.(
              form?.watch(`locationStaffOverrideList.${overrideIndex}.locationOverride.duration`),
            )}
          />
        </div>
      </div>
    );
  };
  const renderLocationIcon = (businessType: BusinessType) => (
    <div className="moe-flex moe-items-center moe-mx-8px-50">
      <Switch>
        <Switch.Case if={businessType === BusinessType.SALON}>
          <SvgIcon src={SvgIconSalonSvg} size={20} />
        </Switch.Case>
        <Switch.Case if={businessType === BusinessType.MOBILE}>
          <SvgIcon src={SvgIconMobileSvg} size={20} />
        </Switch.Case>
        <Switch.Case else>
          <SvgIcon src={SvgIconSalonSvg} size={20} />
        </Switch.Case>
      </Switch>
    </div>
  );
  const renderLocationStaffOverrideList = (locationId: string) => {
    const staffList = store.select(selectBusinessStaffs(Number(locationId))).toArray();
    const selectedDisplayStaffList = intersection(staffList, displayStaffIdList);
    const containerCls = cn('moe-flex moe-flex-col moe-rounded-8px-150 moe-mb-xs');
    return (
      <Switch>
        <Switch.Case if={selectedDisplayStaffList.length > 0}>
          <div
            className={cn(containerCls, {
              'moe-bg-neutral-sunken-0': hasMultipleLocation,
              'moe-rounded-none': !hasMultipleLocation,
            })}
          >
            {selectedDisplayStaffList.map((staffId) => renderLocationStaffOverride(locationId, staffId))}
          </div>
        </Switch.Case>
        <Switch.Case else>
          <div className={cn(containerCls, 'moe-py-m moe-items-center moe-bg-neutral-sunken-0')}>
            <Text variant="small" className="moe-text-tertiary">
              No selected staff work in ths business
            </Text>
          </div>
        </Switch.Case>
      </Switch>
    );
  };
  /**
   * 员工级别的 override 优先级顺序
   * 1. staff 级别的 override
   * 2. location 级别的 override
   * 3. service 级别的 override
   *
   * @param {string} locationId
   * @param {number} staffId
   * @return {*}
   */
  const renderLocationStaffOverride = (locationId: string, staffId: number) => {
    const staffRecord = store.select(selectStaff(staffId));
    const staffName = staffRecord.fullName();
    const [locationOverrideIndex, staffOverrideIndex] = getLocationStaffOverrideIndex(locationId, staffId);
    const staffPricePlaceholder =
      numberToStringV2.input?.(
        form?.watch(`locationStaffOverrideList.${locationOverrideIndex}.locationOverride.price`),
      ) ?? pricePlaceholder;
    const staffDurationPlaceholder =
      numberToStringV2.input?.(
        form?.watch(`locationStaffOverrideList.${locationOverrideIndex}.locationOverride.duration`),
      ) ?? durationPlaceholder;
    return (
      <div key={`${locationId}-${staffId}`} className="moe-flex">
        <div className={cn(FormNameCls, { '!moe-pl-8px-100': !hasMultipleLocation })}>
          <Avatar.Staff
            size="xs"
            className="moe-mx-8px-50"
            name={staffName}
            src={staffRecord.avatarPath}
            color={getStaffColorCode(staffRecord.colorCode, staffRecord.firstName, staffRecord.lastName)}
          />
          <Text
            variant="small"
            className={FormItemNameCls}
            ellipsis={{
              tooltip: { content: staffName },
            }}
          >
            {staffName}
          </Text>
        </div>
        <div className={FormItemCls}>
          <NumberInputV2
            maxLength={10}
            isDisabled={!hasAuth}
            inputFormat={RE_INPUT_AMOUNT}
            placeholder={staffPricePlaceholder}
            prefix={renderPricePrefix()}
            onChange={(value) => handleLocationStaffOverrideChange(locationId, staffId, 'price', value)}
            value={numberToStringV2.input?.(
              form?.watch(
                `locationStaffOverrideList.${locationOverrideIndex}.staffOverrideList.${staffOverrideIndex}.price`,
              ),
            )}
          />
        </div>
        <Condition if={hasMultipleLocation}>
          <div className={FormItemCls} />
        </Condition>
        <div className={FormItemCls}>
          <NumberInputV2
            maxLength={4}
            suffix="mins"
            isDisabled={!hasAuth}
            inputFormat={RE_INPUT_NUMBER}
            placeholder={staffDurationPlaceholder}
            onChange={(value) => handleLocationStaffOverrideChange(locationId, staffId, 'duration', value)}
            value={numberToStringV2.input?.(
              form?.watch(
                `locationStaffOverrideList.${locationOverrideIndex}.staffOverrideList.${staffOverrideIndex}.duration`,
              ),
            )}
          />
        </div>
      </div>
    );
  };
  const renderActiveArrow = (isActive: boolean, { activeDeg, defaultDeg }: ArrowDegConfig) => (
    <MinorChevronDownOutlined
      style={{
        transition: 'all .2s ease-in-out',
        transform: isActive ? `rotate(${activeDeg}deg)` : `rotate(${defaultDeg}deg)`,
      }}
    />
  );
  const renderPricePrefix = () => <span className="moe-leading-[20px]">{business.printCurrency()}</span>;
  return (
    <CollapseView>
      <Collapse bordered={false} activeKey={isActive.value ? ['1'] : void 0} expandIcon={() => null}>
        <Panel key="1" header={renderAdvancePanelHeader()}>
          {renderNoAuthTip()}
          <div className={cn('moe-p-s moe-rounded-8px-150 moe-mt-8px-150', 'moe-border moe-border-divider')}>
            {renderOverrideFormFilter()}
            {renderOverrideFormHeader()}
            <div className="moe-h-[400px] moe-overflow-auto">{renderOverrideFormBody()}</div>
          </div>
        </Panel>
      </Collapse>
    </CollapseView>
  );
};

const isValidStaffOverrideRow = (staffOverride: StaffOverrideRule) => {
  const { staffId, price, duration } = staffOverride;
  return isNormal(staffId) && (!isUndefinedOrNullOrEmptyString(price) || !isUndefinedOrNullOrEmptyString(duration));
};

const isValidLocationOverrideRow = (locationOverride: DeepPartial<LocationOverrideRule>) => {
  const { taxId, price, duration, maxDuration } = locationOverride || {};
  return (
    isNormal(taxId) ||
    !isUndefinedOrNullOrEmptyString(price) ||
    !isUndefinedOrNullOrEmptyString(duration) ||
    !isUndefinedOrNullOrEmptyString(maxDuration)
  );
};

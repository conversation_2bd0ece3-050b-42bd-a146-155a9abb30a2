import { useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { selectAllLocationIdList } from '../../../../../../../store/business/location.selectors';
import { selectCompanyAllStaffList } from '../../../../../../../store/staff/staff.selectors';
import { useServiceSettingContext } from '../../../ServicesSettingContext';
import { AdvancedOptionsForm } from './components/AdvancedOptionsForm';

export interface BizAndStaffOverrideProps {}

export const BizAndStaffOverride = memo<BizAndStaffOverrideProps>(() => {
  const { form } = useServiceSettingContext();
  const [allLocationIdList, companyAllStaffList] = useSelector(selectAllLocationIdList, selectCompanyAllStaffList);

  const [isAllLocation, sourceAvailableBusinessIdList, availableForAllStaff, sourceAvailableStaffIdList] = useWatch({
    control: form?.control,
    name: ['isAllBusiness', 'availableBusinessIdList', 'availableForAllStaff', 'availableStaffIdList'],
  });

  const currentLocationIdList = useMemo(() => {
    return isAllLocation
      ? allLocationIdList.toArray().map((item) => +item)
      : (sourceAvailableBusinessIdList ?? []).map((item) => +item);
  }, [isAllLocation, sourceAvailableBusinessIdList, allLocationIdList]);

  const currentStaffIdList = useMemo(() => {
    return availableForAllStaff
      ? companyAllStaffList.toArray().map((item) => +item)
      : (sourceAvailableStaffIdList || []).map((item) => +item);
  }, [availableForAllStaff, sourceAvailableStaffIdList, companyAllStaffList]);

  return (
    <AdvancedOptionsForm availableLocationIdList={currentLocationIdList} availableStaffIdList={currentStaffIdList} />
  );
});

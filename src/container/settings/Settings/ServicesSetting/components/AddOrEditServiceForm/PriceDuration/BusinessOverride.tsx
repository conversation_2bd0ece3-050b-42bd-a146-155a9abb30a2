import { type LocationStaffOverrideRule } from '@moego/api-web/moego/models/offering/v1/service_models';
import { MajorPlusOutlined, MinorTrashOutlined } from '@moego/icons-react';
import { Button, Form, Heading, Input, LegacySelect as Select, useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { LABEL_KEY, getPriceUnitText } from '../../../../../../../components/ServiceApplicablePicker/utils/priceUnit';
import { WithMultiLocation } from '../../../../../../../components/WithFeature/WithMultiLocation';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import { locationMapBox } from '../../../../../../../store/business/location.boxes';
import { selectAllLocationIdList } from '../../../../../../../store/business/location.selectors';
import { taxMapBox } from '../../../../../../../store/business/tax.boxes';
import { selectBusinessTaxes } from '../../../../../../../store/business/tax.selectors';
import { useEnableFeature } from '../../../../../../../store/metadata/featureEnable.hooks';
import { META_DATA_KEY_LIST } from '../../../../../../../store/metadata/metadata.config';
import { ServiceType } from '../../../../../../../store/service/category.boxes';
import { useEnableServiceDurationCallback } from '../../../../../../../utils/hooks/useEnableServiceDurationCallback';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { useServiceSettingContext } from '../../../ServicesSettingContext';
import { numberToStringV2 } from '../../../utils/inputTransformer';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';

export interface BusinessOverrideProps {}

export const BusinessOverride = memo<BusinessOverrideProps>((_props) => {
  const { form, serviceType, serviceItemType, isDisabledForTax } = useServiceSettingContext();
  const [business, allLocationIdList, locationMap, taxIdList, taxMap] = useSelector(
    selectCurrentBusiness,
    selectAllLocationIdList,
    locationMapBox,
    selectBusinessTaxes,
    taxMapBox,
  );
  const { enable: hasNegativePriceAuth } = useEnableFeature(META_DATA_KEY_LIST.AddonsNegativePriceEnabled);
  const enableServiceDurationCallback = useEnableServiceDurationCallback();

  const isAddonService = serviceType === ServiceType.Addon;
  const isBoarding = serviceItemType === ServiceItemType.BOARDING;
  const isAllowNegativePrice = hasNegativePriceAuth && isAddonService;
  const enableDuration = enableServiceDurationCallback(serviceType, serviceItemType);

  const [priceUnit, isAllLocation, sourceAvailableBusinessIdList, locationStaffOverrideList = []] = useWatch({
    control: form?.control,
    name: ['priceUnit', 'isAllBusiness', 'availableBusinessIdList', 'locationStaffOverrideList'],
  });

  const currentLocationIdList = useMemo(() => {
    return isAllLocation
      ? allLocationIdList.toArray().map((item) => +item)
      : (sourceAvailableBusinessIdList ?? []).map((item) => +item);
  }, [isAllLocation, sourceAvailableBusinessIdList, allLocationIdList]);

  const showAddOverrideButton = locationStaffOverrideList.length !== currentLocationIdList.length;

  const handleDisabledSelectValue = useLatestCallback((e: string) => {
    // 如果已经在 location override 里选中了，就不能再选中
    return locationStaffOverrideList.findIndex((item) => Number(item.locationOverride.businessId) === Number(e)) !== -1;
  });

  const taxOptionList = useMemo(() => {
    return taxIdList.toArray().map((id) => {
      const tax = taxMap.mustGetItem(id);
      return {
        label: `${tax.taxName} (${tax.taxRate}%)`,
        value: String(tax.id),
      };
    });
  }, [taxIdList, taxMap]);

  /**
   * 根据选中的 location list 来生成后续需要 override 的区域里的 location selector 的 options
   */
  const filteredLocationOverrideList = useMemo(() => {
    return currentLocationIdList.map((item) => {
      const location = locationMap.mustGetItem(`${item}`);
      return {
        label: location.name,
        value: location.id,
        isDisabled: handleDisabledSelectValue(location.id),
      };
    });
  }, [currentLocationIdList, locationMap]);

  const handleAddLocationOverride = useLatestCallback(() => {
    if (isAllLocation) {
      if (locationStaffOverrideList?.length === allLocationIdList.size) {
        return;
      }
    }
    // 查找不在 all locations list 或者 locationStaffOverrideList 里的 business
    const firstAvailableBusinessId = currentLocationIdList.find((id) => {
      return !locationStaffOverrideList.some((item) => id === Number(item?.locationOverride?.businessId));
    });

    if (firstAvailableBusinessId) {
      const newOverride: LocationStaffOverrideRule = {
        locationOverride: { businessId: String(firstAvailableBusinessId) },
        staffOverrideList: [],
      };
      const newDraftLocationStaffOverrideList = [...(locationStaffOverrideList || []), newOverride];
      form?.setValue('locationStaffOverrideList', newDraftLocationStaffOverrideList);
    }
  });

  return (
    <WithMultiLocation scene="all">
      <Condition if={locationStaffOverrideList.length > 0}>
        {locationStaffOverrideList.map((item, index) => {
          return (
            <div
              key={item.locationOverride.businessId || index}
              className="moe-flex moe-flex-col moe-w-full moe-gap-y-[16px] moe-p-spacing-s moe-rounded-m moe-bg-neutral-sunken-0"
            >
              <div className="moe-flex moe-justify-between moe-pb-spacing-s moe-border-0 moe-border-solid moe-border-b moe-border-b-divider">
                <Heading size="5" className="moe-mb-0">
                  Variation {index + 1}
                </Heading>
                <MinorTrashOutlined
                  className="moe-cursor-pointer"
                  onClick={() => {
                    // 当draftLocationOverrideList 删除后为空时，表单认为设置的新值和字段的初始值相同,即isDirty为false。此处需要另外记一个状态判断进行了删除操作
                    form?.setValue(
                      'locationStaffOverrideList',
                      locationStaffOverrideList.filter((_, i) => i !== index),
                      { shouldDirty: true },
                    );
                  }}
                />
              </div>
              <div className="moe-flex moe-justify-between moe-gap-x-[24px]">
                <div className="moe-w-full">
                  <Form.Item name={`locationStaffOverrideList.${index}.locationOverride.businessId`} label="Business">
                    <Select options={filteredLocationOverrideList} />
                  </Form.Item>
                </div>
                <div className="moe-w-full">
                  <Form.Item
                    name={`locationStaffOverrideList.${index}.locationOverride.price`}
                    label="Price override"
                    transformer={{
                      // 数组类型的表单，在某项被删除后，再次添加，可能会被 useController 缓存上一次的 value，这里需要用 watch 的正确值填入
                      input: () => {
                        return form?.watch(`locationStaffOverrideList.${index}.locationOverride.price`);
                      },
                    }}
                  >
                    <Input.Number
                      placeholder="Add the price"
                      minValue={isAllowNegativePrice ? undefined : 0}
                      precision={2}
                      maxLength={10}
                      prefix={<span className="moe-leading-[20px]">{business.printCurrency()}</span>}
                      suffix={
                        isBoarding ? (
                          <span className="moe-leading-[20px]">
                            {priceUnit ? getPriceUnitText(priceUnit, LABEL_KEY.label) : ''}
                          </span>
                        ) : (
                          ''
                        )
                      }
                    />
                  </Form.Item>
                </div>
              </div>
              <div className="moe-flex moe-justify-between moe-gap-x-[24px]">
                <div className="moe-w-full">
                  <Form.Item
                    name={`locationStaffOverrideList.${index}.locationOverride.taxId`}
                    label="Tax override"
                    transformer={{
                      // 数组类型的表单，在某项被删除后，再次添加，可能会被 useController 缓存上一次的 value，这里需要用 watch 的正确值填入
                      input: () => {
                        const value = form?.watch(`locationStaffOverrideList.${index}.locationOverride.taxId`);
                        return value;
                      },
                    }}
                  >
                    <Select options={taxOptionList} isClearable />
                  </Form.Item>
                </div>
                <div className="moe-w-full">
                  {/*  Boarding/Daycare 不需要duration */}
                  <Condition if={enableDuration}>
                    <Form.Item
                      name={`locationStaffOverrideList.${index}.locationOverride.duration`}
                      label="Duration override"
                      transformer={{
                        // 数组类型的表单，在某项被删除后，再次添加，可能会被 useController 缓存上一次的 value，这里需要用 watch 的正确值填入
                        input: () => {
                          const value = form?.watch(`locationStaffOverrideList.${index}.locationOverride.duration`);
                          return numberToStringV2.input?.(value);
                        },
                        output: numberToStringV2.output,
                      }}
                    >
                      <Input.Number
                        className="moe-w-full"
                        placeholder="Add the duration"
                        suffix="mins"
                        precision={0}
                        minValue={0}
                        maxLength={4}
                      />
                    </Form.Item>
                  </Condition>
                </div>
              </div>
            </div>
          );
        })}
      </Condition>

      <Condition if={showAddOverrideButton}>
        <div className="moe-flex moe-flex-col moe-justify-start">
          <div className="moe-flex moe-flex-start">
            <Button
              variant="tertiary-legacy"
              icon={<MajorPlusOutlined />}
              onPress={handleAddLocationOverride}
              align="start"
              isDisabled={isDisabledForTax}
            >
              Override by business
            </Button>
          </div>
        </div>
      </Condition>
    </WithMultiLocation>
  );
});

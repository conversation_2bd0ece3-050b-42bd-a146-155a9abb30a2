import { Form, LegacySelect as Select, useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { selectBusinessPetCoatTypes } from '../../../../../../../store/pet/petHairLength.selectors';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { useServiceSettingContext } from '../../../ServicesSettingContext';

export interface CoatTypeFormProps {
  isDisabled?: boolean;
  onClick?: () => void;
}

export const CoatTypeForm = memo(function CoatTypeForm(props: CoatTypeFormProps) {
  const { isDisabled, onClick } = props;
  const { form, isDisabledForRemains } = useServiceSettingContext();
  const [coatFilter, customizedCoat] = useWatch({
    control: form?.control,
    name: ['coatFilter', 'customizedCoat'],
  });
  const [coatTypeList] = useSelector(selectBusinessPetCoatTypes);

  const coatTypeOptions = useMemo(() => {
    return coatTypeList.toJSON().map((item) => {
      return {
        label: item.name,
        value: String(item.id),
      };
    });
  }, [coatTypeList]);

  const isSelectedAll = !coatFilter;

  const calcValues = useMemo(() => {
    if (isSelectedAll) {
      return coatTypeOptions.map((item) => item.value);
    } else {
      return customizedCoat;
    }
  }, [customizedCoat, coatTypeOptions, isSelectedAll]);

  const isInvalid = calcValues?.length === 0 && coatTypeOptions.length !== 0;

  const handleChange = useLatestCallback((value: string[] | undefined) => {
    form?.setValue('customizedCoat', value || [], { shouldDirty: true });
    if (value?.length === coatTypeOptions.length) {
      form?.setValue('coatFilter', false, { shouldDirty: true });
    } else {
      form?.setValue('coatFilter', true, { shouldDirty: true });
    }
  });

  const errorMessage = isInvalid ? 'Please select at least one coat type' : undefined;

  const handleRenderMultipleValues = useLatestCallback(() => {
    return <div>All coat types</div>;
  });

  return (
    <div onClick={onClick}>
      <Form.Item
        name="customizedCoat"
        label="Coat Type"
        transformer={{
          input(coatTypeList) {
            if (isSelectedAll) {
              return coatTypeOptions.map((item) => item.value);
            }
            return coatTypeList;
          },
        }}
        rules={{
          validate: (value) => {
            if (isSelectedAll) {
              return true;
            }
            if (value?.length === 0 && coatTypeOptions.length !== 0) {
              return 'Please select at least one coat type';
            }
            return true;
          },
        }}
      >
        <Select
          isDisabled={isDisabled || isDisabledForRemains}
          isRequired
          isMultiple={true}
          showSelectAll={true}
          multipleMode={isSelectedAll ? 'value' : 'tag'}
          value={calcValues}
          options={coatTypeOptions}
          onChange={handleChange}
          renderMultipleValues={handleRenderMultipleValues}
          errorMessage={errorMessage}
          menuPlacement="top"
          // invalid 的时候需要设置这个 error type，否则出不来。。
          errorType={isInvalid ? 'onChange' : undefined}
        />
      </Form.Item>
    </div>
  );
});

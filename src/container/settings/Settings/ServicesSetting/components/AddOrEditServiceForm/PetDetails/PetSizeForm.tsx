import { Form, Link, LegacySelect as Select, Text, useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { Switch } from '../../../../../../../components/SwitchCase';
import { PATH_SETTING_CLIENTS_AND_PETS } from '../../../../../../../router/paths';
import { selectPetSizeList } from '../../../../../../../store/onlineBooking/settings/petSize.selectors';
import { useWeightSuffix } from '../../../../../../../utils/hooks/useWeightSuffix';
import { ClientAndPetsNav } from '../../../../ClientsAndPetsSetting/types';
import { useServiceSettingContext } from '../../../ServicesSettingContext';
import { WeightForm } from './WeightForm';

export interface PetSizeFormProps {
  isDisabled?: boolean;
  onClick?: () => void;
}

export const PetSizeForm = memo((props: PetSizeFormProps) => {
  const { isDisabled, onClick } = props;
  const { form, isDisabledForPetWeight } = useServiceSettingContext();
  const [petSizeFilter, weightRange] = useWatch({
    control: form?.control,
    name: ['petSizeFilter', 'weightRange'],
  });
  const [petSizeList = []] = useSelector(selectPetSizeList());
  const weightUnit = useWeightSuffix();

  const availableSizeOption = petSizeList.map((item) => {
    const { name, weightHigh, weightLow, id } = item || {};
    return {
      label: `${name}(${weightLow}-${weightHigh}${weightUnit})`,
      value: String(id),
    };
  });

  const isSelectedAll = !petSizeFilter;

  const handleChange = (value: string[] | undefined) => {
    form?.setValue('customizedPetSizes', value || [], { shouldDirty: true });
    if (value?.length === availableSizeOption.length) {
      form?.setValue('petSizeFilter', false, { shouldDirty: true });
    } else {
      form?.setValue('petSizeFilter', true, { shouldDirty: true });
    }
  };

  const handleCloseWeightForm = () => {
    form?.setValue('weightRange', [], { shouldDirty: true });
    form?.setValue('weightFilter', false, { shouldDirty: true });
    form?.setValue('petSizeFilter', true, { shouldDirty: true });
  };

  return (
    <div onClick={onClick}>
      <Switch>
        {/* 旧的weight range字段不为空，显示旧组件 */}
        <Switch.Case if={weightRange?.length}>
          <WeightForm isDisabled={isDisabled} onCloseForm={handleCloseWeightForm} />
        </Switch.Case>
        <Switch.Case else>
          <Form.Item
            name="customizedPetSizes"
            label="Weight"
            transformer={{
              input(lodgingTypeList) {
                if (isSelectedAll) {
                  return availableSizeOption.map((item) => item.value);
                }
                return lodgingTypeList;
              },
            }}
            rules={{
              validate: (value) => {
                if (!value?.length && !isSelectedAll) {
                  return 'Please select at least one pet weight';
                }
                return true;
              },
            }}
          >
            <Select
              isDisabled={isDisabled || isDisabledForPetWeight}
              isRequired
              isMultiple={true}
              showSelectAll={true}
              multipleMode={isSelectedAll ? 'value' : 'tag'}
              placeholder="Select pet weight"
              options={availableSizeOption}
              onChange={handleChange}
              renderMultipleValues={() => <div>All range</div>}
              description={
                <Text variant="small" className="moe-flex moe-items-center">
                  Size options can be modified at&nbsp;
                  <Link
                    variant="small"
                    target="_blank"
                    href={PATH_SETTING_CLIENTS_AND_PETS.queried({
                      panel: ClientAndPetsNav.PetSize,
                    })}
                  >
                    {'Settings > Clients & Pets > Pet size'}
                  </Link>
                </Text>
              }
            />
          </Form.Item>
        </Switch.Case>
      </Switch>
    </div>
  );
});

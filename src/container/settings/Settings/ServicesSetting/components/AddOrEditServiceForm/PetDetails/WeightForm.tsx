import { MinorErrorFilled, MinorMinusOutlined } from '@moego/icons-react';
import { Alert, Button, Form, Radio, RadioGroup, booleanToStringify, useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import { isUndefined } from 'lodash';
import React, { memo } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { RE_INPUT_AMOUNT } from '../../../../../../../components/form/NumberInput';
import { NumberInputV2 } from '../../../../../../../components/form/NumberInputV2';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { useServiceSettingContext } from '../../../ServicesSettingContext';
import { checkWeightIsValid } from '../../../utils/checkWeightIsValid';

export interface WeightFormProps {
  onCloseForm: () => void;
  isDisabled?: boolean;
}

export const WeightForm = memo(function WeightForm(props: WeightFormProps) {
  const { onCloseForm, isDisabled } = props;
  const { form } = useServiceSettingContext();
  // TODO: 换成 company format
  const [business] = useSelector(selectCurrentBusiness());
  const [weightFilter, weightRange] = useWatch({
    control: form?.control,
    name: ['weightFilter', 'weightRange'],
  });
  const [minWeight, maxWeight] = weightRange || [0, 0];
  const handleWeightChange = useLatestCallback((e: string, index: number) => {
    if (e && !RE_INPUT_AMOUNT.test(e)) {
      return;
    }
    if (e === '') {
      form?.setValue(`weightRange.${index}`, undefined as unknown as number, {
        shouldDirty: true,
      });
    } else {
      form?.setValue(`weightRange.${index}`, e as unknown as number, {
        shouldDirty: true,
      });
    }
  });

  const isValid = weightFilter ? checkWeightIsValid(minWeight, maxWeight) : true;

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-[16px]">
      <Form.Item name="weightFilter" label="Weight" transformer={booleanToStringify}>
        <RadioGroup
          isDisabled
          onChange={(value) => {
            // typeof value is boolean
            if (value) {
              form?.setValue('weightRange', [0, 0], {
                shouldDirty: true,
              });
            }
          }}
        >
          <Radio value={`false`}>Full range</Radio>
          <Radio value={`true`}>Customize</Radio>
        </RadioGroup>
      </Form.Item>
      <Condition if={weightFilter}>
        <div className="moe-flex moe-flex-col">
          <div className="moe-pl-[28px] moe-w-full moe-flex moe-justify-start moe-gap-x-xs">
            <NumberInputV2
              isDisabled={isDisabled}
              inputFormat={RE_INPUT_AMOUNT}
              onChange={(e) => handleWeightChange(e, 0)}
              value={!isUndefined(minWeight) ? `${minWeight}` : ''}
              suffix={business.weightUnit}
              className="moe-w-[129.5px]"
            />
            <MinorMinusOutlined />
            <NumberInputV2
              isDisabled={isDisabled}
              inputFormat={RE_INPUT_AMOUNT}
              onChange={(e) => handleWeightChange(e, 1)}
              value={!isUndefined(maxWeight) ? `${maxWeight}` : ''}
              suffix={business.weightUnit}
              className="moe-w-[129.5px]"
            />
          </div>
          <Condition if={isValid !== true}>
            <div className="moe-text-small moe-text-danger moe-pl-[28px] moe-flex moe-items-center moe-mt-[4px] moe-gap-x-[4px]">
              <MinorErrorFilled /> {isValid}
            </div>
          </Condition>
        </div>
      </Condition>
      <Alert
        color="warning"
        isBordered
        isRounded
        description="Rather than setting up custom weight ranges, you can now directly choose pet sizes. Simply reset to begin utilizing pet size selection at your convenience."
        action={
          <Button
            isDisabled={isDisabled}
            className="moe-bg-neutral-default"
            size="s"
            variant="secondary"
            onPressEnd={onCloseForm}
          >
            Reset
          </Button>
        }
      />
    </div>
  );
});

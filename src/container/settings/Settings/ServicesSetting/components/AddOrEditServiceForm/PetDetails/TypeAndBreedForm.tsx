import { MinorPlusOutlined, MinorTrashOutlined } from '@moego/icons-react';
import { Form, IconButton, Radio, RadioGroup, LegacySelect as Select, booleanToStringify, useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import { isUndefined } from 'lodash';
import React, { memo, useEffect, useMemo } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { petTypeMapBox } from '../../../../../../../store/pet/petType.boxes';
import { selectBusinessPetTypes } from '../../../../../../../store/pet/petType.selectors';
import { ServiceTypeBreedFilter } from '../../../../../../../store/service/service.boxes';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { useServiceSettingContext } from '../../../ServicesSettingContext';
import { BreedSelector } from './BreedSelector';

export interface TypeAndBreedFormProps {
  isDisabled?: boolean;
  onClick?: () => void;
}

export const TypeAndBreedForm = memo(function TypeAndBreedForm(props: TypeAndBreedFormProps) {
  const { isDisabled, onClick } = props;
  const { form, isDisabledForPetType } = useServiceSettingContext();

  const [breedFilter, customizedBreed] = useWatch({
    control: form?.control,
    name: ['breedFilter', 'customizedBreed'],
  });

  const [petTypeIdList, petTypeMap] = useSelector(selectBusinessPetTypes, petTypeMapBox);

  const petTypeOptions = useMemo(() => {
    return petTypeIdList
      .toJSON()
      .map((id) => {
        const item = petTypeMap.mustGetItem(id);
        return item;
      })
      .filter((item) => item.isAvailable)
      .map((item) => {
        return {
          label: item.typeName,
          value: String(item.petTypeId),
          isDisabled: customizedBreed?.some((i) => i.petTypeId === String(item.petTypeId)),
        };
      });
  }, [petTypeIdList, petTypeMap, customizedBreed]);

  const handleAddType = useLatestCallback((index: number) => {
    form?.setValue(
      `customizedBreed.${index}`,
      {
        breeds: [],
        isAll: true,
        petTypeId: undefined as unknown as string, // 这里是可以 undefined 的
      },
      { shouldDirty: true },
    );
  });

  useEffect(() => {
    if (breedFilter && !customizedBreed?.length) {
      form?.setValue(
        'customizedBreed',
        [
          {
            breeds: [],
            isAll: true,
            petTypeId: String(petTypeOptions?.[0]?.value), // 这里是可以 undefined 的
          },
        ],
        { shouldDirty: true },
      );
    }
  }, [breedFilter, customizedBreed]);

  const isInValid = useMemo(() => {
    return (
      breedFilter &&
      customizedBreed?.filter((item) => {
        if (isUndefined(item.petTypeId)) {
          return false;
        }
        return item.isAll || item.breeds?.length > 0;
      }).length === 0
    );
  }, [breedFilter, customizedBreed]);

  const handleRemoveType = useLatestCallback((index: number) => {
    const newCustomizedBreed = customizedBreed?.filter((_, i) => i !== index);
    form?.setValue(`customizedBreed`, newCustomizedBreed, { shouldDirty: true });
  });

  return (
    <div onClick={onClick} className="moe-flex moe-flex-col moe-gap-y-[16px]">
      <Form.Item
        name="breedFilter"
        label="Type & breed"
        transformer={booleanToStringify}
        rules={{
          validate: (value) => {
            if (value === ServiceTypeBreedFilter.All) {
              return true;
            }
            if (isInValid) {
              return 'Please select at least one pet type & breed';
            }

            return true;
          },
        }}
      >
        <RadioGroup isDisabled={isDisabled || isDisabledForPetType}>
          <Radio value={`false`} className="moe">
            All types & breeds
          </Radio>
          <Radio value={`true`}>Customize</Radio>
        </RadioGroup>
      </Form.Item>
      <Condition if={breedFilter}>
        <div className="moe-pl-[28px] moe-flex moe-flex-col moe-gap-y-[16px]">
          {customizedBreed?.map((item, index) => {
            return (
              <div key={`${item.petTypeId}_${index}`} className="moe-flex moe-gap-x-[16px]">
                <Select
                  isDisabled={isDisabled}
                  value={customizedBreed?.[index]?.petTypeId}
                  options={petTypeOptions}
                  classNames={{
                    menuList: 'moe-max-h-[200px] moe-overflow-y-auto',
                  }}
                  onChange={(value) => {
                    form?.setValue(
                      `customizedBreed.${index}`,
                      {
                        breeds: [],
                        isAll: true,
                        petTypeId: value!,
                      },
                      {
                        shouldDirty: true,
                      },
                    );
                    form?.clearErrors('breedFilter');
                  }}
                />
                <BreedSelector
                  isAll={customizedBreed?.[index]?.isAll}
                  index={index}
                  petTypeId={customizedBreed?.[index]?.petTypeId}
                  isDisabled={!customizedBreed?.[index]?.petTypeId || isDisabled}
                />
                <div className="moe-flex moe-flex-shrink-0 moe-justify-between moe-gap-x-[16px] moe-w-[80px] moe-items-center">
                  <Condition if={customizedBreed?.length > 1}>
                    <IconButton
                      isDisabled={isDisabled}
                      icon={<MinorTrashOutlined />}
                      onPress={() => handleRemoveType(index)}
                    ></IconButton>
                  </Condition>
                  <Condition if={index > 0 || customizedBreed?.length === 1}>
                    <IconButton
                      isDisabled={isDisabled}
                      icon={<MinorPlusOutlined />}
                      onPress={() => handleAddType(index + 1)}
                    ></IconButton>
                  </Condition>
                </div>
              </div>
            );
          })}
        </div>
      </Condition>
    </div>
  );
});

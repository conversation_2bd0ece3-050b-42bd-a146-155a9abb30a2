import { Select, Text, cn, useWatch, type Node } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect, useMemo } from 'react';
import { getBreedList } from '../../../../../../../store/pet/petBreed.actions';
import { petBreedMapBox } from '../../../../../../../store/pet/petBreed.boxes';
import { selectPetBreeds } from '../../../../../../../store/pet/petBreed.selectors';
import { isNormal } from '../../../../../../../store/utils/identifier';
import { getLongListText } from '../../../../../../../utils/getLongListText';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../../../utils/hooks/useSerialCallback';
import { useServiceSettingContext } from '../../../ServicesSettingContext';

export interface BreedSelectorProps {
  petTypeId?: string;
  index: number;
  className?: string;
  isDisabled?: boolean;
  isAll?: boolean;
}

export const BreedSelector = memo(function BreedSelector(props: BreedSelectorProps) {
  const dispatch = useDispatch();
  const { petTypeId = '', index, className = '', isDisabled, isAll = false } = props;
  const { form } = useServiceSettingContext();

  const [customizedBreed] = useWatch({
    control: form?.control,
    name: ['customizedBreed'],
  });

  const [breedIdList, breedMap] = useSelector(selectPetBreeds(Number(petTypeId)), petBreedMapBox);

  const breedOptionList = useMemo(() => {
    return breedIdList.toJSON().map((id) => {
      const item = breedMap.mustGetItem(id);
      return {
        value: item.name,
        label: item.name,
      };
    });
  }, [breedIdList, breedMap]);

  const values = customizedBreed?.[index]?.breeds;

  const isSelectedAll = values?.length === breedOptionList.length || isAll;

  const calcValues = useMemo(() => {
    if (isAll) {
      return breedOptionList.map((item) => item.value);
    } else {
      return values || [];
    }
  }, [values, breedOptionList, isAll]);

  useEffect(() => {
    if (isNormal(petTypeId)) {
      handleGetBreedList();
    }
  }, [petTypeId]);

  const handleGetBreedList = useSerialCallback(async () => {
    await dispatch(getBreedList(Number(petTypeId)));
  });

  const handleRenderValues = useLatestCallback(
    (
      values: Node<{
        value: string;
        label: string;
      }>[],
    ) => {
      const text = isSelectedAll
        ? 'All breeds'
        : getLongListText(
            values.map((item) => item.props.title),
            'breeds',
          );
      return (
        <Text className="group-data-[open]/select:moe-text-disabled" variant="regular-short">
          {text}
        </Text>
      );
    },
  );

  return (
    <Select.Multiple
      className={cn('moe-w-full', className)}
      classNames={{
        base: 'moe-w-full',
        placeholder: '!moe-whitespace-normal',
      }}
      listClassNames={{
        base: 'moe-max-h-[240px] moe-overflow-y-auto',
      }}
      value={calcValues}
      items={breedOptionList}
      onChange={(value) => {
        if (value) {
          form?.setValue(
            `customizedBreed.${index}`,
            {
              breeds: value,
              isAll: value.length === breedOptionList.length,
              petTypeId,
            },
            { shouldDirty: true },
          );
        }
      }}
      mode="value"
      renderValues={handleRenderValues}
      isDisabled={isDisabled}
      showSelectAll={true}
    >
      {(item) => <Select.Item key={item.value} title={item.label} />}
    </Select.Multiple>
  );
});

import { useSelector } from 'amos';
import React, { useImperativeHandle, useState } from 'react';
import { Condition } from '../../../../../components/Condition';
import { currentAccountIdBox } from '../../../../../store/account/account.boxes';
import { serviceChargeMapBox } from '../../../../../store/service/service.boxes';
import { selectServiceChargeIdList } from '../../../../../store/service/service.selectors';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { memoForwardRef } from '../../../../../utils/react';
import { useMoveList } from '../../components/DraggableList/utils';
import { ServiceSortFormRow } from './ServiceSortFormRow';
import { DndProvider } from 'react-dnd';
import { dndManager } from '../../../../../utils/dndManager';

export interface ServiceChargeSortFormProps {
  isInactive: boolean;
}

export interface ServiceChargeSortFormRef {
  getData: () => string[];
}

export const ServiceChargeSortForm = memoForwardRef<ServiceChargeSortFormRef, ServiceChargeSortFormProps>(
  function ServiceChargeSortForm(props: ServiceChargeSortFormProps, ref) {
    const { isInactive } = props;
    const [currentAccountId] = useSelector(currentAccountIdBox);
    const [serviceChargeIdList, serviceChargeMap] = useSelector(
      selectServiceChargeIdList(!isInactive, currentAccountId),
      serviceChargeMapBox,
    );

    // 这个是没用的一个 function，不过为了满足 types 定义，先留着
    const handleDrop = useLatestCallback(() => {});

    const [localServiceIdList, setLocalServiceIdList] = useState(serviceChargeIdList.toJSON());

    const handleMove = useMoveList(localServiceIdList, setLocalServiceIdList);

    useImperativeHandle(ref, () => ({
      getData() {
        return localServiceIdList;
      },
    }));

    const hasService = localServiceIdList.length > 0;

    return (
      <div className="moe-flex moe-flex-col moe-h-[100%] moe-font-manrope">
        <Condition if={hasService}>
          <DndProvider manager={dndManager}>
            <div className="moe-flex moe-flex-col moe-gap-y-[16px] moe-flex-1 moe-overflow-y-auto">
              {localServiceIdList.map((id, index) => {
                const item = serviceChargeMap.mustGetItem(id);
                return (
                  <ServiceSortFormRow
                    name={item.name}
                    key={item.id}
                    index={index}
                    onDrop={handleDrop}
                    onMove={handleMove}
                  />
                );
              })}
            </div>
          </DndProvider>
        </Condition>
      </div>
    );
  },
);

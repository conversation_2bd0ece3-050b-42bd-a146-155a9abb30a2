import React, { memo } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { WithPermission } from '../../../../../../components/GuardRoute/WithPermission';
import { Button } from '@moego/ui';
import { useCategoryDrawer } from '../CategoryDrawer';
import { ServiceItemType, type ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';

export interface ServiceListHeaderRightProps {
  serviceType: ServiceType;
  serviceItemType: ServiceItemType;
  singularTitle: string;
  onAdd: () => void;
}

export const ServiceListHeaderRight = memo<ServiceListHeaderRightProps>((props) => {
  const { serviceType, serviceItemType, singularTitle, onAdd } = props;
  const showCategory = serviceItemType !== ServiceItemType.GROUP_CLASS;

  const openCategoryDrawer = useCategoryDrawer();
  const handleOpenCategoryDrawer = () => {
    openCategoryDrawer({ serviceType, serviceItemType });
  };

  return (
    <div className="moe-flex moe-gap-x-[8px]">
      <Condition if={showCategory}>
        <WithPermission permissions={'editCategory'}>
          <Button variant="secondary" onPress={handleOpenCategoryDrawer}>
            Edit category
          </Button>
        </WithPermission>
      </Condition>
      <WithPermission permissions={'addService'}>
        <Button onPress={() => onAdd()}>Add new {singularTitle}</Button>
      </WithPermission>
    </div>
  );
});

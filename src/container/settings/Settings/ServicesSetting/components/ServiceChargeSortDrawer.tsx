import { Drawer } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useRef } from 'react';
import { currentAccountIdBox } from '../../../../../store/account/account.boxes';
import { sortServiceChargeList } from '../../../../../store/service/actions/private/serviceCharge.actions';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { ServiceChargeSortForm, type ServiceChargeSortFormRef } from './ServiceChargeSortForm';
import { useModal } from '../../../../../components/Modal/useModal';

export interface ServiceChargeSortDrawerProps {
  isInactive: boolean;
  onClose: () => void;
}

export const ServiceChargeSortDrawer = memo(function ServiceSortDrawer(props: ServiceChargeSortDrawerProps) {
  const { isInactive, onClose } = props;
  const dispatch = useDispatch();
  const serviceChargeFormRef = useRef<ServiceChargeSortFormRef>(null);
  const [currentAccountId] = useSelector(currentAccountIdBox);
  const handleSave = useSerialCallback(async () => {
    const data = serviceChargeFormRef.current?.getData();
    if (data?.length) {
      await dispatch(sortServiceChargeList(data, currentAccountId));
    }
  });
  return (
    <Drawer
      isOpen
      isBlockScroll
      isDismissable
      showCloseButton
      onClose={onClose}
      onConfirm={handleSave}
      size="m"
      title="Sort service charge"
    >
      <ServiceChargeSortForm isInactive={isInactive} ref={serviceChargeFormRef} />
    </Drawer>
  );
});

export const useServiceChargeSortDrawer = () => useModal(ServiceChargeSortDrawer);

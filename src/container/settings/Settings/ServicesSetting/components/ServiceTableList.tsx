import React, { memo } from 'react';
import { Switch } from '../../../../../components/SwitchCase';
import { type ServiceRecord } from '../../../../../store/service/service.boxes';
import { ServiceTable } from './ServiceTable';

export interface ServiceTableProps {
  className?: string;
  type: number; // services / add ons &
  categoryId: string;
  onEdit: (service: ServiceRecord) => void;
  onDelete: (service: ServiceRecord) => void;
  onDuplicate: (service: ServiceRecord) => void;
  onSort: (service: ServiceRecord[], categoryId: string) => void;
  onAdd: (categoryId: string) => void;
  isAllLocation: boolean;
  selectedBusinessId?: number;
  isSingleLocation: boolean;
  categoryIdList: string[];
  listVisible: boolean;
}

export const ServiceTableList = memo((props: ServiceTableProps) => {
  const {
    type,
    categoryId,
    onDelete,
    onEdit,
    onDuplicate,
    onSort,
    onAdd,
    isAllLocation,
    selectedBusinessId,
    isSingleLocation,
    categoryIdList,
    listVisible,
  } = props;

  return (
    <Switch>
      <Switch.Case if={listVisible && categoryIdList?.length}>
        {categoryIdList.map((id) => (
          <ServiceTable
            type={type}
            categoryId={id}
            onDelete={onDelete}
            onEdit={onEdit}
            onDuplicate={onDuplicate}
            key={id}
            onAdd={onAdd}
            onSort={onSort}
            isAllLocation={isAllLocation}
            selectedBusinessId={selectedBusinessId}
            isSingleLocation={isSingleLocation}
          />
        ))}
      </Switch.Case>
      <Switch.Case else>
        <ServiceTable
          type={type}
          categoryId={categoryId}
          onDelete={onDelete}
          onEdit={onEdit}
          onDuplicate={onDuplicate}
          isInactive={true}
          onAdd={onAdd}
          onSort={onSort}
          isAllLocation={isAllLocation}
          selectedBusinessId={selectedBusinessId}
          isSingleLocation={isSingleLocation}
        />
      </Switch.Case>
    </Switch>
  );
});

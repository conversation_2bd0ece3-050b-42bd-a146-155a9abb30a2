import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Checkbox, Modal, Text } from '@moego/ui';
import React, { memo } from 'react';
import { Condition } from '../../../../../components/Condition';
import { useBool } from '../../../../../utils/hooks/useBool';

export type ActionType = 'update' | 'delete';
type ApplyToUpcomingModalProps = {
  onConfirm: (val: boolean) => void;
  onClose: () => void;
  serviceType: ServiceType;
  serviceItemType: ServiceItemType;
  action?: ActionType;
  title?: string;
  contentTitle?: string;
  confirmText?: string;
  confirmColor?: 'brand' | 'danger';
};
const DefaultLabel = 'Apply to all unconfirmed upcoming appointments as well';
const CheckBoxLabelMap: Partial<Record<ServiceItemType, Partial<Record<ActionType, string>>>> = {
  [ServiceItemType.BOARDING]: {
    update: DefaultLabel,
  },
  [ServiceItemType.DAYCARE]: {
    update: DefaultLabel,
  },
  [ServiceItemType.GROOMING]: {
    update: DefaultLabel,
  },
  [ServiceItemType.UNSPECIFIED]: {
    update: 'Apply to all unconfirmed upcoming appointments',
    delete: 'Also remove this charge from all unconfirmed upcoming appointments',
  },
};

const DefaultDesc = 'All unconfirmed upcoming appointments, including appointments with saved price or duration.';
const CheckBoxDescMap: Partial<Record<ServiceItemType, string>> = {
  [ServiceItemType.BOARDING]: DefaultDesc,
  [ServiceItemType.DAYCARE]: DefaultDesc,
  [ServiceItemType.GROOMING]: DefaultDesc,
  [ServiceItemType.UNSPECIFIED]:
    'All existing appointments with a start time after the current date, with a status of unconfirmed and not fully paid.',
};

export const ApplyToUpcomingModal = memo((props: ApplyToUpcomingModalProps) => {
  const {
    onConfirm,
    onClose,
    serviceType,
    serviceItemType,
    title,
    contentTitle,
    action = 'update',
    confirmColor = 'brand',
    confirmText = 'Confirm',
  } = props;
  const isBoarding = serviceType === ServiceType.SERVICE && serviceItemType === ServiceItemType.BOARDING;
  const isServiceCharge = serviceType === ServiceType.UNSPECIFIED && serviceItemType === ServiceItemType.UNSPECIFIED;
  const isApply = useBool();

  return (
    <Modal
      isOpen
      size="s"
      title={title || 'Confirm before saving'}
      onConfirm={() => onConfirm(isApply.value)}
      onClose={onClose}
      confirmText={confirmText}
      confirmButtonProps={{
        color: confirmColor,
      }}
    >
      {contentTitle && (
        <Text variant="regular" className="moe-mb-8px-150">
          {contentTitle}
        </Text>
      )}
      <Condition if={!isServiceCharge}>
        <Text variant="regular" className="moe-mb-8px-300">
          You are making <strong>price{!isBoarding ? ' & duration' : ''}</strong> changes to an existing service.
        </Text>
      </Condition>
      <Checkbox onChange={isApply.as}>{CheckBoxLabelMap[serviceItemType]?.[action] || DefaultLabel}</Checkbox>
      <Text variant="caption" className="moe-mt-8px-50 moe-text-tertiary moe-mx-[28px]">
        {CheckBoxDescMap[serviceItemType] || DefaultDesc}
      </Text>
    </Modal>
  );
});

import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { MajorMoreOutlined, MajorPlusOutlined } from '@moego/icons-react';
import { Button, Dropdown, Heading, IconButton, Text } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo, useRef } from 'react';
import { Condition } from '../../../../../components/Condition';
import { WithPermission } from '../../../../../components/GuardRoute/WithPermission';
import { toastApi } from '../../../../../components/Toast/Toast';
import { currentAccountIdBox } from '../../../../../store/account/account.boxes';
import { selectCurrentPermissions } from '../../../../../store/business/role.selectors';
import { selectPetSizeList } from '../../../../../store/onlineBooking/settings/petSize.selectors';
import { ServiceType, serviceCategoryMapBox } from '../../../../../store/service/category.boxes';
import { type ServiceRecord, companyServiceMapBox } from '../../../../../store/service/service.boxes';
import { selectCategoryServices, selectInactiveCategoryServices } from '../../../../../store/service/service.selectors';
import { isNormal } from '../../../../../store/utils/identifier';
import { useVisibleEffect } from '../../../../../utils/hooks/hooks';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { EXPAND_ROW_INDENT } from '../../consts';
import { useServiceSettingContext } from '../ServicesSettingContext';
import { useGetExpandedRowDataList } from '../hooks/useGetExpandedRowDataList';
import { useGetServicesColumns } from '../hooks/useGetServicesColumn';
import { getExpandedRowColWidth } from '../utils/getExpandedRowColWidth';
import { isValidLocationStaffOverride, isValidServiceStaffOverride } from '../utils/serviceOverrideLocationHandler';
import { BasedTable } from './BasedTable';

export interface ServiceTableProps {
  className?: string;
  type?: number; // services / add ons &
  categoryId: string;
  onEdit: (service: ServiceRecord) => void;
  onDelete: (service: ServiceRecord) => void;
  onDuplicate: (service: ServiceRecord) => void;
  onSort?: (service: ServiceRecord[], categoryId: string) => void;
  onAdd: (categoryId: string) => void;
  isInactive?: boolean;
  isAllLocation: boolean;
  selectedBusinessId?: number;
  isSingleLocation: boolean;
}

export const ServiceTable = memo(function ServiceTable(props: ServiceTableProps) {
  const {
    className,
    categoryId,
    onDelete,
    onEdit,
    isInactive,
    onDuplicate,
    onSort,
    onAdd,
    isAllLocation,
    selectedBusinessId,
    isSingleLocation,
  } = props;
  const { serviceItemType, serviceType } = useServiceSettingContext();
  const ref = useRef<HTMLDivElement>(null);
  const basedTableRef = useRef<HTMLDivElement>(null);
  const [currentAccountId] = useSelector(currentAccountIdBox);
  const [category, services, serviceMap, petSizeList, permissions] = useSelector(
    serviceCategoryMapBox.mustGetItem(Number(categoryId)),
    !isInactive
      ? selectCategoryServices(`${serviceType}-${serviceItemType}`, Number(categoryId), currentAccountId)
      : selectInactiveCategoryServices(`${serviceType}-${serviceItemType}`, currentAccountId),
    companyServiceMapBox,
    selectPetSizeList(),
    selectCurrentPermissions(),
  );
  const isDaycareService = serviceType === ServiceType.Service && serviceItemType === ServiceItemType.DAYCARE;
  const isBoardingService = serviceType === ServiceType.Service && serviceItemType === ServiceItemType.BOARDING;
  const isGroupClassService = serviceType === ServiceType.Service && serviceItemType === ServiceItemType.GROUP_CLASS;

  const typeText = ServiceType.mapLabels[serviceType];

  const canSortService = isSingleLocation || isAllLocation;

  const data = useMemo(
    () =>
      services
        // filter out the services that are not normal
        // see https://moegoworkspace.slack.com/archives/C01TT9K995M/p1734601833186259
        .filter((id) => isNormal(id))
        .map((id) => {
          const service = serviceMap.mustGetItem(id);
          return service;
        })
        .toArray(),
    [services, serviceMap],
  );

  const columns = useGetServicesColumns({
    onDelete,
    onEdit,
    onDuplicate,
    selectedBusinessId,
    serviceItemType,
    serviceType,
    petSizeList,
  });

  const getExpandedRowDataList = useGetExpandedRowDataList({
    isShowByStaff: isNormal(selectedBusinessId) || isSingleLocation,
    selectedBusinessId,
  });

  const title = isInactive ? '' : category.name || 'Uncategorized';

  const handleRenderExpandedRow = useLatestCallback((record: ServiceRecord): JSX.Element => {
    const expandedRowDataList = getExpandedRowDataList(record);
    const {
      name: serviceWidth,
      price: priceWidth,
      taxId: taxWidth,
    } = getExpandedRowColWidth(columns, basedTableRef.current?.scrollWidth ?? 0);

    return (
      <div className="moe-bg-neutral-sunken-0 moe-rounded-[16px]">
        {expandedRowDataList.map((item) => {
          const { rowId, serviceCol, priceCol, taxCol, durationCol } = item;

          return (
            <div className="moe-w-full" key={rowId}>
              <div className="moe-flex moe-py-spacing-xs moe-text-primary">
                <Text
                  variant="small"
                  className="moe-flex-shrink-0 moe-truncate moe-pl-[20px] moe-pr-8px-100"
                  style={{ width: serviceWidth - EXPAND_ROW_INDENT }}
                >
                  {serviceCol}
                </Text>
                <Text
                  variant="small"
                  ellipsis={{
                    tooltip: {
                      content: priceCol,
                      side: 'top',
                    },
                  }}
                  className="moe-flex-shrink-0 moe-px-8px-200"
                  style={{ width: priceWidth }}
                >
                  {priceCol}
                </Text>
                <Text
                  as="span"
                  variant="small"
                  className="moe-flex-shrink-0 moe-px-8px-200"
                  style={{ width: taxWidth }}
                >
                  {taxCol}
                </Text>
                <Condition if={!isBoardingService && !isDaycareService}>
                  <Text
                    variant="small"
                    ellipsis={{
                      tooltip: {
                        content: priceCol,
                        side: 'top',
                      },
                    }}
                    className="moe-flex-shrink-0 moe-pl-8px-200"
                  >
                    {durationCol}
                  </Text>
                </Condition>
              </div>
            </div>
          );
        })}
      </div>
    );
  });

  const handleExpandable = useLatestCallback((record: ServiceRecord) => {
    // valid location or staff override list
    if (isAllLocation) {
      return record.locationStaffOverrideList.some((item) => isValidLocationStaffOverride(item));
    }

    const selectedLocationStaffOverride = record.locationStaffOverrideList.find(
      (item) => item.locationOverride.businessId === selectedBusinessId?.toString(),
    );
    return selectedLocationStaffOverride?.staffOverrideList.some((item) => isValidServiceStaffOverride(item)) ?? false;
  });

  const handleTableAction = useLatestCallback((key: string | number) => {
    if (key === 'sort') {
      if (!canSortService) {
        toastApi.neutral('Please change to all businesses to sort services');
        return;
      }
      onSort?.(data, categoryId);
    } else if (key === 'add') {
      onAdd(categoryId);
    }
  });

  const handleGetRowKey = useLatestCallback((record: ServiceRecord) => {
    return `${record.serviceId}`;
  });

  const handleRenderNoDataArea = useLatestCallback(() => {
    return (
      <div className="moe-flex moe-flex-col moe-items-center moe-gap-y-[4px]">
        <div className="moe-text-sm-20 moe-text-secondary moe-font-normal">
          No {typeText.toLowerCase()} has been set up yet
        </div>
        <WithPermission permissions={'addService'}>
          <Button
            variant="tertiary-legacy"
            icon={<MajorPlusOutlined />}
            onPress={() => onAdd(categoryId)}
            align="start"
          >
            Add {typeText.toLowerCase()}
          </Button>
        </WithPermission>
      </div>
    );
  });

  const visible = useVisibleEffect(ref, [categoryId, services.size]);

  if (!isNormal(categoryId) && services.size < 1) {
    return null;
  }

  return (
    <div className="moe-full">
      {/* group class will not show category */}
      <Condition if={!isGroupClassService}>
        <div className="moe-flex moe-justify-between moe-items-center moe-mb-[16px] moe-pr-[16px]">
          <Heading size="4">{title}</Heading>
          <Dropdown>
            <Dropdown.Trigger>
              <IconButton variant="secondary" icon={<MajorMoreOutlined />} size="l" />
            </Dropdown.Trigger>
            {permissions.has('addService') ? (
              <Dropdown.Menu onAction={handleTableAction}>
                <Dropdown.MenuItem title={`Sort ${typeText.toLowerCase()}`} key="sort" />
                <Dropdown.MenuItem title={`Add ${typeText.toLowerCase()}`} key="add" />
              </Dropdown.Menu>
            ) : (
              <Dropdown.Menu onAction={handleTableAction}>
                <Dropdown.MenuItem title={`Sort ${typeText.toLowerCase()}`} key="sort" />
              </Dropdown.Menu>
            )}
          </Dropdown>
        </div>
      </Condition>
      <div ref={ref}>
        {visible ? (
          <BasedTable
            ref={basedTableRef}
            className={className}
            data={data}
            columns={columns}
            expandable={handleExpandable}
            expandedRowRender={handleRenderExpandedRow}
            onRowClick={onEdit}
            rowKey={handleGetRowKey}
            noDataArea={handleRenderNoDataArea}
          />
        ) : null}
      </div>
    </div>
  );
});

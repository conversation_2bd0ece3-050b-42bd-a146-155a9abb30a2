import { Form, Input, LegacySelect as Select } from '@moego/ui';
import React, { memo } from 'react';
import { type BusinessRecord } from '../../../../../store/business/business.boxes';

export interface PriceFormItemProps {
  business: BusinessRecord;
  isDisabledForTax: boolean | undefined;
  taxOptionList: {
    label: string;
    value: number;
  }[];
  priceLabel?: string;
  pricePlaceholder?: string;
}

export const PriceFormItem = memo<PriceFormItemProps>((props) => {
  const {
    business,
    isDisabledForTax,
    taxOptionList,
    priceLabel = 'Price',
    pricePlaceholder = `Add the ${priceLabel}`,
  } = props;

  return (
    <div className="moe-flex moe-justify-between moe-gap-x-[24px]">
      <div className="moe-w-full">
        <Form.Item
          name="price"
          label={priceLabel}
          rules={{
            required: `${priceLabel} is required`,
          }}
        >
          <Input.Number
            classNames={{
              inputBox: 'moe-w-full',
            }}
            isRequired
            minValue={0}
            precision={2}
            maxLength={10}
            isDisabled={isDisabledForTax}
            placeholder={pricePlaceholder}
            prefix={<span className="moe-leading-[20px]">{business.printCurrency()}</span>}
          />
        </Form.Item>
      </div>
      <div className="moe-w-full">
        <Form.Item
          name="taxId"
          label="Tax"
          rules={{
            required: 'Tax is required',
            validate: (value) => {
              if (taxOptionList.some((item) => item.value === value)) {
                return true;
              }
              return 'Tax is required';
            },
          }}
        >
          <Select
            isDisabled={isDisabledForTax}
            options={taxOptionList}
            isRequired
            formatOptionLabel={(opt: { label: string; value: number }) => opt?.label || ''}
          />
        </Form.Item>
      </div>
    </div>
  );
});

PriceFormItem.displayName = 'PriceFormItem';

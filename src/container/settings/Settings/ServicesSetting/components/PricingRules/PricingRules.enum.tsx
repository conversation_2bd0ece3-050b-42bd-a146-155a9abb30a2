import { RuleType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { SurchargeType } from '@moego/api-web/moego/models/order/v1/service_charge_enums';
import { createEnum } from '../../../../../../store/utils/createEnum';

export enum PricingRuleTypes {
  MultiplePets = 'MultiplePets',
  MultipleNights = 'MultipleNights',
  PeakDates = 'PeakDates',
  LatePickUpAndEarlyDropOff = 'LatePickUpAndEarlyDropOff',
  CustomFees = 'CustomFees',
  OverTimeFees = 'OverTimeFees',
  FeedingCharge = 'FeedingCharge',
  MedicationCharge = 'MedicationCharge',
}

export const PricingRuleNavInfoEnum = createEnum({
  PeakDates: [
    PricingRuleTypes.PeakDates,
    {
      title: 'Peak dates',
      description: 'Apply an additional fee for bookings on high-demand dates, such as holidays',
      ruleType: RuleType.PEAK_DATE,
    },
  ],
  MultiplePets: [
    PricingRuleTypes.MultiplePets,
    {
      title: 'Multiple pets',
      description: 'Apply a price reduction when booking services for multiple pets from the same household',
      ruleType: RuleType.MULTIPLE_PET,
    },
  ],
  MultipleNights: [
    PricingRuleTypes.MultipleNights,
    {
      title: 'Multiple nights/days',
      description: 'Apply a price reduction for pet(s) staying over a certain number of consecutive nights/days.',
      ruleType: RuleType.MULTIPLE_STAY,
    },
  ],
  CustomFees: [
    PricingRuleTypes.CustomFees,
    {
      title: 'Custom fees',
      description: 'Apply extra charges automatically or manually based on specific needs',
      ruleType: RuleType.UNSPECIFIED,
    },
  ],
  LatePickUpAndEarlyDropOff: [
    PricingRuleTypes.LatePickUpAndEarlyDropOff,
    {
      title: 'Late pick-up/Early drop-off',
      description: 'Apply extra charges for pickups or drop-offs outside standard operating hours',
      ruleType: RuleType.UNSPECIFIED,
    },
  ],
  OverTimeFees: [
    PricingRuleTypes.OverTimeFees,
    {
      title: 'Over time fees',
      description: 'Extra charges apply for pickups or drop-offs outside standard operating hours.',
      ruleType: RuleType.UNSPECIFIED,
    },
  ],
  FeedingCharge: [
    PricingRuleTypes.FeedingCharge,
    {
      title: 'Feeding charge',
      description: 'Apply a price reduction when booking services for multiple pets from the same household',
      ruleType: RuleType.UNSPECIFIED,
    },
  ],
  MedicationCharge: [
    PricingRuleTypes.MedicationCharge,
    {
      title: 'Medication charge',
      description: '',
      ruleType: RuleType.UNSPECIFIED,
    },
  ],
});

export const PricingRuleConfig = createEnum({
  PEAK_DATE: [
    RuleType.PEAK_DATE,
    {
      title: 'peak dates rule',
      basicInfoName: 'Peak dates name',
      nav: PricingRuleTypes.PeakDates,
      rulePrefixLabel: '',
      unsaveLabel: 'Peak dates rule has unsaved changes',
      tooltipText: '',
    },
  ],
  MULTIPLE_PET: [
    RuleType.MULTIPLE_PET,
    {
      title: 'multiple pets rule',
      basicInfoName: 'Rule name',
      nav: PricingRuleTypes.MultiplePets,
      rulePrefixLabel: '# of pets',
      unsaveLabel: 'Multiple pets rule has unsaved changes',
      tooltipText: 'pets',
    },
  ],
  MULTIPLE_STAY: [
    RuleType.MULTIPLE_STAY,
    {
      title: 'multiple nights/days rule',
      basicInfoName: 'Rule name',
      nav: PricingRuleTypes.MultipleNights,
      rulePrefixLabel: '# of nights',
      unsaveLabel: 'Multiple nights/days rule has unsaved changes',
      tooltipText: 'nights',
    },
  ],
});

export const ServiceChargeConfig = createEnum({
  FEEDING_CHARGE: [
    SurchargeType.FEEDING_FEE,
    {
      title: 'Feeding charge rule',
      nav: PricingRuleTypes.FeedingCharge,
      onboarding: {
        key: undefined,
        content: '',
        onboardingTitle: '',
      },
    },
  ],
  MEDICATION_CHARGE: [
    SurchargeType.MEDICATION_FEE,
    {
      title: 'Medication charge rule',
      nav: undefined,
      onboarding: {
        key: undefined,
        content: '',
        onboardingTitle: '',
      },
    },
  ],
  OVER_TIME_FEES: [
    SurchargeType.CHARGE_24_HOUR,
    {
      title: 'Exceed 24-hour period rule',
      nav: undefined,
      onboarding: {
        key: 'hideOverTimeFeesTips',
        onboardingTitle: 'What is Exceed 24-hour period rule?',
        content:
          'In addition to charging by day or night, some businesses use a 24-hour billing cycle. If a pet stays beyond the calculated 24-hour periods due to early drop-off or late pick-up, additional fees will apply. This feature automatically adds the extra charge when this occurs.',
      },
    },
  ],
});

export const PricingDiscountSettingRadioGroup = createEnum({
  ApplyBestOnly: [0, 'Only apply the rule with the best discount'],
  ApplySequence: [1, 'Apply all rules in sequence'],
});

export const PricingDiscountSettingSequenceList = createEnum({
  MultiplePets: [RuleType.MULTIPLE_PET, 'Multiple Pets'],
  MultipleNights: [RuleType.MULTIPLE_STAY, 'Multiple Nights'],
});

export enum PricingRuleModalAction {
  DeactivateEditSave = 0,
  NewSave = 1,
  Delete = 2,
  Active = 3,
  Inactive = 4,
}

const PricingRuleActionDescription =
  'All existing appointments with a start time after the current date, with a status of unconfirmed and not fully paid.';

export const PricingRuleActionEnum = createEnum({
  DeactivateEditSave: [
    PricingRuleModalAction.DeactivateEditSave,
    {
      title: 'Confirm before saving',
      contentTitle: '',
      content: 'Remove the pricing rule from all upcoming appointments',
      description: PricingRuleActionDescription,
    },
  ],
  NewSave: [
    PricingRuleModalAction.NewSave,
    {
      title: 'Confirm before saving',
      contentTitle: '',
      content: 'Apply to all upcoming appointments',
      description: PricingRuleActionDescription,
    },
  ],
  Delete: [
    PricingRuleModalAction.Delete,
    {
      title: 'Delete pricing rule',
      contentTitle: 'Are you sure you want to delete this pricing rule?',
      content: 'Also remove this rule from all unconfirmed upcoming appointments',
      description: PricingRuleActionDescription,
      confirmText: 'Delete',
      confirmButtonProps: { color: 'danger' },
    },
  ],
  Active: [
    PricingRuleModalAction.Active,
    {
      title: 'Confirm before saving',
      contentTitle: '',
      content: 'Remove the pricing rule from all upcoming appointments',
      description: PricingRuleActionDescription,
    },
  ],
  Inactive: [
    PricingRuleModalAction.Inactive,
    {
      title: 'Confirm before saving',
      contentTitle: '',
      content: 'Apply to all upcoming appointments',
      description: PricingRuleActionDescription,
    },
  ],
} as const);

export enum PricingRuleStatus {
  Active = 'active',
  Inactive = 'inactive',
}

export const PricingRuleStatusEnum = createEnum({
  Active: [PricingRuleStatus.Active, { value: PricingRuleStatus.Active, label: 'Active' }],
  Inactive: [PricingRuleStatus.Inactive, { value: PricingRuleStatus.Inactive, label: 'Inactive' }],
});

export const PricingRuleServiceFormSections = createEnum({
  BasicInfo: ['basicInfo', { title: 'Basic info' }],
  RuleConfiguration: ['ruleConfiguration', { title: 'Rule configuration' }],
} as const);

import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type SurchargeType } from '@moego/api-web/moego/models/order/v1/service_charge_enums';
import { useLatestCallback } from '@moego/finance-utils';
import { <PERSON><PERSON><PERSON><PERSON>og, Button } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, type ReactNode, useEffect, useMemo, useState } from 'react';
import { useHistory } from 'react-router';
import { WithPermission } from '../../../../../../../components/GuardRoute/WithPermission';
import { toastApi } from '../../../../../../../components/Toast/Toast';
import {
  type AddOrEditSurchargeQuery,
  PATH_ADD_OR_EDIT_SURCHARGES,
  PATH_SERVICE_SETTING,
} from '../../../../../../../router/paths';
import { currentAccountIdBox } from '../../../../../../../store/account/account.boxes';
import { selectPricingPermission } from '../../../../../../../store/company/company.selectors';
import {
  getCompanyServiceChargeList,
  removeServiceCharge,
} from '../../../../../../../store/service/actions/private/serviceCharge.actions';
import { type ServiceChargeRecord } from '../../../../../../../store/service/service.boxes';
import { useRouteState } from '../../../../../../../utils/RoutePath';
import { useCancelableCallback } from '../../../../../../../utils/hooks/useCancelableCallback';
import { ToggleValue } from '../../../../components/Selector/ToggleSelector';
import { SimpleUpgradeBanner } from '../../../../components/SimpleUpgradeBanner';
import { ServicesSettingBox } from '../../../ServicesSettingBox';
import { useServiceSettingContext } from '../../../ServicesSettingContext';
import { useApplyUpcomingModal } from '../../../hooks/useApplyUpcomingModal';
import { SurchargesPricingRuleTable, type SurchargesPricingRuleTableProps } from './SurchargesPricingRuleTable';

export interface SurchargePricingRuleProps {
  surchargeType: SurchargeType;
  showCareTypeSelector?: boolean;
  showLongDescriptionCareType?: boolean;
  renderRuleConfigurationColumn?: (data: ServiceChargeRecord, selectedBusinessId?: number) => ReactNode;
  renderTableBlock?: (props: Omit<SurchargesPricingRuleTableProps, 'renderRuleConfigurationColumn'>) => ReactNode;
}

export const SurchargesPricingRule = memo<SurchargePricingRuleProps>(
  ({
    surchargeType,
    showCareTypeSelector = false,
    showLongDescriptionCareType = false,
    renderRuleConfigurationColumn,
    renderTableBlock,
  }) => {
    const history = useHistory();
    const dispatch = useDispatch();
    const { isInactive: statedInactive } = useRouteState(PATH_SERVICE_SETTING) || {};
    const { navType } = useServiceSettingContext();
    const [activeType, setActiveType] = useState<ToggleValue>(
      statedInactive ? ToggleValue.Inactive : ToggleValue.Active,
    );
    const isActive = activeType === ToggleValue.Active;
    const [currentAccountId] = useSelector(currentAccountIdBox);
    const checkApplyUpcoming = useApplyUpcomingModal(ServiceType.UNSPECIFIED, ServiceItemType.UNSPECIFIED);

    const [pricingPermissions] = useSelector(selectPricingPermission());

    const hasServiceChargePermission = pricingPermissions.enable.has('serviceCharge');

    const getData = useCancelableCallback(async (token) => {
      await dispatch(
        getCompanyServiceChargeList(
          {
            surchargeType,
            isActive,
            businessIds: [],
          },
          token,
        ),
      );
    });

    const loading = getData.isBusy();

    useEffect(() => {
      getData();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [navType, activeType]);

    const handleOpenDeleteModal = useLatestCallback(async (service: ServiceChargeRecord) => {
      let result;
      // 删除service charge：status为active时，才出弹窗
      if (service.isActive) {
        result = await checkApplyUpcoming({
          action: 'delete',
          title: 'Delete service charge',
          contentTitle: 'Are you sure you want to delete this service charge?',
          confirmColor: 'danger',
          confirmText: 'Delete',
        });
        if (result === undefined) {
          return;
        }
      } else {
        AlertDialog.open({
          variant: 'danger',
          size: 's',
          title: `Delete pricing rule`,
          content: ` Are you sure to delete this pricing rule? This action cannot be reversed.`,
          confirmText: 'Delete',
          onConfirm: () => handleDelete(service),
        });
        return;
      }

      handleDelete(service, result);
    });

    const handleDelete = useLatestCallback(async (serviceCharge: ServiceChargeRecord, applyUpcomingAppt?: boolean) => {
      await dispatch(removeServiceCharge(serviceCharge.id, currentAccountId, applyUpcomingAppt));

      toastApi.success(`Delete pricing rule successfully`);
    });

    const goEditOrAdd = (params: AddOrEditSurchargeQuery) => {
      history.push(PATH_ADD_OR_EDIT_SURCHARGES.fully({ surchargeType }, { isActive }, { ...params }));
    };

    const handleAdd = useLatestCallback(() => {
      goEditOrAdd({});
    });

    const handleEditCustomFees = useLatestCallback((service: ServiceChargeRecord) => {
      goEditOrAdd({ id: service.id });
    });

    const handleDuplicate = useLatestCallback((service: ServiceChargeRecord) => {
      goEditOrAdd({ id: service.id, isDuplicate: '1' });
    });

    const titleBottomBlock = (
      <SimpleUpgradeBanner
        permission="serviceCharge"
        /* @text-lint ignore */
        title="Upgrade to manage additional charges with simplicity."
        className="moe-mb-[32px]"
      />
    );

    const tableBlock = useMemo(() => {
      if (typeof renderTableBlock === 'function') {
        return renderTableBlock({
          isInactive: !isActive,
          onDelete: handleOpenDeleteModal,
          onEdit: handleEditCustomFees,
          onDuplicate: handleDuplicate,
          onAdd: handleAdd,
          selectedBusinessId: undefined,
        });
      }

      return (
        <SurchargesPricingRuleTable
          isInactive={!isActive}
          onAdd={handleAdd}
          onEdit={handleEditCustomFees}
          onDelete={handleOpenDeleteModal}
          onDuplicate={handleDuplicate}
          showLongDescriptionCareType={showLongDescriptionCareType}
          renderRuleConfigurationColumn={renderRuleConfigurationColumn}
        />
      );
    }, [
      handleAdd,
      handleDuplicate,
      handleEditCustomFees,
      handleOpenDeleteModal,
      isActive,
      showLongDescriptionCareType,
      renderTableBlock,
      renderRuleConfigurationColumn,
    ]);

    const headerRightBlock = (
      <WithPermission permissions={'addService'}>
        <Button onPress={() => handleAdd()} isDisabled={!hasServiceChargePermission}>
          Add new rule
        </Button>
      </WithPermission>
    );

    if (!navType) {
      return null;
    }

    return (
      <ServicesSettingBox
        showBackButton={true}
        titleBottomBlock={titleBottomBlock}
        tableBlock={tableBlock}
        headerRightBlock={headerRightBlock}
        loading={loading}
        showBusinessSelector={false}
        showCareTypeSelector={showCareTypeSelector}
        activeType={activeType}
        onSelectActiveType={setActiveType}
      />
    );
  },
);

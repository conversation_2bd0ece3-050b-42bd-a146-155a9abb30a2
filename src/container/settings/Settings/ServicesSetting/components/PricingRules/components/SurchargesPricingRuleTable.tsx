import { useLatestCallback } from '@moego/finance-utils';
import { Empty } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, type ReactNode, useRef } from 'react';
import { currentAccountIdBox } from '../../../../../../../store/account/account.boxes';
import { selectPricingPermission } from '../../../../../../../store/company/company.selectors';
import { type ServiceChargeRecord } from '../../../../../../../store/service/service.boxes';
import { selectServiceChargeList } from '../../../../../../../store/service/service.selectors';
import { BasedTable } from '../../BasedTable';
import { useSurchargesPricingRuleColumns } from '../hooks/useSurchargesPricingRuleColumns';

export interface SurchargesPricingRuleTableProps {
  className?: string;
  onEdit: (service: ServiceChargeRecord) => void;
  onDelete: (service: ServiceChargeRecord) => void;
  onDuplicate: (service: ServiceChargeRecord) => void;
  onAdd: () => void;
  isInactive: boolean;
  showLongDescriptionCareType?: boolean;
  selectedBusinessId?: number;
  renderRuleConfigurationColumn?: (data: ServiceChargeRecord, selectedBusinessId?: number) => ReactNode;
}

export const SurchargesPricingRuleTable = memo((props: SurchargesPricingRuleTableProps) => {
  const {
    className,
    onDelete,
    onEdit,
    onDuplicate,
    isInactive,
    selectedBusinessId,
    renderRuleConfigurationColumn,
    showLongDescriptionCareType,
  } = props;
  const [currentAccountId] = useSelector(currentAccountIdBox);
  const [serviceCharges, pricingPermission] = useSelector(
    selectServiceChargeList(!isInactive, currentAccountId),
    selectPricingPermission(),
  );

  const basedTableRef = useRef<HTMLDivElement>(null);

  const haveServiceChargePermission = pricingPermission.enable.has('serviceCharge');

  const columns = useSurchargesPricingRuleColumns({
    onDelete,
    onEdit,
    onDuplicate,
    selectedBusinessId,
    showLongDescriptionCareType,
    renderRuleConfigurationColumn,
  });

  const data = serviceCharges.toArray();

  const handleGetRowKey = useLatestCallback((record: ServiceChargeRecord) => {
    return `${record.id}`;
  });

  const handleRowClick = useLatestCallback((record: ServiceChargeRecord) => {
    if (haveServiceChargePermission) {
      onEdit(record);
    }
  });

  const handleRenderNoDataArea = useLatestCallback(() => {
    return <Empty />;
  });

  return (
    <div className="moe-full">
      <BasedTable
        showHeaderWhenEmpty
        ref={basedTableRef}
        className={className}
        data={data}
        columns={columns}
        onRowClick={handleRowClick}
        rowKey={handleGetRowKey}
        noDataArea={handleRenderNoDataArea}
      />
    </div>
  );
});

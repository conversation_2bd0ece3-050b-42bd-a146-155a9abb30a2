import { type SurchargeType } from '@moego/api-web/moego/models/order/v1/service_charge_enums';
import { Spin } from '@moego/ui';
import { useAsyncEffect } from 'ahooks';
import { useDispatch } from 'amos';
import React, { type FC } from 'react';
import { useHistory } from 'react-router';
import { PATH_ADD_OR_EDIT_SURCHARGES } from '../../../../../../../router/paths';
import { getCompanyServiceChargeList } from '../../../../../../../store/service/actions/private/serviceCharge.actions';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';

interface RedirectToAddOrEditProps {
  surchargeType: SurchargeType;
}

/**
 * 有些类型的 charge 没有列表页面只存在 active 或者 inactive 两种状态
 * 如果是这种 charge 就直接重定向到对应的 add 或者 edit 页面即可
 *
 * @param surchargeType
 * @returns
 */
export const RedirectToAddOrEdit: FC<RedirectToAddOrEditProps> = ({ surchargeType }) => {
  const history = useHistory();
  const dispatch = useDispatch();

  const loadData = useLatestCallback(async () => {
    const result = await dispatch(getCompanyServiceChargeList({ businessIds: [], surchargeType }));
    return result[0];
  });

  useAsyncEffect(async () => {
    const result = await loadData();
    history.replace(
      PATH_ADD_OR_EDIT_SURCHARGES.fully({ surchargeType }, { isActive: !!result?.isActive }, { id: result?.id }),
    );
  }, []);

  return (
    <div className="moe-w-full moe-min-h-[400px] moe-flex moe-items-center moe-justify-center">
      <Spin isLoading={true} />
    </div>
  );
};

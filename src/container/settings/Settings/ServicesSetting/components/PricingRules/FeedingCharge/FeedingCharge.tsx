import { SurchargeType } from '@moego/api-web/moego/models/order/v1/service_charge_enums';
import React, { useCallback } from 'react';
import { SurchargesPricingRule } from '../components/SurchargesPricingRule';
import { RuleConfigurationColumn } from './RuleConfigurationColumn';
import { type ServiceChargeRecord } from '../../../../../../../store/service/service.boxes';

export const FeedingCharge = () => {
  return (
    <SurchargesPricingRule
      surchargeType={SurchargeType.FEEDING_FEE}
      renderRuleConfigurationColumn={useCallback(
        (data: ServiceChargeRecord, selectedBusinessId?: number) => (
          <RuleConfigurationColumn data={data} selectedBusinessId={selectedBusinessId} />
        ),
        [],
      )}
    />
  );
};

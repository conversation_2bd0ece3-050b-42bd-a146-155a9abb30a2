import React, { memo } from 'react';
import { useRouteParams } from '../../../../../../../utils/RoutePath';
import { PATH_ADD_OR_EDIT_SURCHARGES } from '../../../../../../../router/paths';
import { Switch } from '../../../../../../../components/SwitchCase';
import { SurchargeType } from '@moego/api-web/moego/models/order/v1/service_charge_enums';
import { isNormal } from '../../../../../../../store/utils/identifier';
import { AddOrEditCustomFees } from '../CustomFees/AddOrEditCustomFees';
import { AddOrEditLatePickUpAndEarlyDropOff } from '../LatePickUpAndEarlyDropOff/AddOrEditLatePickUpAndEarlyDropOff';
import { AddOrEditCommonServiceCharge } from '../AddOrEditCommonServiceCharge/AddOrEditCommonServiceCharge';
import { useBizIdReadyEffect } from '../../../../../../../utils/hooks/useBizIdReadyEffect';
import { useDispatch } from 'amos';
import { getTaxList } from '../../../../../../../store/business/tax.actions';
import { getCompanyServiceChargeList } from '../../../../../../../store/service/actions/private/serviceCharge.actions';

const CommonSurchargeTypes = [SurchargeType.FEEDING_FEE, SurchargeType.MEDICATION_FEE, SurchargeType.CHARGE_24_HOUR];

export const AddOrEditSurcharges = memo(() => {
  const { surchargeType: surchargeTypeStr } = useRouteParams(PATH_ADD_OR_EDIT_SURCHARGES);
  const dispatch = useDispatch();

  const surchargeType = isNormal(surchargeTypeStr) ? (+surchargeTypeStr as SurchargeType) : SurchargeType.UNSPECIFIED;

  useBizIdReadyEffect(() => {
    dispatch([
      getTaxList(),
      getCompanyServiceChargeList({
        businessIds: [],
        surchargeType,
      }),
    ]);
  }, []);

  return (
    <Switch shortCircuit>
      <Switch.Case if={surchargeType === SurchargeType.OFF_HOURS_FEE}>
        <AddOrEditLatePickUpAndEarlyDropOff />
      </Switch.Case>
      <Switch.Case if={CommonSurchargeTypes.includes(surchargeType)}>
        <AddOrEditCommonServiceCharge />
      </Switch.Case>
      <Switch.Case if={surchargeType === SurchargeType.CUSTOM_FEE}>
        <AddOrEditCustomFees />
      </Switch.Case>
    </Switch>
  );
});

import { Form, Input } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import { TaxSelector } from '../../../../AddOrEditServiceForm/PriceDuration/components/TaxSelector';
import { selectCurrentBusiness } from '../../../../../../../../../store/business/business.selectors';
import { useCommonServiceChargeContext } from '../../CommonServiceChargeContext';
import { useServiceStaffPermissions } from '../../../../AddOrEditComponents/hooks/useServiceStaffPermissions';

export const FixedRate = () => {
  const { isEdit } = useCommonServiceChargeContext();
  const [business] = useSelector(selectCurrentBusiness);
  const { isDisabledForPriceAndTax } = useServiceStaffPermissions(isEdit);

  return (
    <div className="moe-flex moe-flex-row moe-gap-x-8px-300">
      <Form.Item name="name" label="Fee name" rules={{ required: true }}>
        <Input isRequired className="moe-w-full" placeholder="Fee name" />
      </Form.Item>
      <Form.Item name="price" label="Enter price" disabled={isDisabledForPriceAndTax} rules={{ required: true }}>
        <Input.Number
          isRequired
          minValue={0}
          precision={2}
          maxLength={10}
          className="moe-w-full"
          placeholder="Enter price"
          isDisabled={isDisabledForPriceAndTax}
          prefix={business.currencySymbol}
        />
      </Form.Item>
      <Form.Item name="taxId" label="Tax" rules={{ required: true }}>
        <TaxSelector isRequired className="moe-w-full" isDisabled={isDisabledForPriceAndTax} />
      </Form.Item>
    </div>
  );
};

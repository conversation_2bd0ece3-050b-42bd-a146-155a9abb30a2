import { booleanToStringify, Form, Radio, RadioGroup } from '@moego/ui';
import React, { memo } from 'react';
import { useCommonServiceChargeContext } from '../CommonServiceChargeContext';
import { useServiceStaffPermissions } from '../../../AddOrEditComponents/hooks/useServiceStaffPermissions';

export const Status = memo(() => {
  const { isEdit } = useCommonServiceChargeContext();
  const { isDisabledForRemains } = useServiceStaffPermissions(isEdit);

  return (
    <Form.Item name="isActive" label="Status" transformer={booleanToStringify}>
      <RadioGroup isRequired orientation="horizontal" isDisabled={isDisabledForRemains}>
        <Radio value={`true`}>Active</Radio>
        <Radio value={`false`}>Inactive</Radio>
      </RadioGroup>
    </Form.Item>
  );
});

import { type Dispatchable } from 'amos';
import { type FormState } from '@moego/ui';
import { type SurchargeType } from '@moego/api-web/moego/models/order/v1/service_charge_enums';
import { ServiceChargeApplyType } from '@moego/api-web/moego/models/order/v1/service_charge_model';
import { type ServiceChargeRecord } from '../../../../../../../../store/service/service.boxes';
import { type RecordProps } from '../../../../../../../../store/utils/RecordMap';
import {
  getValidServiceChargeOverrideLocationList,
  type TransformedServiceChargeRecord,
} from '../../../../utils/serviceOverrideLocationHandler';

/**
 * 所有 service charge 的表单初始化逻辑的基类
 *
 * - 提供基础的表单初始化逻辑
 * - 提供基础的表单值
 * - 提供基础的表单 effect 即页面初始化时需要执行的 actions
 *
 * @class BaseChargeFormHelper
 */
export abstract class BaseChargeFormHelper {
  protected abstract surchargeType: SurchargeType;

  /**
   * 自定义创建表单时需要初始化的值
   *
   * @memberof BaseChargeFormHelper
   */
  protected abstract initAddFormCustomizedValue(): Partial<TransformedServiceChargeRecord>;

  /**
   * 自定义编辑表单时需要初始化的值
   *
   * @memberof BaseChargeFormHelper
   */
  protected abstract initEditFormCustomizedValue(
    service: RecordProps<ServiceChargeRecord>,
    isDuplicate?: boolean,
  ): Partial<TransformedServiceChargeRecord>;

  /**
   * 自定义初始化需要 init 的 actions
   *
   * @memberof BaseChargeFormHelper
   */
  protected initCustomizedEffect?(): Array<Dispatchable> {
    return [];
  }

  /**
   * 获取新建 service charge 的基础表单值
   *
   * @memberof BaseChargeFormHelper
   */
  protected getAddFormBaseValue(): Partial<TransformedServiceChargeRecord> {
    return {
      name: '',
      price: undefined,
      taxId: undefined,
      isActive: true,
      isAllLocation: true,
      applyType: ServiceChargeApplyType.PER_PET,
    };
  }

  /**
   * 初始化表单的值
   *
   * @memberof BaseChargeFormHelper
   */
  public initFormValue(params: {
    service?: RecordProps<ServiceChargeRecord>;
    isDuplicate?: boolean;
  }): Partial<TransformedServiceChargeRecord> {
    const { service, isDuplicate } = params;
    if (!service) {
      return {
        ...this.getAddFormBaseValue(),
        ...this.initAddFormCustomizedValue(),
      };
    } else {
      return {
        ...service,
        name: isDuplicate ? `${service?.name} (copy)` : service.name,
        draftLocationOverrideList: getValidServiceChargeOverrideLocationList(service.locationOverrideList ?? []),
        ...this.initEditFormCustomizedValue(service, isDuplicate),
      };
    }
  }

  /**
   * 需要初始化的 actions
   *
   * @return {*}
   * @memberof BaseChargeFormHelper
   */
  public getInitEffectActions() {
    return this.initCustomizedEffect?.() ?? [];
  }

  /**
   * 计算是否需要出现 upcoming confirm modal 出现时机:
   * - 创建 charge
   * - 修改关键信息
   *
   * @param {boolean} isEdit
   * @param {FormState<TransformedServiceChargeRecord>} state
   * @return {*}
   * @memberof BaseChargeFormHelper
   */
  public getUpcomingConfirmModalVisible(isEdit: boolean, state: FormState<TransformedServiceChargeRecord>): boolean {
    const { dirtyFields } = state;
    return !isEdit || Boolean(dirtyFields?.price) || Boolean(dirtyFields?.isActive);
  }
}

import { useSelector } from 'amos';
import React, { memo } from 'react';
import { Button, cn, Form, IconButton, Input, useFieldArray, useWatch } from '@moego/ui';
import { MajorPlusOutlined, MajorTrashOutlined } from '@moego/icons-react';
import { ServiceChargeMultiplePetsChargeType } from '@moego/api-web/moego/models/order/v1/service_charge_model';
import { useCommonServiceChargeContext } from '../../CommonServiceChargeContext';
import { selectCurrentBusiness } from '../../../../../../../../../store/business/business.selectors';
import { TaxSelector } from '../../../../AddOrEditServiceForm/PriceDuration/components/TaxSelector';
import { Condition } from '../../../../../../../../../components/Condition';
import { isUndefined } from 'lodash';
import { Switch } from '../../../../../../../../../components/SwitchCase';
import { MultiplePetChargeType } from './MultiplePetChargeType';
import { OverTimeChargeFormHelper } from '../../utils/OverTimeChargeFormHelper';
import { useServiceStaffPermissions } from '../../../../AddOrEditComponents/hooks/useServiceStaffPermissions';

export const TieredRate = memo(() => {
  const { form, isEdit } = useCommonServiceChargeContext();
  const [business] = useSelector(selectCurrentBusiness);
  const { isDisabledForPriceAndTax } = useServiceStaffPermissions(isEdit);

  const [multiplePetsChargeType] = useWatch({
    control: form?.control,
    name: ['multiplePetsChargeType'],
  });
  const {
    fields: hourlyExceedRules,
    append,
    remove,
  } = useFieldArray({ control: form?.control, name: 'hourlyExceedRules' });
  const isDifferent =
    multiplePetsChargeType === ServiceChargeMultiplePetsChargeType.DIFFERENT_CHARGE_FOR_ADDITIONAL_PETS;
  return (
    <div className="moe-flex moe-flex-col moe-gap-y-m">
      <div className="moe-flex moe-flex-row moe-gap-x-8px-300">
        <MultiplePetChargeType />
        <Form.Item name="taxId" label="Tax">
          <TaxSelector isRequired className="moe-w-full" isDisabled={isDisabledForPriceAndTax} />
        </Form.Item>
      </div>
      <div className="moe-bg-neutral-sunken-light moe-rounded-8px-200 moe-p-8px-200 moe-w-full">
        {hourlyExceedRules.map((rule, index) => {
          const isFirstItem = index === 0;
          return (
            <div key={rule.id} className={cn('moe-flex moe-flex-row moe-gap-x-[8px] moe-mb-8px-100')}>
              <Form.Item
                name={`hourlyExceedRules.${index}.feeName`}
                label={isFirstItem && 'Fee name'}
                rules={{ required: true }}
              >
                <Input isRequired className={cn(isDifferent ? 'moe-w-[146px]' : 'moe-flex-1')} placeholder="Fee name" />
              </Form.Item>
              <Form.Item
                name={`hourlyExceedRules.${index}.hour`}
                label={isFirstItem && 'Additional # of hour'}
                rules={{
                  required: true,
                  validate: (currentHourValue) => {
                    if (!isFirstItem) {
                      const previousRule = form?.getValues(`hourlyExceedRules.${index - 1}`);
                      if (previousRule?.hour && currentHourValue <= previousRule?.hour) {
                        return 'Additional hours must increase with each tier';
                      }
                    }
                    return true;
                  },
                }}
              >
                <Input.Number
                  isRequired
                  minValue={0}
                  precision={0}
                  maxLength={10}
                  prefix="≥"
                  suffix="hour"
                  className="moe-flex-1"
                  placeholder="Additional # of hour"
                  isDisabled={isDisabledForPriceAndTax}
                />
              </Form.Item>
              <Switch>
                <Switch.Case if={isDifferent}>
                  <Form.Item
                    name={`hourlyExceedRules.${index}.basePrice`}
                    label={isFirstItem && '1st pet'}
                    rules={{
                      required: true,
                      validate: (currentBasePrice) => {
                        if (!isFirstItem) {
                          const previousRule = form?.getValues(`hourlyExceedRules.${index - 1}`);
                          if (
                            !isUndefined(previousRule?.basePrice) &&
                            Number(currentBasePrice) <= Number(previousRule?.basePrice)
                          ) {
                            return 'Fee amount must be greater than the one before.';
                          }
                        }
                        return true;
                      },
                    }}
                  >
                    <Input.Number
                      isRequired
                      minValue={0}
                      precision={2}
                      maxLength={10}
                      className="moe-w-[120px]"
                      placeholder="1st pet amount"
                      prefix={business.currencySymbol}
                      isDisabled={isDisabledForPriceAndTax}
                    />
                  </Form.Item>
                </Switch.Case>
                <Switch.Case else>
                  <Form.Item
                    name={`hourlyExceedRules.${index}.basePrice`}
                    label={isFirstItem && 'Amount'}
                    rules={{
                      required: true,
                      validate: (currentBasePrice) => {
                        if (!isFirstItem) {
                          const previousRule = form?.getValues(`hourlyExceedRules.${index - 1}`);
                          if (
                            !isUndefined(previousRule?.basePrice) &&
                            Number(currentBasePrice) <= Number(previousRule?.basePrice)
                          ) {
                            return 'Fee amount must be greater than the one before.';
                          }
                        }
                        return true;
                      },
                    }}
                  >
                    <Input.Number
                      isRequired
                      minValue={0}
                      precision={2}
                      maxLength={10}
                      className="moe-flex-1"
                      placeholder="Enter price"
                      prefix={business.currencySymbol}
                      isDisabled={isDisabledForPriceAndTax}
                    />
                  </Form.Item>
                </Switch.Case>
              </Switch>
              <Condition if={isDifferent}>
                <Form.Item
                  name={`hourlyExceedRules.${index}.additionalPetPrice`}
                  label={isFirstItem && (isDifferent ? 'Other pet(s)' : 'Fee amount')}
                  rules={{
                    required: true,
                    validate: (currentAdditionalPetPrice) => {
                      if (!isFirstItem) {
                        const previousRule = form?.getValues(`hourlyExceedRules.${index - 1}`);
                        if (
                          !isUndefined(previousRule?.additionalPetPrice) &&
                          Number(currentAdditionalPetPrice) <= Number(previousRule?.additionalPetPrice)
                        ) {
                          return 'Fee amount must be greater than the one before.';
                        }
                      }
                      return true;
                    },
                  }}
                >
                  <Input.Number
                    isRequired
                    minValue={0}
                    precision={2}
                    maxLength={10}
                    placeholder="Fee amount"
                    prefix={business.currencySymbol}
                    className={cn(isDifferent ? 'moe-w-[124px]' : 'moe-flex-1')}
                    isDisabled={isDisabledForPriceAndTax}
                  />
                </Form.Item>
              </Condition>
              <Condition if={hourlyExceedRules.length > 1}>
                <div className="moe-w-[40px] moe-flex moe-items-start moe-justify-center">
                  <Condition if={!isFirstItem}>
                    <IconButton
                      size="s"
                      icon={<MajorTrashOutlined />}
                      className="moe-mt-8px-100"
                      classNames={{ base: 'moe-bg-transparent' }}
                      onPress={() => remove(index)}
                    />
                  </Condition>
                </div>
              </Condition>
            </div>
          );
        })}
        <Button
          variant="tertiary"
          icon={<MajorPlusOutlined />}
          onPress={() => append(OverTimeChargeFormHelper.createHourlyExceedRule())}
        >
          Add tier
        </Button>
      </div>
    </div>
  );
});

import { MinorCloseOutlined } from '@moego/icons-react';
import { <PERSON><PERSON>, Heading, IconButton, useForm, useFormState } from '@moego/ui';
import { useSelector } from 'amos';
import { useDispatch } from 'amos';
import React, { memo, useEffect, useMemo } from 'react';
import { useHistory } from 'react-router';
import { toast<PERSON><PERSON> } from '../../../../../../../components/Toast/Toast';
import { ScrollerProvider } from '../../../../../../../layout/components/ScrollerProvider';
import { PATH_ADD_OR_EDIT_SURCHARGES, PATH_SERVICE_SETTING } from '../../../../../../../router/paths';
import { serviceChargeMapBox } from '../../../../../../../store/service/service.boxes';
import { useRouteParams, useRouteQueryV2, useRouteState } from '../../../../../../../utils/RoutePath';
import { abortNavigation } from '../../../../../../../utils/abortNavigation';
import { useBizIdReadyEffect } from '../../../../../../../utils/hooks/useBizIdReadyEffect';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../../../utils/hooks/useSerialCallback';
import { useUnsavedConfirmGlobalV2 } from '../../../../../../../utils/hooks/useUnsavedConfirmGlobalV2';
import { ServicesNav } from '../../../types';
import { type TransformedServiceChargeRecord } from '../../../utils/serviceOverrideLocationHandler';
import { ServiceChargeConfig } from '../PricingRules.enum';
import { useHandlePricingRulesSubmit } from '../hooks/useHandlePricingRulesSubmit';
import { AddOrEditCommonServiceChargeForm } from './AddOrEditCommonServiceChargeForm';
import { CommonServiceChargeContext } from './CommonServiceChargeContext';
import { useUpcomingConfirm } from './hooks/useUpcomingConfirm';
import { serviceChargeFormHelperFactory } from './utils/ServiceChargeFormHelperFactory';
import { Onboarding } from './Onboarding/Onboarding';
import { type SurchargeType } from '@moego/api-web/moego/models/order/v1/service_charge_enums';

export const AddOrEditCommonServiceCharge = memo(() => {
  const history = useHistory();
  const dispatch = useDispatch();
  const form = useForm<Partial<TransformedServiceChargeRecord>>();
  const [serviceChargeMap] = useSelector(serviceChargeMapBox);
  const { surchargeType: surchargeTypeQuery } = useRouteParams(PATH_ADD_OR_EDIT_SURCHARGES) || {};
  const { isActive } = useRouteState(PATH_ADD_OR_EDIT_SURCHARGES) || {};
  const { id, isDuplicate } = useRouteQueryV2(PATH_ADD_OR_EDIT_SURCHARGES);
  const { isValid, dirtyFields } = useFormState({ control: form.control });
  const surchargeType = Number(surchargeTypeQuery) as SurchargeType;
  const { title, nav, onboarding } = ServiceChargeConfig.mapLabels[surchargeType];
  const isEdit = !!id && !isDuplicate;
  const { handleSubmit } = useHandlePricingRulesSubmit();
  const upcomingConfirm = useUpcomingConfirm(form, isEdit, surchargeType);
  const helper = serviceChargeFormHelperFactory.getHelper(surchargeType);
  const data = id ? serviceChargeMap.mustGetItem(id) : undefined;
  const service = useMemo(
    () => helper.initFormValue({ service: data?.toJSON(), isDuplicate: !!isDuplicate }),
    [data, isDuplicate, isEdit],
  );

  useEffect(() => {
    if (service) {
      form.reset({ ...service });
    }
  }, [service]);

  useBizIdReadyEffect(() => {
    dispatch(helper.getInitEffectActions());
  }, []);

  const handleGoBack = useLatestCallback(() => {
    history.goBackWithFallback(PATH_SERVICE_SETTING.build({ panel: ServicesNav.PricingRules, childPanel: nav }));
  });

  const handleSave = useSerialCallback(async () => {
    return form.handleSubmit(async (data) => {
      // 判断用户的改动是否需要应用到未来的预约
      const { success, applyUpcomingAppt } = await upcomingConfirm();
      if (!success) return;
      data.applyUpcomingAppt = applyUpcomingAppt;
      await handleSubmit({ data, isEdit, isActive, surchargeType });
      form.reset(undefined, { keepValues: true, keepDirty: false, keepDefaultValues: false });
      setTimeout(() => {
        handleGoBack();
      }, 100);
    })();
  });

  const handleDoubleConfirm = useLatestCallback(async () => {
    if (!isValid) {
      // 触发 error
      await form.handleSubmit(() => {})();
      toastApi.error(`${title} save failed. Please fill in the required fields.`);
      abortNavigation();
    } else {
      await handleSave();
    }
  });

  const handleClose = useLatestCallback(() => {
    handleGoBack();
  });

  // 原 isDirty 字段在不知道哪个地方被手动污染了，导致无法正常判断是否需要弹窗。
  // 这里使用 dirtyFields 来判断是否需要弹窗
  const isNotDirty = !dirtyFields || Object.keys(form.formState.dirtyFields).length === 0;
  useUnsavedConfirmGlobalV2({
    showConfirm: !isNotDirty,
    modalProps: {
      title: `Service charge has unsaved changes`,
      content: 'Would you like to save your changes before exiting?',
      onConfirm: handleDoubleConfirm,
      onCancel: handleGoBack,
      cancelText: 'Discard changes',
      confirmText: 'Save',
    },
  });

  return (
    <ScrollerProvider style={{ maxHeight: '100vh', padding: 0 }}>
      <CommonServiceChargeContext.Provider
        value={{
          form,
          surchargeType,
          isEdit,
          serviceId: id,
          isDuplicate: !!isDuplicate,
        }}
      >
        <div className="moe-pb-[16px] moe-px-[24px] moe-w-full moe-font-manrope">
          <div className="moe-flex moe-w-full moe-h-[72px] moe-justify-between moe-items-center moe-sticky moe-top-[0px] moe-z-[1] moe-bg-white">
            <IconButton icon={<MinorCloseOutlined />} onPress={handleClose} color="transparent" size="xl" />
            <Button onPress={handleSave} isLoading={handleSave.isBusy()} size="l">
              Save
            </Button>
          </div>
          <div className="moe-flex moe-justify-center moe-mt-[16px] [@media(min-width:1450px)]:moe-grid [@media(min-width:1450px)]:moe-grid-cols-12 [@media(min-width:1450px)]:moe-gap-x-[24px]">
            <div className="[@media(max-width:1450px)]:moe-w-[670px] [@media(min-width:1450px)]:moe-col-start-4 [@media(min-width:1450px)]:moe-col-span-6">
              <Heading size="2" className="moe-mb-xl">
                {title}
              </Heading>
              <Onboarding onboarding={onboarding} />
              <AddOrEditCommonServiceChargeForm />
            </div>
          </div>
        </div>
      </CommonServiceChargeContext.Provider>
    </ScrollerProvider>
  );
});

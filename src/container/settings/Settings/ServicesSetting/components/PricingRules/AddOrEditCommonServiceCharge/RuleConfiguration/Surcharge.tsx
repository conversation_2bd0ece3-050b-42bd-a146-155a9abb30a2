import { useSelector } from 'amos';
import React, { memo } from 'react';
import { selectCurrentBusiness } from '../../../../../../../../store/business/business.selectors';
import { FormItemLabel } from '@moego/ui/dist/esm/components/Form/FormItemLabel';
import { Form, Input, Text } from '@moego/ui';
import { useCommonServiceChargeContext } from '../CommonServiceChargeContext';
import { useServiceStaffPermissions } from '../../../AddOrEditComponents/hooks/useServiceStaffPermissions';

export const Surcharge = memo(() => {
  const [business] = useSelector(selectCurrentBusiness);
  const { isEdit } = useCommonServiceChargeContext();
  const { isDisabledForPriceAndTax } = useServiceStaffPermissions(isEdit);

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-[8px]">
      <FormItemLabel isRequired>Surcharge</FormItemLabel>
      <div className="moe-flex moe-gap-[12px] moe-items-start">
        <Form.Item name="price" rules={{ required: true }}>
          <Input prefix={business.currencySymbol} className="moe-w-[120px]" isDisabled={isDisabledForPriceAndTax} />
        </Form.Item>
        <Text variant="regular-short" className="moe-mt-[10px]">
          for each pet
        </Text>
      </div>
    </div>
  );
});

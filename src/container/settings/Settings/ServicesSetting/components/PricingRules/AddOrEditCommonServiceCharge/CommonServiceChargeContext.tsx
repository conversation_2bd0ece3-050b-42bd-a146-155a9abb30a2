import { type useForm } from '@moego/ui';
import { createContext, useContext } from 'react';
import { type TransformedServiceChargeRecord } from '../../../utils/serviceOverrideLocationHandler';
import { SurchargeType } from '@moego/api-web/moego/models/order/v1/service_charge_enums';

export type ServiceChargeForm = ReturnType<typeof useForm<Partial<TransformedServiceChargeRecord>>>;

export interface CommonServiceChargeContextProps {
  isEdit: boolean;
  surchargeType: SurchargeType;
  form?: ServiceChargeForm;
  serviceId?: string;
  isDuplicate: boolean;
}

export const CommonServiceChargeContext = createContext<CommonServiceChargeContextProps>({
  isEdit: false,
  isDuplicate: false,
  surchargeType: SurchargeType.UNSPECIFIED,
});

export const useCommonServiceChargeContext = () => useContext(CommonServiceChargeContext);

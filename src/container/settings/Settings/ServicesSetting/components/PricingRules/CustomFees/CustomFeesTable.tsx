import { useLatestCallback } from '@moego/finance-utils';
import { Empty, Text } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useRef } from 'react';
import { currentAccountIdBox } from '../../../../../../../store/account/account.boxes';
import { businessMapBox } from '../../../../../../../store/business/business.boxes';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import { taxMapBox } from '../../../../../../../store/business/tax.boxes';
import { selectPricingPermission } from '../../../../../../../store/company/company.selectors';
import { type ServiceChargeRecord } from '../../../../../../../store/service/service.boxes';
import { selectServiceChargeList } from '../../../../../../../store/service/service.selectors';
import { isUndefinedOrNullOrEmptyString } from '../../../../../../../utils/common';
import { getValidServiceChargeOverrideLocationList } from '../../../utils/serviceOverrideLocationHandler';
import { BasedTable } from '../../BasedTable';
import { type SurchargesPricingRuleTableProps } from '../components/SurchargesPricingRuleTable';
import { useCustomFeesColumns } from './useGetCustomFeesColumns';
import { EXPAND_ROW_INDENT } from '../../../../consts';

export interface CustomFeesTableProps extends SurchargesPricingRuleTableProps {}

export const CustomFeesTable = memo((props: CustomFeesTableProps) => {
  const { className, onDelete, onEdit, onDuplicate, isInactive, selectedBusinessId } = props;
  const [currentAccountId] = useSelector(currentAccountIdBox);
  const [currentBusiness, serviceCharges, taxMap, pricingPermission, businessMap] = useSelector(
    selectCurrentBusiness,
    selectServiceChargeList(!isInactive, currentAccountId),
    taxMapBox,
    selectPricingPermission(),
    businessMapBox,
  );

  const basedTableRef = useRef<HTMLDivElement>(null);

  const haveServiceChargePermission = pricingPermission.enable.has('serviceCharge');

  const columns = useCustomFeesColumns({ onDelete, onEdit, onDuplicate, selectedBusinessId });

  const data = serviceCharges.toArray();

  const handleGetRowKey = useLatestCallback((record: ServiceChargeRecord) => {
    return `${record.id}`;
  });

  const handleExpandable = useLatestCallback((record: ServiceChargeRecord) => {
    return getValidServiceChargeOverrideLocationList(record.locationOverrideList)?.length > 0;
  });

  const handleRenderExpandedRow = useLatestCallback((record: ServiceChargeRecord): JSX.Element => {
    const locationOverrideList = getValidServiceChargeOverrideLocationList(record.locationOverrideList);

    return (
      <div className="moe-bg-neutral-sunken-0 moe-rounded-[16px]">
        {locationOverrideList.map((item) => {
          const price = isUndefinedOrNullOrEmptyString(item.price) ? '-' : currentBusiness.formatAmount(item.price!);
          const tax = isUndefinedOrNullOrEmptyString(item.taxId) ? '-' : `${taxMap.mustGetItem(+item.taxId!).taxRate}%`;
          const { businessName } = businessMap.mustGetItem(+(item.businessId || 0));
          return (
            <div className="moe-w-full" key={`${price}_${tax}_${item.businessId}`}>
              <div className="moe-flex moe-py-spacing-xs moe-text-sm-20 moe-text-primary ">
                <Text
                  variant="small"
                  className="moe-flex-shrink-0 moe-truncate moe-pl-[20px] moe-w-[300px]"
                  style={{
                    width: 300 - EXPAND_ROW_INDENT,
                  }}
                >
                  {businessName}
                </Text>
                <Text variant="small" className="moe-flex-shrink-0 moe-px-8px-200 moe-w-[160px]">
                  {price}
                </Text>
                <Text variant="small" className="moe-flex-shrink-0 moe-px-8px-200 moe-w-[160px]">
                  {tax}
                </Text>
              </div>
            </div>
          );
        })}
      </div>
    );
  });

  const handleRowClick = useLatestCallback((record: ServiceChargeRecord) => {
    if (haveServiceChargePermission) {
      onEdit(record);
    }
  });

  const handleRenderNoDataArea = useLatestCallback(() => {
    return <Empty />;
  });

  return (
    <div className="moe-full">
      <BasedTable
        showHeaderWhenEmpty
        ref={basedTableRef}
        className={className}
        data={data}
        columns={columns}
        expandable={handleExpandable}
        expandedRowRender={handleRenderExpandedRow}
        onRowClick={handleRowClick}
        rowKey={handleGetRowKey}
        noDataArea={handleRenderNoDataArea}
      />
    </div>
  );
});

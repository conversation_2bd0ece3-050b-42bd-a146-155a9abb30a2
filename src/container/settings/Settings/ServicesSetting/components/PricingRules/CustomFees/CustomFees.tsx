import { SurchargeType } from '@moego/api-web/moego/models/order/v1/service_charge_enums';
import { useCallback } from 'react';
import React from 'react';
import { SurchargesPricingRule } from '../components/SurchargesPricingRule';
import { type SurchargesPricingRuleTableProps } from '../components/SurchargesPricingRuleTable';
import { CustomFeesTable } from './CustomFeesTable';

export const CustomFees = () => {
  const renderTableBlock = useCallback((props: SurchargesPricingRuleTableProps) => {
    return <CustomFeesTable {...props} />;
  }, []);

  return (
    <SurchargesPricingRule
      surchargeType={SurchargeType.CUSTOM_FEE}
      showCareTypeSelector={false}
      renderTableBlock={renderTableBlock}
    />
  );
};

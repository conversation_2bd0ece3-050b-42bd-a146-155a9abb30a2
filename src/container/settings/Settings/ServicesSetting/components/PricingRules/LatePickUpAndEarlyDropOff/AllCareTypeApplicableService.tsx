import { Form, Checkbox, CheckboxGroup, useWatch } from '@moego/ui';
import React, { type FC, useMemo } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { type AddOrEditLatePickUpAndEarlyDropOffFormProps } from './utils/AddOrEditLatePickUpAndEarlyDropOffForm.types';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { ApplicableServicePicker } from './ApplicableServicePicker';
import { useSelector } from 'amos';
import { selectCompanyCareTypeNameMap } from '../../../../../../../store/careType/careType.selectors';

export const AllCareTypeApplicableService: FC<Omit<AddOrEditLatePickUpAndEarlyDropOffFormProps, 'isEdit'>> = (
  props,
) => {
  const { form } = props;
  const [companyCareTypeNameMap] = useSelector(selectCompanyCareTypeNameMap);
  const [serviceItemTypes = []] = useWatch({ control: form?.control, name: ['serviceItemTypes'] });
  const [allBoardingApplicable, allDaycareApplicable, selectedBoardingServices, selectedDaycareServices] = useWatch({
    control: form?.control,
    name: ['allBoardingApplicable', 'allDaycareApplicable', 'selectedBoardingServices', 'selectedDaycareServices'],
  });

  const isValid = useMemo(() => {
    if (!serviceItemTypes?.length) {
      return false;
    }

    if (serviceItemTypes.includes(ServiceItemType.BOARDING)) {
      if (!allBoardingApplicable && !selectedBoardingServices?.length) {
        return false;
      }
    }

    if (serviceItemTypes.includes(ServiceItemType.DAYCARE)) {
      if (!allDaycareApplicable && !selectedDaycareServices?.length) {
        return false;
      }
    }

    return true;
  }, [
    serviceItemTypes,
    allBoardingApplicable,
    allDaycareApplicable,
    selectedBoardingServices,
    selectedDaycareServices,
  ]);

  if (!form) return null;

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-8px-200">
      <Form.Item<number[], number[]>
        label="Applicable services"
        name="serviceItemTypes"
        rules={{ validate: () => (isValid ? true : 'Please select applicable services.') }}
      >
        <CheckboxGroup isRequired>
          <Checkbox value={ServiceItemType.BOARDING}>{`${companyCareTypeNameMap.Boarding} services`}</Checkbox>
          <Condition if={serviceItemTypes.includes(ServiceItemType.BOARDING)}>
            <ApplicableServicePicker form={form} serviceItemType={ServiceItemType.BOARDING} className="moe-ml-[28px]" />
          </Condition>
          <Checkbox value={ServiceItemType.DAYCARE}>{companyCareTypeNameMap.Daycare} services</Checkbox>
          <Condition if={serviceItemTypes.includes(ServiceItemType.DAYCARE)}>
            <ApplicableServicePicker form={form} serviceItemType={ServiceItemType.DAYCARE} className="moe-ml-[28px]" />
          </Condition>
        </CheckboxGroup>
      </Form.Item>
    </div>
  );
};

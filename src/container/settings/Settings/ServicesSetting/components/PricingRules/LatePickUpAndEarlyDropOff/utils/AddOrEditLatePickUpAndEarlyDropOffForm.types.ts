import { type useForm } from '@moego/ui';
import { type TransformedServiceChargeRecord } from '../../../../utils/serviceOverrideLocationHandler';

export interface FormFields extends TransformedServiceChargeRecord {
  allBoardingApplicable: boolean;
  allDaycareApplicable: boolean;
  selectedBoardingServices: string[];
  selectedDaycareServices: string[];
}

export type LatePickUpAndEarlyDropOffFormFields = Partial<FormFields>;

export type LatePickUpAndEarlyDropOffForm = ReturnType<typeof useForm<LatePickUpAndEarlyDropOffFormFields>>;

export interface AddOrEditLatePickUpAndEarlyDropOffFormProps {
  form: LatePickUpAndEarlyDropOffForm;
  isEdit: boolean;
}

import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { SurchargeType } from '@moego/api-web/moego/models/order/v1/service_charge_enums';
import {
  ServiceChargeAutoApplyCondition,
  ServiceChargeAutoApplyStatus,
  ServiceChargeAutoApplyTimeType,
} from '@moego/api-web/moego/models/order/v1/service_charge_model';
import { MinorCloseOutlined } from '@moego/icons-react';
import { Button, Heading, IconButton, useForm, useFormState } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { useHistory } from 'react-router';
import { toastApi } from '../../../../../../../components/Toast/Toast';
import { DefaultWorkingHours } from '../../../../../../../components/WeekTimeScheduleV2/WeekTimePeriodRow';
import { ScrollerProvider } from '../../../../../../../layout/components/ScrollerProvider';
import { PATH_ADD_OR_EDIT_SURCHARGES, PATH_SERVICE_SETTING } from '../../../../../../../router/paths';
import { serviceChargeMapBox } from '../../../../../../../store/service/service.boxes';
import { useRouteQueryV2, useRouteState } from '../../../../../../../utils/RoutePath';
import { abortNavigation } from '../../../../../../../utils/abortNavigation';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../../../utils/hooks/useSerialCallback';
import { useUnsavedConfirmGlobalV2 } from '../../../../../../../utils/hooks/useUnsavedConfirmGlobalV2';
import { type ApplyUpcomingModalConfig, ServicesSettingContext } from '../../../ServicesSettingContext';
import { useApplyUpcomingModal } from '../../../hooks/useApplyUpcomingModal';
import { ServicesNav } from '../../../types';
import {
  type TransformedServiceChargeRecord,
  transformServiceChargeLocalOverrideList,
} from '../../../utils/serviceOverrideLocationHandler';
import { PricingRuleTypes } from '../PricingRules.enum';
import { useHandlePricingRulesSubmit } from '../hooks/useHandlePricingRulesSubmit';
import { AddOrEditLatePickUpAndEarlyDropOffForm } from './AddOrEditLatePickUpAndEarlyDropOffForm';
import {
  transformFormValueToServiceCharge,
  transformServiceChargeToFormValue,
} from './utils/AddOrEditLatePickUpAndEarlyDropOffForm.transform';
import { isNormal } from '../../../../../../../store/utils/identifier';

export const AddOrEditLatePickUpAndEarlyDropOff = memo(() => {
  const history = useHistory();
  const form = useForm<Partial<TransformedServiceChargeRecord>>({
    mode: 'onBlur',
  });
  const [serviceChargeMap] = useSelector(serviceChargeMapBox);
  const { isActive } = useRouteState(PATH_ADD_OR_EDIT_SURCHARGES) || {};
  const { id, isDuplicate } = useRouteQueryV2(PATH_ADD_OR_EDIT_SURCHARGES);
  const data = id ? serviceChargeMap.mustGetItem(id) : undefined;
  const checkApplyUpcoming = useApplyUpcomingModal(ServiceType.UNSPECIFIED, ServiceItemType.UNSPECIFIED);
  const [applyUpcomingConfig, setApplyUpcomingConfig] = useState<ApplyUpcomingModalConfig>();
  const { isValid, dirtyFields } = useFormState({ control: form.control });
  const { handleSubmit } = useHandlePricingRulesSubmit();

  const isEdit = !!id && !isDuplicate;

  const title = `${isEdit ? 'Edit' : 'Add'} late pick-up/early drop-off rule`;

  const service = useMemo(() => {
    // Create
    if (!isNormal(id)) {
      return {
        ...transformServiceChargeLocalOverrideList(data?.toJSON(), !!isDuplicate),
        autoApplyCondition: ServiceChargeAutoApplyCondition.BD_LATE_PICKUP,
        autoApplyTimeType: ServiceChargeAutoApplyTimeType.CERTAIN_TIME,
        autoApplyStatus: ServiceChargeAutoApplyStatus.AUTO_APPLY_ENABLED_WITH_CONDITION,
        autoApplyTime: DefaultWorkingHours.EndTime,
      };
    }
    // Edit and duplicate
    return transformServiceChargeLocalOverrideList(data?.toJSON(), !!isDuplicate);
  }, [data, isDuplicate, isEdit]);

  const handleSave = useSerialCallback(async () => {
    return form.handleSubmit(async (data) => {
      // 判断用户的改动是否需要应用到未来的预约
      if (applyUpcomingConfig?.visible) {
        const result = await checkApplyUpcoming({ action: applyUpcomingConfig.action! });
        if (result === undefined) {
          return;
        }
        data.applyUpcomingAppt = result;
      }

      const transformedData = transformFormValueToServiceCharge(data);
      await handleSubmit({ data: transformedData, isEdit, isActive, surchargeType: SurchargeType.OFF_HOURS_FEE });

      // 置空 dirty 态
      form.reset(undefined, { keepValues: true, keepDirty: false, keepDefaultValues: false });
      setTimeout(() => {
        handleGoBack();
        // 等一下 toast 动画
      }, 100);
    })();
  });

  useEffect(() => {
    if (service) {
      const formValue = transformServiceChargeToFormValue(service);
      form.reset(formValue);
    }
  }, [service]);

  const handleGoBack = useLatestCallback(() => {
    history.goBackWithFallback(
      PATH_SERVICE_SETTING.build({
        panel: ServicesNav.PricingRules,
        childPanel: PricingRuleTypes.LatePickUpAndEarlyDropOff,
      }),
    );
  });

  const handleDoubleConfirm = useLatestCallback(async () => {
    if (!isValid) {
      // 触发 error
      await form.handleSubmit(() => {})();
      toastApi.error(`Service charge save failed. Please fill in the required fields.`);
      abortNavigation();
    } else {
      await handleSave();
    }
  });

  const handleClose = useLatestCallback(() => {
    handleGoBack();
  });

  // 原 isDirty 字段在不知道哪个地方被手动污染了，导致无法正常判断是否需要弹窗。
  // 这里使用 dirtyFields 来判断是否需要弹窗
  const isNotDirty = !dirtyFields || Object.keys(form.formState.dirtyFields).length === 0;
  useUnsavedConfirmGlobalV2({
    showConfirm: !isNotDirty,
    modalProps: {
      title: `Service charge has unsaved changes`,
      content: 'Would you like to save your changes before exiting?',
      onConfirm: handleDoubleConfirm,
      onCancel: handleGoBack,
      cancelText: 'Discard changes',
      confirmText: 'Save',
    },
  });

  return (
    <ScrollerProvider
      style={{
        maxHeight: '100vh',
        padding: 0,
      }}
    >
      <ServicesSettingContext.Provider
        value={{
          serviceItemType: ServiceItemType.UNSPECIFIED,
          serviceType: ServiceType.UNSPECIFIED,
          setApplyUpcomingModalConfig: setApplyUpcomingConfig,
        }}
      >
        <div className="moe-pb-[16px] moe-px-[24px] moe-w-full moe-font-manrope">
          <div className="moe-flex moe-w-full moe-h-[72px] moe-justify-between moe-items-center moe-sticky moe-top-[0px] moe-z-[1] moe-bg-white">
            <IconButton icon={<MinorCloseOutlined />} onPress={handleClose} color="transparent" size="xl" />
            <Button onPress={handleSave} isLoading={handleSave.isBusy()} size="l">
              Save
            </Button>
          </div>
          <div className="moe-flex moe-justify-center moe-mt-[16px] [@media(min-width:1450px)]:moe-grid [@media(min-width:1450px)]:moe-grid-cols-12 [@media(min-width:1450px)]:moe-gap-x-[24px]">
            <div className="[@media(max-width:1450px)]:moe-w-[670px] [@media(min-width:1450px)]:moe-col-start-4 [@media(min-width:1450px)]:moe-col-span-6">
              <Heading size="2" className="moe-mb-xl">
                {title}
              </Heading>
              <AddOrEditLatePickUpAndEarlyDropOffForm form={form} isEdit={isEdit} />
            </div>
          </div>
        </div>
      </ServicesSettingContext.Provider>
    </ScrollerProvider>
  );
});

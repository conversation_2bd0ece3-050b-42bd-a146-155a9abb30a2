import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { ServiceChargeApplyType } from '@moego/api-web/moego/models/order/v1/service_charge_model';
import { Form, Heading, Input, Radio, RadioGroup, booleanToStringify } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import { taxMapBox } from '../../../../../../../store/business/tax.boxes';
import { selectBusinessTaxes } from '../../../../../../../store/business/tax.selectors';
import { AutomationConditions } from '../../../../../../../store/service/service.boxes';
import { GrowthBookFeatureList } from '../../../../../../../utils/growthBook/growthBook.config';
import { AutoApplyTimeFormItem } from '../../AutoApplyTimeFormItem';
import { PriceFormItem } from '../../PriceFormItem';
import { SelectBusinessFormItem } from '../../SelectBusinessFormItem';
import { type AddOrEditLatePickUpAndEarlyDropOffFormProps } from './utils/AddOrEditLatePickUpAndEarlyDropOffForm.types';
import { AllCareTypeApplicableService } from './AllCareTypeApplicableService';
import { useServiceStaffPermissions } from '../../AddOrEditComponents/hooks/useServiceStaffPermissions';

export const AddOrEditLatePickUpAndEarlyDropOffForm = memo((props: AddOrEditLatePickUpAndEarlyDropOffFormProps) => {
  const { form, isEdit } = props;

  const isEnable = useFeatureIsOn(GrowthBookFeatureList.EnableServiceChargePricingUnit);
  const [business, taxIdList, taxMap] = useSelector(selectCurrentBusiness, selectBusinessTaxes, taxMapBox);

  const { isDisabledForPriceAndTax, isDisabledForRemains } = useServiceStaffPermissions(!!isEdit);

  const taxOptionList = useMemo(() => {
    return taxIdList.toJSON().map((id) => {
      const tax = taxMap.mustGetItem(id);
      return {
        label: `${tax.taxName} (${tax.taxRate}%)`,
        value: Number(tax.id),
      };
    });
  }, [taxIdList, taxMap]);

  return (
    <div className="moe-flex moe-flex-col moe-w-full moe-pb-[40px]">
      <Form form={form} footer={null}>
        <Heading size="3">Basic info</Heading>
        <Form.Item
          name="name"
          label="Rule name"
          rules={{
            required: 'Rule name is required',
          }}
        >
          <Input
            isRequired
            className="moe-w-full"
            placeholder="Rule name"
            maxLength={70}
            isDisabled={isDisabledForRemains}
          />
        </Form.Item>
        <SelectBusinessFormItem form={form} />

        <AllCareTypeApplicableService form={form} />

        <Form.Item name="isActive" label="Status" transformer={booleanToStringify}>
          <RadioGroup isRequired orientation="horizontal" isDisabled={isDisabledForRemains}>
            <Radio value={`true`}>Active</Radio>
            <Radio value={`false`}>Inactive</Radio>
          </RadioGroup>
        </Form.Item>
        <Form.Item name="applyType" label="Applicable to">
          <RadioGroup isRequired orientation="horizontal" isDisabled={isDisabledForRemains}>
            <Radio value={ServiceChargeApplyType.PER_APPOINTMENT}>Each appointment</Radio>
            <Radio value={ServiceChargeApplyType.PER_PET}>Each pet</Radio>
            <Condition if={isEnable}>
              <Radio value={ServiceChargeApplyType.PER_PRICING_UNIT}>Each pricing unit</Radio>
            </Condition>
          </RadioGroup>
        </Form.Item>

        <Heading size="3" className="moe-text-primary moe-mt-[24px]">
          Rule configuration
        </Heading>
        <Form.Item
          name="autoApplyCondition"
          label="Condition"
          rules={{
            required: true,
          }}
        >
          <RadioGroup orientation="horizontal" isDisabled={isDisabledForRemains} isRequired>
            <Radio value={AutomationConditions.LatePickUp}>Late pick-up</Radio>
            <Radio value={AutomationConditions.EarlyDropOff}>Early drop-off</Radio>
          </RadioGroup>
        </Form.Item>
        <AutoApplyTimeFormItem form={form} isDisabled={isDisabledForRemains} business={business} />
        <PriceFormItem
          business={business}
          isDisabledForTax={isDisabledForPriceAndTax}
          taxOptionList={taxOptionList}
          priceLabel="Surcharge"
          pricePlaceholder="Add surcharge"
        />
      </Form>
    </div>
  );
});

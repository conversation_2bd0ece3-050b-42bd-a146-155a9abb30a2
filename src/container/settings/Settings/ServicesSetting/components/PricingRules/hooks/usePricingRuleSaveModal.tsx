import { Checkbox, Modal, Text } from '@moego/ui';
import React, { type FC } from 'react';
import { useModal } from '../../../../../../../components/Modal/useModal';
import { useBool } from '../../../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { PricingRuleActionEnum, type PricingRuleModalAction } from '../PricingRules.enum';

interface PricingRuleSaveModalProps extends React.ComponentProps<typeof Modal> {
  action: PricingRuleModalAction;
  onSave: (isSelected: boolean) => Promise<void>;
}
const PricingRuleSaveModal: FC<PricingRuleSaveModalProps> = ({ onClose, onSave, action, ...rest }) => {
  const isSelected = useBool(false);
  const { title, contentTitle, content, description, ...restModalProps } = PricingRuleActionEnum.mapLabels[action];
  const handleConfirm = useLatestCallback(() => {
    return onSave(isSelected.value);
  });
  return (
    <Modal {...restModalProps} {...rest} isOpen onClose={onClose} size="s" title={title} onConfirm={handleConfirm}>
      {contentTitle && (
        <Text variant="regular" className="moe-mb-8px-150">
          {contentTitle}
        </Text>
      )}
      <Checkbox isSelected={isSelected.value} onChange={isSelected.as} description={description}>
        {content}
      </Checkbox>
    </Modal>
  );
};

export const usePricingRuleSaveModal = () => {
  return useModal(PricingRuleSaveModal);
};

import React, { memo } from 'react';
import { FormSection } from '../../../../AddOrEditComponents/FormSection/FormSection';
import { ServiceFormSections } from '../../../../AddOrEditComponents/FormSection/formSectionConfig';
import { Price } from './Price';
import { Tax } from './Tax';
import { Duration } from './Duration';

export const PriceAndDuration = memo(() => {
  return (
    <FormSection section={ServiceFormSections.PriceDuration} title="Price & Duration">
      <div className="moe-grid moe-grid-cols-2 moe-gap-m">
        <Price />
        <Tax />
        <Duration />
      </div>
    </FormSection>
  );
});

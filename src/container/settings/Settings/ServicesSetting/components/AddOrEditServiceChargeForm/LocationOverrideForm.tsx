import { ServiceChargeAutoApplyStatus } from '@moego/api-web/moego/models/order/v1/service_charge_model';
import { MajorPlusOutlined, MinorTrashOutlined } from '@moego/icons-react';
import { Button, Form, Heading, Input, LegacySelect as Select, type useForm, useFormState, useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import { isEqual } from 'lodash';
import React, { memo, useEffect, useMemo } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { WithMultiLocation } from '../../../../../../components/WithFeature/WithMultiLocation';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { locationMapBox } from '../../../../../../store/business/location.boxes';
import { selectAllLocationIdList } from '../../../../../../store/business/location.selectors';
import { taxMapBox } from '../../../../../../store/business/tax.boxes';
import { selectBusinessTaxes } from '../../../../../../store/business/tax.selectors';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useServiceSettingContext } from '../../ServicesSettingContext';
import { type TransformedServiceChargeRecord } from '../../utils/serviceOverrideLocationHandler';
import { PriceFormItem } from '../PriceFormItem';
import { SelectBusinessFormItem } from '../SelectBusinessFormItem';
import { useServiceStaffPermissions } from '../AddOrEditComponents/hooks/useServiceStaffPermissions';

export interface LocationOverrideFormProps {
  form: ReturnType<typeof useForm<Partial<TransformedServiceChargeRecord>>>;
  isEdit: boolean;
}

export const LocationOverrideForm = memo(function LocationOverrideForm(props: LocationOverrideFormProps) {
  const { form, isEdit } = props;
  const { control } = form;
  const { setApplyUpcomingModalConfig } = useServiceSettingContext();
  const { isDisabledForPriceAndTax } = useServiceStaffPermissions(isEdit);
  const [business] = useSelector(selectCurrentBusiness);

  const [isAllLocation, sourceLocationOverrideList, draftSourceLocationOverrideList, isActive, taxId, autoApplyStatus] =
    useWatch({
      control,
      name: [
        'isAllLocation',
        'locationOverrideList',
        'draftLocationOverrideList',
        'isActive',
        'taxId',
        'autoApplyStatus',
      ],
    });

  const isApplyAutomation = autoApplyStatus !== ServiceChargeAutoApplyStatus.AUTO_APPLY_DISABLED;
  const { dirtyFields, defaultValues } = useFormState({ control: form.control });

  const draftLocationOverrideList = draftSourceLocationOverrideList || [];
  const locationOverrideList = sourceLocationOverrideList || [];

  const [allLocationIdList, taxIdList, taxMap, locationMap] = useSelector(
    selectAllLocationIdList,
    selectBusinessTaxes,
    taxMapBox,
    locationMapBox,
  );

  const locationOptionIdList = useMemo(() => {
    return allLocationIdList.toJSON();
  }, [allLocationIdList]);

  const taxOptionList = useMemo(() => {
    return taxIdList.toJSON().map((id) => {
      const tax = taxMap.mustGetItem(id);
      return {
        label: `${tax.taxName} (${tax.taxRate}%)`,
        value: Number(tax.id),
      };
    });
  }, [taxIdList, taxMap]);

  const currentLocationIdList = useMemo(() => {
    return isAllLocation
      ? allLocationIdList.toJSON().map((item) => item)
      : locationOverrideList.map((item) => item.businessId!);
  }, [isAllLocation, locationOverrideList, allLocationIdList]);

  const handleAddLocationOverride = useLatestCallback(() => {
    if (isAllLocation) {
      if (draftLocationOverrideList?.length === locationOptionIdList.length) {
        return;
      }
    }

    // 查找不在 all locations list 或者 locationOverrideList 里的 business
    const [findOneLocationId] = currentLocationIdList
      .filter((id) => {
        return !draftLocationOverrideList.some((item2) => id === item2.businessId);
      })
      .map((item) => item);

    if (findOneLocationId) {
      const newDraftLocationOverrideList = [...(draftLocationOverrideList || []), { businessId: findOneLocationId }];
      form.setValue('draftLocationOverrideList', newDraftLocationOverrideList);
    }
  });

  const showAddOverrideButton = draftLocationOverrideList?.length !== currentLocationIdList.length;
  // 是否展示 Also apply to unconfirmed upcoming appointments 的 checkbox
  // 只有在编辑模式下 price、tax、locationOverrideList 有变化的时候才展示
  const triggerRemove = useBool();
  const isServiceTaxIdChanged = useMemo(() => !isEqual(defaultValues?.taxId, taxId), [defaultValues?.taxId, taxId]);
  /**
   * 弹窗逻辑：
   * 1. 删除service charge：status为active时，才出弹窗
   * 2. 新增service charge：status为active 并且 auto apply 为on时才出弹窗
   *
   * 【以下几点前提：price、tax、business、auto apply有改动】：
   * 3. status 总是为inactive时：不出弹窗
   * 4. status 总为active时：必出弹窗
   * 5. status 从 active切换为inactive时：必出弹窗
   * 6. status从inactive切换active时：auto apply 为 on 才出弹窗
   *
   */
  useEffect(() => {
    // 新建的 service charge， status 为 active 并且 auto apply 为 on 时才出弹窗
    if (!isEdit) {
      if (isApplyAutomation && isActive) {
        setApplyUpcomingModalConfig?.({ visible: true, action: 'update' });
      } else {
        setApplyUpcomingModalConfig?.({ visible: false });
      }
    } else if (
      isServiceTaxIdChanged ||
      dirtyFields.price ||
      dirtyFields.draftLocationOverrideList ||
      dirtyFields.locationOverrideList ||
      dirtyFields.autoApplyStatus ||
      dirtyFields.isActive ||
      triggerRemove.value
    ) {
      //  status没有状态切换，总是为inactive时：不出弹窗
      if (!dirtyFields.isActive && !isActive) {
        setApplyUpcomingModalConfig?.({ visible: false });
      } else if (!dirtyFields.isActive && isActive) {
        //status 总为active时：必出弹窗
        // 如果 auto apply 从 on 到 off，出 remove 弹窗
        if (dirtyFields.autoApplyStatus && !isApplyAutomation) {
          setApplyUpcomingModalConfig?.({ visible: true, action: 'delete' });
        } else {
          setApplyUpcomingModalConfig?.({ visible: true, action: 'update' });
        }
      } else if (dirtyFields.isActive && !isActive) {
        // status 从 active切换为inactive时：必出弹窗,且为 remove 弹窗
        setApplyUpcomingModalConfig?.({ visible: true, action: 'delete' });
      } else if (dirtyFields.isActive && isActive && isApplyAutomation) {
        // status从inactive切换active时：auto apply 为 on 才出弹窗
        setApplyUpcomingModalConfig?.({ visible: true, action: 'update' });
      } else {
        setApplyUpcomingModalConfig?.({ visible: false });
      }
    } else {
      setApplyUpcomingModalConfig?.({ visible: false });
    }
  }, [
    isEdit,
    isServiceTaxIdChanged,
    dirtyFields.price,
    dirtyFields.draftLocationOverrideList,
    dirtyFields.locationOverrideList,
    dirtyFields.autoApplyStatus,
    dirtyFields.isActive,
    draftSourceLocationOverrideList,
    triggerRemove.value,
  ]);

  const handleDisabledSelectValue = useLatestCallback((e: string) => {
    // 如果已经在 location override 里选中了，就不能再选中
    return draftLocationOverrideList.findIndex((item) => item.businessId === e) !== -1;
  });

  /**
   * 根据选中的 location list 来生成后续需要 override 的区域里的 location selector 的 options
   */
  const filteredLocationOverrideList = useMemo(() => {
    return currentLocationIdList.map((item) => {
      const location = locationMap.mustGetItem(`${item}`);
      return {
        label: location.name,
        value: location.id,
        isDisabled: handleDisabledSelectValue(location.id),
      };
    });
  }, [currentLocationIdList, locationMap]);

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-[24px] moe-mt-[16px]">
      <SelectBusinessFormItem form={form} header={<Heading size="3">Business</Heading>} />
      <Heading size="3">Price</Heading>
      <PriceFormItem business={business} isDisabledForTax={isDisabledForPriceAndTax} taxOptionList={taxOptionList} />
      <WithMultiLocation scene="all">
        <Condition if={draftLocationOverrideList.length > 0}>
          {draftLocationOverrideList.map((item, index) => {
            return (
              <div
                key={item.businessId || index}
                className="moe-flex moe-flex-col moe-w-full moe-gap-y-[16px] moe-p-spacing-s moe-rounded-m moe-bg-neutral-sunken-0"
              >
                <div className="moe-flex moe-justify-between moe-pb-spacing-s moe-border-0 moe-border-solid moe-border-b moe-border-b-divider">
                  <Heading size="5" className="moe-mb-0">
                    Variation {index + 1}
                  </Heading>
                  <MinorTrashOutlined
                    className="moe-cursor-pointer"
                    onClick={() => {
                      // 当draftLocationOverrideList 删除后为空时，表单认为设置的新值和字段的初始值相同,即isDirty为false。此处需要另外记一个状态判断进行了删除操作
                      triggerRemove.open();
                      form.setValue(
                        'draftLocationOverrideList',
                        draftLocationOverrideList.filter((_, i) => i !== index),
                        { shouldDirty: true },
                      );
                    }}
                  />
                </div>
                <div className="moe-flex moe-justify-between moe-gap-x-[24px]">
                  <div className="moe-w-full">
                    <Form.Item name={`draftLocationOverrideList.${index}.businessId`} label="Business">
                      <Select options={filteredLocationOverrideList} />
                    </Form.Item>
                  </div>
                  <div className="moe-w-full">
                    <Form.Item
                      name={`draftLocationOverrideList.${index}.price`}
                      label="Price override"
                      transformer={{
                        // 数组类型的表单，在某项被删除后，再次添加，可能会被 useController 缓存上一次的 value，这里需要用 watch 的正确值填入
                        input: () => {
                          return form.watch(`draftLocationOverrideList.${index}.price`);
                        },
                      }}
                    >
                      <Input.Number
                        placeholder="Add the price"
                        minValue={0}
                        precision={2}
                        maxLength={10}
                        isDisabled={isDisabledForPriceAndTax}
                        prefix={<span className="moe-leading-[20px]">{business.printCurrency()}</span>}
                      />
                    </Form.Item>
                  </div>
                </div>
                <div className="moe-flex moe-justify-between moe-gap-x-[24px]">
                  <div className="moe-w-[calc(50%-12px)]">
                    <Form.Item
                      name={`draftLocationOverrideList.${index}.taxId`}
                      label="Tax override"
                      transformer={{
                        // 数组类型的表单，在某项被删除后，再次添加，可能会被 useController 缓存上一次的 value，这里需要用 watch 的正确值填入
                        input: () => {
                          return form.watch(`draftLocationOverrideList.${index}.taxId`);
                        },
                      }}
                    >
                      <Select options={taxOptionList} isDisabled={isDisabledForPriceAndTax} />
                    </Form.Item>
                  </div>
                </div>
              </div>
            );
          })}
        </Condition>
      </WithMultiLocation>
      <WithMultiLocation scene="all">
        <Condition if={showAddOverrideButton}>
          <div className="moe-flex moe-flex-col moe-justify-start">
            <div className="moe-flex moe-flex-start">
              <Button
                variant="tertiary-legacy"
                icon={<MajorPlusOutlined />}
                onPress={handleAddLocationOverride}
                align="start"
                isDisabled={isDisabledForPriceAndTax}
              >
                Override by business
              </Button>
            </div>
          </div>
        </Condition>
      </WithMultiLocation>
    </div>
  );
});

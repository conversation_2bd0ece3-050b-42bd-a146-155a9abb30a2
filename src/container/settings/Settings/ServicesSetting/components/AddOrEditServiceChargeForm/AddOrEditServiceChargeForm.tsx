import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { ServiceChargeApplyType } from '@moego/api-web/moego/models/order/v1/service_charge_model';
import { Form, Heading, Input, Radio, RadioGroup, booleanToStringify, type useForm } from '@moego/ui';
import React, { memo } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { GrowthBookFeatureList } from '../../../../../../utils/growthBook/growthBook.config';
import { LONG_TEXT_LENGTH } from '../../../consts';
import type { TransformedServiceChargeRecord } from '../../utils/serviceOverrideLocationHandler';
import { AutomationForm, type AutomationFormProps } from './AutomationForm';
import { LocationOverrideForm } from './LocationOverrideForm';
import { useServiceStaffPermissions } from '../AddOrEditComponents/hooks/useServiceStaffPermissions';

export interface AddOrEditServiceChargeFormProps extends Omit<AutomationFormProps, 'form'> {
  form: ReturnType<typeof useForm<Partial<TransformedServiceChargeRecord>>>;
  isEdit: boolean;
  nameInputProps?: {
    label: string;
    placeholder: string;
  };
  descriptionInputProps?: {
    label: string;
    placeholder: string;
  };
}

export const AddOrEditServiceChargeForm = memo(function AddOrEditServiceForm(props: AddOrEditServiceChargeFormProps) {
  const { form, isEdit, nameInputProps, descriptionInputProps } = props;
  const isEnable = useFeatureIsOn(GrowthBookFeatureList.EnableServiceChargePricingUnit);
  const { isDisabledForRemains } = useServiceStaffPermissions(isEdit);

  return (
    <div className="moe-flex moe-flex-col moe-w-full moe-pb-[40px]">
      <Form form={form} footer={null}>
        <Heading size="3">Basic info</Heading>
        <Form.Item
          name="name"
          label={nameInputProps?.label || 'Service charge name'}
          rules={{
            required: nameInputProps?.placeholder
              ? `${nameInputProps.placeholder} is required`
              : 'Service charge name is required',
          }}
        >
          <Input
            isRequired
            className="moe-w-full"
            placeholder={nameInputProps?.placeholder || 'Service charge name'}
            maxLength={70}
            isDisabled={isDisabledForRemains}
          />
        </Form.Item>
        <Form.Item name="description" label={descriptionInputProps?.label || 'Description'}>
          <Input.TextArea
            className="moe-w-full"
            placeholder={descriptionInputProps?.placeholder || 'Input the service description'}
            maxLength={LONG_TEXT_LENGTH}
            isDisabled={isDisabledForRemains}
          />
        </Form.Item>
        <Form.Item name="isActive" label="Status" transformer={booleanToStringify}>
          <RadioGroup isRequired orientation="horizontal" isDisabled={isDisabledForRemains}>
            <Radio value={`true`}>Active</Radio>
            <Radio value={`false`}>Inactive</Radio>
          </RadioGroup>
        </Form.Item>
        <Form.Item name="applyType" label="Applicable to">
          <RadioGroup isRequired orientation="horizontal" isDisabled={isDisabledForRemains}>
            <Radio value={ServiceChargeApplyType.PER_APPOINTMENT}>Each appointment</Radio>
            <Radio value={ServiceChargeApplyType.PER_PET}>Each pet</Radio>
            <Condition if={isEnable}>
              <Radio value={ServiceChargeApplyType.PER_PRICING_UNIT}>Each pricing unit</Radio>
            </Condition>
          </RadioGroup>
        </Form.Item>
        <LocationOverrideForm form={form} isEdit={isEdit} />
        <AutomationForm form={form} isEdit={isEdit} />
        <div className="moe-w-full moe-mb-[20px]"></div>
      </Form>
    </div>
  );
});

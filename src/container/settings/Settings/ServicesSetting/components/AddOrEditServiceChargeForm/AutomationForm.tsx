import { ServiceChargeAutoApplyStatus } from '@moego/api-web/moego/models/order/v1/service_charge_model';
import { Form, Heading, Radio, RadioGroup, LegacySelect as Select, type useForm, useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { selectBDFeatureEnable } from '../../../../../../store/company/company.selectors';
import { selectSceneCareTypeAsOptions } from '../../../../../../store/careType/careType.selectors';
import { Scene } from '../../../../../../store/service/scene.enum';
import { type TransformedServiceChargeRecord } from '../../utils/serviceOverrideLocationHandler';
import { useServiceStaffPermissions } from '../AddOrEditComponents/hooks/useServiceStaffPermissions';

export interface AutomationFormProps {
  form: ReturnType<typeof useForm<Partial<TransformedServiceChargeRecord>>>;
  isEdit: boolean;
}

export const AutomationForm = memo((props: AutomationFormProps) => {
  const { form, isEdit } = props;
  const { isDisabledForRemains } = useServiceStaffPermissions(isEdit);
  const [isBD] = useSelector(selectBDFeatureEnable);
  const [autoApplyStatus] = useWatch({
    control: form.control,
    name: ['autoApplyStatus'],
  });
  const [autoAppliedCareTypes] = useSelector(selectSceneCareTypeAsOptions(Scene.BDAutoApply));

  return (
    <>
      <Heading size="3" className="moe-mt-8px-200">
        Automation
      </Heading>
      <Form.Item name="autoApplyStatus" label="Auto-apply during checkout">
        <RadioGroup isDisabled={isDisabledForRemains} isRequired>
          <Radio value={ServiceChargeAutoApplyStatus.AUTO_APPLY_DISABLED}>No auto-apply</Radio>
          <Radio value={ServiceChargeAutoApplyStatus.AUTO_APPLY_ENABLED}>Auto-apply</Radio>
        </RadioGroup>
      </Form.Item>

      <Condition if={autoApplyStatus === ServiceChargeAutoApplyStatus.AUTO_APPLY_ENABLED && isBD}>
        <div className="moe-bg-neutral-sunken-0 moe-rounded-[16px] moe-p-spacing-s moe-flex moe-flex-col moe-gap-y-s">
          <Form.Item
            label="Applicable care types"
            name="serviceItemTypes"
            rules={{ required: 'Applicable care types can’t be empty' }}
          >
            <Select options={autoAppliedCareTypes} isRequired isMultiple isDisabled={isDisabledForRemains} />
          </Form.Item>
        </div>
      </Condition>
    </>
  );
});

import { Heading } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useImperativeHandle, useState } from 'react';
import { Condition } from '../../../../../components/Condition';
import { currentAccountIdBox } from '../../../../../store/account/account.boxes';
import { serviceCategoryMapBox } from '../../../../../store/service/category.boxes';
import { companyServiceMapBox } from '../../../../../store/service/service.boxes';
import { selectCategoryServices, selectInactiveCategoryServices } from '../../../../../store/service/service.selectors';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { memoForwardRef } from '../../../../../utils/react';
import { useMoveList } from '../../components/DraggableList/utils';
import { ServiceSortFormRow } from './ServiceSortFormRow';
import { type ServiceItemType, type ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { DndProvider } from 'react-dnd';
import { dndManager } from '../../../../../utils/dndManager';

export interface ServiceSortFormProps {
  isInactive: boolean;
  categoryId: string;
  serviceType: ServiceType;
  serviceItemType: ServiceItemType;
}

export interface ServiceSortFormRef {
  getData: () => number[];
}

export const ServiceSortForm = memoForwardRef<ServiceSortFormRef, ServiceSortFormProps>(function ServiceSortForm(
  props: ServiceSortFormProps,
  ref,
) {
  const { isInactive, categoryId, serviceType, serviceItemType } = props;
  const type = `${serviceType}-${serviceItemType}`;
  const [currentAccountId] = useSelector(currentAccountIdBox);
  const [category, serviceIds, serviceMap] = useSelector(
    serviceCategoryMapBox.mustGetItem(Number(categoryId)),
    !isInactive
      ? selectCategoryServices(type, Number(categoryId), currentAccountId)
      : selectInactiveCategoryServices(type, currentAccountId),
    companyServiceMapBox,
  );
  const [localServiceIdList, setLocalServiceIdList] = useState(serviceIds.toJSON());
  const handleMove = useMoveList(localServiceIdList, setLocalServiceIdList);

  // 这个是没用的一个 function，不过为了满足 types 定义，先留着
  const handleDrop = useLatestCallback(() => {});

  useImperativeHandle(ref, () => ({
    getData() {
      return localServiceIdList;
    },
  }));

  const hasService = localServiceIdList.length > 0;
  return (
    <div className="moe-flex moe-flex-col moe-h-[100%] moe-font-manrope">
      <div className="moe-flex moe-mb-[24px]">
        <Heading size="4">{category.name || 'Uncategorized'}</Heading>
      </div>
      <Condition if={hasService}>
        <DndProvider manager={dndManager}>
          <div className="moe-flex moe-flex-col moe-gap-y-[16px] moe-flex-1 moe-overflow-y-auto">
            {localServiceIdList.map((id, index) => {
              const item = serviceMap.mustGetItem(id);
              return (
                <ServiceSortFormRow
                  name={item.name}
                  key={item.serviceId}
                  index={index}
                  onDrop={handleDrop}
                  onMove={handleMove}
                />
              );
            })}
          </div>
        </DndProvider>
      </Condition>
    </div>
  );
});

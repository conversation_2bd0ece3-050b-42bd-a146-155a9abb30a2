import { Drawer } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { memo, useRef } from 'react';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { ServiceSortForm, type ServiceSortFormRef } from './ServiceSortForm';
import { sortCompanyServices } from '../../../../../store/service/actions/private/companyService.actions';
import { useModal } from '../../../../../components/Modal/useModal';
import { type ServiceItemType, type ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';

export interface ServiceSortDrawerProps {
  isInactive: boolean;
  onClose: () => void;
  categoryId?: string;
  typeName: string;
  serviceType: ServiceType;
  serviceItemType: ServiceItemType;
}

export const ServiceSortDrawer = memo(function ServiceSortDrawer(props: ServiceSortDrawerProps) {
  const { isInactive, categoryId = '', onClose, typeName, serviceType, serviceItemType } = props;
  const dispatch = useDispatch();
  const formRef = useRef<ServiceSortFormRef>(null);
  const handleSave = useSerialCallback(async () => {
    const data = formRef.current?.getData();
    if (data?.length) {
      await dispatch(sortCompanyServices(data.map(String), Number(categoryId), `${serviceType}-${serviceItemType}`));
    }
  });
  return (
    <Drawer
      isOpen
      isBlockScroll
      isDismissable
      showCloseButton
      onClose={onClose}
      onConfirm={handleSave}
      size="m"
      title={`Sort ${typeName}`}
    >
      <ServiceSortForm
        serviceType={serviceType}
        serviceItemType={serviceItemType}
        categoryId={categoryId}
        isInactive={isInactive}
        ref={formRef}
      />
    </Drawer>
  );
});

export const useServiceSortDrawer = () => useModal(ServiceSortDrawer);

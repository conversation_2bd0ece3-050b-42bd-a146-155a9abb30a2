import { type SuggestedAddress } from '@moego/api-web/moego/models/map/v1/map_models';
import { type LocationModel } from '@moego/api-web/moego/models/organization/v1/location_models';
import { Avatar, Button, Form, Input, type UploadRequest, type useForm, useFormState, useWatch } from '@moego/ui';
import { useSerialCallback } from '@moego/tools';
import { useDispatch, useSelector } from 'amos';
import React, { memo } from 'react';
import isURL from 'validator/lib/isURL';
import { AddressInputV2 } from '../../../../../../components/AddressForm/AddressInputV2';
import { getAddress } from '../../../../../../components/AddressForm/getAddress';
import { Condition } from '../../../../../../components/Condition';
import { toastApi } from '../../../../../../components/Toast/Toast';
import { Upload } from '../../../../../../components/Upload/Upload';
import { RE_INPUT_NUMBER, stripNonNumericInput } from '../../../../../../components/form/NumberInput';
import { updateLocation } from '../../../../../../store/business/location.actions';
import { formatLocationAddress } from '../../../../../../store/business/location.boxes';
import { selectCurrentCompany } from '../../../../../../store/company/company.selectors';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { SHORT_TEXT_LENGTH } from '../../../consts';
import { SectionCard } from './SectionCard';

export interface BusinessBasicInfoProps {
  form: ReturnType<typeof useForm<LocationModel>>;
}

export const BusinessBasicInfo = memo(function BusinessBasicInfo(props: BusinessBasicInfoProps) {
  const dispatch = useDispatch();
  const [company] = useSelector(selectCurrentCompany());
  const { form } = props;

  const { control } = form;
  const { isDirty, isValid } = useFormState({
    control,
  });

  const [address, twilioPhoneNumber] = useWatch({
    control,
    name: ['address', 'twilioPhoneNumber'],
  });

  // 展示全量address info
  const fullAddress = formatLocationAddress(address);

  const avatarPath = form.watch('avatarPath');

  const handleConfirm = useSerialCallback(async () => {
    await form.handleSubmit(async (validRes) => {
      const { address, ...others } = validRes;
      if (!address?.address1) {
        toastApi.error('Please select an address');
        return;
      }
      await dispatch(
        updateLocation({
          location: {
            address: {
              ...address,
              address2: '',
            },
            ...others,
          },
        }),
      );
      toastApi.success('Business info updated!');
    })();
  });

  const handleUploadFile: UploadRequest = useLatestCallback(({ file, onSuccess, onError, onProgress }) => {
    Upload.defaultUploadHandler(
      0,
      file,
      (progress) => onProgress(progress),
      (url) => onSuccess({ url }),
      onError,
    );
  });

  const isDisabled = !(isDirty && isValid && isURL(avatarPath || ''));

  const handleSelectAddress = useLatestCallback(async (address: SuggestedAddress) => {
    const addressInfo = await getAddress(address);
    form.setValue('address', addressInfo, { shouldDirty: true });
  });

  return (
    <SectionCard title="Basic info">
      <Form form={form} footer={null}>
        <Form.Item
          name="avatarPath"
          label={<div className="moe-text-nowrap">Business logo</div>}
          rules={{
            required: true,
          }}
        >
          <Avatar.Upload src={avatarPath} customRequest={handleUploadFile} isRequired className="moe-w-[72px]" />
        </Form.Item>
        <Form.Item
          name="name"
          label="Business name"
          rules={{
            required: true,
            validate: (v: string) => {
              if (!v || v.trim() === '') {
                return 'Required';
              }
              return true;
            },
          }}
        >
          <Input isRequired maxLength={SHORT_TEXT_LENGTH} />
        </Form.Item>
        <Form.Item
          name="contactEmail"
          rules={{
            required: true,
          }}
        >
          <Input
            label="Contact email"
            isRequired
            tooltip="The business contact email will be displayed at the bottom of the receipt and in the upcoming appointment link sent to your client."
          />
        </Form.Item>
        <Form.Item name="contactPhoneNumber">
          <Input
            label="Phone number"
            onChange={(e) => {
              const value = stripNonNumericInput(e);
              if (!!value && !RE_INPUT_NUMBER.test(value)) {
                throw Error('Please enter a valid phone number');
              }
            }}
          />
        </Form.Item>
        <div>
          {/* 这里不直接用 form.item，是因为 form item 的一些默认行为会让 address 变成 undefined */}
          <Form.Label label="Address" isRequired />
          <AddressInputV2 value={fullAddress} onSelectAddress={handleSelectAddress} />
        </div>
        <div>
          {/* 纯展示 */}
          <Input label="Country" isRequired isDisabled value={company.country} />
        </div>
        <Condition if={twilioPhoneNumber}>
          <Form.Item label="MoeGo business phone number" name="twilioPhoneNumber">
            <Input
              isDisabled
              tooltip="Business phone number refers to the number from which you text your customers in MoeGo. You will get one number for each business."
            />
          </Form.Item>
        </Condition>
      </Form>
      <Button
        isDisabled={isDisabled}
        className="moe-mt-[24px]"
        onPress={handleConfirm}
        isLoading={handleConfirm.isBusy()}
      >
        Save
      </Button>
    </SectionCard>
  );
});

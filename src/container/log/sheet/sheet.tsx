import { generateUUID } from '@moego/pagespy';

import { useRequest } from 'ahooks';
import dayjs, { type Dayjs } from 'dayjs';
import React, { memo, useCallback, useEffect, useMemo } from 'react';
import { toastApi } from '../../../components/Toast/Toast';
import { PATH_LOG_SHEET } from '../../../router/paths';
import { useRouteQueryV2 } from '../../../utils/RoutePath';
import type { TopMessage, UploadLogsInitMessage } from './workerHelper';
import { sleep } from '@moego/finance-utils';
import { T_SECOND } from '@moego/reporting';
import { DataDogActionName, logger, LoggerModule } from '../../../utils/logger';
import { datadogRum } from '@datadog/browser-rum';
import { LogUploadCore, type SumbitSheetParam } from '../shared/LogUploadCore';

export interface SheetProps {}

export interface UploadLogsParams {
  start: number;
  end: number;
  onProgress?: (tabId: string, progress: number) => void;
  onInit?: (data: UploadLogsInitMessage['data']) => void;
  from?: 'intercom' | 'manual';
}

export async function uploadLogsFromParent({ start, end, onInit, onProgress, from = 'intercom' }: UploadLogsParams) {
  const topUUID = generateUUID();
  (window.top ?? window).postMessage(
    {
      type: 'uploadLogs',
      id: topUUID,
      params: {
        start,
        end,
      },
      from,
    },
    '*',
  );

  let tabs: UploadLogsInitMessage['data'] = [];
  const params: SumbitSheetParam[] = [];
  return new Promise<SumbitSheetParam[]>((resolve, reject) => {
    const onMessage = (e: {
      data: TopMessage;
    }) => {
      try {
        if (e.data.id === topUUID) {
          if (e.data.type === 'uploadLogsInit') {
            onInit?.(e.data.data);
            tabs = e.data.data;
            logger.get(LoggerModule.PAGESPY).info('uploadLogsInit', {
              tabs,
              start,
              end,
            });
            if (tabs.length === 0) {
              window.removeEventListener('message', onMessage);
              resolve([]);
              return;
            }
            return;
          }
          if (e.data.type === 'uploadLogsProgress') {
            const data = e.data.data;
            onProgress?.(data.tabId, data.progress);
            if (data.url) {
              const tab = tabs.find((item) => item.tabId === data.tabId);
              const { meta } = tab || {};
              params.push({
                url: data.url,
                ...meta,
              });
            }
            if (params.length === tabs.length) {
              window.removeEventListener('message', onMessage);
              resolve(params);
              return;
            }
          }
          if (e.data.error) {
            reject(e.data.error);
            return;
          }
        }
      } catch (e) {
        reject(e);
      }
    };
    window.addEventListener('message', onMessage);
  });
}

export const SheetInner: React.FC<SheetProps> = () => {
  const sheetLibrary = (
    window as unknown as {
      INTERCOM_MESSENGER_SHEET_LIBRARY: {
        submitSheet: (params: any) => Promise<void>;
      };
    }
  ).INTERCOM_MESSENGER_SHEET_LIBRARY;
  const query = useRouteQueryV2(PATH_LOG_SHEET);

  const formDefaultValues = useMemo(() => {
    const { startTime, endTime } = query;

    const startTimeInt = isNaN(Number(startTime)) ? 0 : Number(startTime);
    const endTimeInt = isNaN(Number(endTime)) ? 0 : Number(endTime);

    const start = startTimeInt ? dayjs(startTimeInt) : dayjs().add(-1, 'hour');
    const end = endTimeInt ? dayjs(+endTimeInt) : dayjs();

    return {
      startDate: start,
      startTime: start,
      endDate: end,
      endTime: end,
    };
  }, [query]);

  const handleTimeChange = useCallback(
    (params: {
      startDate: Dayjs;
      startTime: Dayjs;
      endDate: Dayjs;
      endTime: Dayjs;
    }) => {
      const startDateTime = params.startDate.format('YYYY-MM-DD') + ' ' + params.startTime.format('HH:mm:ss');
      const endDateTime = params.endDate.format('YYYY-MM-DD') + ' ' + params.endTime.format('HH:mm:ss');
      const start = new Date(startDateTime).getTime();
      const end = new Date(endDateTime).getTime();

      // 更新 URL 参数
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.set('startTime', start.toString());
      newUrl.searchParams.set('endTime', end.toString());
      window.history.replaceState({}, '', newUrl.toString());
    },
    [],
  );

  if (!sheetLibrary) {
    throw Promise.resolve();
  }

  const handleUploadFunction = async (params: {
    start: number;
    end: number;
    onProgress?: (tabId: string, progress: number) => void;
    onInit?: (data: UploadLogsInitMessage['data']) => void;
  }) => {
    const task = datadogRum.startDurationVital(DataDogActionName.UPLOAD_PAGESPY_LOGS);

    const urls = await uploadLogsFromParent({
      start: params.start,
      end: params.end,
      onInit: params.onInit,
      onProgress: params.onProgress,
    });

    task &&
      datadogRum.stopDurationVital(task, {
        context: {
          start: params.start,
          end: params.end,
        },
      });

    if (urls.length === 0) {
      toastApi.neutral(
        'We could not find any logs for the given time range. Please try again with a different time range.',
      );
      return [];
    }

    return urls;
  };

  const handleUploadComplete = async (logs: SumbitSheetParam[]) => {
    try {
      sheetLibrary.submitSheet(logs);
      await sleep(T_SECOND);
    } catch (e) {
      console.error(e);
      logger.get(LoggerModule.PAGESPY).info('uploadLogsError', {
        error: e,
        logs,
      });
      toastApi.error('This is a bug from our system. Please try again later.');
    }
  };

  return (
    <LogUploadCore
      enableZipDownload={false}
      uploadFunction={handleUploadFunction}
      onUploadComplete={handleUploadComplete}
      formDefaultValues={formDefaultValues}
      onTimeChange={handleTimeChange}
    />
  );
};

function loadScriptAsync(src: string) {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = src;
    script.onload = resolve;
    script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
    document.head.appendChild(script);
  });
}

export const Sheet = memo(() => {
  const { loading, run } = useRequest(
    () => loadScriptAsync('https://s3.amazonaws.com/intercom-sheets.com/messenger-sheet-library.latest.js'),
    {
      manual: true,
    },
  );

  useEffect(run, []);

  if (loading) {
    throw Promise.resolve();
  }

  return <SheetInner />;
});

import React, { useCallback, useMemo } from 'react';
import { LogUploadCore } from '../shared/LogUploadCore';
import type { UploadLogsInitMessage } from '../sheet/workerHelper';
import { uploadLogsFromParent } from '../sheet/sheet';
import { useHistory, useLocation } from 'react-router-dom';
import dayjs, { type Dayjs } from 'dayjs';

export const LogUpload: React.FC = () => {
  const location = useLocation();
  const history = useHistory();

  // 解析 URL 查询参数
  const searchParams = useMemo(() => {
    return new URLSearchParams(location.search);
  }, [location.search]);

  // 从 URL 获取时间戳
  const startTimeFromUrl = searchParams.get('startTime');
  const endTimeFromUrl = searchParams.get('endTime');

  // 设置表单默认值
  const formDefaultValues = useMemo(() => {
    const defaultValues: {
      startDate: Dayjs;
      startTime: Dayjs;
      endDate: Dayjs;
      endTime: Dayjs;
    } = {
      startDate: dayjs().add(-1, 'hour'),
      startTime: dayjs().add(-1, 'hour'),
      endDate: dayjs(),
      endTime: dayjs(),
    };

    if (startTimeFromUrl) {
      defaultValues.startTime = dayjs(parseInt(startTimeFromUrl));
      defaultValues.startDate = dayjs(parseInt(startTimeFromUrl));
    }
    if (endTimeFromUrl) {
      defaultValues.endTime = dayjs(parseInt(endTimeFromUrl));
      defaultValues.endDate = dayjs(parseInt(endTimeFromUrl));
    }

    return defaultValues;
  }, [startTimeFromUrl, endTimeFromUrl]);

  const handleTimeChange = useCallback(
    (params: {
      startDate: Dayjs;
      startTime: Dayjs;
      endDate: Dayjs;
      endTime: Dayjs;
    }) => {
      const startDateTime = params.startDate.format('YYYY-MM-DD') + ' ' + params.startTime.format('HH:mm:ss');
      const endDateTime = params.endDate.format('YYYY-MM-DD') + ' ' + params.endTime.format('HH:mm:ss');
      const newSearchParams = new URLSearchParams(location.search);

      if (startDateTime) {
        newSearchParams.set('startTime', new Date(startDateTime).getTime().toString());
      } else {
        newSearchParams.delete('startTime');
      }

      if (endDateTime) {
        newSearchParams.set('endTime', new Date(endDateTime).getTime().toString());
      } else {
        newSearchParams.delete('endTime');
      }

      // 更新 URL

      history.replace({
        pathname: location.pathname,
        search: newSearchParams.toString(),
      });
    },
    [location.pathname, location.search, history],
  );

  const handleUploadFunction = useCallback(
    async (params: {
      start: number;
      end: number;
      onProgress?: (tabId: string, progress: number) => void;
      onInit?: (data: UploadLogsInitMessage['data']) => void;
    }) => {
      console.log('urls', params);
      const urls = await uploadLogsFromParent({
        start: params.start,
        end: params.end,
        onInit: params.onInit,
        onProgress: params.onProgress,
        from: 'manual',
      });

      return urls;
    },
    [],
  );

  return (
    <div className="moe-min-h-screen moe-bg-gray-50 moe-py-8">
      <div className="moe-max-w-2xl moe-mx-auto moe-bg-white moe-rounded-lg moe-shadow-sm moe-p-8">
        <div className="moe-text-center moe-mb-8">
          <h1 className="moe-text-2xl moe-font-bold moe-text-gray-900 moe-mb-2">Log Upload</h1>
          <p className="moe-text-gray-600">
            Upload and download your application logs for debugging and analysis. Select a date range and optionally
            provide a description to help identify the logs.
          </p>
        </div>

        <LogUploadCore
          enableZipDownload={true}
          uploadFunction={handleUploadFunction}
          formDefaultValues={formDefaultValues}
          onTimeChange={handleTimeChange}
        />
      </div>
    </div>
  );
};

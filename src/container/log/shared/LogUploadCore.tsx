import { useSerialCallback } from '@moego/tools';
import { Button, Checkbox, DatePicker, Form, FormProvider, Spin, TimePicker, useForm } from '@moego/ui';
import dayjs, { type Dayjs } from 'dayjs';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { toastApi } from '../../../components/Toast/Toast';
import { MOEGO_PRIVACY_URL, MOEGO_TERMS_URL } from '../../../config/host/const';
import type { UploadLogsInitMessage } from '../sheet/workerHelper';
import { mixEncrypt } from '../../../utils/files/encyypt';

export type SumbitSheetParam = {
  url: string;
  size?: number;
  startTime?: number;
  endTime?: number;
} & Partial<UploadLogsInitMessage['data'][0]['meta']>;

export interface LogUploadCoreProps {
  onUploadComplete?: (res: SumbitSheetParam[]) => void;
  enableZipDownload?: boolean;
  uploadFunction?: (params: {
    start: number;
    end: number;
    onProgress?: (tabId: string, progress: number) => void;
    onInit?: (data: UploadLogsInitMessage['data']) => void;
  }) => Promise<SumbitSheetParam[]>;
  formDefaultValues?: {
    startDate: Dayjs;
    startTime: Dayjs;
    endDate: Dayjs;
    endTime: Dayjs;
  };
  onTimeChange?: (params: {
    startDate: Dayjs;
    startTime: Dayjs;
    endDate: Dayjs;
    endTime: Dayjs;
  }) => void;
}

export const LogUploadCore: React.FC<LogUploadCoreProps> = ({
  onUploadComplete,
  enableZipDownload = false,
  uploadFunction,
  formDefaultValues,
  onTimeChange,
}) => {
  const defaultValues = formDefaultValues || {
    startDate: dayjs().add(-1, 'hour'),
    startTime: dayjs().add(-1, 'hour'),
    endDate: dayjs(),
    endTime: dayjs(),
    privacyConsent: false,
  };

  const form = useForm<{
    startDate: Dayjs;
    startTime: Dayjs;
    endDate: Dayjs;
    endTime: Dayjs;
    privacyConsent: boolean;
  }>({
    defaultValues,
  });

  const privacyConsent = form.watch('privacyConsent');
  const [uploadedLogs, setUploadedLogs] = useState<SumbitSheetParam[]>([]);
  const [tabs, setTabs] = useState<UploadLogsInitMessage['data']>([]);
  const totalSize = useMemo(() => tabs.reduce((acc, item) => acc + item.meta.size, 0), [tabs]);
  const [tabProgress, setTabProgress] = useState<Record<string, number>>({});

  const progress = useMemo(() => {
    if (tabs.length === 0 || totalSize === 0) {
      return 0;
    }

    return tabs.reduce((acc, tab) => {
      const tabId = tab.tabId;
      const tabSize = tab.meta.size;
      const tabProgressValue = tabProgress[tabId] || 0;

      return acc + tabProgressValue * (tabSize / totalSize);
    }, 0);
  }, [tabs, tabProgress, totalSize]);

  const handleProgress = (tabId: string, progress: number) => {
    setTabProgress((prev) => ({
      ...prev,
      [tabId]: progress,
    }));
  };

  const handleInit = (data: UploadLogsInitMessage['data']) => {
    setTabs(data);
  };

  useEffect(() => {
    const subscription = form.watch((values) => {
      setTabs([]);
      setTabProgress({});
      // 触发时间变化回调
      if (onTimeChange && values.startDate && values.startTime && values.endDate && values.endTime) {
        onTimeChange({
          startDate: values.startDate as Dayjs,
          startTime: values.startTime as Dayjs,
          endDate: values.endDate as Dayjs,
          endTime: values.endTime as Dayjs,
        });
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  const generateLogAndDownload = useCallback(async () => {
    if (uploadedLogs.length === 0) {
      toastApi.error('No logs available for download');
      return;
    }

    try {
      const values = form.getValues();
      const { startDate, startTime, endDate, endTime } = values;

      const startDateTime = startDate.format('YYYY-MM-DD') + ' ' + startTime.format('HH:mm:ss');
      const endDateTime = endDate.format('YYYY-MM-DD') + ' ' + endTime.format('HH:mm:ss');
      const timestamp = dayjs().format('YYYY-MM-DD-HH-mm-ss');

      const logData = {
        metadata: {
          startTime: startDateTime,
          endTime: endDateTime,
          generatedAt: new Date().toISOString(),
          totalLogs: uploadedLogs.length,
        },
        logs: uploadedLogs,
      };

      const jsonString = mixEncrypt(JSON.stringify(logData));
      const jsonFileName = `moego-logs-${timestamp}.log`;

      const blob = new Blob([jsonString], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = jsonFileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toastApi.success('Log file downloaded successfully');
    } catch (error) {
      console.error('Failed to generate log file:', error);
      toastApi.error('Failed to generate log file');
    }
  }, [uploadedLogs, form]);

  const submit = useSerialCallback(
    form.handleSubmit(async ({ startDate, startTime, endDate, endTime }) => {
      const startDateTime = startDate.format('YYYY-MM-DD') + ' ' + startTime.format('HH:mm:ss');
      const endDateTime = endDate.format('YYYY-MM-DD') + ' ' + endTime.format('HH:mm:ss');
      const start = new Date(startDateTime).getTime();
      const end = new Date(endDateTime).getTime();

      try {
        setUploadedLogs([]);

        if (uploadFunction) {
          const results = await uploadFunction({
            start,
            end,
            onProgress: handleProgress,
            onInit: handleInit,
          });

          if (results.length === 0) {
            toastApi.neutral('Sorry, no logs available for upload. Please contact us for more information.');
            return;
          }

          setUploadedLogs(results);
          onUploadComplete?.(results);

          if (enableZipDownload) {
            toastApi.success('Upload completed! You can now download the log archive.');
          } else {
            toastApi.success('The logs have been uploaded successfully.');
          }
        }
      } catch (error) {
        console.error('Upload failed:', error);
        toastApi.error('Upload failed. Please try again later.');
        setUploadedLogs([]);
      }
    }),
  );

  return (
    <div className="moe-m-8">
      <FormProvider {...form}>
        <form className="moe-p-8 moe-max-w-[420px] moe-gap-3 moe-flex moe-flex-col" onSubmit={submit}>
          <div className="moe-flex moe-flex-col moe-gap-4">
            <Form.Label label="Start time" />
            <div className="moe-flex moe-flex-col moe-gap-2">
              <Form.Item rules={{ required: true }} name="startDate" label="Date">
                <DatePicker />
              </Form.Item>
              <Form.Item rules={{ required: true }} name="startTime" label="Time">
                <TimePicker />
              </Form.Item>
            </div>
          </div>

          <div className="moe-flex moe-flex-col moe-gap-3">
            <Form.Label label="End time" />
            <div className="moe-flex moe-flex-col moe-gap-2">
              <Form.Item rules={{ required: true }} name="endDate" label="Date">
                <DatePicker />
              </Form.Item>
              <Form.Item rules={{ required: true }} name="endTime" label="Time">
                <TimePicker />
              </Form.Item>
            </div>
          </div>

          <div className="moe-flex moe-items-center moe-mb-2">
            <Form.Item name="privacyConsent" valuePropName="checked">
              <Checkbox className="moe-font-medium">
                <span className="moe-text-sm moe-text-[#666]">
                  {`With your consent, MoeGo may access recent activity logs stored in your browser to provide better support and enhance your experience. All data is handled in accordance with our `}{' '}
                  <a href={MOEGO_TERMS_URL} className="link">
                    Terms of Use
                  </a>
                  {' and '}
                  <a href={MOEGO_PRIVACY_URL} target="_blank" rel="noreferrer">
                    Privacy Policy
                  </a>
                </span>
              </Checkbox>
            </Form.Item>
          </div>

          <Button type="submit" className="moe-w-full" isDisabled={submit.isBusy() || !privacyConsent}>
            {(progress && progress !== 1) || submit.isBusy() ? (
              <>
                <Spin className="moe-mr-2" isLoading />
                <span>{`${Math.round(progress * 100)}%`}</span>
              </>
            ) : enableZipDownload ? (
              'Upload Logs'
            ) : (
              'Submit'
            )}
          </Button>

          {enableZipDownload && uploadedLogs.length > 0 && (
            <Button onPress={generateLogAndDownload} className="moe-w-full moe-mt-2">
              Download Log Archive
            </Button>
          )}
        </form>
      </FormProvider>
    </div>
  );
};

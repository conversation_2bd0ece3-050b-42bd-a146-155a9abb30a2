import { useDispatch, useSelector } from 'amos';
import { merge } from 'lodash';
import { type TicketFormType } from '../../../router/paths';
import { BusinessType } from '../../../store/business/business.options';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { printAddress } from '../../../store/customer/customerAddress.selectors';
import { openSmartSchedulingModal } from '../../../store/smartScheduling/actions/private/smartSchedulingStore.actions';
import {
  INITIAL_ADDING_CARD,
  type SmartSchedulingFormParams,
  type SmartSchedulingStartModalState,
} from '../../../store/smartScheduling/smartSchedulingStore.boxes';
import { flat } from '../../../store/utils/utils';
import { ReportActionName } from '../../../utils/reportType';
import { reportData } from '../../../utils/tracker';
import { type CalendarEvents, type ICalendarEvents } from '../../Calendar/Grooming/interfaces';
import { useChangeTicket } from './useChangeTicket';
import { useTicketStoreCtx } from './useTicketForm';

export function useSmartSchedule() {
  const [
    {
      colorCode,
      alertNotes,
      currentClient,
      ticketComments,
      ticketId,
      date: serviceDate,
      petsService,
      apiClientData,
      clientPets,
    },
  ] = useTicketStoreCtx();
  const dispatch = useDispatch();
  const { getTicketTotalPrice, getTicketDuration } = useChangeTicket();
  const [business] = useSelector(selectCurrentBusiness);

  /**
   * generate ticket card
   * only for smart scheduling
   */
  const convertTicketToCalendarCard = () => {
    const convertServices = () => {
      const servicesArray = petsService.map((petServices) => {
        return petServices.services.map((service) => ({
          // edit ticket 时 serviceColorCode 为 undefined
          serviceColorCode: service.colorCode || '#333',
          serviceName: service.name || service.serviceName,
          serviceTime: service.duration,
        }));
      });
      return flat(servicesArray) as any[];
    };

    const convertPets = () => {
      const petsArray = petsService.map((petServices) => {
        return clientPets.find((pet) => pet.petId === petServices.petId)!;
      });
      return petsArray;
    };
    const ticketCard: Partial<Omit<CalendarEvents, 'extendedProps'> & { extendedProps: Partial<ICalendarEvents> }> = {
      id: ticketId ? ticketId : undefined,
      extendedProps: {
        services: convertServices(),
        pets: convertPets(),
        serviceTime: getTicketDuration(),
        servicePrice: getTicketTotalPrice(),
        colorCode: colorCode,
        alertNotes: alertNotes,
        customerId: currentClient.customerId,
        customerFirstName: currentClient.firstName,
        customerLastName: currentClient.lastName,
        clientColor: currentClient.customerDetail?.clientColor,
        clientFullAddress: printAddress(currentClient.customerDetail?.address),
        city: currentClient.customerDetail?.address?.city,
        zipcode: currentClient.customerDetail?.address?.zipcode,
        ticketComments: ticketComments,
        ticketId: Number(ticketId),
      },
    };
    const calendarCard = merge({}, INITIAL_ADDING_CARD, ticketCard);
    return calendarCard;
  };

  const handleSmartScheduling = () => {
    reportData(ReportActionName.AdvancedEditSmartScheduling);
    // get smart scheduling params
    const serviceDuration = getTicketDuration();
    const customerAddress = apiClientData.customerDetail.address;
    const staffId = petsService[0].services[0].staffId;
    const smartSchedulingParams: Partial<SmartSchedulingFormParams> = {
      duration: serviceDuration,
      address: customerAddress,
      startDate: serviceDate,
      staffIdList: [staffId],
      // NOTE(icy): isMobileGrooming 应该被 businessMode 约束，否则后续判断有问题
      isMobileGrooming:
        business.appType !== BusinessType.GroomingSalon && !!customerAddress?.lat && !!customerAddress?.lng,
    };
    const ticketCard = convertTicketToCalendarCard();
    // 从 Calendar 跳转回来使用
    const presetPetAndServices: TicketFormType['petAndServices'] = petsService.map((petService) => {
      return {
        pet: petService.petId,
        // 过滤掉没有 serviceId 的 service
        serviceItem: petService.services
          .filter((service) => service.serviceId)
          .map((service) => {
            return {
              staffId,
              value: service.serviceId,
              price: service.price,
              duration: service.duration,
              type: service.type,
              name: service.name || service.serviceName,
              priceOverrideType: service.priceOverrideType,
              durationOverrideType: service.durationOverrideType,
            } as any;
          }),
      };
    });

    const petParamListForSS = petsService.map(({ petId, services }) => ({
      petId,
      serviceIds: services.map((service) => service.serviceId).filter(Boolean) as number[],
    }));

    const smartSchedulingModalOptions: Partial<SmartSchedulingStartModalState> = {
      isCreateOrEditTicket: true,
      addingCard: ticketCard,
      presetPetAndServices,
      petParamListForSS,
    };

    dispatch(openSmartSchedulingModal(smartSchedulingModalOptions, smartSchedulingParams));
  };

  return {
    handleSmartScheduling,
  };
}

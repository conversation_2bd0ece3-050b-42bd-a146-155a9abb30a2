import { useDispatch } from 'amos';
import { isEmpty } from 'lodash';
import { useMemo } from 'react';
import { useHistory } from 'react-router-dom';
import { isMultipleStaffService } from '../../../components/ServiceApplicablePicker/utils/isMultipleStaffService';
import { toastApi } from '../../../components/Toast/Toast';
import { http } from '../../../middleware/api';
import { type OpenApiDefinitions, type OpenApiModels } from '../../../openApi/schema';
import { type GroomingCalendarState, PATH_GROOMING_CALENDAR } from '../../../router/paths';
import { AutoMessageType } from '../../../store/autoMessage/autoMessage.boxes';
import {
  type UpdateRepeatTicketParams,
  createTicketAction,
  createWaitingTicketAction,
  editWaitingTicketAction,
  updateRepeatTicketAction,
  updateRepeatTicketAlertNotesAction,
  updateRepeatTicketColorCodeAction,
  updateTicketAction,
  updateTicketCommentsAction,
} from '../../../store/createTicket/createTicket.action';
import { isNormal } from '../../../store/utils/identifier';
import { globalEvent } from '../../../utils/events/events';
import { computeMinutes } from '../../../utils/utils';
import { useAgreement } from '../../Appt/modules/ApptDetailDrawer/hooks/useAgreement';
import { usePreAuthOnboarding } from '../../Calendar/Grooming/PreAuthForAppt/usePreAuthOnboarding';
import { useRedirect } from './useRedirect';
import { calcOperationServiceTime } from './useServiceDuration';
import { useTicketComputed } from './useTicketComputed';
import { useTicketFieldsEdit } from './useTicketFieldsEdit';
import { useTicketStoreCtx } from './useTicketForm';
import { useValidate } from './useValidate';

type CreateApptReqParams = OpenApiModels['POST/grooming/appointment']['Req'];
type ApptServiceItem = OpenApiDefinitions['grooming']['com.moego.server.grooming.params.PetDetailParams'];

const applyCustomService = async (params: any) => {
  await http.open('POST/grooming/appointment/applyCustomService', params);
};

export function useSubmitTicket() {
  const dispatch = useDispatch();
  const history = useHistory();
  const [
    {
      isOB,
      ticketId,
      petsService,
      date: serviceDate,
      time: serviceTime,
      initDate,
      initTime,
      currentClient,
      ticketComments,
      colorCode,
      alertNotes,
      staffLists,
      repeatModalProps,
    },
    updateTicketStore,
  ] = useTicketStoreCtx();
  const { validateServiceOperationPrice, validateServiceOperationTaskName, validateServiceOperationStaff } =
    useValidate();
  const jump2Redirect = useRedirect();
  const { isRepeat } = useTicketComputed();
  const { markUsedPreAuth } = usePreAuthOnboarding();
  const getServiceFormParams = useGetServiceFormParams();
  const submitRepeatTicket = useSubmitRepeatTicket();
  const { showAgreementAlertIfNeed } = useAgreement({
    mode: AutoMessageType.AppointmentBooked,
  });
  const { backCalendarView, backDate } = repeatModalProps;

  const postWaiting = async () => {
    updateTicketStore({ loading: true });
    const apptFormValues = { date: serviceDate, time: serviceTime };
    const { customerAddressId, customerId } = currentClient;
    if (!apptFormValues.time) {
      throw new Error('wrong time');
    }
    const params = {
      petServices: [],
      source: 22018,
      appointmentDateString: apptFormValues.date.format('YYYY-MM-DD'),
      ticketComments,
      alertNotes,
      appointmentStartTime: computeMinutes(apptFormValues.time!),
      customerId,
      colorCode,
    } as any;
    customerAddressId && (params.customerAddressId = customerAddressId);
    petsService.forEach((pet) => {
      pet.services.forEach((service) => {
        const { serviceId, operationList, duration, workMode } = service;
        const enableOperation = isMultipleStaffService(service);
        const operationListMaped = enableOperation
          ? operationList!.map((op) => {
              const duration = Number(op.duration);
              const price = Number(op.price);
              return {
                staffId: op.staffId,
                startTime: computeMinutes(op.startTime!),
                price: Number.isFinite(price) ? price : 0,
                priceRatio: Number.isFinite(op.priceRatio) ? op.priceRatio : 0,
                duration: Number.isFinite(duration) ? duration : 0,
                operationName: op.operationName,
              };
            })
          : null;

        const petService: any = {
          serviceType: service.type,
          enableOperation,
          petId: pet.petId,
          scopeTypePrice: service.scopeTypePrice,
          startTime: computeMinutes(service.startTime!),
          servicePrice: service.price,
          serviceId,
          serviceTime: enableOperation ? calcOperationServiceTime(service) : duration,
          staffId: service.staffId,
          scopeTypeTime: service.scopeTypeTime,
          star: staffLists.find((i) => i.staffId === service.staffId)?.star ?? false,
          workMode,
          operationList: operationListMaped as any,
        };
        const staffItem = staffLists.find((i) => i.staffId === service.staffId);
        staffItem && (petService.star = staffItem.star);
        params.petServices.push(petService);
      });
    });

    try {
      if (ticketId) {
        params.id = +ticketId;
        await dispatch(editWaitingTicketAction(params));
      } else {
        await dispatch(createWaitingTicketAction(params));
      }

      await applyCustomService(params);
      toastApi.success(ticketId ? 'Edited successfully!' : 'Created successfully!');
      history.push(
        PATH_GROOMING_CALENDAR.stated({
          backCalendarView,
          backDate,
        }),
      );
    } catch (e) {
      console.error(e);
    } finally {
      updateTicketStore({ loading: false });
    }
  };

  const postAppointment = async () => {
    updateTicketStore({ loading: true });
    const params = getServiceFormParams();

    try {
      if (!ticketId) {
        const { data } = await dispatch(createTicketAction(params));
        toastApi.success('Created successfully!');
        globalEvent.createdAppt.emit({ apptId: String(data.id) });
        await applyCustomService(params);
        if (params.preAuthEnable) {
          markUsedPreAuth();
        }
        history.push(
          PATH_GROOMING_CALENDAR.stated({
            ticketId: data.id,
            backCalendarView,
            backDate,
            customerId: currentClient.customerId,
          }),
        );
        return;
      }

      params.id = +ticketId;
      params.isWaitingList = 0;

      if (isRepeat) {
        await submitRepeatTicket();
      } else {
        await dispatch(updateTicketAction(params));
      }

      toastApi.success(isOB ? 'Scheduled successfully!' : 'Edited successfully!');
      if (!isRepeat) await applyCustomService(params);
      const date = serviceDate;
      const stateParams: GroomingCalendarState = {
        apptDate: date.utc(true).startOf('date').valueOf(),
        apptStartTime: params.appointmentStartTime,
        ticketId: +ticketId,
        customerId: currentClient.customerId,
        isOB: isOB,
      };
      if (backCalendarView && backDate) {
        stateParams.backCalendarView = backCalendarView;
        stateParams.backDate = backDate;
      }
      if (initDate && initTime) {
        const initialDate = initDate.utc(true).startOf('date').valueOf();
        const initialTime = initTime.hour() * 60 + initTime.minute();
        if (initialDate !== stateParams.apptDate || initialTime !== stateParams.apptStartTime) {
          stateParams['isChangedStartTime'] = true;
        }
      }
      if (params.preAuthEnable) {
        markUsedPreAuth();
      }
      /**
       * 在 client/[id]/bookings 的场景，
       * Edit booking 后，跳转回时需要带上 rescheduleTicketId，
       * 以便在 BookingTable 中弹出 Reschedule 后发 Auto Message 的弹窗
       */
      const routeState = stateParams.isChangedStartTime ? { rescheduleTicketId: stateParams.ticketId } : undefined;
      if (jump2Redirect(routeState)) {
        return;
      }
      history.push(PATH_GROOMING_CALENDAR.stated(stateParams));
    } catch (e) {
      console.error(e);
    } finally {
      updateTicketStore({ loading: false });
      if (isNormal(ticketId) && isOB) {
        await showAgreementAlertIfNeed({ customerId: currentClient.customerId, ticketId: +ticketId });
      }
    }
  };

  const disabledBookNow = useMemo(() => {
    for (const pet of petsService) {
      if (!pet.petId) {
        continue;
      }
      const { services } = pet;
      for (const service of services) {
        const priceValidate = validateServiceOperationPrice(service);
        const taskNameValidate = validateServiceOperationTaskName(service);
        const staffValidate = validateServiceOperationStaff(service);
        if (!priceValidate.validate || !taskNameValidate || !staffValidate) {
          return true;
        }
      }
    }
    return false;
  }, [petsService]);

  return {
    postAppointment,
    postWaiting,
    getServiceFormParams,
    disabledBookNow,
  };
}

export function useGetServiceFormParams() {
  const [
    {
      petsService,
      date: serviceDate,
      time: serviceTime,
      currentClient,
      ticketComments,
      colorCode,
      alertNotes,
      staffLists,
      preAuthValues,
    },
  ] = useTicketStoreCtx();

  return () => {
    const apptFormValues = { date: serviceDate, time: serviceTime };
    const { customerAddressId, customerId } = currentClient;
    if (!apptFormValues.time) {
      throw new Error('wrong time');
    }
    const petServices = petsService
      .filter((pet) => isNormal(pet.petId))
      .map((pet) =>
        pet.services
          .filter((service) => isNormal(service.serviceId))
          .map((service) => {
            const { serviceId, operationList, duration, workMode } = service;
            const enableOperation = isMultipleStaffService(service);
            const operationListMaped = enableOperation
              ? operationList!.map((op) => {
                  const duration = Number(op.duration);
                  const price = Number(op.price);
                  return {
                    staffId: op.staffId,
                    startTime: computeMinutes(op.startTime!),
                    price: Number.isFinite(price) ? price : 0,
                    priceRatio: Number.isFinite(op.priceRatio) ? op.priceRatio : 0,
                    duration: Number.isFinite(duration) ? duration : 0,
                    operationName: op.operationName,
                  };
                })
              : null;

            const petService = {
              groomingId: undefined as any,
              enableOperation,
              serviceType: service.type,
              petId: pet.petId,
              scopeTypePrice: service.scopeTypePrice,
              startTime: computeMinutes(service.startTime!),
              servicePrice: service.price,
              serviceId: serviceId!,
              serviceTime: enableOperation ? calcOperationServiceTime(service) : duration,
              staffId: service.staffId,
              scopeTypeTime: service.scopeTypeTime,
              star: staffLists.find((i) => i.staffId === service.staffId)?.star ?? false,
              workMode,
              operationList: operationListMaped as any,
              priceOverrideType: service.priceOverrideType,
              durationOverrideType: service.durationOverrideType,
            } as ApptServiceItem;
            return petService;
          }),
      )
      .flat();
    const params: CreateApptReqParams = {
      petServices,
      source: 22018,
      appointmentDateString: apptFormValues.date.format('YYYY-MM-DD'),
      ticketComments,
      appointmentStartTime: computeMinutes(apptFormValues.time!),
      customerId,
      colorCode,
      alertNotes,
      noStartTime: !apptFormValues.time?.isValid(), // time 是个 dayjs object / null，有 time，就手动更新 no start time
      ...preAuthValues,
    };
    customerAddressId && (params.customerAddressId = customerAddressId);
    return params;
  };
}

export function useGetSubmitRepeatTicketParams() {
  const { editSet } = useTicketFieldsEdit();
  const getServiceFormParams = useGetServiceFormParams();
  const [{ isSameTime, preAuthValues }] = useTicketStoreCtx();

  return () => {
    const allParams = getServiceFormParams();

    const params: UpdateRepeatTicketParams = {};
    if (editSet.includes('apptServiceStartAtTheSameTime')) {
      params.startAtSameTime = isSameTime;
    }
    if (editSet.includes('pet') || editSet.includes('service') || editSet.includes('staff')) {
      params.serviceList = allParams.petServices;
      params.startAtSameTime = isSameTime;
    }
    if (editSet.includes('apptStartDate')) {
      params.appointmentDateString = allParams.appointmentDateString;
    }
    if (editSet.includes('apptStartTime')) {
      params.appointmentStartTime = allParams.appointmentStartTime;
    }
    if (editSet.includes('preAuth')) {
      Object.assign(params, { preAuthParams: preAuthValues });
    }

    return params;
  };
}

export function useSubmitRepeatTicket() {
  const dispatch = useDispatch();
  const { editSet, reset } = useTicketFieldsEdit();
  const [{ ticketId, ticketComments, colorCode, alertNotes, saveUpdateProps }] = useTicketStoreCtx();
  const getParams = useGetSubmitRepeatTicketParams();

  const id = Number(ticketId);
  const repeatType = saveUpdateProps.type;

  return async () => {
    const params = getParams();

    await Promise.all(
      [
        !isEmpty(params) &&
          dispatch(
            updateRepeatTicketAction({
              ...params,
              id,
              repeatType,
            }),
          ),
        editSet.includes('colorCode') &&
          dispatch(
            updateRepeatTicketColorCodeAction({
              colorCode,
              id,
              repeatType,
            }),
          ),
        editSet.includes('alertNotes') &&
          dispatch(
            updateRepeatTicketAlertNotesAction(
              {
                appointmentId: id,
                context: alertNotes,
              },
              repeatType,
            ),
          ),
        editSet.includes('ticketComments') &&
          dispatch(
            updateTicketCommentsAction({
              ticketComments,
              ticketId: id,
              repeatType,
            }),
          ),
      ].filter(Boolean),
    );
    reset();
  };
}

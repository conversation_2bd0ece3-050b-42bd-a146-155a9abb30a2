import { type HistoryLog_Action, HistoryLog_Source } from '@moego/bff-openapi/clients/client.leads';
import { createEnum } from '../../../store/utils/createEnum';

export const ConvertVia = createEnum({
  Manual: [HistoryLog_Source.STAFF, 'Manual conversion'],
  Appointment: [HistoryLog_Source.APPOINTMENT, 'Appointment Booking'],
  OnlineBooking: [HistoryLog_Source.ONLINE_BOOKING, 'Appointment Booking'],
  Product: [HistoryLog_Source.PRODUCT, 'Retail product purchase'],
  Package: [HistoryLog_Source.PACKAGE, 'Package purchase'],
  // don't know what this is
  NewAppointment: [HistoryLog_Source.FULFILLMENT, 'Appointment Booking'],
  Membership: [HistoryLog_Source.MEMBERSHIP, 'Membership purchase'],
  Unspecified: [HistoryLog_Source.SOURCE_UNSPECIFIED, ''],
} as const);

export type RemoveArray<T> = T extends Array<infer U> ? U : T;

export type HistoryActionAction = RemoveArray<HistoryLog_Action['action']>;
export type HistoryAction = {
  action: HistoryActionAction;
};

type RemoveNullOrUndefined<T> = T extends null | undefined ? never : T;

export type HistoryValue = RemoveNullOrUndefined<HistoryActionAction['value']>;

import { type HistoryLog_Task, HistoryLog_Task_Type } from '@moego/bff-openapi/clients/client.leads';
import React, { memo, useMemo } from 'react';
import { type HistoryItemParams } from '../HistoryItem';
import { HistoryItemWrapper } from '../HistoryItemWrapper';

export const TaskHistory = memo<HistoryItemParams<HistoryLog_Task>>(function TaskHistory(props) {
  const { sourceName, dateTime, isLast, value } = props;

  const label = useMemo(() => {
    switch (value.type) {
      case HistoryLog_Task_Type.CREATE:
        return `Task ${value.task?.name} created by ${sourceName}`;
      case HistoryLog_Task_Type.UPDATE:
        return `Task ${value.task?.name} updated by ${sourceName}`;
      case HistoryLog_Task_Type.DELETE:
        return `Task ${value.task?.name} deleted by ${sourceName}`;
      case HistoryLog_Task_Type.FINISH:
        return `Task ${value.task?.name} completed by ${sourceName}`;
      default:
        return `Task ${value.task?.name} updated by ${sourceName}`;
    }
  }, [sourceName, value]);

  return (
    <HistoryItemWrapper label={label} dateTime={dateTime} isLast={isLast}>
      {null}
    </HistoryItemWrapper>
  );
});

import React, { memo, useMemo } from 'react';
import { HistoryItemWrapper } from './HistoryItemWrapper';
import { ConvertVia } from '../../../../LeadsConvertHistory/constants';
import { type HistoryItemParams } from './HistoryItem';
import { type HistoryLog_Convert } from '@moego/bff-openapi/clients/client.leads';

export const LeadConvert = memo<HistoryItemParams<HistoryLog_Convert>>(function LeadConvert(props) {
  const { dateTime, isLast, source } = props;
  const label = useMemo(() => {
    return `Converted via ${ConvertVia.mapLabels[source].toLowerCase()}`;
  }, [source]);
  return (
    <HistoryItemWrapper label={label} dateTime={dateTime} isLast={isLast}>
      {null}
    </HistoryItemWrapper>
  );
});

import { type HistoryLog_Note } from '@moego/bff-openapi/clients/client.leads';
import { MinorEditOutlined } from '@moego/icons-react';
import { useSerialCallback } from '@moego/tools';
import { Button, IconButton, Text, cn } from '@moego/ui';
import { useBoolean } from 'ahooks';
import React, { memo, useMemo, useState } from 'react';
import { TextAreaWithFooter } from '../../LeadsActivities/TextAreaWithFooter';
import { type HistoryItemParams } from '../HistoryItem';
import { HistoryItemWrapper } from '../HistoryItemWrapper';

export const NoteHistory = memo<HistoryItemParams<HistoryLog_Note>>(function NoteHistory(props) {
  const { value, dateTime, sourceName, updateLog, isLast } = props;
  const label = useMemo(() => {
    return `Note created by ${sourceName}`;
  }, [sourceName]);

  const [isEditing, isEditingAction] = useBoolean(false);

  const [textValue, setTextValue] = useState(value.text);

  const handleCancel = () => {
    setTextValue(value.text);
    isEditingAction.toggle();
  };

  // TODO: 直接调接口之后本地更新，不托管到上层了
  const handleSave = useSerialCallback(async () => {
    await updateLog({
      text: textValue,
    });
    isEditingAction.toggle();
  });

  return (
    <HistoryItemWrapper label={label} dateTime={dateTime} isLast={isLast}>
      {isEditing ? (
        <TextAreaWithFooter
          value={textValue}
          onChange={setTextValue}
          footer={
            <>
              <Button size="s" variant="secondary" onPress={handleCancel}>
                Cancel
              </Button>
              <Button size="s" isLoading={handleSave.isBusy()} variant="primary" onPress={handleSave}>
                Save
              </Button>
            </>
          }
        />
      ) : (
        <Text
          variant="small"
          className=" moe-group moe-relative moe-text-tertiary moe-pr-[20px] moe-p-[8px] moe-w-full moe-bg-neutral-sunken-0 moe-rounded-[8px]"
        >
          {value.text}
          <IconButton
            className={cn('moe-absolute moe-top-[2px] moe-right-[2px] moe-hidden group-hover:moe-block', {})}
            icon={<MinorEditOutlined />}
            variant="primary"
            color="transparent"
            onPress={isEditingAction.toggle}
          />
        </Text>
      )}
    </HistoryItemWrapper>
  );
});

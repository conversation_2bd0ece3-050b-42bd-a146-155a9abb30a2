import { type HistoryLog_Call, HistoryLog_Call_Direction } from '@moego/bff-openapi/clients/client.leads';
import React, { memo, useMemo } from 'react';
import { type HistoryItemParams } from '../HistoryItem';
import { HistoryItemWrapper } from '../HistoryItemWrapper';

export const CallHistory = memo<HistoryItemParams<HistoryLog_Call>>(function CallHistory(props) {
  const { value, dateTime, isLast, sourceName, customerName } = props;

  const label = useMemo(() => {
    const { direction } = value;
    if (direction === HistoryLog_Call_Direction.INCOMING) {
      return `Call received from ${customerName}`;
    }
    if (direction === HistoryLog_Call_Direction.OUTGOING) {
      return `Call by ${sourceName}`;
    }
    return `Phone call`;
  }, [value, sourceName, customerName]);

  return (
    <HistoryItemWrapper label={label} dateTime={dateTime} isLast={isLast}>
      {null}
    </HistoryItemWrapper>
  );
});

import { type HistoryLog_Message, HistoryLog_Message_Direction } from '@moego/bff-openapi/clients/client.leads';
import { Button } from '@moego/ui';
import React, { memo, useMemo } from 'react';
import { useHistory } from 'react-router';
import { PATH_MESSAGE_CENTER } from '../../../../../../../router/paths';
import { type HistoryItemParams } from '../HistoryItem';
import { HistoryItemWrapper } from '../HistoryItemWrapper';

export const MessageHistory = memo<HistoryItemParams<HistoryLog_Message>>(function MessageHistory(props) {
  const { value, dateTime, isLast, sourceName, customerName, customerId } = props;
  const history = useHistory();

  const label = useMemo(() => {
    if (value.direction === HistoryLog_Message_Direction.RECEIVE) {
      return `SMS message received from ${customerName}`;
    }
    if (value.direction === HistoryLog_Message_Direction.SEND) {
      return `SMS message sent by ${sourceName}`;
    }
    return `SMS message`;
  }, [value, sourceName, customerName]);

  const goDetail = () => {
    history.push(
      PATH_MESSAGE_CENTER.queried({
        setClientId: Number(customerId),
      }),
    );
  };
  return (
    <HistoryItemWrapper
      label={label}
      dateTime={dateTime}
      isLast={isLast}
      extra={
        <Button size="s" variant="tertiary" onPress={goDetail}>
          View details
        </Button>
      }
    >
      {null}
    </HistoryItemWrapper>
  );
});

// add history item for lead create

import { type HistoryLog_Create } from '@moego/bff-openapi/clients/client.leads';
import React, { memo, useMemo } from 'react';
import { type HistoryItemParams } from './HistoryItem';
import { HistoryItemWrapper } from './HistoryItemWrapper';

export const LeadCreate = memo<HistoryItemParams<HistoryLog_Create>>(function LeadCreate(props) {
  const { dateTime, sourceName, isLast } = props;
  const label = useMemo(() => {
    let label = `Lead was created`;
    if (sourceName) {
      label += ` by ${sourceName}`;
    }
    return label;
  }, [sourceName]);

  return (
    <HistoryItemWrapper label={label} dateTime={dateTime} isLast={isLast}>
      {null}
    </HistoryItemWrapper>
  );
});

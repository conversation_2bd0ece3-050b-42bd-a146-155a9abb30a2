import { type Timestamp } from '@moego/bff-openapi/clients/client.leads';
import { type z } from '@moego/bff-openapi';
import { type schemas } from '@moego/bff-openapi/clients/client.leads';
import { Form, Input, Modal, type ModalProps, LegacySelect as Select, useForm } from '@moego/ui';
import { useStore } from 'amos';
import { type Dayjs } from 'dayjs';
import { memo, useEffect, useMemo } from 'react';
import React from 'react';
import { DateTimePicker } from '../../../../../components/DateTimePicker/DateTimePicker';
import { staffMapBox } from '../../../../../store/staff/staff.boxes';
import { selectCompanyAllStaffList } from '../../../../../store/staff/staff.selectors';
import { googleTimestampToDayjs } from '../../../../../utils/googleTimestamp';

export interface TaskFormParams {
  name: string;
  allocateStaffId?: string;
  completeTime?: Dayjs;
}

interface CreateOrEditTaskModalProps extends Omit<ModalProps, 'onSubmit'> {
  task?: z.infer<typeof schemas.Task>;
  onSubmit: (value: TaskFormParams) => void;
}

export const CreateOrEditTaskModal = memo<CreateOrEditTaskModalProps>(function CreateOrEditTaskModal(props) {
  const { task, onSubmit, ...rest } = props;
  const store = useStore();
  const staffIdList = store.select(selectCompanyAllStaffList);
  const staffMap = store.select(staffMapBox);
  const staffOptions = useMemo(
    () =>
      staffIdList
        .map((id) => {
          const staff = staffMap.mustGetItem(id);
          return {
            label: staff.fullName(),
            value: String(staff.id),
          };
        })
        .toArray(),
    [staffIdList, staffMap],
  );

  const form = useForm<TaskFormParams>();
  useEffect(() => {
    form.reset({
      name: task?.name,
      allocateStaffId: task?.allocateStaffId,
      completeTime: task?.completeTime
        ? (googleTimestampToDayjs(task.completeTime as unknown as Timestamp) ?? undefined)
        : undefined,
    });
  }, [task, form]);

  const handleSubmit = () => {
    form.handleSubmit(onSubmit)();
  };

  return (
    <Modal
      {...rest}
      title={task ? 'Edit Task' : 'Create Task'}
      onConfirm={handleSubmit}
      size="s"
      autoCloseOnConfirm={false}
    >
      <Form form={form} footer={null}>
        <Form.Item label="Task name" name="name" rules={{ required: 'Please enter task name' }}>
          <Input isRequired />
        </Form.Item>
        <Form.Item label="Agent" name="allocateStaffId">
          <Select options={staffOptions} />
        </Form.Item>
        <Form.Item label="Date and time" name="completeTime">
          <DateTimePicker />
        </Form.Item>
      </Form>
    </Modal>
  );
});

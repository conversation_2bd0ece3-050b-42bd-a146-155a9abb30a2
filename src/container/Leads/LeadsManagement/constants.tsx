import {
  ListCustomersRequest_OrderDirection,
  ListCustomersRequest_OrderField,
  type Customer_ActionState,
} from '@moego/bff-openapi/clients/client.leads';
import { type z } from '@moego/bff-openapi';
import { type CustomerWithNameAndTask, type schemas } from '@moego/bff-openapi/clients/client.leads';
import { MinorGalleryOutlined, MinorListOutlined } from '@moego/icons-react';
import { type RowSelectionDetail } from '@moego/ui';
import React from 'react';
import { type ClientFilterListSource } from '../../../store/customer/clientFilters.boxes';
import { type GetCustomerListInput } from '../../../store/customer/customer.actions';

export interface RowSelectionFn {
  (selection: Record<string, boolean>, info: Pick<RowSelectionDetail, 'selectionType' | 'isSelected' | 'rowId'>): void;
}

export interface UseRowSelectionActionsProps {
  source: ClientFilterListSource;
  viewId: number;
}

export enum LeadViewMode {
  LIST = 'list',
  KANBAN = 'kanban',
}

export const LeadViewModeMap: Record<LeadViewMode, string> = {
  [LeadViewMode.LIST]: 'List',
  [LeadViewMode.KANBAN]: 'Kanban',
};

export const LeadViewModeIconMap: Record<LeadViewMode, JSX.Element> = {
  [LeadViewMode.LIST]: <MinorListOutlined />,
  [LeadViewMode.KANBAN]: <MinorGalleryOutlined />,
};

export interface KanBanItemItems {
  customers: CustomerWithNameAndTask[];
  pagination: {
    pageNum: number;
    pageSize: number;
  };
}

export type Containers = Record<
  Exclude<Customer_ActionState, Customer_ActionState.ACTION_STATE_UNSPECIFIED>,
  {
    items: KanBanItemItems[];
    maxPageNum: number;
    totalCount: number;
  }
>;

export interface KanBanItemProps {
  selectedRowIds: number[];
  keyword: string;
  actionStateId: string;
  className: string;
  onKanBanSelection: (isSelected: boolean, rowId: string) => void;
  isOver: boolean;
  setCurrentCustomer: (customer: z.infer<typeof schemas.CustomerWithNameAndTask>) => void;
  openConvertModal: () => void;
  startInit: () => void;
  deleteRowSelection: (id: string) => void;
}

export interface LeadsKanbanProps {
  selectedRowIds: number[];
  onKanBanSelection: (isSelected: boolean, rowId: string) => void;
  setCurrentCustomer: (customer: z.infer<typeof schemas.CustomerWithNameAndTask>) => void;
  openConvertModal: () => void;
  deleteRowSelection: (id: string) => void;
}

export interface LeadItemProps {
  id: string;
  data: CustomerWithNameAndTask;
  isSelected: boolean;
  onKanBanSelection?: (isSelected: boolean, rowId: string) => void;
  actionStateId: string;
  setCurrentCustomer?: (customer: z.infer<typeof schemas.CustomerWithNameAndTask>) => void;
  openConvertModal?: () => void;
  startInit?: () => void;
  className?: string;
  style?: React.CSSProperties;
  deleteRowSelection?: (id: string) => void;
}

export const SortKeyMap: Record<string, ListCustomersRequest_OrderField> = {
  createTime: ListCustomersRequest_OrderField.CREATE_TIME,
  updateTime: ListCustomersRequest_OrderField.UPDATE_TIME,
};
export const DirectionMap: Record<string, ListCustomersRequest_OrderDirection> = {
  desc: ListCustomersRequest_OrderDirection.DESC,
  asc: ListCustomersRequest_OrderDirection.ASC,
};
export interface LeadsListRef {
  getDataList: (input: GetCustomerListInput) => Promise<void>;
}

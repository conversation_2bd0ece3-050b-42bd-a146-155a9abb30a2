import { Spin, cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { Condition } from '../../components/Condition';
import { Switch } from '../../components/SwitchCase';
import { LayoutContainer } from '../../layout/LayoutContainer';
import { currentBusinessIdBox } from '../../store/business/business.boxes';
import { type PlaygroupDailyView, listPlaygroupCalendarView } from '../../store/playgroups/playgroups.actions';
import { type EnumValues } from '../../store/utils/createEnum';
import { generateWeekHeader } from '../../utils/DateTimeUtil';
import { globalEvent } from '../../utils/events/events';
import { useAsyncEffect } from '../../utils/hooks/useAsyncEffect';
import { useBool } from '../../utils/hooks/useBool';
import { useCancelableCallback } from '../../utils/hooks/useCancelableCallback';
import { stringToDateMessage } from '../../utils/utils';
import { PlaygroupProvider, ViewType } from './Playgroup.context';
import { PlaygroupDayView } from './PlaygroupDayView/PlaygroupDayView';
import { PlaygroupWeekView } from './PlaygroupWeekView/PlaygroupWeekView';
import { PlayGroupHeader } from './components/PlayGroupHeader';

export const Playgroup = () => {
  const [viewType, setViewType] = useState<EnumValues<typeof ViewType>>(ViewType.Day);
  const [selectedDate, setSelectedDate] = useState(dayjs());
  const [playgroupsData, setPlaygroupsData] = useState<PlaygroupDailyView[]>([]);
  const dispatch = useDispatch();
  const [businessId] = useSelector(currentBusinessIdBox);
  const isLoading = useBool(false);

  const fetchData = useCancelableCallback(async (signal: AbortSignal) => {
    let startDate = selectedDate;
    let endDate = selectedDate;

    if (viewType === ViewType.Week) {
      const days = generateWeekHeader(selectedDate.toDate());
      startDate = dayjs(days[0].date);
      endDate = dayjs(days[days.length - 1].date);
    }

    const res = await dispatch(
      listPlaygroupCalendarView(
        {
          startDate: stringToDateMessage(startDate),
          endDate: stringToDateMessage(endDate),
        },
        signal,
      ),
    );

    setPlaygroupsData(res);
  });

  useAsyncEffect(async () => {
    isLoading.open();
    try {
      await fetchData();
    } finally {
      isLoading.close();
    }
  }, [selectedDate, viewType, businessId]);

  useEffect(() => {
    const dispose = globalEvent.refresh.on(fetchData);
    return dispose;
  }, []);

  return (
    <PlaygroupProvider
      value={{ viewType, setViewType, selectedDate, setSelectedDate, fetchData, playgroupsData, setPlaygroupsData }}
    >
      <LayoutContainer
        className={cn('moe-bg-white moe-select-none moe-relative', { '!moe-overflow-hidden': isLoading.value })}
      >
        <PlayGroupHeader />
        <Condition if={isLoading.value}>
          <div className="moe-absolute moe-mt-[88px] moe-inset-0 moe-w-full moe-h-full moe-opacity-90 moe-bg-white moe-z-10">
            <Spin isLoading className="moe-absolute moe-left-[50%] moe-top-[220px]" />
          </div>
        </Condition>
        <Switch>
          <Switch.Case if={viewType === ViewType.Day}>
            <PlaygroupDayView />
          </Switch.Case>
          <Switch.Case if={viewType === ViewType.Week}>
            <PlaygroupWeekView />
          </Switch.Case>
        </Switch>
      </LayoutContainer>
    </PlaygroupProvider>
  );
};

import { ServiceOverrideType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Modal, Spin, useForm, useFormState } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import { pick } from 'lodash';
import React, { memo, useEffect, useMemo } from 'react';
import { useHistory } from 'react-router';
import { useAddressState } from '../../components/AddressForm/AddressFormV2';
import { ReportModule } from '../../components/ErrorBoundary/types';
import { withErrorBoundary } from '../../components/ErrorBoundary/withErrorBoundary';
import { toastApi } from '../../components/Toast/Toast';
import { SSTestIds } from '../../config/testIds/smartScheduling';
import { PATH_GROOMING_CALENDAR } from '../../router/paths';
import { BusinessType } from '../../store/business/business.options';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import {
  closeSmartSchedulingModal,
  startSmartScheduling,
} from '../../store/smartScheduling/actions/private/smartSchedulingStore.actions';
import { getSmartScheduleParkingLocations } from '../../store/smartScheduling/actions/public/smartScheduling.actions';
import { getSmartScheduleDrivingRules } from '../../store/smartScheduling/actions/public/smartScheduling.actions';
import { getSmartSchedulingSetting } from '../../store/smartScheduling/actions/public/smartScheduling.actions';
import { selectSmartSchedulingCurrentBuffer } from '../../store/smartScheduling/smartScheduling.selectors';
import {
  ALL_STAFF_ID,
  type SmartSchedulingFormParams,
  smartSchedulingModalStore,
} from '../../store/smartScheduling/smartSchedulingStore.boxes';
import { getStaffList } from '../../store/staff/staff.actions';
import { getWorkingRangeList } from '../../store/staff/workingRange.actions';
import { selectAccessibleStaffList } from '../../store/staff/workingRange.selectors';
import { getVanList } from '../../store/van/van.actions';
import { staffVanListBox } from '../../store/van/van.boxes';
import { DATE_FORMAT_EXCHANGE } from '../../utils/DateTimeUtil';
import { abortNavigation } from '../../utils/abortNavigation';
import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { SmartSchedulingStartForm } from './components/SmartSchedulingStartForm/SmartSchedulingStartForm';
import { useGetStaffDurationMapList } from './hooks/useGetStaffDurationMapList';
import { type SmartSchedulingFormModel } from './utils/SmartSchedulingStartForm.types';

const SmartSchedulingStartModalComponent = memo(() => {
  const dispatch = useDispatch();
  const history = useHistory();
  const [modalState, business, smartSchedulingCurrentBuffer, staffList, staffVanList] = useSelector(
    smartSchedulingModalStore,
    selectCurrentBusiness,
    selectSmartSchedulingCurrentBuffer,
    selectAccessibleStaffList,
    staffVanListBox,
  );
  const isSalonBusiness = business.appType === BusinessType.GroomingSalon;
  const disableMobileGroomingForAddress = !!modalState.isCreateOrEditTicket && !modalState.params?.address?.lat;
  const disableEditDurationAndAddress = !!modalState.isCreateOrEditTicket;
  const allAssignedVanStaffList = staffList.filter((staffId) => staffVanList.getList(staffId).size > 0);

  const params: SmartSchedulingFormParams = useMemo(
    () =>
      modalState.params
        ? modalState.params
        : {
            isMobileGrooming: !isSalonBusiness,
            duration: 60,
            staffIdList: [ALL_STAFF_ID],
            startDate: dayjs(),
            bufferTime: smartSchedulingCurrentBuffer,
          },
    [modalState.params, isSalonBusiness, smartSchedulingCurrentBuffer],
  );
  const form = useForm<SmartSchedulingFormModel>({
    mode: 'all',
    defaultValues: {
      ...params,
      address: params?.address?.address1,
    },
  });

  const getStaffDurationMapList = useGetStaffDurationMapList();

  const { isValid } = useFormState({ control: form.control });
  const serviceDurationList = useMemo(() => {
    return modalState.presetPetAndServices
      ?.map((petService) => {
        return petService.serviceItem.map(
          ({ value, duration, durationOverrideType = ServiceOverrideType.UNSPECIFIED }) => {
            return {
              serviceId: value,
              serviceTime: duration,
              durationOverrideType,
            };
          },
        );
      })
      .flat();
  }, [modalState.presetPetAndServices]);

  const paramsAddress = useMemo(
    () =>
      modalState.params?.address &&
      pick(modalState.params?.address, ['lat', 'lng', 'city', 'state', 'zipcode', 'country']),
    [modalState.params?.address],
  );

  const [searchAddressResult, handleSelectAddress] = useAddressState(undefined, paramsAddress);

  useEffect(() => {
    if (modalState.visible) {
      initData();
    }
  }, [modalState.visible]);
  useEffect(() => {
    const isGroomingAllStaff =
      allAssignedVanStaffList.size > 0 &&
      allAssignedVanStaffList.every((staffId) => Boolean(params.staffIdList?.includes(staffId)));
    const isSalonAllStaff =
      staffList.size > 0 && staffList.every((staffId) => Boolean(params.staffIdList?.includes(staffId)));
    const isAllStaff = isSalonBusiness ? isSalonAllStaff : isGroomingAllStaff;

    form.reset({
      ...params,
      address: params?.address?.address1,
      staffIdList: isAllStaff ? [ALL_STAFF_ID] : params.staffIdList,
    });
  }, [params]);

  const initData = useSerialCallback(async () => {
    const now = dayjs().format(DATE_FORMAT_EXCHANGE);
    await dispatch(getWorkingRangeList({ startDate: now, endDate: now }));
    // TODO(icy): 需要优化
    await dispatch(getStaffList());
    await dispatch(getVanList());
    await Promise.all([
      dispatch(getSmartScheduleDrivingRules()),
      dispatch(getSmartScheduleParkingLocations()),
      dispatch(getSmartSchedulingSetting()),
    ]);
  });
  const handleClose = () => {
    dispatch(closeSmartSchedulingModal());
  };
  const handleSubmit = useSerialCallback(async () => {
    if (!isValid) {
      // 触发 error
      await form.handleSubmit(() => {})();
      abortNavigation();
    }
    await form.handleSubmit(async (input) => {
      let staffIdListParam = input.staffIdList?.includes(ALL_STAFF_ID) ? staffList.toArray() : input.staffIdList!;
      if (input.isMobileGrooming) {
        if (!disableEditDurationAndAddress && (!searchAddressResult.current.lat || !searchAddressResult.current.lng)) {
          form.setError('address', { type: 'invalid-address', message: 'Please select an address.' });
          return;
        }

        if (staffIdListParam.length === 1) {
          const hasVan = staffVanList.getList(staffIdListParam[0]).size > 0;
          if (!hasVan) {
            toastApi.error('This staff has not been assigned to a mobile van.');
            return;
          }
        } else {
          const staffWithNoVan = staffList
            .filter((staffId) => staffIdListParam.includes(staffId))
            .filter((staffId) => staffVanList.getList(staffId).size === 0);
          if (staffList.every((staffId) => staffWithNoVan.includes(staffId))) {
            toastApi.error('All of the selected staffs have not been assigned to a mobile van.');
            return;
          } else if (staffWithNoVan.size > 0) {
            staffIdListParam = staffIdListParam.filter((staffId) => staffVanList.getList(staffId).size > 0);
            if (allAssignedVanStaffList.some((staffId) => !staffIdListParam.includes(staffId))) {
              toastApi.neutral('Staffs not assigned to mobile van will not be in the result.');
            }
          }
        }
      }

      const staffDurationMapList = getStaffDurationMapList({ staffIdList: staffIdListParam, serviceDurationList });

      const payload: SmartSchedulingFormParams = {
        ...input,
        staffIdList: staffIdListParam,
        staffPetServiceDurationList: staffDurationMapList,
        startDate: (input.startDate || dayjs()).startOf('day'),
        address: {
          // 若之前的参数中曾经有地址信息，则保留
          address1: input.address || params?.address?.address1 || '',
          address2: '', // 看起来没有用到
          city: '',
          state: '',
          zipcode: '',
          country: '',
          ...pick(searchAddressResult.current, ['lat', 'lng', 'city', 'state', 'zipcode', 'country']),
        },
      };
      // 适配禁止编辑 duration 的情况
      if (disableEditDurationAndAddress && params.duration) {
        payload.duration = params.duration;
      }
      const findAvailableSlot = await dispatch(startSmartScheduling(payload, modalState.addingCard));
      if (findAvailableSlot && modalState.isCreateOrEditTicket) {
        // 在编辑或者新建 ticket 页面
        history.push(PATH_GROOMING_CALENDAR.build());
      }
    })();
  });
  return (
    <Modal
      title="Smart scheduling"
      className="moe-w-[540px]"
      cancelText="Cancel"
      confirmText="Search"
      isOpen={modalState.visible}
      onClose={handleClose}
      onCancel={handleClose}
      onConfirm={handleSubmit}
      autoCloseOnConfirm={false}
      cancelButtonProps={{
        'data-testid': SSTestIds.CancelBtn,
      }}
      confirmButtonProps={{
        'data-testid': SSTestIds.SearchBtn,
        isLoading: handleSubmit.isBusy(),
      }}
    >
      <Spin
        classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }}
        isLoading={initData.isBusy()}
      >
        <SmartSchedulingStartForm
          form={form}
          serviceDurationList={serviceDurationList}
          handleSelectAddress={handleSelectAddress}
          disableMobileGroomingForSalon={isSalonBusiness}
          disableMobileGroomingForAddress={disableMobileGroomingForAddress}
          disableEditDurationAndAddress={disableEditDurationAndAddress}
        />
      </Spin>
    </Modal>
  );
});

export const SmartSchedulingStartModal = withErrorBoundary(SmartSchedulingStartModalComponent, {
  reportModule: ReportModule.SmartScheduling,
});

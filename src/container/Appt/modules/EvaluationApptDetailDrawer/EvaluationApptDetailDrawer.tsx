import { datadogRum } from '@datadog/browser-rum';
import { Spin } from '@moego/ui';
import { useDispatch, useSelector, useStore } from 'amos';
import classNames from 'classnames';
import React, { useEffect, useLayoutEffect } from 'react';
import { Condition } from '../../../../components/Condition';
import { Switch } from '../../../../components/SwitchCase';
import { ScrollerProvider } from '../../../../layout/components/ScrollerProvider';
import { switchBusinessWithCheckWorkingLocation } from '../../../../store/business/business.actions';
import { apptDetailDrawerBox } from '../../../../store/calendarLatest/calendar.boxes';
import { CommentNoteType } from '../../../../store/calendarLatest/calendar.utils';
import { getAllClientInfo } from '../../../../store/customer/customer.actions';
import { setEvaluationApptDetailDrawerEdit } from '../../../../store/evaluation/evaluation.actions';
import {
  EvaluationApptDetailStep,
  evaluationApptDetailDrawerBox,
  evaluationApptDetailDrawerEditBox,
} from '../../../../store/evaluation/evaluation.boxes';
import { useInitBusinessApplicableEvaluation } from '../../../../store/evaluation/evaluation.hooks';
import { ApptEvaluationNavigationTabs } from '../../../../store/evaluation/evaluation.types';
import { getLodgingUnitList } from '../../../../store/lodging/actions/public/lodgingUnit.actions';
import { isNormal } from '../../../../store/utils/identifier';
import { useAsyncEffect } from '../../../../utils/hooks/useAsyncEffect';
import { DataDogActionName } from '../../../../utils/logger';
import { apptInfoMapBox } from '../../store/appt.boxes';
import { ApptDrawerV3Style } from '../../style/apptDrawerStyle';
import { ApptActivityLog } from '../ApptDetailDrawer/ApptActivityLog/ApptActivityLog';
import { ApptCancelConfirmModal } from '../ApptDetailDrawer/ApptInfoPanel/ApptModals/ApptCancelConfirmModal';
import { ApptReadyForPickupConfirmModal } from '../ApptDetailDrawer/ApptInfoPanel/ApptModals/ApptReadyForPickupConfirmModal';
import { ApptTakePaymentModal } from '../ApptDetailDrawer/ApptInfoPanel/ApptModals/ApptTakePaymentModal';
import { ApptPetBelongings } from '../ApptDetailDrawer/ApptPetBelongings/ApptPetBelongings';
import { ApptTicketCommentPanel } from '../ApptDetailDrawer/ApptTicketCommentPanel/ApptTicketCommentPanel';
import { EvaluationApptInfoPanel } from './EvaluationApptInfoPanel';
import { EvaluationApptDetailAddNewPet } from './components/EvaluationApptDetailAddNewPet';
import { EvaluationApptEditPet } from './components/EvaluationApptEditPet';
import { EvaluationApptReschedule } from './components/EvaluationApptReschedule';
import { EvaluationLeftNav } from './components/EvaluationLeftNav';

export const EvaluationApptDetailDrawer = () => {
  const dispatch = useDispatch();
  const store = useStore();
  const [detailInfo, editInfo, apptInfoMap] = useSelector(
    evaluationApptDetailDrawerBox,
    evaluationApptDetailDrawerEditBox,
    apptInfoMapBox,
  );

  const { activeTab, stepInfo } = editInfo;
  const { ticketId, loading, visible } = detailInfo;
  const currentAppt = apptInfoMap.mustGetItem(ticketId + '')?.appointment;
  const currentApptBusinessId = currentAppt?.businessId;

  useLayoutEffect(() => {
    datadogRum.stopDurationVital(visible ? DataDogActionName.OPEN_APPT_DRAWER : DataDogActionName.CLOSE_APPT_DRAWER, {
      context: {
        type: 'evaluation',
      },
    });
  }, [visible]);

  useAsyncEffect(async () => {
    // 打开抽屉的时候默认展示第一页，能加载一些关键的数据
    // 抽屉打开后，切换ticket也重置一下
    const { visible } = store.select(apptDetailDrawerBox);
    const { scene } = store.select(evaluationApptDetailDrawerBox);
    if (visible && isNormal(ticketId)) {
      if (isNormal(currentAppt?.customerId)) {
        dispatch(getAllClientInfo(Number(currentAppt?.customerId)));
      }
      dispatch(switchBusinessWithCheckWorkingLocation(currentAppt?.businessId));

      if (scene !== 'overviewClientNotes') {
        dispatch(
          setEvaluationApptDetailDrawerEdit({
            activeTab: ApptEvaluationNavigationTabs.INFO,
            ticketCommentsActiveTab: CommentNoteType.TicketComments,
          }),
        );
      }
      if (scene !== 'checkInAlert') {
        dispatch(evaluationApptDetailDrawerEditBox.setState(evaluationApptDetailDrawerEditBox.initialState));
      }
    }
  }, [ticketId, currentAppt]);

  useEffect(() => {
    const { visible } = store.select(apptDetailDrawerBox);
    if (visible && isNormal(currentApptBusinessId)) {
      dispatch(switchBusinessWithCheckWorkingLocation(currentApptBusinessId));
      dispatch(getLodgingUnitList());
    }
  }, [currentApptBusinessId]);

  useInitBusinessApplicableEvaluation(visible);

  return (
    <>
      <ScrollerProvider className="moe-flex-1 moe-flex moe-flex-row moe-min-w-0">
        <Switch shortCircuit>
          {/* 编辑pet & service */}
          <Switch.Case if={stepInfo.type === EvaluationApptDetailStep.EditExistPet}>
            <EvaluationApptEditPet />
          </Switch.Case>
          {/* 编辑ticket的时间 */}
          <Switch.Case if={stepInfo.type === EvaluationApptDetailStep.RescheduleDateAndTime}>
            <EvaluationApptReschedule />
          </Switch.Case>
          {/* 添加新的pet */}
          <Switch.Case if={stepInfo.type === EvaluationApptDetailStep.AddNewPet}>
            <EvaluationApptDetailAddNewPet />
          </Switch.Case>
          {/* ticket信息页 */}
          <Switch.Case else>
            <EvaluationLeftNav
              activeTab={activeTab}
              onChangeActiveTab={(activeTab) => dispatch(setEvaluationApptDetailDrawerEdit({ activeTab }))}
            />
            <div
              className={classNames('moe-flex-1 moe-relative moe-min-w-0 moe-overflow-auto', {
                'moe-opacity-[0.5] moe-cursor-not-allowed moe-pointer-events-none': loading,
              })}
            >
              <Condition if={activeTab === ApptEvaluationNavigationTabs.INFO}>
                <EvaluationApptInfoPanel ticketId={ticketId} />
              </Condition>
              <Condition if={activeTab === ApptEvaluationNavigationTabs.TICKET_COMMENT}>
                <ApptTicketCommentPanel
                  ticketId={ticketId}
                  defaultPetId={editInfo.petNotesDefaultPetId}
                  activeTab={editInfo.ticketCommentsActiveTab}
                  onTabChange={(key) =>
                    dispatch(setEvaluationApptDetailDrawerEdit({ ticketCommentsActiveTab: key as CommentNoteType }))
                  }
                />
              </Condition>
              <Condition if={activeTab === ApptEvaluationNavigationTabs.ACTIVITY_LOG}>
                <ApptActivityLog ticketId={ticketId} />
              </Condition>
              <Condition if={activeTab === ApptEvaluationNavigationTabs.PET_BELONGINGS}>
                <ApptPetBelongings />
              </Condition>
              {/* 直接用 Spin 套住整个容器会有 flex 高度问题，所以单独处理了一下 */}
              <Condition if={loading && activeTab === ApptEvaluationNavigationTabs.INFO}>
                <Spin className="!moe-absolute moe-top-1/2 moe-left-1/2 moe-z-[10] " />
              </Condition>
            </div>
          </Switch.Case>
        </Switch>
      </ScrollerProvider>
      <ApptDrawerV3Style />
      <ApptCancelConfirmModal ticketId={ticketId} />
      <ApptTakePaymentModal ticketId={ticketId!} />
      <ApptReadyForPickupConfirmModal ticketId={ticketId} />
    </>
  );
};

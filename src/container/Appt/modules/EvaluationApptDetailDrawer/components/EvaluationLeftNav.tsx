import React, { memo, useMemo } from 'react';
import { ApptEvaluationNavigationTabs } from '../../../../../store/evaluation/evaluation.types';
import { DrawerLeftNav } from '../../../components/DrawerLeftNav/DrawerLeftNav';

export interface ApptLeftNavProps {
  activeTab: number;
  onChangeActiveTab?: (tab: number) => void;
  disabledTabs?: number[];
}

export const EvaluationLeftNav = memo((props: ApptLeftNavProps) => {
  const { disabledTabs, activeTab, onChangeActiveTab } = props;

  const options = useMemo(
    () =>
      ApptEvaluationNavigationTabs.values
        .filter((tab) => tab !== ApptEvaluationNavigationTabs.FEEDING_AND_MEDICATION)
        .map((tab) => ({
          ...ApptEvaluationNavigationTabs.mapLabels[tab],
          value: tab,
          isActive: tab === activeTab,
          isDisabled: disabledTabs?.includes(tab),
        })),
    [activeTab, disabledTabs],
  );

  return <DrawerLeftNav options={options} value={activeTab} onChange={onChangeActiveTab} />;
});

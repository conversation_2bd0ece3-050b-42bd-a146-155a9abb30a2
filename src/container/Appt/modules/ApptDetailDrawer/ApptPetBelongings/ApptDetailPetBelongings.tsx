import { useDispatch } from 'amos';
import React, { useMemo } from 'react';
import { useClientPets } from '../../../../../components/PetInfo/hooks/useClientPets';
import { addPetBelonging, removePetBelonging, updatePetBelonging } from '../../../../../store/pet/petBelongs.actions';
import { type PetBelongsRecord } from '../../../../../store/pet/petBelongs.boxes';
import { isNormal } from '../../../../../store/utils/identifier';
import { PetBelongings } from '../../../components/PetBelongings/PetBelongings';
import { PetListInfoRender } from '../../../components/PetListInfoRender';

export interface ApptDetailPetBelongingsProps {
  className?: string;
  clientId: number;
  defaultPetId?: number;
  petIds?: number[];
  appointmentId: number;
}

export const ApptDetailPetBelongings: React.FC<ApptDetailPetBelongingsProps> = (props) => {
  const { clientId, defaultPetId, petIds, appointmentId } = props;
  const dispatch = useDispatch();
  const clientAlivePets = useClientPets(clientId, { filterAlive: false });
  const clientAlivePetsSorted = useMemo(() => {
    const hasFilterPets = Array.isArray(petIds);
    const list = clientAlivePets.filter((id) => (hasFilterPets ? petIds.includes(id) : true));
    if (hasFilterPets) {
      list.sort((a, b) => petIds.indexOf(a) - petIds.indexOf(b));
    }
    return list;
  }, [clientAlivePets, petIds?.length]);
  const initPetId = isNormal(defaultPetId) ? defaultPetId : clientAlivePetsSorted[0];

  const handleRemove = async (params: { petBelongingId: number; appointmentId: number; petId: number }) => {
    await dispatch(removePetBelonging(params));
  };

  const handleEdit = async (params: Partial<PetBelongsRecord>) => {
    await dispatch(updatePetBelonging(params));
  };

  const handleAdd = async (params: Partial<PetBelongsRecord> & { appointmentId: number; petId: number }) => {
    await dispatch(addPetBelonging(params));
  };

  const petAmount = clientAlivePetsSorted.length;

  if (!petAmount) {
    return null;
  }

  return (
    <PetListInfoRender
      petIds={clientAlivePetsSorted}
      preventAutoReload={false}
      customerId={clientId}
      appointmentId={appointmentId}
    >
      {({ pets, belongsMap, isBusy }) => {
        return (
          <PetBelongings
            appointmentId={appointmentId}
            defaultPetId={initPetId}
            pets={pets}
            belongsMap={belongsMap}
            isBusy={isBusy}
            onRemove={handleRemove}
            onEdit={handleEdit}
            onAdd={handleAdd}
          />
        );
      }}
    </PetListInfoRender>
  );
};

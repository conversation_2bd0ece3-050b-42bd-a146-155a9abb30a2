import { useSelector } from 'amos';
import React from 'react';
import { apptDetailDrawerBox, apptDetailDrawerEditBox } from '../../../../../store/calendarLatest/calendar.boxes';
import { DrawerHead } from '../../../components/DrawerHead/DrawerHead';
import { apptInfoMapBox } from '../../../store/appt.boxes';
import { ApptDetailPetBelongings } from './ApptDetailPetBelongings';

export const ApptPetBelongings: React.FC = () => {
  const [{ ticketId }, editInfo] = useSelector(apptDetailDrawerBox, apptDetailDrawerEditBox);

  const [ticket] = useSelector(apptInfoMapBox.mustGetItem(String(ticketId)));

  const { customerId, serviceDetail } = ticket;

  const onlyPetsInTicket = React.useMemo<number[]>(
    () => (serviceDetail ?? []).map((item) => Number(item.pet.id)),
    [serviceDetail],
  );

  return (
    <div className="moe-flex moe-flex-col moe-h-full">
      <DrawerHead title={'Pet belongings'} />
      <div className="moe-flex moe-flex-col moe-flex-1 moe-p-[24px] moe-gap-y-[8px] moe-overflow-auto">
        <ApptDetailPetBelongings
          clientId={Number(customerId)}
          defaultPetId={editInfo.petNotesDefaultPetId}
          petIds={onlyPetsInTicket}
          appointmentId={ticketId}
        />
      </div>
    </div>
  );
};

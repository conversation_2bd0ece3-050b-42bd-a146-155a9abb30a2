import { cn } from '@moego/ui';
import { useDispatch, useSelector, useStore } from 'amos';
import { cloneDeep, debounce } from 'lodash';
import React, { useMemo, useRef } from 'react';
import { useSetState } from 'react-use';
import { DrawerHeader } from '../../../../components/Drawer/DrawerHeader';
import { type ServiceEntry } from '../../../../components/ServiceApplicablePicker/types/serviceEntry';
import { toastApi } from '../../../../components/Toast/Toast';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import {
  exitApptDetailDrawerEdit,
  setApptDetailDrawerEdit,
  setDrivingTimeOpenState,
} from '../../../../store/calendarLatest/actions/private/calendar.actions';
import { apptDetailDrawerBox, apptDetailDrawerEditBox } from '../../../../store/calendarLatest/calendar.boxes';
import { type ApptInfoClientPetInfo } from '../../../../store/calendarLatest/calendar.types';
import { deleteApptPreviewCardList } from '../../../../store/calendarLatest/card.actions';
import { ScopeType } from '../../../../store/grooming/grooming.boxes';
import { verifyAmount } from '../../../../store/payment/actions/private/payment.actions';
import { isNormal } from '../../../../store/utils/identifier';
import { useCancelableCallback } from '../../../../utils/hooks/useCancelableCallback';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { permissionAlertsAsync } from '../../../../utils/message';
import { optionalFunction } from '../../../../utils/utils';
import { useHandleRepeatConfirm } from '../../../Calendar/latest/ApptCalendar/hooks/useRepeatSeriesConfirm';
import { useRefund } from '../../../Calendar/shared/useRefund';
import { useInvoiceReinvent } from '../../../PaymentFlow/hooks/useInvoiceReinvent';
import { EditPetServiceList } from '../../components/EditPetServiceList/EditPetServiceList';
import { useAddSelectedCalendarStaff } from '../../components/EditPetServiceList/hooks/useAddSelectedCalendarStaff';
import {
  GroomingOnlyScheduleMode,
  isValidServiceList,
  useCalcGroomingOnlySchedule,
} from '../../hooks/useCalcGroomingOnlySchedule';
import { useGetPetsInfoForApi } from '../../hooks/useGetPetsInfoForApi';
import { deletePetService, updatePetService } from '../../store/appt.api';
import { apptInfoMapBox, apptPetMapBox } from '../../store/appt.boxes';
import { selectServiceListWithPet } from '../../store/appt.selectors';
import { usePetDelete } from './hooks/usePetDelete';
import { useTicketActions } from './hooks/useTicketActions';

export function ApptEditPet() {
  const [{ ticketId }, editInfo, permissions] = useSelector(
    apptDetailDrawerBox,
    apptDetailDrawerEditBox,
    selectCurrentPermissions(),
  );

  // ticketId equals appointmentId,but appointmentId is string, ticketId is number
  const appointmentId = String(ticketId);
  const [ticket, apptDetailPetMap] = useSelector(apptInfoMapBox.mustGetItem(appointmentId), apptPetMapBox);
  const { pets } = apptDetailPetMap.mustGetItem(appointmentId);
  const store = useStore();
  const {
    serviceDetail,
    customerId: clientId,
    appointment: { repeatId, startAtSameTime },
  } = ticket;

  const { petId: defaultPetId, serviceList: editServiceList, starStaffId, isDirty } = editInfo;
  const defaultServiceList = useMemo(() => {
    return cloneDeep(editServiceList);
  }, [editServiceList]);

  const [state, setState] = useSetState<ApptInfoClientPetInfo>({
    petId: defaultPetId,
    services: defaultServiceList,
  });
  const stateRef = useRef(state);

  const isRepeatSeries = isNormal(repeatId);
  const { refreshTicket } = useTicketActions();
  const addSelectedStaff = useAddSelectedCalendarStaff();
  const dispatch = useDispatch();
  const refreshDrawer = useLatestCallback(async () => refreshTicket());
  const disabledPetIds = useMemo(
    () => serviceDetail.map((i) => +i.pet.id).filter((id) => id !== defaultPetId),
    [ticket, defaultPetId],
  );
  const handleRepeatConfirm = useHandleRepeatConfirm();
  const getPetsInfoForApi = useGetPetsInfoForApi();
  const calcGroomingOnlySchedule = useCalcGroomingOnlySchedule();
  const { requestRefund } = useRefund();
  const { isEnableToNewFlow } = useInvoiceReinvent();

  const handleSubmit = useSerialCallback(async () => {
    await permissionAlertsAsync(permissions, ['canAdvancedEditTicket'], false);
    const { petId, services } = stateRef.current;

    // 当前edit的信息是否勾选了save following 或者 allUpcoming 任意一个
    const hasSelectedSavedInfo = services.some((service) => {
      const { scopeTypePrice, scopeTypeTime } = service;
      return (
        scopeTypePrice === ScopeType.ThisAndFollowing ||
        scopeTypePrice === ScopeType.AllUpComing ||
        scopeTypeTime === ScopeType.ThisAndFollowing ||
        scopeTypeTime === ScopeType.AllUpComing
      );
    });
    await handleRepeatConfirm(!hasSelectedSavedInfo && isRepeatSeries, async (repeatType?: number) => {
      // 老流程才需要才修改时检查是否需要退款
      if (!isEnableToNewFlow) {
        const otherPetsServices = pets
          .filter((pet) => pet.petId !== `${petId}`)
          .reduce((acc, pet) => {
            const v = store.select(selectServiceListWithPet(pet, appointmentId));
            return [...acc, ...v];
          }, [] as ServiceEntry[]);
        const params = {
          groomingId: +ticketId!,
          serviceList: [...services, ...otherPetsServices],
        };
        const rsp = await dispatch(verifyAmount(params));

        if (rsp?.refundAmount > 0) {
          const result = await requestRefund(rsp.invoiceId);
          if (result === false) return;
        }
      }
      await calcGroomingOnlySchedule({
        petIdsServiceList: [{ petId, serviceList: services }],
        allPetsStartAtSameTime: startAtSameTime,
        customerId: String(clientId),
        appointmentId,
        mode: GroomingOnlyScheduleMode.OnlySyncData,
      });
      const newPet = getPetsInfoForApi(appointmentId).find((pet) => pet.petId === String(petId));
      if (!newPet) {
        return;
      }
      await Promise.all([
        dispatch(
          updatePetService({
            appointmentId,
            petDetails: [newPet],
            repeatAppointmentModifyScope: repeatType,
          }),
        ),
      ]);
      if (petId !== defaultPetId) {
        // 老grooming only的逻辑就是切换pet，就删掉原来那只pet
        await dispatch(
          deletePetService({
            appointmentId,
            petId: String(defaultPetId),
          }),
        );
      }
      await refreshDrawer();
      addSelectedStaff(services);
      toastApi.success('Updated successfully!');
      dispatch([exitApptDetailDrawerEdit(), setDrivingTimeOpenState(false)]);
    });
  });

  const deletePet = usePetDelete(refreshDrawer);

  const showDeletePet = useMemo(() => {
    const pets = Array.isArray(serviceDetail) ? serviceDetail : [];
    return pets.length > 1;
  }, [serviceDetail]);

  const handleServiceChange = useCancelableCallback(async (signal, petId: number, serviceList: ServiceEntry[]) => {
    if (handleSubmit.isBusy()) return;

    const res = await calcGroomingOnlySchedule({
      petIdsServiceList: [{ petId, serviceList }],
      allPetsStartAtSameTime: startAtSameTime,
      customerId: String(clientId),
      appointmentId,
      signal,
      originalPetId: isNormal(defaultPetId) && petId !== defaultPetId ? String(defaultPetId) : undefined,
      mode: GroomingOnlyScheduleMode.OnlyPreview,
    });

    const newServiceList = res?.newSchedules?.find((pet) => pet.petId === String(petId))?.groomingServiceSchedules;
    if (newServiceList) {
      // 接口回来之后，覆盖为正确的数据
      setState((pre) => {
        return {
          ...pre,
          petId,
          services: serviceList.map((s) => {
            const newVal = newServiceList.find((ns) => ns.serviceId === String(s.serviceId));
            return newVal
              ? {
                  ...s,
                  startDate: newVal.startDate,
                  startTime: newVal.startTime,
                  endDate: newVal.endDate,
                  endTime: newVal.endTime,
                }
              : s;
          }),
        };
      });
    }
  });

  const handleServiceChangeDebounced = useMemo(() => debounce(handleServiceChange, 300), []);

  const onCancelEditPet = useLatestCallback(async () => {
    dispatch(deleteApptPreviewCardList(String(ticketId)));
    dispatch(exitApptDetailDrawerEdit());
  });

  return (
    <div className={cn('moe-flex moe-flex-col moe-flex-1 moe-min-w-0 moe-min-h-0')}>
      <DrawerHeader title="Edit pet and service" onClick={onCancelEditPet} />
      <EditPetServiceList
        value={state}
        appointmentId={ticketId}
        clientId={+clientId}
        onChange={(newVal) => {
          setState(newVal);
          // TODO(vision,p3) 这个是一个hack，解决的问题是用户在没有失焦的情况下，直接save，导致的数据不一致问题
          // 如果 EditPetAndService 重构的话，可以考虑子组件的 change/ input 改如何向父级传递事件
          stateRef.current = newVal;
          if (isValidServiceList(newVal.services)) {
            dispatch(setApptDetailDrawerEdit({ isDirty: true }));
            handleServiceChangeDebounced(newVal.petId, newVal.services);
          }
        }}
        disabledPetIds={disabledPetIds}
        disabled={!isDirty}
        btnLoading={{
          primary: handleSubmit.isBusy(),
          extra: deletePet.isBusy(),
        }}
        onSave={handleSubmit}
        onDeletePet={optionalFunction(deletePet, showDeletePet)}
        onCancel={onCancelEditPet}
        starStaffId={starStaffId}
        onChangeStarStaff={(starStaffId) => {
          dispatch(setApptDetailDrawerEdit({ starStaffId }));
        }}
        disabledSavedPriceAndDuration={isRepeatSeries}
        isRepeatAppt={isRepeatSeries}
      />
    </div>
  );
}

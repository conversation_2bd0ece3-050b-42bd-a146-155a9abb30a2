import { AppointmentStatus } from '@moego/api-web/moego/models/appointment/v1/appointment_enums';
import { AppointmentNoteType } from '@moego/api-web/moego/models/appointment/v1/appointment_note_enums';
import { useDispatch, useStore } from 'amos';
import dayjs from 'dayjs';
import { useHistory, useRouteMatch } from 'react-router';
import { toastApi } from '../../../../../components/Toast/Toast';
import { PATH_GROOMING_CALENDAR, PATH_HOME_OVERVIEW, PATH_LODGING_CALENDAR } from '../../../../../router/paths';
import { switchBusiness } from '../../../../../store/business/business.actions';
import { currentBusinessIdBox } from '../../../../../store/business/business.boxes';
import { selectCurrentPermissions } from '../../../../../store/business/role.selectors';
import { apptDetailDrawerBox } from '../../../../../store/calendarLatest/calendar.boxes';
import { QuickAddSource, ViewType } from '../../../../../store/calendarLatest/calendar.types';
import { getSavedPriceList } from '../../../../../store/pet/petSavedPrice.actions';
import { getAllBusinessBasicServiceInfoList } from '../../../../../store/service/actions/public/service.actions';
import { selectAllActiveServiceIdList } from '../../../../../store/service/service.selectors';
import { isNormal } from '../../../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../../../../utils/DateTimeUtil';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { permissionAlertAsync } from '../../../../../utils/message';
import { useLazySelectors } from '../../../../../utils/unstable/createSelectAccessor';
import { useAutoFillAddOn, useAutoFillService } from '../../../hooks/useAutoFill';
import { useCalcGroomingOnlySchedule } from '../../../hooks/useCalcGroomingOnlySchedule';
import { getLodgingIds, useCheckConflictAlert } from '../../../hooks/useCheckConflictAlert';
import { useCreateApptDrawer } from '../../../hooks/useCreateApptDrawer';
import { transformToApptServices, useLastApptPetAndService } from '../../../hooks/useLastApptPetAndService';
import { useResolveStaff } from '../../../hooks/useResolveStaff';
import { getAppointment } from '../../../store/appt.api';
import { apptInfoMapBox, apptPetMapBox } from '../../../store/appt.boxes';
import { matchApptFlowScene } from '../../../store/appt.options';
import { selectMainServiceInAppt } from '../../../store/appt.selectors';
import { ApptFlowScene, CreateApptId } from '../../../store/appt.types';

export function useBookAgainFromTicket() {
  const store = useStore();
  const dispatch = useDispatch();
  const inCalendarRoute = useRouteMatch(PATH_GROOMING_CALENDAR.path);
  const inLodgingRoute = useRouteMatch(PATH_LODGING_CALENDAR.path);
  const inOverviewRoute = useRouteMatch(PATH_HOME_OVERVIEW.path);
  const checkConflictAlert = useCheckConflictAlert();

  const history = useHistory();

  const [permissions, activeIdList, businessId, apptDetail] = useLazySelectors(
    selectCurrentPermissions(),
    selectAllActiveServiceIdList(),
    currentBusinessIdBox,
    apptDetailDrawerBox,
  );

  const autoFillAddOn = useAutoFillAddOn();
  const autoFillService = useAutoFillService();
  const calcGroomingOnlySchedule = useCalcGroomingOnlySchedule();
  const getFilterServices = useLastApptPetAndService();
  const { getAvailableStaff } = useResolveStaff();
  const openCreateDrawer = useCreateApptDrawer();

  return useSerialCallback(async (outTicketId?: number) => {
    await permissionAlertAsync(permissions(), 'createAppointment');

    const finalTicketId = isNormal(outTicketId) ? outTicketId : apptDetail().ticketId;
    const [availableStaffs] = await Promise.all([
      getAvailableStaff(),
      dispatch(getAppointment({ appointmentId: String(finalTicketId) })),
      !activeIdList().size && dispatch(getAllBusinessBasicServiceInfoList()),
    ]);
    const ticket = store.select(apptInfoMapBox.mustGetItem(String(finalTicketId)));
    const {
      notes,
      appointment: { colorCode, startAtSameTime, status },
      customerId,
    } = ticket;
    const originApptId = ticket.appointment.id;
    const newApptId = CreateApptId;
    const alertNotes = notes.find((note) => note.type === AppointmentNoteType.ALERT_NOTES)?.note || '';
    const pets = ticket.serviceDetail.map((s) => s.pet.id);

    const filterServices = getFilterServices({
      appointment: ticket.appointment,
      serviceDetail: ticket.serviceDetail,
      serviceItemTypes: ticket.serviceItemTypes,
      availableStaffs,
    });
    const filterPets = await transformToApptServices(filterServices);

    if (!filterPets.length) {
      return toastApi.error('Book again failed, pets or services in this appointment are no longer available.');
    }

    const lodgingUnitIds = filterServices.reduce((prev, curr) => {
      return prev.concat(curr.services.map((s) => getLodgingIds(s.serviceDetail.lodgingId, s.splitLodgings)).flat());
    }, [] as string[]);
    const { startDate, endDate } = filterServices
      .reduce(
        (prev, curr) => {
          const { services, addOns, evaluations } = curr;
          return prev.concat(
            services
              .map(({ serviceDetail }) => ({ startDate: serviceDetail.startDate, endDate: serviceDetail.endDate }))
              .concat(
                addOns.map(({ serviceDetail }) => ({
                  startDate: serviceDetail.startDate,
                  endDate: serviceDetail.endDate,
                })),
              )
              .concat(evaluations.map((e) => ({ startDate: e.startDate, endDate: e.endDate }))),
          );
        },
        [] as { startDate: string; endDate: string }[],
      )
      .reduce(
        (prev, curr) => {
          const { startDate, endDate } = curr;
          return {
            startDate: dayjs(startDate).isBefore(prev.startDate, 'day') ? startDate : prev.startDate,
            endDate: dayjs(endDate).isAfter(prev.endDate, 'day') ? endDate : prev.endDate,
          };
        },
        {
          startDate: dayjs().format(DATE_FORMAT_EXCHANGE),
          endDate: dayjs().format(DATE_FORMAT_EXCHANGE),
        },
      );
    if (status !== AppointmentStatus.FINISHED) {
      await checkConflictAlert({
        appointmentId: newApptId,
        petIds: pets,
        lodgingUnitIds,
        startDateStr: startDate,
        endDateStr: endDate,
        showCancel: false,
        confirmText: 'Got it',
        clientId: String(customerId),
      });
    }

    await dispatch(switchBusiness(Number(businessId())));

    await Promise.all(pets.map((id) => dispatch(getSavedPriceList(Number(id)))));

    await dispatch(apptPetMapBox.mergeItem(newApptId, { appointmentId: newApptId, pets: filterPets }));

    const actions = filterServices
      .map(({ pet, services, addOns }) => {
        const params = { petId: pet.id, originApptId, newApptId, availableStaffs };
        return [
          ...services.map((s) => {
            return autoFillService({ ...params, originService: s });
          }),
          ...addOns.map((s) => {
            return autoFillAddOn({ ...params, originService: s });
          }),
        ];
      })
      .flat();

    await Promise.all(dispatch(actions));

    const { serviceItemType } = store.select(selectMainServiceInAppt());
    if (matchApptFlowScene(ApptFlowScene.AutoCalc, serviceItemType)) {
      await calcGroomingOnlySchedule({
        allPetsStartAtSameTime: startAtSameTime,
        customerId: customerId,
        appointmentId: newApptId,
      });
    }

    openCreateDrawer({
      params: {
        source: QuickAddSource.BookAgain,
        clientId: +customerId,
        disableViewClientLastAppt: true,
        allPetsStartAtSameTime: startAtSameTime,
        alertNotes,
        colorCode,
      },
      onReady() {
        if (!inCalendarRoute && !inOverviewRoute && !inLodgingRoute) {
          // 这个逻辑我认为不对，book again 应该根据appt 的类型来跳不同的路由，而不是都到 staff calendar
          history.push(
            PATH_GROOMING_CALENDAR.stated({
              backCalendarView: ViewType.DAY,
            }),
          );
        }
      },
    });
  });
}

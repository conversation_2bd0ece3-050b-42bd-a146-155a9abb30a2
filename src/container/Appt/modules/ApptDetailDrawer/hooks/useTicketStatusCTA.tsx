import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useSelector } from 'amos';
import type React from 'react';
import { getMainCareType } from '../../../../../components/PetAndServicePicker/utils/getMainCareType';
import { ApptTestIds } from '../../../../../config/testIds/apptDrawer';
import { selectCurrentPermissions } from '../../../../../store/business/role.selectors';
import {
  ReadyForPickupConfirmModalMode,
  calendarLoadingEventsBox,
} from '../../../../../store/calendarLatest/calendar.boxes';
import { type TicketDetailInfo } from '../../../../../store/calendarLatest/calendar.types';
import { bookingTableLoadingEventsBox } from '../../../../../store/grooming/grooming.boxes';
import { overviewLoadingEventsBox } from '../../../../../store/overview/overview.boxes';
import { useBusinessIsWorkingLocation } from '../../../../../utils/BusinessUtil';
import { useChangeTicketStatus } from '../../../../Calendar/Grooming/GroomingTicketModal/hooks/useChangeTicketStatus';
import { useInvoiceReinvent } from '../../../../PaymentFlow/hooks/useInvoiceReinvent';
import { AppointmentStatus } from '../../../../TicketDetail/AppointmentStatus';
import { useConfirmCheckOut } from '../../../components/ApptCheckInOutAlert/hooks/useConfirmCheckOut';
import { type ActionItem } from '../../../components/types';
import { useWrapAsyncActionItem } from '../../../hooks/useWrapAsyncActionItem';
import { type ApptDaycareServiceExceedDuration } from '../../../store/appt.boxes';
import { selectMainServiceInAppt } from '../../../store/appt.selectors';
import { reportCheckInScene } from '../../../utils/apptReport';
import { type ApptCTAScene } from '../../../utils/types';
import { useBookAgainFromTicket } from './useBookAgainFromTicket';
import { useFutureApptCheckoutAlert } from './useFutureApptCheckoutAlert';
import { useRenderCheckoutExceedDuration } from './useRenderCheckoutExceedDuration';
import { useTicketActions } from './useTicketActions';
import { OnlineBookingReporter } from '../../../../../utils/reportData/reporter/onlineBookingReporter';

export const NextActionLabel = {
  Unconfirmed: 'Check in',
  Confirmed: 'Check in',
  CheckedIn: 'Mark as ready',
  Ready: 'Check out',
  Finished: 'Book again',
  Canceled: 'Book again',
};

/**
 * @deprecated use useApptEditStatusCTA instead after invoice reinvent fully launched
 */
export const useTicketStatusCTA = (ticket: // 方便复用至overview, 显示传参
{
  id: TicketDetailInfo['id'];
  appointmentStatus: TicketDetailInfo['appointmentStatus'];
  groomingCustomerInfoIsDeleted: TicketDetailInfo['groomingCustomerInfo']['isDeleted'];
  petExceedDurationList?: ApptDaycareServiceExceedDuration[];
  serviceItemTypes?: ServiceItemType[];
  apptCTAScene: ApptCTAScene;
}): {
  label: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  testId?: string;
} => {
  const {
    id: ticketId,
    appointmentStatus,
    groomingCustomerInfoIsDeleted,
    petExceedDurationList = [],
    serviceItemTypes,
    apptCTAScene,
  } = ticket;
  const dispatch = useDispatch();
  const [mainService] = useSelector(selectMainServiceInAppt(String(ticketId)));
  // 列表里拿到的serviceType默认是 ServiceItemType.Grooming, 得到 not bd ，所以需要判断serviceItemTypes.includes的逻辑
  const isBD =
    serviceItemTypes?.includes(ServiceItemType.BOARDING) ||
    serviceItemTypes?.includes(ServiceItemType.DAYCARE) ||
    [ServiceItemType.BOARDING, ServiceItemType.DAYCARE].includes(mainService.serviceItemType);

  const clientIsDeleted = !!groomingCustomerInfoIsDeleted;
  const { refreshTicket, setTakePaymentModal, setReadyForPickupModal } = useTicketActions(ticketId);
  const bookAgain = useBookAgainFromTicket();
  const handleChangeTicketStatus = useChangeTicketStatus(() => refreshTicket());
  const futureApptAlert = useFutureApptCheckoutAlert(ticketId);
  const exceedDuration = useRenderCheckoutExceedDuration(petExceedDurationList);
  const handleConfirmCheckout = useConfirmCheckOut({
    appointmentId: String(ticketId),
    renderAlertBefore: futureApptAlert ? () => futureApptAlert : undefined,
    renderAlertAfter: petExceedDurationList?.length ? exceedDuration : undefined,
  });
  const { warpAsyncActionItem } = useWrapAsyncActionItem();
  const isWorkingLocation = useBusinessIsWorkingLocation();
  const [permissions] = useSelector(selectCurrentPermissions);
  const canProcessPayment = permissions.has('canProcessPayment');
  const { isNewOrderV4Flow } = useInvoiceReinvent();
  const isActionDisabled = !isWorkingLocation;

  const handlePayment = () => {
    if (canProcessPayment) {
      setTakePaymentModal(true, { isCheckout: true });
    }
  };

  const actionMap: { [key in AppointmentStatus]?: ActionItem } = {
    [AppointmentStatus.UNCONFIRMED]: {
      label: NextActionLabel.Unconfirmed,
      onClick: (params) => {
        reportCheckInScene({
          apptCTAScene,
          mainServiceItemType: serviceItemTypes?.length
            ? getMainCareType(serviceItemTypes)
            : mainService.serviceItemType,
        });
        OnlineBookingReporter.reportApptClickCheckIn(ticketId);
        const { extra, needRefreshTicket } = params?.checkInParams ?? {};
        return handleChangeTicketStatus(ticketId, AppointmentStatus.CHECKED_IN, extra, needRefreshTicket);
      },
      disabled: isActionDisabled,
      testId: ApptTestIds.ApptCheckInBtn,
    },
    [AppointmentStatus.CONFIRMED]: {
      label: NextActionLabel.Confirmed,
      onClick: (params) => {
        reportCheckInScene({
          apptCTAScene,
          mainServiceItemType: serviceItemTypes?.length
            ? getMainCareType(serviceItemTypes)
            : mainService.serviceItemType,
        });
        const { extra, needRefreshTicket } = params?.checkInParams ?? {};
        return handleChangeTicketStatus(ticketId, AppointmentStatus.CHECKED_IN, extra, needRefreshTicket);
      },
      disabled: isActionDisabled,
      testId: ApptTestIds.ApptCheckInBtn,
    },
    [AppointmentStatus.CHECKED_IN]: {
      label: isBD ? NextActionLabel.Ready : NextActionLabel.CheckedIn,
      onClick: async () => {
        if (isBD) {
          OnlineBookingReporter.reportApptClickCheckOut(ticketId);
          const { next, extra } = await handleConfirmCheckout();
          if (next) {
            await handleChangeTicketStatus(ticketId, AppointmentStatus.FINISHED, extra);
            handlePayment();
          }
          return;
        }
        if (isNewOrderV4Flow) {
          handlePayment();
          return;
        }
        return setReadyForPickupModal(true, ReadyForPickupConfirmModalMode.ManualSendMessage);
      },
      disabled: isActionDisabled,
      testId: isBD ? 'appt-check-out-btn' : 'appt-mark-as-ready-btn',
    },
    [AppointmentStatus.READY]: {
      label: NextActionLabel.Ready,
      onClick: async () => {
        OnlineBookingReporter.reportApptClickCheckOut(ticketId);
        dispatch([
          calendarLoadingEventsBox.setState(true),
          overviewLoadingEventsBox.setState(true),
          bookingTableLoadingEventsBox.setState(true),
        ]);

        try {
          const { extra, next } = await handleConfirmCheckout();
          if (next) {
            await handleChangeTicketStatus(ticketId, AppointmentStatus.FINISHED, extra);
            handlePayment();
          }
        } finally {
          dispatch([
            calendarLoadingEventsBox.setState(false),
            overviewLoadingEventsBox.setState(false),
            bookingTableLoadingEventsBox.setState(false),
          ]);
        }
      },
      disabled: isActionDisabled,
      testId: ApptTestIds.ApptCheckOutBtn,
    },
    [AppointmentStatus.FINISHED]: {
      label: NextActionLabel.Finished,
      onClick: async () => {
        bookAgain();
      },
      disabled: clientIsDeleted,
      testId: ApptTestIds.ApptBookAgainBtn,
    },
    [AppointmentStatus.CANCELED]: {
      label: NextActionLabel.Canceled,
      onClick: async () => {
        bookAgain();
      },
      disabled: clientIsDeleted,
      testId: ApptTestIds.ApptBookAgainBtn,
    },
  };

  if (ticket.appointmentStatus) {
    const item = actionMap[appointmentStatus as AppointmentStatus];

    if (item) {
      return warpAsyncActionItem(item);
    }
  }

  // fall back to book again?
  return {
    label: 'Book again',
    onClick: bookAgain,
    disabled: clientIsDeleted,
    testId: ApptTestIds.ApptBookAgainBtn,
  };
};

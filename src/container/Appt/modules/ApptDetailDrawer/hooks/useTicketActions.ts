import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch } from 'amos';
import { useHistory, useRouteMatch } from 'react-router';
import { openGlobalModal } from '../../../../../components/globals/GlobalModals.store';
import { store } from '../../../../../provider';
import { PATH_GROOMING_CALENDAR, PATH_TICKET_EDIT } from '../../../../../router/paths';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../../../store/business/role.selectors';
import { setApptDetailModalState } from '../../../../../store/calendarLatest/actions/private/calendar.actions';
import { setApptDetailDrawer } from '../../../../../store/calendarLatest/actions/public/calendar.actions';
import {
  type ApptDetailModalStateBox,
  type ReadyForPickupConfirmModalMode,
  type SetTakePaymentModalOptions,
  apptDetailDrawerBox,
  calendarCurrentViewBox,
  calendarSelectedDate,
} from '../../../../../store/calendarLatest/calendar.boxes';
import { deleteApptPreviewCardList } from '../../../../../store/calendarLatest/card.actions';
import { emitReloadMapViewAppts } from '../../../../../store/mapView/actions/public/mapView.actions';
import { OverviewMapBox, OverviewRecord } from '../../../../../store/overview/overview.boxes';
import { getInvoice } from '../../../../../store/payment/actions/private/payment.actions';
import { isNormal } from '../../../../../store/utils/identifier';
import { autoParallelTask } from '../../../../../utils/autoParallelTask';
import { globalEvent } from '../../../../../utils/events/events';
import { growthBook } from '../../../../../utils/growthBook/growthBook';
import { GrowthBookFeatureList } from '../../../../../utils/growthBook/growthBook.config';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { useOpenApptDetailDrawer } from '../../../../../utils/hooks/useOpenApptDetailDrawer';
import { permissionAlertsAsync } from '../../../../../utils/message';
import { useLazySelectors } from '../../../../../utils/unstable/createSelectAccessor';
import { useGetCalendarEvents } from '../../../../Calendar/latest/ApptCalendar/hooks/useFullCalendarEvents';
import { useReloadWorkingStaff } from '../../../../Calendar/latest/hooks/useLoadWorkingStaff';
import { getAppointment } from '../../../store/appt.api';
import { apptInfoMapBox } from '../../../store/appt.boxes';
import { ApptEventEnum, apptEmitter } from '../../../utils/apptEmitter';

export interface FetchTicketDataParams {
  appointmentId?: string;
  invoiceId?: string;
  serviceItemTypes?: ServiceItemType[];
  reloadCalendarLodgingOverview?: boolean;
}

/**
 * 场景1: 在 appt drawer 中的 详情页
 * 场景2: overview 这种列表型的组件中通过 forceTicketId 强行覆盖
 */
export function useTicketActions(forceTicketId?: number) {
  const history = useHistory();
  const dispatch = useDispatch();
  const inCalendarRoute = useRouteMatch(PATH_GROOMING_CALENDAR.path);
  const { openApptDetailDrawer } = useOpenApptDetailDrawer();
  const reloadWorkingStaff = useReloadWorkingStaff();
  const { reloadAppts } = useGetCalendarEvents();

  const [selectedDate, currentView, apptDetail, permissions, business, overviewMap] = useLazySelectors(
    calendarSelectedDate,
    calendarCurrentViewBox,
    apptDetailDrawerBox,
    selectCurrentPermissions(),
    selectCurrentBusiness,
    OverviewMapBox,
  );

  const ticketId = () => forceTicketId ?? apptDetail().ticketId;

  /** 跳转到编辑页面 */
  const go2AdvancedEdit = useLatestCallback(async () => {
    await permissionAlertsAsync(permissions(), ['canAdvancedEditTicket'], false);
    closeApptDetailDrawer();
    const go2Path = PATH_TICKET_EDIT.fully(
      { ticketId: ticketId() },
      {
        backCalendarView: currentView(),
        backDate: selectedDate().valueOf(),
      },
      {},
    );
    history.push(go2Path);
  });

  const closeApptDetailDrawer = useLatestCallback(() => {
    dispatch(setApptDetailDrawer({ visible: false, loading: false }));
  });

  const setPrintCardModalVisible = useLatestCallback((visible: boolean) => {
    dispatch(
      setApptDetailModalState({
        printCardModalVisible: visible,
      }),
    );
  });

  const setCancelTicketModalVisible = useLatestCallback((visible: boolean) => {
    dispatch(
      setApptDetailModalState({
        cancelApptModalVisible: visible,
      }),
    );
  });

  const setWaitingListModalVisible = useLatestCallback((visible: boolean) => {
    dispatch(
      setApptDetailModalState({
        waitingListModalVisible: visible,
      }),
    );
  });

  const setTakePaymentModal = useLatestCallback((visible: boolean, options?: SetTakePaymentModalOptions) => {
    if (!permissions().has('canProcessPayment')) return;
    dispatch(
      setApptDetailModalState({
        takePaymentModal: {
          visible,
          isDeposit: !!options?.isDeposit,
          isViewOnly: !!options?.isViewOnly,
          isCheckout: !!options?.isCheckout,
        },
      }),
    );
  });

  const setReadyForPickupModal = useLatestCallback(
    (visible: boolean, mode: ReadyForPickupConfirmModalMode, markDone?: boolean) => {
      dispatch(
        setApptDetailModalState({
          readyForPickupModal: {
            visible,
            mode,
            markDone,
          },
        }),
      );
    },
  );

  /** 刷新当前 ticket 详情，带 loading */
  const fetchTicketData = useLatestCallback(async (params: FetchTicketDataParams) => {
    return getTicketData(
      {
        ...params,
        onReload: async () => {
          if (inCalendarRoute) {
            await Promise.all([reloadAppts({ loading: false }), reloadWorkingStaff()]);
          }
          await globalEvent.refresh.emit();
        },
      },
      forceTicketId,
    );
  });

  /** 预取 ticket 详情 */
  const refreshTicket = useLatestCallback(async (reloadCalendarLodgingOverview = true) => {
    const res = await fetchTicketData({ reloadCalendarLodgingOverview });
    apptEmitter.emit(ApptEventEnum.APPT_REFRESHED);
    return res;
  });

  const prefetchTicket = useLatestCallback(
    async (params: Omit<FetchTicketDataParams, 'reloadCalendarLodgingOverview'>) => {
      return await fetchTicketData({ ...params, reloadCalendarLodgingOverview: false });
    },
  );

  const setChargeNoShowFeeModal = useLatestCallback((visible: boolean) => {
    dispatch(
      setApptDetailModalState({
        chargeNoShowFeeModalVisible: visible,
      }),
    );
  });

  const setNoShowTakePaymentModal = useLatestCallback((visible: boolean) => {
    dispatch(
      setApptDetailModalState({
        noShowTakePaymentModalVisible: visible,
      }),
    );
  });

  const setApptToWaitlistModal = useLatestCallback((props: ApptDetailModalStateBox['createWaitlistModal']) => {
    dispatch(setApptDetailModalState({ createWaitlistModal: props }));
  });

  const setEditLodgingAssignmentModalVisible = useLatestCallback((visible: boolean) => {
    const overviewInfo = overviewMap().mustGetItem(OverviewRecord.ownKey(business().id, String(ticketId())));
    if (overviewInfo.isSplitLodgingsAppt) {
      openApptDetailDrawer({ ticketId: ticketId() });
    } else {
      dispatch(
        setApptDetailModalState({
          editLodgingAssignmentModalVisible: visible,
        }),
      );
    }
  });

  const setPetIncidentModalVisible = useLatestCallback((visible: boolean, petIds: string[]) => {
    dispatch(
      openGlobalModal({
        petIncident: { visible, defaultPetIdList: petIds },
      }),
    );
  });
  return {
    go2AdvancedEdit,
    setPrintCardModalVisible,
    setCancelTicketModalVisible,
    setWaitingListModalVisible,
    closeApptDetailDrawer,
    setPetIncidentModalVisible,
    setTakePaymentModal,
    refreshTicket,
    prefetchTicket,
    setReadyForPickupModal,
    setChargeNoShowFeeModal,
    setNoShowTakePaymentModal,
    setApptToWaitlistModal,
    setEditLodgingAssignmentModalVisible,
  };
}

export const getTicketData = async (
  params: FetchTicketDataParams & {
    onReload?: () => void;
  },
  forceTicketId?: number,
) => {
  const dispatch = store.dispatch;
  const apptInfoMap = store.select(apptInfoMapBox);
  const apptDetail = store.select(apptDetailDrawerBox);
  const ticketId = forceTicketId ?? apptDetail.ticketId;

  const { reloadCalendarLodgingOverview = true, onReload } = params;
  try {
    const appointmentId = params?.appointmentId ?? ticketId;
    const apptInfo = apptInfoMap.mustGetItem(String(appointmentId));
    const serviceItemTypes = params?.serviceItemTypes ?? apptInfo.serviceItemTypes;
    const invoiceId = params?.invoiceId ?? apptInfo.invoice?.invoiceId;

    dispatch(setApptDetailDrawer({ loading: true }));
    const [appointmentData] = await autoParallelTask(
      [
        {
          condition: ({ appointmentId }) => isNormal(appointmentId),
          run: async (params, setParams) => {
            const res = await dispatch(
              getAppointment({
                appointmentId: String(params.appointmentId),
                serviceItemTypes: params.serviceItemTypes,
              }),
            );
            setParams({ invoiceId: res.invoice?.invoiceId });
            return res;
          },
        },
        {
          condition: ({ appointmentId, invoiceId }) =>
            !growthBook.isOn(GrowthBookFeatureList.NewOrderV4Flow) && isNormal(appointmentId) && isNormal(invoiceId),
          run: async ({ invoiceId }) => {
            return await dispatch(getInvoice(Number(invoiceId), 'grooming'));
          },
        },
      ],
      { appointmentId, invoiceId, serviceItemTypes },
    );
    return appointmentData;
  } finally {
    dispatch(setApptDetailDrawer({ loading: false }));
    dispatch(emitReloadMapViewAppts());
    if (reloadCalendarLodgingOverview) {
      onReload?.();
    }
    dispatch(deleteApptPreviewCardList(String(ticketId)));
  }
};
export const prefetchTicket = async (params: Omit<FetchTicketDataParams, 'reloadCalendarLodgingOverview'>) => {
  return await getTicketData({ ...params, reloadCalendarLodgingOverview: false });
};
export const setPetIncidentModalVisible = (visible: boolean, petIds: string[]) => {
  store.dispatch(
    openGlobalModal({
      petIncident: { visible, defaultPetIdList: petIds },
    }),
  );
};

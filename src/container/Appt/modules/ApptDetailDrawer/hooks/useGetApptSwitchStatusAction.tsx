import { useDispatch, useSelector } from 'amos';
import classNames from 'classnames';
import React from 'react';
import IconThinRevert from '../../../../../assets/svg/icon-revert-thin.svg';
import { SvgIcon } from '../../../../../components/Icon/Icon';
import { Tooltip } from '../../../../../components/Popup/Tooltip';
import { ApptTestIds } from '../../../../../config/testIds/apptDrawer';
import { selectCurrentPermissions } from '../../../../../store/business/role.selectors';
import { undoTicketStatus } from '../../../../../store/grooming/grooming.actions';
import { type LatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { permissionAlertsAsync } from '../../../../../utils/message';
import { ReportActionName } from '../../../../../utils/reportType';
import { reportData } from '../../../../../utils/tracker';
import { useRevertTicketStatusConfirm } from '../../../../Calendar/Grooming/GroomingTicketModal/hooks/useRevertTicketStatusConfirm';
import { AppointmentStatus } from '../../../../TicketDetail/AppointmentStatus';
import { type ITicketStatusOption, ticketStatusOptions } from '../../../../TicketDetail/interfaces.latest';
import { type DropdownMenuProps } from '../../../components/DropdownMenu';
import { type ApptInfo } from '../../../store/appt.boxes';
import { OnlineBookingReporter } from '../../../../../utils/reportData/reporter/onlineBookingReporter';

interface ApptSwitchStatusParams<T = () => unknown> {
  appointmentStatus: AppointmentStatus;
  ticketOptionList: ITicketStatusOption[];
  onStatusChange: LatestCallback<(targetStatus: AppointmentStatus) => void>;
  ticket: ApptInfo;
  refreshTicket: T;
  ticketId: number;
}

export const useGetApptSwitchStatusAction = (params: ApptSwitchStatusParams) => {
  const { appointmentStatus, ticketOptionList, onStatusChange, ticket, refreshTicket, ticketId } = params;
  const [permissions] = useSelector(selectCurrentPermissions());
  const hasCancelPermission = permissions.has('cancelOrDeleteTicket');
  const dispatch = useDispatch();
  const handleRevertStatusConfirm = useRevertTicketStatusConfirm(ticket);

  const actions: DropdownMenuProps['actions'] = ticketOptionList.map((item) => {
    const { id } = item;
    const target = ticketStatusOptions.find((s) => s.id === id)!;
    const isCancel = target.id === AppointmentStatus.CANCELED;

    return {
      id: target.id,
      label: isCancel ? (
        <Tooltip
          width={292}
          destroyTooltipOnHide
          theme="black"
          overlay="Please request “cancel ticket” permission from the business owner"
          disabled={hasCancelPermission}
        >
          <div
            className="moe-flex moe-items-center moe-gap-x-[10px]"
            onClick={(e) => {
              if (!hasCancelPermission) {
                // 就不会触发 外面的DropdownMenu 的 onClick了
                e.stopPropagation();
              }
            }}
          >
            <div className="moe-w-[16px] moe-h-[16px] moe-rounded-full" style={{ backgroundColor: item.color }} />
            <div className={classNames(!hasCancelPermission && 'moe-text-[#ccc]')}>{target.label}</div>
          </div>
        </Tooltip>
      ) : (
        <div className="moe-flex moe-items-center moe-gap-x-[10px]">
          <div className="moe-w-[16px] moe-h-[16px] moe-rounded-full" style={{ backgroundColor: item.color }} />
          {target.label}
        </div>
      ),
      onClick: async () => {
        if (target.id === AppointmentStatus.CHECKED_IN) {
          OnlineBookingReporter.reportApptClickCheckIn(ticketId);
        }
        reportData(ReportActionName.CalendarSwitchStatus);
        onStatusChange(id);
      },
      testId: target.testId,
    };
  });

  if (appointmentStatus === AppointmentStatus.CHECKED_IN) {
    actions.push({
      id: -AppointmentStatus.CHECKED_IN,
      label: (
        <div className="moe-flex moe-items-center moe-gap-x-[8px]">
          <SvgIcon src={IconThinRevert} size={20} />
          <div className="moe-text-sm moe-font-medium moe-text-[#202020]">Undo check-in</div>
        </div>
      ),
      className: '!moe-border-0 !moe-border-solid !moe-border-t !moe-border-t-[#E6E6E6]',
      onClick: async () => {
        reportData(ReportActionName.CalendarSwitchStatus);
        const res = await handleRevertStatusConfirm();
        if (res) {
          await dispatch(undoTicketStatus(ticketId));
          await refreshTicket();
        }
      },
      testId: ApptTestIds.ApptStatusUndoCheckInBtn,
    });
  } else if (appointmentStatus === AppointmentStatus.READY) {
    actions.push({
      id: -AppointmentStatus.READY,
      label: (
        <div className="moe-flex moe-items-center moe-gap-x-[8px]">
          <SvgIcon src={IconThinRevert} size={20} />
          <div className="moe-text-sm moe-font-medium moe-text-[#202020]">Undo ready</div>
        </div>
      ),
      className: '!moe-border-0 !moe-border-solid !moe-border-t !moe-border-t-[#E6E6E6]',
      onClick: async () => {
        reportData(ReportActionName.CalendarSwitchStatus);
        const res = await handleRevertStatusConfirm();
        if (res) {
          await dispatch(undoTicketStatus(ticketId));
          await refreshTicket();
        }
      },
      testId: ApptTestIds.ApptStatusUndoReadyBtn,
    });
  } else if (appointmentStatus === AppointmentStatus.FINISHED) {
    actions.push({
      id: -AppointmentStatus.FINISHED,
      label: (
        <div className="moe-flex moe-items-center moe-gap-x-[8px]">
          <SvgIcon src={IconThinRevert} size={20} />
          <div className="moe-text-sm moe-font-medium moe-text-[#202020]">Undo check-out</div>
        </div>
      ),
      className: '!moe-border-0 !moe-border-solid !moe-border-t !moe-border-t-[#E6E6E6]',
      onClick: async () => {
        reportData(ReportActionName.CalendarSwitchStatus);
        // undo 里只有 undo checkout 校验 createNewBooking 权限
        await permissionAlertsAsync(permissions, ['canAdvancedEditTicket'], false);
        const res = await handleRevertStatusConfirm();
        if (res) {
          await dispatch(undoTicketStatus(ticketId));
          await refreshTicket();
        }
      },
      testId: ApptTestIds.ApptStatusUndoCheckOutBtn,
    });
  }
  return actions;
};

import { AppointmentStatus } from '@moego/api-web/moego/models/appointment/v1/appointment_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { MajorPlusOutlined } from '@moego/icons-react';
import { Button, cn } from '@moego/ui';
import { useDispatch, useSelector, useStore } from 'amos';
import React, { memo, useCallback } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { PetServiceCard } from '../../../../../../components/PetAndServicePicker/components/PetServiceCard';
import { type ServiceEntry } from '../../../../../../components/ServiceApplicablePicker/types/serviceEntry';
import { getDefaultService } from '../../../../../../components/ServiceApplicablePicker/utils/getDefaultService';
import { toastApi } from '../../../../../../components/Toast/Toast';
import { ApptTestIds } from '../../../../../../config/testIds/apptDrawer';
import { selectCurrentPermissions } from '../../../../../../store/business/role.selectors';
import {
  setApptDetailDrawerAdd,
  setApptDetailDrawerEdit,
  setApptDetailDrawerHybridEdit,
} from '../../../../../../store/calendarLatest/actions/private/calendar.actions';
import { apptDetailDrawerAddBox, apptDetailDrawerBox } from '../../../../../../store/calendarLatest/calendar.boxes';
import {
  type ApptInfoPetServiceInfo,
  type ApptInfoPetServiceInfoWithTime,
} from '../../../../../../store/calendarLatest/calendar.types';
import { CommentNoteType } from '../../../../../../store/calendarLatest/calendar.utils';
import { verifyAmount } from '../../../../../../store/payment/actions/private/payment.actions';
import { isNormal } from '../../../../../../store/utils/identifier';
import { useBusinessIsWorkingLocation } from '../../../../../../utils/BusinessUtil';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { permissionAlertsAsync } from '../../../../../../utils/message';
import { PaymentActionName } from '../../../../../../utils/reportData/payment';
import { type reportData } from '../../../../../../utils/tracker';
import { optionalFunction } from '../../../../../../utils/utils';
import { useDeletePetConfirm } from '../../../../../Calendar/latest/ApptCalendar/hooks/useDeletePetConfirm';
import { useGetCalendarEvents } from '../../../../../Calendar/latest/ApptCalendar/hooks/useFullCalendarEvents';
import { ApptNavigationTabsV3 } from '../../../../../Calendar/latest/AwesomeCalendar.utils';
import { useRefund } from '../../../../../Calendar/shared/useRefund';
import { useInvoiceReinvent } from '../../../../../PaymentFlow/hooks/useInvoiceReinvent';
import { useInvoiceReinventReport } from '../../../../../PaymentFlow/hooks/useInvoiceReinvent.report';
import { EvaluationApptDetailTips } from '../../../../components/EvaluationCombined/EvaluationApptDetailTips';
import { PricingRuleRender } from '../../../../components/PricingRuleRender/PricingRuleRender';
import { useCheckAddNewServiceType } from '../../../../hooks/useCheckAddNewServiceType';
import { useGetServiceListByPet } from '../../../../hooks/useGetServiceListByPet';
import { deletePetService } from '../../../../store/appt.api';
import { apptInfoMapBox, apptPetMapBox } from '../../../../store/appt.boxes';
import { matchApptFlowScene } from '../../../../store/appt.options';
import { selectMainServiceInAppt, selectServiceListWithPet } from '../../../../store/appt.selectors';
import { ApptFlowScene } from '../../../../store/appt.types';
import { ApptDailyReportEntrance } from '../../Report/ApptDailyReport';
import { ApptGroomingReportEntrance } from '../../Report/ApptGroomingReportEntrance';
import { useTicketActions } from '../../hooks/useTicketActions';
import { useTicketDrawerDetail } from '../../hooks/useTicketDrawerDetail';
import { EditTicketPermissionTip } from '../EditTicketPermissionTip';

export interface PetInfoDetailProps {
  className?: string;
  appointmentId: string;
  /** editable means the ticket is not finished or cancelled */
  editable: boolean;
  disabled?: boolean;
}

export const PetInfoDetail = memo<PetInfoDetailProps>(function PetInfoDetail(props) {
  const dispatch = useDispatch();
  const store = useStore();
  const { refreshTicket } = useTicketActions();
  const { className, appointmentId, disabled } = props;
  const editable = props.editable && !disabled;
  const { reloadAppts } = useGetCalendarEvents();
  const { clientId, repeatId } = useTicketDrawerDetail();
  const isWorkingLocation = useBusinessIsWorkingLocation();
  const [{ ticketId }, { pets }, permissions, { appointmentStatus, appointmentEndDate }] = useSelector(
    apptDetailDrawerBox,
    apptPetMapBox.mustGetItem(appointmentId),
    selectCurrentPermissions(),
    apptInfoMapBox.mustGetItem(appointmentId),
  );
  const { isEnableToNewFlow } = useInvoiceReinvent();
  const getServiceListByPet = useGetServiceListByPet();
  const checkAddNewServiceType = useCheckAddNewServiceType(String(ticketId));
  const { requestRefund } = useRefund();
  const refreshDrawer = useLatestCallback(async () => {
    await Promise.all([refreshTicket(), reloadAppts({ loading: false })]);
  });
  const reportApptData = useInvoiceReinventReport<typeof reportData>();

  const handleDeletePetConfirm = useDeletePetConfirm({
    isInRepeatSeries: isNormal(repeatId),
  });

  const handleDelete = useSerialCallback(async (petId: string) => {
    await handleDeletePetConfirm(async (repeatType?: number) => {
      await dispatch(
        deletePetService({
          appointmentId: appointmentId,
          petId,
          repeatAppointmentModifyScope: repeatType,
        }),
      );

      const petServiceList = pets
        .filter((pet) => pet.petId !== `${petId}`)
        .reduce((acc, pet) => {
          const v = store.select(selectServiceListWithPet(pet, appointmentId));
          return [...acc, ...v];
        }, [] as ServiceEntry[]);
      const params = {
        groomingId: +ticketId!,
        serviceList: petServiceList,
      };

      await refreshDrawer();
      if (!isEnableToNewFlow) {
        dispatch(verifyAmount(params)).then(async (rsp) => {
          if (rsp?.refundAmount > 0) {
            await requestRefund(rsp.invoiceId);
          }
        });
      }
      toastApi.success('Updated successfully!');
    });
  });

  const handleEdit = async (index: number, petId: number, services: ApptInfoPetServiceInfo[]) => {
    reportApptData(PaymentActionName.ApptEdit);

    await permissionAlertsAsync(permissions, ['canAdvancedEditTicket'], false);
    const firstService = services[0] as ApptInfoPetServiceInfoWithTime;
    dispatch(
      setApptDetailDrawerEdit({
        visible: true,
        index,
        petId,
        serviceList: services.map(getDefaultService),
        petFirstServiceStartTime: firstService?.startTime ?? 0,
      }),
    );
  };

  const handleGo2PetNotes = useCallback((petId: number) => {
    dispatch(
      setApptDetailDrawerEdit({
        activeTab: ApptNavigationTabsV3.TICKET_COMMENT,
        ticketCommentsActiveTab: CommentNoteType.PetNotes,
        petNotesDefaultPetId: petId,
      }),
    );
  }, []);

  return (
    <div className={cn('moe-gap-s moe-flex moe-flex-col', className)}>
      {pets.map((pet, index) => {
        const { petId } = pet;
        const deletable = pets.length > 1 && editable;
        const serviceList = getServiceListByPet(pet, appointmentId);
        const showAnotherCareTypeButton = checkAddNewServiceType(petId) && editable;

        return (
          <EditTicketPermissionTip disabled={!editable} key={pet.petId}>
            {(hasEditTicketPermission) => (
              <>
                <EvaluationApptDetailTips petId={petId} appointmentId={appointmentId} />
                <PetServiceCard
                  showIncident
                  serviceList={serviceList}
                  key={`${petId}-${appointmentId}`}
                  petId={petId}
                  disabled={disabled || !hasEditTicketPermission || !isWorkingLocation}
                  noPermissionEdit={editable && (!hasEditTicketPermission || !isWorkingLocation)}
                  appointmentEndDate={appointmentEndDate}
                  priceContent={(serviceId, defaultContent) => (
                    <PricingRuleRender
                      appointmentId={appointmentId}
                      petId={petId}
                      serviceId={serviceId}
                      defaultContent={defaultContent}
                    />
                  )}
                  suffix={(petId: string, serviceItemType: ServiceItemType) => (
                    <>
                      <Condition
                        if={
                          serviceItemType === ServiceItemType.BOARDING &&
                          isNormal(appointmentId) &&
                          appointmentStatus !== AppointmentStatus.CANCELED
                        }
                      >
                        <ApptDailyReportEntrance appointmentId={+appointmentId} petId={+petId} />
                      </Condition>
                      <Condition
                        if={
                          serviceItemType === ServiceItemType.DAYCARE &&
                          isNormal(appointmentId) &&
                          appointmentStatus !== AppointmentStatus.CANCELED
                        }
                      >
                        <ApptDailyReportEntrance appointmentId={+appointmentId} petId={+petId} />
                      </Condition>
                      <Condition
                        if={
                          serviceItemType === ServiceItemType.GROOMING &&
                          appointmentStatus !== AppointmentStatus.CANCELED
                        }
                      >
                        <ApptGroomingReportEntrance appointmentId={+appointmentId} petId={+petId} />
                      </Condition>
                    </>
                  )}
                  onEdit={optionalFunction(() => {
                    if (!hasEditTicketPermission) {
                      return;
                    }
                    const { serviceItemType } = store.select(selectMainServiceInAppt(appointmentId));
                    if (matchApptFlowScene(ApptFlowScene.EditPetService, serviceItemType)) {
                      handleEdit(index, Number(petId), serviceList);
                    } else {
                      dispatch(
                        setApptDetailDrawerHybridEdit({ visible: true, index, petId: Number(petId), serviceList }),
                      );
                    }
                  }, editable)}
                  onDelete={optionalFunction(async () => {
                    await handleDelete(petId);
                  }, deletable)}
                  onGo2PetNotes={() => {
                    handleGo2PetNotes(Number(petId));
                  }}
                >
                  <Condition if={showAnotherCareTypeButton && hasEditTicketPermission}>
                    <Button
                      size="s"
                      icon={<MajorPlusOutlined />}
                      variant="tertiary-legacy"
                      className="moe-me-auto"
                      align="start"
                      onPress={() => {
                        dispatch(
                          setApptDetailDrawerAdd({
                            ...apptDetailDrawerAddBox.initialState,
                            visible: true,
                            clientId: Number(clientId),
                            petId: Number(petId),
                            isDisabled: true,
                            serviceList: [],
                          }),
                        );
                      }}
                      data-testid={ApptTestIds.ApptPetServiceAddCareType}
                    >
                      Add another care type
                    </Button>
                  </Condition>
                </PetServiceCard>
              </>
            )}
          </EditTicketPermissionTip>
        );
      })}
    </div>
  );
});

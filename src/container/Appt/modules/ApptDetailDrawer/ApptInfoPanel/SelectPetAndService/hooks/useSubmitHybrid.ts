import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useStore } from 'amos';
import { flatMap } from 'lodash';
import { type ServiceEntry } from '../../../../../../../components/ServiceApplicablePicker/types/serviceEntry';
import {
  setApptDetailDrawerAdd,
  setApptDetailDrawerAddDetail,
} from '../../../../../../../store/calendarLatest/actions/private/calendar.actions';
import {
  apptDetailDrawerAddBox,
  apptDetailDrawerAddDetailBox,
} from '../../../../../../../store/calendarLatest/calendar.boxes';
import { quickServiceToServices } from '../../../../../components/SelectServiceDetail/SelectServiceDetail.utils';
import { useGetMergeServiceList } from '../../../../../hooks/useGetMergeServiceList';
import { addApptMultiPetService } from '../../../../../store/appt.actions';
import { apptPetMapBox } from '../../../../../store/appt.boxes';
import { useDuplicateServiceToOtherPets } from './useDuplicateServiceToOtherPets';

export const useSubmitHybrid = () => {
  const dispatch = useDispatch();
  const getMergeServiceList = useGetMergeServiceList();
  const duplicateServiceToOtherPets = useDuplicateServiceToOtherPets();
  const store = useStore();

  return async (
    petId: number,
    serviceList: ServiceEntry[],
    serviceItemType: ServiceItemType,
    appointmentId: string,
  ) => {
    const { newServiceList: mergeServiceList, skipDetail } = getMergeServiceList({
      appointmentId,
      petIdsServiceList: [{ petId, serviceList }],
      serviceItemType,
    });
    const snapshotPet = store.select(apptPetMapBox.mustGetItem(appointmentId)).toJSON();

    const serviceDetailPetServiceList = duplicateServiceToOtherPets({
      petId: String(petId),
      appointmentId,
      serviceItemType,
      mergeServiceList,
    });

    dispatch(addApptMultiPetService(serviceDetailPetServiceList, appointmentId));

    if (skipDetail) {
      return;
    }

    let promiseResolver: { reject: (v?: unknown) => void; resolve: (v?: unknown) => void } | null = null;
    const promise = new Promise((res, rej) => {
      promiseResolver = {
        reject: rej,
        resolve: res,
      };
    });

    const onBack = () => {
      dispatch([
        setApptDetailDrawerAddDetail(apptDetailDrawerAddDetailBox.initialState),
        // keep serviceItemType otherwise it will be changed due to only one care type option
        setApptDetailDrawerAdd({ serviceItemType }),
      ]);
    };

    const handleBack = () => {
      dispatch(apptPetMapBox.mergeItem(appointmentId, snapshotPet));
      onBack();
      promiseResolver?.reject();
    };

    const handleSave = () => {
      promiseResolver?.resolve();
      dispatch(apptDetailDrawerAddDetailBox.setState(apptDetailDrawerAddDetailBox.initialState));
      dispatch(setApptDetailDrawerAdd(apptDetailDrawerAddBox.initialState));
      dispatch(setApptDetailDrawerAddDetail(apptDetailDrawerAddDetailBox.initialState));
    };

    const petIds = serviceDetailPetServiceList.map(({ petId }) => Number(petId));
    const petIdsServiceList = serviceDetailPetServiceList.map((item) => {
      const { petId, serviceList } = item;
      return {
        petId: Number(petId),
        serviceList: quickServiceToServices(serviceList, serviceItemType),
      };
    });

    dispatch(
      setApptDetailDrawerAddDetail({
        appointmentId,
        promiseResolver,
        visible: true,
        petIds,
        serviceItemType,
        services: flatMap(petIdsServiceList, (item) => item.serviceList),
        petIdsServiceList,
        onBack: handleBack,
        onConfirm: handleSave,
      }),
    );
    await promise;
  };
};

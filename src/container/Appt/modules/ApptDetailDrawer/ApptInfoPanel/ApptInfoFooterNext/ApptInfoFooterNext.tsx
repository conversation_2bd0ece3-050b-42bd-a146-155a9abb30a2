import {
  AppointmentPaymentStatus,
  AppointmentStatus,
} from '@moego/api-web/moego/models/appointment/v1/appointment_enums';
import { OrderSourceType, OrderStatus } from '@moego/api-web/moego/models/order/v1/order_enums';
import { Amount<PERSON><PERSON>, <PERSON><PERSON> } from '@moego/finance-utils';
import { useOverpaidDepositOrders } from '@moego/finance-web-kit';
import { MinorInfoOutlined, MinorPaymentInProgress } from '@moego/icons-react';
import { Alert, Button, cn, Heading, Markup, Text, Tooltip } from '@moego/ui';
import { useSelector } from 'amos';
import { isUndefined } from 'lodash';
import React, { useEffect, useState } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { AppliedDiscountCodePopover } from '../../../../../../components/Invoice/AppliedDiscountCodePopover';
import { FinanceKit } from '../../../../../../service/finance-kit';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../../../../store/business/role.selectors';
import { isNormal } from '../../../../../../store/utils/identifier';
import { useBusinessIsWorkingLocation } from '../../../../../../utils/BusinessUtil';
import { ApptPayStatus } from '../../../../../Calendar/latest/ApptCalendar/components/ApptPayStatus';
import { usePreviewDepositOrderAmountByRules } from '../../../../../PaymentFlow/CartMixinOrderDrawer/SubpageLeft/Deposit/usePreviewDepositOrderAmountByRules';
import { useInvoiceReinvent } from '../../../../../PaymentFlow/hooks/useInvoiceReinvent';
import { OrderPaymentStatus } from '../../../../../PaymentFlow/shared/OrderPaymentStatus';
import { WithInvoiceReinvent } from '../../../../../PaymentFlow/shared/WithInvoiceReinvent';
import { useTakePayment } from '../../../../../PaymentFlow/TakePaymentDrawer/useTakePayment';
import { TicketPaymentStatus } from '../../../../../TicketDetail/interfaces.latest';
import { ApptPaymentStatus } from '../../../../components/ApptPaymentStatus';
import { apptInfoMapBox } from '../../../../store/appt.boxes';
import { selectDepositInfo } from '../../../../store/appt.selectors';
import { ApptEventEnum, useApptEvent } from '../../../../utils/apptEmitter';
import { ApptTicketInfoRender } from '../../ApptTicketInfoRender';
import { useTicketActions } from '../../hooks/useTicketActions';
import { ApptEditStatusFooter } from './modules/ApptEditStatusFooter/ApptEditStatusFooter';
import { OrderModelOrderType } from '@moego/api-web/moego/models/order/v1/order_models';
import { useIsApptPaymentProcessing } from '../../../../hooks/useIsApptPaymentProcessing';

export interface ApptInfoFooterNextProps {
  className?: string;
  ticketId?: number;
  scene?: string;
}

export function ApptInfoFooterNext(props: ApptInfoFooterNextProps) {
  const { className, ticketId } = props;
  const [apptInfo, business, depositInfo, permissions] = useSelector(
    apptInfoMapBox.mustGetItem(String(ticketId)),
    selectCurrentBusiness,
    selectDepositInfo(String(ticketId)),
    selectCurrentPermissions,
  );
  const { isNewOrderV4Flow } = useInvoiceReinvent();
  const previewDepositOrderAmountByRules = usePreviewDepositOrderAmountByRules();
  const [rulesAmount, setRulesAmount] = useState<number | undefined>(undefined);
  const { requestTakeDeposit } = useTakePayment();
  const { refreshTicket } = useTicketActions(Number(ticketId));
  const isPaid = apptInfo.appointment.isPaid;
  const isUnpaid = isPaid === AppointmentPaymentStatus.UNPAID;
  const hasDepositRequested = !!depositInfo;
  const depositPaid = hasDepositRequested && depositInfo.status === 2;
  const isWorkingLocation = useBusinessIsWorkingLocation(); // TODO: 可以收敛到 useTicketPaymentActionList 里计算disabled
  const { isApptPaymentProcessing } = useIsApptPaymentProcessing({
    apptId: apptInfo.appointment.id,
  });

  const canProcessPayment = permissions.has('canProcessPayment');
  const disabledStatus = !canProcessPayment || !isWorkingLocation;
  const isAfterConfirmed = apptInfo.appointment.status > AppointmentStatus.CONFIRMED;
  const hasOrders = !!apptInfo.orders?.filter(
    (o) => o.status !== OrderStatus.REMOVED && o.orderType !== OrderModelOrderType.DEPOSIT,
  ).length;
  const disabledDeposit =
    !isUnpaid ||
    depositPaid ||
    disabledStatus ||
    isAfterConfirmed ||
    hasOrders ||
    MoeMoney.fromMoney(apptInfo.paymentSummary.subtotalAmount).isZero();

  const disableDepositRules = !isNormal(apptInfo.appointment.id) || !isNewOrderV4Flow || disabledDeposit;

  const { isOverpaid, refresh: refreshOverpaid } = useOverpaidDepositOrders(FinanceKit, {
    businessId: apptInfo.appointment.businessId,
    sourceId: apptInfo.appointment.id,
    sourceType: OrderSourceType.APPOINTMENT,
  });

  useApptEvent(ApptEventEnum.APPT_REFRESHED, refreshOverpaid);

  useEffect(() => {
    if (disableDepositRules) return;
    previewDepositOrderAmountByRules(String(ticketId)).then(({ remainAmount }) => {
      if (isUndefined(remainAmount)) return;
      setRulesAmount(Math.min(remainAmount, MoeMoney.fromMoney(apptInfo.paymentSummary.subtotalAmount).valueOf()));
    });
  }, [disableDepositRules, ticketId, apptInfo.paymentSummary.subtotalAmount]);

  const renderPaymentProcessing = () => {
    if (!isApptPaymentProcessing) return null;
    return (
      <div className="moe-min-h-[20px] moe-flex moe-items-center">
        <MinorPaymentInProgress />
        <Markup variant="caption">Payment processing</Markup>
      </div>
    );
  };

  if (isNewOrderV4Flow) {
    const isCancelled = apptInfo.appointment.status === AppointmentStatus.CANCELED;
    const showDepositRequired = rulesAmount && !disabledDeposit;
    return (
      <div
        className={cn('moe-p-[20px] moe-flex moe-flex-col moe-gap-y-[16px] moe-border-t moe-border-divider', className)}
      >
        <div className="moe-flex moe-flex-col moe-gap-y-[4px]">
          <div className="moe-flex moe-items-start moe-justify-between">
            <span className="moe-flex moe-items-center moe-gap-x-xxs">
              <Text variant="regular">Service subtotal</Text>
              <Tooltip side="top" content="Total for all services in this appointment, including service fees.">
                <MinorInfoOutlined className="moe-text-tertiary" />
              </Tooltip>
            </span>
            <div className="moe-flex moe-flex-col moe-items-end">
              <Heading size={4}>{business.formatMoney(apptInfo.paymentSummary.subtotalAmount)}</Heading>
              {renderPaymentProcessing()}
              {!isCancelled && !showDepositRequired && !isApptPaymentProcessing && (
                <ApptPaymentStatus
                  isPaid={apptInfo.appointment.isPaid}
                  collectedAmount={apptInfo.paymentSummary.collectedAmount}
                  isOverpaid={isOverpaid}
                />
              )}
            </div>
          </div>
          {showDepositRequired ? (
            <Alert
              isRounded
              color="information"
              isCloseable={false}
              description={`${business.formatAmount(rulesAmount)} deposit is required.`}
              action={
                <Button
                  variant="secondary"
                  size="s"
                  onPress={() => {
                    const depositOrder = apptInfo.orders?.find(
                      (o) => o.orderType === OrderModelOrderType.DEPOSIT && o.status !== OrderStatus.REMOVED,
                    );
                    const orderId = depositOrder?.id;
                    requestTakeDeposit({
                      invoiceId: orderId ? Number(orderId) : (undefined as unknown as number),
                      appointmentId: orderId ? (undefined as unknown as number) : Number(apptInfo.appointment.id),
                      module: 'grooming',
                      onClose: () => {
                        refreshTicket();
                      },
                    });
                  }}
                >
                  Charge deposit
                </Button>
              }
            />
          ) : null}
        </div>

        <ApptEditStatusFooter appointmentId={apptInfo.appointment.id} isOverpaid={isOverpaid} />
      </div>
    );
  }

  return (
    <div
      className={cn('moe-p-[20px] moe-flex moe-flex-col moe-gap-y-[16px] moe-border-t moe-border-divider', className)}
    >
      <ApptTicketInfoRender ticketId={ticketId}>
        {({ ticket, business }) => {
          const { paidAmount, estimatedTotal, apptPayStatus, orderPaymentStatus, invoice } = ticket;
          const isPartialPaid = apptPayStatus === TicketPaymentStatus.PartialPaid;
          const isPaid = apptPayStatus === TicketPaymentStatus.Paid;
          return (
            <>
              <div className="moe-flex moe-items-start moe-justify-between">
                <div className="moe-text-[#333] moe-text-[16px] moe-font-medium moe-leading-[18px]">
                  {isPaid ? 'Total paid' : 'Estimated total'}
                </div>
                <div className="moe-flex moe-flex-col moe-items-end">
                  <div className="moe-flex moe-items-center moe-text-[24px] meo-text-[#202020] moe-font-bold moe-leading-[28px]">
                    {business.formatAmount(isPaid ? invoice.paidAmount : estimatedTotal)}
                    <Condition if={isNormal(invoice?.invoiceId)}>
                      <AppliedDiscountCodePopover invoiceId={+invoice.invoiceId} />
                    </Condition>
                  </div>
                  {renderPaymentProcessing()}
                  {!isApptPaymentProcessing && (
                    <WithInvoiceReinvent
                      fallback={
                        <ApptPayStatus
                          payStatus={apptPayStatus}
                          labelTxt={isPartialPaid ? `${business.formatAmount(paidAmount)} paid` : undefined}
                        />
                      }
                    >
                      <OrderPaymentStatus
                        overPaidAmountString={business.formatAmount(Math.abs(invoice?.outstandingBalance ?? 0))}
                        paidAmountString={business.formatAmount(Math.abs(invoice?.paidAmount ?? 0))}
                        isOverPaid={AmountUtil.checkIsCompatibleNegative(invoice?.outstandingBalance ?? 0)}
                        status={orderPaymentStatus}
                        showOverPaidAmount
                        showPaidAmount
                      />
                    </WithInvoiceReinvent>
                  )}
                </div>
              </div>
              <ApptEditStatusFooter appointmentId={ticket.appointment.id} />
            </>
          );
        }}
      </ApptTicketInfoRender>
    </div>
  );
}

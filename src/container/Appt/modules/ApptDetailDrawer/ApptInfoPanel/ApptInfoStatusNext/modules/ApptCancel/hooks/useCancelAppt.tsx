import { OrderSourceType } from '@moego/api-web/moego/models/order/v1/order_enums';
import { OrderModelOrderType } from '@moego/api-web/moego/models/order/v1/order_models';
import { MoeMoney } from '@moego/finance-utils';
import { toast } from '@moego/ui';
import { useDispatch, useStore } from 'amos';
import React from 'react';
import { type RepeatTicketApplyTypes } from '../../../../../../../../../components/ModalRepeatApply/ModalRepeatApply';
import { getDepositDetails } from '../../../../../../../../../store/PaymentFlow/order.actions';
import { AutoMessageType } from '../../../../../../../../../store/autoMessage/autoMessage.boxes';
import {
  getTicketDetailInfo,
  setApptDetailModalState,
  setDrivingTimeOpenState,
} from '../../../../../../../../../store/calendarLatest/actions/private/calendar.actions';
import {
  groomingCreateNoShowInvoice,
  groomingCreatePreAuthNoShowInvoice,
} from '../../../../../../../../../store/grooming/grooming.actions';
import { GroomingCancelByType } from '../../../../../../../../../store/grooming/grooming.boxes';
import { getInvoice } from '../../../../../../../../../store/payment/actions/private/payment.actions';
import { type PreAuthRecord } from '../../../../../../../../../store/stripe/preAuth.boxes';
import { type EnumValues } from '../../../../../../../../../store/utils/createEnum';
import { useFloatableHost } from '../../../../../../../../../utils/hooks/useFloatableHost';
import { useLatestCallback } from '../../../../../../../../../utils/hooks/useLatestCallback';
import { PaymentActionName } from '../../../../../../../../../utils/reportData/payment';
import { type reportData } from '../../../../../../../../../utils/tracker';
import { type ConfirmValue } from '../../../../../../../../Calendar/Grooming/GroomingTicketModal/components/ModalCancelConfirm';
import { useChangeTicketStatus } from '../../../../../../../../Calendar/Grooming/GroomingTicketModal/hooks/useChangeTicketStatus';
import { useTakePayment } from '../../../../../../../../PaymentFlow/TakePaymentDrawer/useTakePayment';
import { useViewOrder } from '../../../../../../../../PaymentFlow/ViewOrderDrawer/useViewOrder';
import { useInvoiceReinvent } from '../../../../../../../../PaymentFlow/hooks/useInvoiceReinvent';
import { useInvoiceReinventReport } from '../../../../../../../../PaymentFlow/hooks/useInvoiceReinvent.report';
import { AppointmentStatus } from '../../../../../../../../TicketDetail/AppointmentStatus';
import { useRecommendWaitList } from '../../../../../../../../WaitList/hooks/useRecommendWaitList';
import { useTicketAlerts } from '../../../../../../../hooks/useTicketAlerts';
import { getAppointment } from '../../../../../../../store/appt.api';
import { type ApptInfo, apptInfoMapBox } from '../../../../../../../store/appt.boxes';
import { useTicketActions } from '../../../../../hooks/useTicketActions';
import { ApptCancelAfterFinishModal } from '../ApptCancelAfterFinishModla';
import { type ApptCancelBeforeFinishFormValues } from '../ApptCancelBeforeFinishForm';
import { ApptCancelBeforeFinishModal } from '../ApptCancelBeforeFinishModal';
import { useRepeatCancel } from './useRepeatCancel';

export interface CancelBeforeFinishApptProps {
  refundableAmount: number;
  apptInfo: ApptInfo;
  preAuth: PreAuthRecord;
  showForfeit?: boolean;
}

export interface CancelAfterFinishApptProps {
  apptInfo: ApptInfo;
  preAuth: PreAuthRecord;
}

export interface CancelApptParams {
  appointmentId: number;
}

export const useCancelAppt = (props: CancelApptParams) => {
  const { appointmentId } = props;
  const { mountModal } = useFloatableHost();
  const { openViewOrderDrawer } = useViewOrder();
  const store = useStore();
  const dispatch = useDispatch();
  const alertTicketCreate = useTicketAlerts();
  const { getRepeatApplyType: openRepeatApplyTypeConfirm } = useRepeatCancel();
  const reportApptData = useInvoiceReinventReport<typeof reportData>();

  const handleChangeTicketStatus = useChangeTicketStatus(() => dispatch(getTicketDetailInfo(appointmentId)));
  const recommendWaitList = useRecommendWaitList();
  const { refreshTicket } = useTicketActions(appointmentId);
  const { requestTakeNoShowFee } = useTakePayment();

  const { isNewOrderV4Flow } = useInvoiceReinvent();

  const handleApptCancel = useLatestCallback(
    async ({
      noShow,
      noShowFee,
      cancelReason,
      repeatApplyType,
      autoRefundOrder,
      preAuth,
      apptInfo,
      chargeNoShowFee,
    }: ConfirmValue & {
      apptInfo: ApptInfo;
      preAuth: PreAuthRecord;
      chargeNoShowFee?: boolean;
    }) => {
      if (!appointmentId) return;

      const hasPreAuthOnholdAmount = preAuth.isToBeCapture;
      const data = {
        noShow,
        cancelReason,
        repeatType: repeatApplyType,
        noShowFee,
        releasePreAuth: hasPreAuthOnholdAmount && noShow && !!noShowFee ? false : true,
        cancelByType: GroomingCancelByType.Business,
        autoRefundOrder,
        chargeNoShowFee,
      };

      reportApptData(PaymentActionName.SubmitCancelAppt, {
        appointmentId,
        withNoShow: !!noShow && !!noShowFee,
        withRefund: !!autoRefundOrder,
      });

      await handleChangeTicketStatus(appointmentId, AppointmentStatus.CANCELED, data, true);
      dispatch(setDrivingTimeOpenState(false));
      dispatch(
        setApptDetailModalState({
          hasCancelAppt: recommendWaitList.fetch(appointmentId),
        }),
      );

      if (!data.noShowFee || !data.noShow) {
        await Promise.all([
          alertTicketCreate({
            ticketId: appointmentId,
            customerId: +apptInfo.customerId,
            mode: AutoMessageType.AppointmentCancelled,
          }),
          refreshTicket(),
        ]);
        return;
      }

      const action = !data.releasePreAuth
        ? // 如果是不释放金额的话，说明需要 capture 这笔钱，所以使用新接口。
          groomingCreatePreAuthNoShowInvoice(appointmentId, data.noShowFee)
        : groomingCreateNoShowInvoice(appointmentId, data.noShowFee);

      await dispatch(action);
      await dispatch(getAppointment({ appointmentId: String(appointmentId) }));
      const newApptInfo = store.select(apptInfoMapBox.mustGetItem(String(appointmentId)));

      if (+newApptInfo.noShowInvoiceId) {
        await dispatch(getInvoice(+newApptInfo.noShowInvoiceId, 'grooming'));
      }
      await refreshTicket();

      return {
        invoiceId: newApptInfo.noShowInvoiceId,
      };
    },
  );

  const getRepeatApplyType = useLatestCallback(async (apptInfo: ApptInfo): Promise<number | 'cancel' | null> => {
    let repeatApplyType: EnumValues<typeof RepeatTicketApplyTypes> | 'cancel' | null = null;
    const isOpenRepeatApplyTypeConfirm =
      apptInfo.isRepeatAppt && apptInfo.appointment?.status !== AppointmentStatus.FINISHED;

    if (isOpenRepeatApplyTypeConfirm) {
      await openRepeatApplyTypeConfirm({
        onConfirm: (value) => {
          repeatApplyType = value;
        },
        onClose: () => {
          if (!repeatApplyType) {
            repeatApplyType = 'cancel';
          }
        },
      });
    }

    return isOpenRepeatApplyTypeConfirm ? repeatApplyType : null;
  });

  const cancelBeforeFinishWithNewOrderFlow = useLatestCallback(
    (props: CancelBeforeFinishApptProps & { forfeitableAmount: number }) => {
      const { forfeitableAmount, refundableAmount, apptInfo, preAuth, showForfeit } = props;

      const { closeFloatable, promise } = mountModal(({ visible, zIndex }) => {
        const handleConfirm = async (data: ApptCancelBeforeFinishFormValues) => {
          const repeatApplyType = await getRepeatApplyType(apptInfo);

          if (repeatApplyType === 'cancel') {
            return;
          }

          const isForfeit = data.noShow.markNoShow && !data.refundPayment;
          const isChargeNoShowFee = data.noShow.markNoShow && !!data.noShow.noShowFee;

          const result = await handleApptCancel({
            noShow: data.noShow.markNoShow,
            noShowFee: isForfeit ? forfeitableAmount : data.noShow.noShowFee,
            cancelReason: data.cancelReason,
            ...(repeatApplyType ? { repeatApplyType } : {}),
            autoRefundOrder: data.refundPayment,
            preAuth,
            apptInfo,
            chargeNoShowFee: isForfeit || isChargeNoShowFee,
          });

          const chargedNoShowFeeToast = () => {
            toast({
              type: 'success',
              title: 'No-show fee charged!',
              actionText: 'View receipt(s)',
              onActionClick: () => {
                openViewOrderDrawer({
                  sourceId: String(apptInfo.appointment.id),
                  sourceType: OrderSourceType.APPOINTMENT,
                  businessId: apptInfo.appointment.businessId,
                  module: 'grooming',
                });
              },
            });
          };

          toast({
            type: 'success',
            title: data.refundPayment && refundableAmount ? 'Refund successful!' : 'Appointment cancelled!',
          });

          if (isForfeit) {
            // 直接 deposit => no-show fee 了
            chargedNoShowFeeToast();
          } else if (isChargeNoShowFee && result) {
            // Charge no show fee
            await requestTakeNoShowFee({
              invoiceId: +result.invoiceId,
              module: 'grooming',
            });
          }

          closeFloatable();
          return refreshTicket();
        };

        return (
          <ApptCancelBeforeFinishModal
            showForfeit={showForfeit}
            forfeitableAmount={forfeitableAmount}
            refundableAmount={refundableAmount}
            isOpen={visible}
            zIndex={zIndex}
            onClose={() => closeFloatable()}
            onConfirm={handleConfirm}
            customerId={apptInfo.customerId}
            hasStoreCredit={apptInfo.paymentSummary.useStoreCredit}
          />
        );
      });

      return promise;
    },
  );

  const cancelBeforeFinish = useLatestCallback((props: CancelBeforeFinishApptProps) => {
    const { refundableAmount, apptInfo, preAuth } = props;
    const orderId = apptInfo.invoice.invoiceId;

    const { closeFloatable, promise } = mountModal(({ visible, zIndex }) => {
      const handleConfirm = async (data: ApptCancelBeforeFinishFormValues) => {
        const repeatApplyType = await getRepeatApplyType(apptInfo);

        if (repeatApplyType === 'cancel') {
          return;
        }

        const result = await handleApptCancel({
          noShow: data.noShow.markNoShow,
          noShowFee: data.noShow.noShowFee ? data.noShow.noShowFee : undefined,
          cancelReason: data.cancelReason,
          ...(repeatApplyType ? { repeatApplyType } : {}),
          autoRefundOrder: data.refundPayment,
          preAuth,
          apptInfo,
        });
        if (data.refundPayment)
          toast({
            type: 'success',
            title: 'Refund successful!',
          });
        // 打开 invoice
        // 1. 不 mark no show
        // 2. mark no show 但不 charge no show fee
        if (!data.noShow.markNoShow || (data.noShow.markNoShow && !data.noShow.noShowFee)) {
          closeFloatable();
          return openViewOrderDrawer({
            orderId,
            module: 'grooming',
          });
        }
        // Charge no show fee
        if (data.noShow.markNoShow && result && data.noShow.noShowFee) {
          closeFloatable();
          await requestTakeNoShowFee({
            invoiceId: +result.invoiceId,
            module: 'grooming',
          });
          await openViewOrderDrawer({
            orderId,
            module: 'grooming',
          });
          return refreshTicket();
        }
        return closeFloatable();
      };

      return (
        <ApptCancelBeforeFinishModal
          refundableAmount={refundableAmount}
          isOpen={visible}
          zIndex={zIndex}
          onClose={() => closeFloatable()}
          onConfirm={handleConfirm}
          customerId={apptInfo.customerId}
        />
      );
    });

    return promise;
  });

  const cancelAfterFinish = useLatestCallback(async (props: CancelAfterFinishApptProps) => {
    const { apptInfo, preAuth } = props;
    const orderId = isNewOrderV4Flow ? apptInfo.orders?.[0]?.id : apptInfo.invoice.invoiceId;
    const apptId = apptInfo.appointment.id;
    const apptBusinessId = apptInfo.appointment.businessId;

    const { closeFloatable, promise } = mountModal(({ visible, zIndex }) => (
      <ApptCancelAfterFinishModal
        isOpen={visible}
        zIndex={zIndex}
        onClose={() => closeFloatable()}
        onConfirm={async () => {
          const repeatApplyType = await getRepeatApplyType(apptInfo);

          await handleApptCancel({
            noShow: false,
            cancelReason: '',
            ...(repeatApplyType ? { repeatType: repeatApplyType } : {}),
            autoRefundOrder: true,
            preAuth,
            apptInfo,
          });
          openViewOrderDrawer({
            orderId,
            sourceId: String(apptId),
            sourceType: isNewOrderV4Flow ? OrderSourceType.APPOINTMENT : OrderSourceType.UNSPECIFIED,
            businessId: apptBusinessId,
            module: 'grooming',
          });
          closeFloatable();
        }}
        orderId={String(orderId)}
        customerId={apptInfo.customerId}
        hasStoreCredit={apptInfo.paymentSummary.useStoreCredit}
      />
    ));

    return promise;
  });

  const cancelAppt = useLatestCallback(async (props: { apptInfo: ApptInfo; preAuth: PreAuthRecord }) => {
    reportApptData(PaymentActionName.CancelAppointment, {
      appointmentId,
    });
    const { apptInfo, preAuth } = props;
    if (apptInfo.appointment.status === AppointmentStatus.FINISHED) {
      return cancelAfterFinish({
        apptInfo,
        preAuth,
      });
    }
    if (!isNewOrderV4Flow) {
      return cancelBeforeFinish({
        refundableAmount: Number(apptInfo.invoice?.paidAmount) - Number(apptInfo.invoice?.refundedAmount),
        apptInfo,
        preAuth,
      });
    }

    const refundableAmount = apptInfo.orders
      .reduce((acc, order) => acc.plus(order.paidAmount).minus(order.refundedAmount), MoeMoney.empty())
      .valueOf();

    const depositOrder = apptInfo.orders.find((order) => order.orderType === OrderModelOrderType.DEPOSIT);
    let forfeitableAmount = 0;
    if (depositOrder) {
      const depositOrderDetail = await dispatch(getDepositDetails({ orderId: depositOrder.id }));
      forfeitableAmount = MoeMoney.fromMoney(depositOrderDetail.details?.balance ?? MoeMoney.empty()).valueOf();
    }

    return cancelBeforeFinishWithNewOrderFlow({
      forfeitableAmount,
      refundableAmount,
      apptInfo,
      preAuth,
      showForfeit: !!forfeitableAmount,
    });
  });

  return {
    cancelAppt,
  };
};

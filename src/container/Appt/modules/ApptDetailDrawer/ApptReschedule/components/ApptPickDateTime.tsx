import { DatePicker, TimePicker, cn } from '@moego/ui';
import dayjs, { type Dayjs } from 'dayjs';
import React from 'react';
import { ApptTestIds } from '../../../../../../config/testIds/apptDrawer';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { BusinessInfoRender } from '../../../../components/BusinessInfoRender';
import { SectionInfo } from '../../../../components/SectionInfo';
import { type BusinessRecord } from '../../../../../../store/business/business.boxes';
import { TimeSlot } from '../../../../components/SelectServiceDetail/components/Service/TimeSlot/TimeSlot';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../utils/DateTimeUtil';
import { type ServiceEntry } from '../../../../../../components/ServiceApplicablePicker/types/serviceEntry';
import { reportApptEditTimeBeforeToday } from '../../../../../../utils/reportData/reporter/apptReporter';

export interface ApptPickDateTimeProps {
  className?: string;
  value: Dayjs;
  onChange?: (date: Dayjs) => void;
  showAvailableTimeSlotPicker?: boolean;
  serviceEntry?: ServiceEntry;
  petId: string;
  appointmentId: string;
  isDateDisabled?: boolean;
  isTimeDisabled?: boolean;
}

export function ApptPickDateTime(props: ApptPickDateTimeProps) {
  const {
    className,
    value,
    onChange,
    showAvailableTimeSlotPicker,
    serviceEntry,
    petId,
    appointmentId,
    isDateDisabled,
    isTimeDisabled,
  } = props;
  const onDateTimeChange = useLatestCallback(async (nextDate: Dayjs | null) => {
    if (nextDate) {
      onChange?.(nextDate);
    }
  });

  const handleSelectTime = useLatestCallback((startTime: number) => {
    const nextDate = value.setMinutes(startTime);
    onDateTimeChange(nextDate);
  });

  const renderDate = (business: BusinessRecord) => {
    return (
      <SectionInfo label="Date" className="moe-flex-1">
        <div data-testid={ApptTestIds.ApptEditScheduleDateBtn}>
          <DatePicker
            isClearable={false}
            format={business.dateFormat}
            value={value}
            isDisabled={isDateDisabled}
            onChange={(newVal) => {
              if (newVal) {
                const nextDate = newVal.setMinutes(value.getMinutes());
                onDateTimeChange(nextDate);

                reportApptEditTimeBeforeToday({
                  originalDate: value,
                  newDate: newVal,
                });
              }
            }}
          />
        </div>
      </SectionInfo>
    );
  };

  const renderTime = (business: BusinessRecord) => {
    return (
      <SectionInfo label="Time" className="moe-flex-1">
        <div data-testid={ApptTestIds.ApptEditScheduleTimeBtn}>
          <TimePicker
            isClearable={false}
            value={serviceEntry?.startTime ? dayjs().setMinutes(serviceEntry.startTime) : value}
            format={business.timeFormat()}
            minuteStep={5}
            isDisabled={isTimeDisabled}
            onChange={(newVal) => {
              if (newVal) {
                const nextDate = value.setMinutes(newVal.getMinutes());
                onDateTimeChange(nextDate);
              }
            }}
          />
        </div>
      </SectionInfo>
    );
  };

  return (
    <BusinessInfoRender>
      {({ business }) => (
        <div
          className={cn(
            'moe-flex',
            {
              'moe-flex-col moe-gap-s': showAvailableTimeSlotPicker,
              'moe-items-center moe-gap-x-[8px]': !showAvailableTimeSlotPicker,
            },
            className,
          )}
        >
          {showAvailableTimeSlotPicker ? (
            <>
              {renderDate(business)}
              {showAvailableTimeSlotPicker && serviceEntry && (
                <TimeSlot
                  value={{
                    id: serviceEntry.id,
                    startTime: value.getMinutes(),
                    appointmentId,
                    staffId: String(serviceEntry.staffId),
                    startDate: value.format(DATE_FORMAT_EXCHANGE),
                    petId,
                  }}
                  isEnableSlotCalender
                  business={business}
                  onSelect={handleSelectTime}
                  textClassName="moe-mt-[-16px]"
                  timeSlotClassName="moe-mt-0"
                />
              )}
              {renderTime(business)}
            </>
          ) : (
            <>
              {renderDate(business)}
              {renderTime(business)}
            </>
          )}
        </div>
      )}
    </BusinessInfoRender>
  );
}

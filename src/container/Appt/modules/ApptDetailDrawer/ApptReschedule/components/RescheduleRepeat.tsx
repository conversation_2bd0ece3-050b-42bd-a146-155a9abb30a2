import { cn } from '@moego/ui';
import { useDispatch, useSelector, useStore } from 'amos';
import React, { forwardRef, memo, useImperativeHandle, useRef } from 'react';
import { calendarEditTicketInfoBox } from '../../../../../../store/calendarLatest/calendar.boxes';
import { customerMapBox } from '../../../../../../store/customer/customer.boxes';
import { isNormal } from '../../../../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../utils/DateTimeUtil';
import { globalEvent } from '../../../../../../utils/events/events';
import { useCloseAllDrawer } from '../../../../../../utils/hooks/useCloseAllDrawer';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { ReportActionName } from '../../../../../../utils/reportType';
import { reportData } from '../../../../../../utils/tracker';
import { useHandleRepeatConfirm } from '../../../../../Calendar/latest/ApptCalendar/hooks/useRepeatSeriesConfirm';
import { RepeatSeriesSection } from '../../../../components/RepeatSeries/RepeatSeriesSection';
import { type RepeatSeriesSectionRef } from '../../../../components/RepeatSeries/RepeatSeriesSection.options';
import { buildSaveApptParamsForApptDetail } from '../../../../components/RepeatSeries/adapters/ApptParamAdaptersForApptDetail';
import { useRepeatSeriesState } from '../../../../components/RepeatSeries/hooks/useRepeatSeriesState';
import { selectIsRepeatPreviewMode } from '../../../../components/RepeatSeries/store/repeatSeries.selectors';
import { SectionInfo } from '../../../../components/SectionInfo';
import { apptInfoMapBox } from '../../../../store/appt.boxes';

export interface RescheduleRepeatProps {
  supportRepeatWeekByDays?: boolean;
  appointmentId: string;
  onPreviewOpenChange: (v: boolean) => void;
  isDisabled?: boolean;
  className?: string;
}

export interface RescheduleRepeatActionRef {
  save: (sync?: boolean) => Promise<void | number>;
}

export const RescheduleRepeat = memo(
  forwardRef<RescheduleRepeatActionRef, RescheduleRepeatProps>((props, ref) => {
    const dispatch = useDispatch();
    const store = useStore();
    const { appointmentId, supportRepeatWeekByDays = false, onPreviewOpenChange, isDisabled, className } = props;
    const [{ date }, ticket, { isDirty }] = useSelector(
      calendarEditTicketInfoBox,
      apptInfoMapBox.mustGetItem(appointmentId),
      calendarEditTicketInfoBox,
    );
    const {
      customerId,
      appointment: { repeatId },
      serviceTimeRanges,
    } = ticket;
    const repeatSectionRef = useRef<RepeatSeriesSectionRef>(null);

    const { closeAllDrawer } = useCloseAllDrawer();
    const { isDirty: isRepeatDirty, previewRepeat, openPreviewDrawer, checkAndJumpToCalendar } = useRepeatSeriesState();
    const handleRepeatConfirm = useHandleRepeatConfirm();

    const handleSetDirty = useLatestCallback(() => {
      dispatch(calendarEditTicketInfoBox.setState((p) => ({ ...p, isDirty: true })));
    });

    const handleDeleted = useLatestCallback(async () => {
      closeAllDrawer();
      // TODO(gq, P2): 相比于 setTimeout 更优雅的方式，直接调会触发一个报错
      setTimeout(async () => {
        // 重置 edit ticket 的状态，因为会涉及到一个备份逻辑，导致残留卡片
        dispatch(calendarEditTicketInfoBox.setState(calendarEditTicketInfoBox.initialState));
        globalEvent.refresh.emit();
      }, 300);
    });

    const handlePreviewRepeat = useLatestCallback(async (syncCalendar: boolean) => {
      const { primaryAddressId } = store.select(customerMapBox.mustGetItem(Number(customerId)));
      const previewParams = serviceTimeRanges;
      const saveApptParams = buildSaveApptParamsForApptDetail(ticket, {
        appointmentId: Number(appointmentId),
        customerId: Number(customerId),
        customerAddressId: primaryAddressId,
      });
      await previewRepeat({
        previewParams,
        saveApptParams,
        existAppointmentId: appointmentId,
        ...(isDirty
          ? {
              updateAppointmentDate: date.format(DATE_FORMAT_EXCHANGE),
              updateAppointmentStartTime: date.getMinutes(),
            }
          : undefined),
      });
      openPreviewDrawer('detail');
      syncCalendar && checkAndJumpToCalendar(Number(appointmentId));
    });

    useImperativeHandle(ref, () => ({
      async save(syncCalendar = true) {
        if (isRepeatDirty) {
          await repeatSectionRef.current?.save();
        }
        if (store.select(selectIsRepeatPreviewMode())) {
          reportData(ReportActionName.RepeatApptDetailPreview);
          await handlePreviewRepeat(syncCalendar);
          throw new Error(); // 中止后续操作
        }

        if (isNormal(repeatId)) {
          // Confirm for rescheduling repeat series
          return await handleRepeatConfirm(true);
        }
        return undefined;
      },
    }));

    return (
      <SectionInfo
        label="Repeat"
        className={cn('moe-mt-[24px]', className)}
        childClassName="moe-flex moe-flex-col moe-gap-y-[8px]"
      >
        <RepeatSeriesSection
          ref={repeatSectionRef}
          startOnDate={date}
          supportRepeatWeekByDays={supportRepeatWeekByDays}
          repeatId={Number(repeatId)}
          customerId={Number(customerId)}
          onChange={handleSetDirty}
          onDeleted={handleDeleted}
          onShouldPreviewUpdate={onPreviewOpenChange}
          isDisabled={isDisabled}
        />
      </SectionInfo>
    );
  }),
);

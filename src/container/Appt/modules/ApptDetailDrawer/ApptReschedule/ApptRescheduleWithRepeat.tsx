import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@moego/ui';
import { useDispatch, useSelector, useStore } from 'amos';
import classNames from 'classnames';
import React, { memo, useRef, useState } from 'react';
import { DrawerHeader } from '../../../../../components/Drawer/DrawerHeader';
import { Switch } from '../../../../../components/SwitchCase';
import { AutoMessageType } from '../../../../../store/autoMessage/autoMessage.boxes';
import {
  setDrivingTimeOpenState,
  setTicketAlertsProps,
} from '../../../../../store/calendarLatest/actions/private/calendar.actions';
import { apptDetailDrawerBox, calendarEditTicketInfoBox } from '../../../../../store/calendarLatest/calendar.boxes';
import { selectIsEnableSlotCalender } from '../../../../../store/calendarLatest/calendar.selectors';
import { DATE_FORMAT_EXCHANGE } from '../../../../../utils/DateTimeUtil';
import { globalEvent } from '../../../../../utils/events/events';
import { useBool } from '../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { usePreAuthRelease } from '../../../../Calendar/Grooming/PreAuthForAppt/usePreAuthRelease';
import { DrawerFooter } from '../../../../Calendar/latest/AwesomeCalendar.style';
import { useRepeatSeriesState } from '../../../components/RepeatSeries/hooks/useRepeatSeriesState';
import { useTimeSlotEditPetService } from '../../../components/SelectServiceDetail/components/Service/TimeSlot/hooks/useTimeSlot.hooks';
import { useCheckConflictAlert } from '../../../hooks/useCheckConflictAlert';
import { savePetsServiceAddonDates } from '../../../store/appt.actions';
import {
  selectApptComputedInfo,
  selectPetsInAppt,
  selectPetsServiceAddonDateInfo,
} from '../../../store/appt.selectors';
import { useResetApptDetailDrawer } from '../hooks/useResetApptDetailDrawer';
import { useTicketActions } from '../hooks/useTicketActions';
import { useTicketDrawerDetail } from '../hooks/useTicketDrawerDetail';
import { type RescheduleActionRef } from './ApptRescheduleWithRepeat.type';
import { RescheduleDaycare } from './components/RescheduleDaycare';
import { RescheduleNormal } from './components/RescheduleNormal';

export interface ApptRescheduleWithRepeatProps {
  className?: string;
}

export const ApptRescheduleWithRepeat = memo<ApptRescheduleWithRepeatProps>((props) => {
  const { className } = props;
  const dispatch = useDispatch();
  const store = useStore();
  const [{ date }, { ticketId }, { isDirty }] = useSelector(
    calendarEditTicketInfoBox,
    apptDetailDrawerBox,
    calendarEditTicketInfoBox,
  );
  const appointmentId = String(ticketId);

  const [{ isDaycare, isDogWalking }, apptDates, isEnableSlotCalender] = useSelector(
    selectApptComputedInfo(appointmentId),
    selectPetsServiceAddonDateInfo(appointmentId),
    selectIsEnableSlotCalender,
  );

  const [prevApptDates] = useState(apptDates);

  const { ticket } = useTicketDrawerDetail();
  const checkConflictAlert = useCheckConflictAlert();
  const { handleSubmitTimeSlotPetService, handleClearTimeSlotPetService } = useTimeSlotEditPetService(appointmentId);

  const {
    customer: {
      customerProfile: { id: clientId },
    },
    appointment: { repeatId },
  } = ticket;
  const customerId = +clientId;
  const { refreshTicket } = useTicketActions();
  const preAuthRelease = usePreAuthRelease();
  const { resetEditScheduleApptCard } = useResetApptDetailDrawer();
  const { initRepeatState } = useRepeatSeriesState();

  const onCancel = useLatestCallback(() => {
    if (isEnableSlotCalender) {
      handleClearTimeSlotPetService();
    }

    // 退出情况，重置 repeat series 的状态与后台一致
    initRepeatState(Number(repeatId));
    dispatch(savePetsServiceAddonDates(prevApptDates));
    resetEditScheduleApptCard();
  });

  const ref = useRef<RescheduleActionRef>(null);
  const isDraftPreviewMode = useBool();

  const handleSave = useSerialCallback(async () => {
    try {
      const pets = store.select(selectPetsInAppt(appointmentId));
      await checkConflictAlert({
        appointmentId,
        startDateStr: date.format(DATE_FORMAT_EXCHANGE),
        endDateStr: date.format(DATE_FORMAT_EXCHANGE),
        petIds: pets.map((i) => i.petId),
      });

      const repeatType = await ref.current?.save?.();
      if (repeatType) {
        return await handleSubmit(repeatType);
      }
      AlertDialog.open({
        title: 'Update the change',
        content: 'Are you sure to update the change?',
        onConfirm: async () => {
          AlertDialog.close();
          await handleSubmit();
        },
      });
    } catch (e: any) {
      e?.message && console.log(e.message);
    }
  });

  const handleSubmit = useSerialCallback(async (repeatType?: number) => {
    // Confirm for pre-auth releasing
    const preAuthReleaseInfo = preAuthRelease.check(ticketId, date);
    if (preAuthReleaseInfo.isRequired) {
      await new Promise((resolve, reject) => {
        AlertDialog.open({
          title: 'Update the change',
          content: `${preAuthReleaseInfo.message} Are you sure to update the change?`,
          onConfirm: () => {
            AlertDialog.close();
            resolve(undefined);
          },
          onCancel: () => {
            AlertDialog.close();
            reject();
          },
          onClose: () => {
            AlertDialog.close();
            reject();
          },
        });
      });
    }

    await ref.current?.submit?.(repeatType);
    if (isEnableSlotCalender) {
      await handleSubmitTimeSlotPetService({ repeatType });
    }
    globalEvent.refresh.emit();
    await refreshTicket();
    dispatch(calendarEditTicketInfoBox.setState(calendarEditTicketInfoBox.initialState));
    dispatch([
      setTicketAlertsProps({
        ticketId,
        customerId,
        mode: AutoMessageType.AppointmentRescheduled,
      }),
      setDrivingTimeOpenState(false),
    ]);
  });

  return (
    <div className={classNames(className, 'moe-flex moe-flex-col moe-flex-1 moe-min-w-0')}>
      <DrawerHeader title="Edit schedule" onClick={onCancel} />
      <div className="moe-flex-1 moe-min-w-0 moe-py-[20px] moe-px-[32px] moe-overflow-auto">
        <Switch shortCircuit>
          <Switch.Case if={isDaycare}>
            <RescheduleDaycare
              ref={ref}
              appointmentId={appointmentId}
              onPreviewOpenChange={isDraftPreviewMode.as}
              showAvailableTimeSlotPicker={isEnableSlotCalender}
            />
          </Switch.Case>
          <Switch.Case if={isDogWalking}>
            <RescheduleNormal
              ref={ref}
              appointmentId={appointmentId}
              onPreviewOpenChange={isDraftPreviewMode.as}
              supportRepeatWeekByDays
              showAvailableTimeSlotPicker={isEnableSlotCalender}
            />
          </Switch.Case>
          <Switch.Case else>
            <RescheduleNormal
              ref={ref}
              appointmentId={appointmentId}
              onPreviewOpenChange={isDraftPreviewMode.as}
              showAvailableTimeSlotPicker={isEnableSlotCalender}
            />
          </Switch.Case>
        </Switch>
      </div>
      <DrawerFooter className="border">
        <Button variant="secondary" className="moe-flex-1" onPress={onCancel}>
          Cancel
        </Button>
        <Button
          variant="primary"
          className="moe-flex-1"
          isDisabled={isDaycare ? false : !isDirty && !isDraftPreviewMode.value}
          isLoading={handleSave.isBusy() || handleSubmit.isBusy()}
          onPress={handleSave}
        >
          {/* Preview / Save 的展示需在草稿态时控制 */}
          {isDraftPreviewMode.value ? 'Preview' : 'Save'}
        </Button>
      </DrawerFooter>
    </div>
  );
});

import { Scroll, Tabs } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useMemo } from 'react';
import { FlexGrowFixedHeightBox } from '../../../../../components/FlexGrowFixedHeightBox';
import { CommentNoteType } from '../../../../../store/calendarLatest/calendar.utils';
import { useBusinessIsWorkingLocation } from '../../../../../utils/BusinessUtil';
import { DrawerHead } from '../../../components/DrawerHead/DrawerHead';
import { apptInfoMapBox } from '../../../store/appt.boxes';
import { CommentBox } from './ApptTicketCommentPanel.style';
import { CommentAndHistory } from './CommentAndHistory';
import { TicketClientNotes } from '../../../components/TicketClientNotes/TicketClientNotes';
import { TicketPetNotes } from '../../../components/TicketPetNotes/TicketPetNotes';

export interface ApptTicketCommentPanelProps {
  ticketId: number;
  defaultPetId: number;
  activeTab: CommentNoteType;
  onTabChange: (key: string | number) => void;
}

export function ApptTicketCommentPanel(props: ApptTicketCommentPanelProps) {
  const { ticketId, defaultPetId, activeTab, onTabChange } = props;
  const [ticket] = useSelector(apptInfoMapBox.mustGetItem(String(ticketId)));
  const {
    customerId: clientId,
    serviceDetail,
    customer: {
      customerProfile: { deleted },
    },
  } = ticket;
  const onlyPetsInTicket = useMemo(() => (serviceDetail ?? []).map((pet) => +pet.pet.id), [serviceDetail]);
  const isWorkingLocation = useBusinessIsWorkingLocation();
  const tabListInfo = [
    {
      key: CommentNoteType.TicketComments,
      label: 'Ticket comments',
      tabClassName: 'moe-w-[130px]',
      element: <CommentAndHistory ticketId={ticketId} clientId={+clientId} />,
    },
    {
      key: CommentNoteType.PetNotes,
      label: 'Pet notes',
      tabClassName: 'moe-w-[72px]',
      element: <TicketPetNotes clientId={+clientId} defaultPetId={defaultPetId} filterPets={onlyPetsInTicket} />,
    },
    {
      key: CommentNoteType.ClientNotes,
      label: 'Client notes',
      tabClassName: 'moe-w-[92px]',
      element: <TicketClientNotes clientId={+clientId} disabled={deleted || !isWorkingLocation} />,
    },
  ];

  return (
    <CommentBox className="moe-h-full moe-overflow-hidden moe-flex moe-flex-col">
      <DrawerHead title={`Comments & Notes${!isWorkingLocation ? ' (read only)' : ''}`} />
      <div className="moe-flex-1 moe-py-[24px]">
        <Tabs
          onChange={onTabChange}
          selectedKey={activeTab}
          classNames={{
            panel: 'moe-h-full moe-flex-1 moe-mx-[-32px]',
            base: 'moe-h-full moe-flex moe-flex-col moe-px-[32px]',
          }}
        >
          {tabListInfo.map((tab) => (
            <Tabs.Item key={tab.key} label={tab.label} classNames={{ content: tab.tabClassName }}>
              <FlexGrowFixedHeightBox className="moe-h-full">
                <Scroll
                  className="moe-w-full moe-h-full moe-px-l"
                  classNames={{
                    scrollbar: 'moe-w-[4px] moe-p-none',
                    viewport: 'moe-pb-8px-600 moe-h-full',
                  }}
                >
                  {tab.element}
                </Scroll>
              </FlexGrowFixedHeightBox>
            </Tabs.Item>
          ))}
        </Tabs>
      </div>
    </CommentBox>
  );
}

import React, { useMemo } from 'react';
import { useMount } from 'react-use';
import { ScrollerProvider } from '../../../../layout/components/ScrollerProvider';
import { useInitBusinessApplicableEvaluation } from '../../../../store/evaluation/evaluation.hooks';
import { useEvaluationQuickAdd } from './hooks/useEvaluationQuickAdd';
import { CommentsCreate } from './modules/CommentsCreate';
import { EvaluationQuickAddFooter, type EvaluationQuickAddFooterProps } from './modules/EvaluationQuickAddFooter';
import { QuickAddEvaluationAddNewPet } from './modules/QuickAddEvaluationEdit/QuickAddEvaluationAddNewPet';
import { QuickAddEvaluationEdit } from './modules/QuickAddEvaluationEdit/QuickAddEvaluationEdit';
import { QuickAddEvaluationReschedule } from './modules/QuickAddEvaluationEdit/QuickAddEvaluationReschedule';
import { QuickAddInfoPanel } from './modules/QuickAddInfoPanel/QuickAddInfoPanel';
import { QuickAddLeftNav } from './modules/QuickAddLeftNav';
import { QuickAddPetBelongings } from './modules/QuickAddPetBelongings';
import { QuickAddSelectPetAndEvaluation } from './modules/QuickAddSelectPetAndEvaluation/QuickAddSelectPetAndEvaluation';
import {
  CreateEvaluationRouteName,
  DrawerRouterProvider,
  useCreateEvaluationRouter,
  type DrawerRouterType,
} from './QuickAddEvaluationApptDrawer.router';

export interface QuickAddEvaluationApptDrawerProps extends Pick<EvaluationQuickAddFooterProps, 'onCreated'> {
  onClose?: () => void;
  onReady?: (v: { router: DrawerRouterType | null; close: () => void }) => void;
}

export const QuickAddEvaluationApptDrawer = (props: QuickAddEvaluationApptDrawerProps) => {
  const { onClose, onCreated, onReady } = props;

  const { customerId } = useEvaluationQuickAdd();
  const drawerRouter = useCreateEvaluationRouter();

  useInitBusinessApplicableEvaluation(true);

  useMount(() => {
    onReady?.({
      router: drawerRouter,
      close() {
        onClose?.();
      },
    });
  });

  const subPage = useMemo(() => {
    if (drawerRouter.is(CreateEvaluationRouteName.EditPetEvaluation)) {
      return <QuickAddEvaluationEdit />;
    }
    if (drawerRouter.is(CreateEvaluationRouteName.SelectPetAndEvaluation)) {
      return <QuickAddSelectPetAndEvaluation />;
    }
    if (drawerRouter.is(CreateEvaluationRouteName.RescheduleDateAndTime)) {
      return <QuickAddEvaluationReschedule />;
    }
    if (drawerRouter.is(CreateEvaluationRouteName.AddNewPet)) {
      return <QuickAddEvaluationAddNewPet />;
    }
    return null;
  }, [drawerRouter]);

  const tabPage = useMemo(() => {
    if (drawerRouter.is(CreateEvaluationRouteName.Comment)) {
      return <CommentsCreate clientId={customerId} />;
    }
    if (drawerRouter.is(CreateEvaluationRouteName.PetBelongings)) {
      return <QuickAddPetBelongings />;
    }
    return null;
  }, [drawerRouter, customerId]);

  return (
    <DrawerRouterProvider value={drawerRouter}>
      <div className="moe-relative moe-w-full moe-h-full moe-overflow-hidden">
        {subPage && (
          <div className="moe-absolute moe-top-0 moe-left-0 moe-right-0 moe-bottom-0 moe-z-10 moe-bg-white moe-flex">
            {subPage}
          </div>
        )}

        <div className="moe-h-full moe-flex moe-flex-row moe-flex-1 moe-items-stretch moe-min-w-0">
          <QuickAddLeftNav />

          <div className="moe-relative moe-flex-1 moe-h-full moe-flex moe-overflow-hidden">
            {tabPage && (
              <div className="moe-absolute moe-top-0 moe-left-0 moe-right-0 moe-bottom-0 moe-z-10 moe-bg-white moe-flex">
                {tabPage}
              </div>
            )}
            {/* 保护 home 页面在不断切换是不被重复创建销毁。一次 drawer 打开，Home 的渲染应该只有一次，减少例如 client 接口的重复请求 */}
            <ScrollerProvider className="moe-flex-1 moe-h-full moe-min-w-0 moe-flex moe-flex-col">
              <QuickAddInfoPanel footer={<EvaluationQuickAddFooter onCancel={onClose} onCreated={onCreated} />} />
            </ScrollerProvider>
          </div>
        </div>
      </div>
    </DrawerRouterProvider>
  );
};

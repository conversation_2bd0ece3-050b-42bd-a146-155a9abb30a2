import { Button } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useMemo } from 'react';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { submitNewEvaluation } from '../../../../../store/evaluation/evaluation.actions';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { useEvaluationQuickAdd } from '../hooks/useEvaluationQuickAdd';

export interface EvaluationQuickAddFooterProps {
  onCancel?: () => void;
  onCreated?: (v: { apptId: string; customerId: number }) => void;
}

export const EvaluationQuickAddFooter: React.FC<EvaluationQuickAddFooterProps> = (props) => {
  const { onCancel, onCreated } = props;
  const dispatch = useDispatch();
  const [business] = useSelector(selectCurrentBusiness());
  const { evaluationInfo, customerId, businessId } = useEvaluationQuickAdd();

  const { isDisabled, totalPrice } = useMemo(() => {
    const { petServiceList } = evaluationInfo;
    return {
      isDisabled: !petServiceList.length,
      totalPrice: petServiceList.reduce((acc, curr) => {
        return acc + curr.servicePrice;
      }, 0),
    };
  }, [evaluationInfo]);

  const handleBookNow = useSerialCallback(async () => {
    const appointmentId = await dispatch(submitNewEvaluation(businessId));
    onCreated?.({ apptId: appointmentId, customerId });
  });

  return (
    <div className="moe-w-full moe-flex moe-flex-col moe-gap-y-s moe-py-s">
      <div className="moe-flex moe-items-center moe-justify-between moe-gap-x-xs">
        <div className="moe-text-primary moe-h4">Estimated total</div>
        <div className="moe-text-primary moe-h4">{business.formatAmount(totalPrice)}</div>
      </div>
      <div className="moe-flex moe-justify-between moe-gap-[16px]">
        <Button className="moe-flex-1" variant="secondary" onPress={onCancel}>
          Cancel
        </Button>
        <Button
          className="moe-flex-1"
          isLoading={handleBookNow.isBusy()}
          isDisabled={isDisabled}
          onPress={handleBookNow}
        >
          Book now
        </Button>
      </div>
    </div>
  );
};

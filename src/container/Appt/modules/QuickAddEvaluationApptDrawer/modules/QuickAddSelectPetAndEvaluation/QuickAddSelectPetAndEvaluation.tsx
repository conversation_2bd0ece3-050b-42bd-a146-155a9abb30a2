import { Form, Scroll, useForm, useFormState } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useEffect, useState } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { DrawerHeader } from '../../../../../../components/Drawer/DrawerHeader';
import { FlexGrowFixedHeightBox } from '../../../../../../components/FlexGrowFixedHeightBox';
import { PetPicker } from '../../../../../../components/PetPicker/PetPicker';
import { businessEvaluationMapBox } from '../../../../../../store/evaluation/evaluation.boxes';
import { isNormal } from '../../../../../../store/utils/identifier';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { ReportActionName } from '../../../../../../utils/reportType';
import { reportData } from '../../../../../../utils/tracker';
import { EditPetAndServiceFooter } from '../../../../components/EditPetServiceList/EditPetAndServiceFooter';
import { type EvaluationItemFormFields } from '../../components/PetEvaluationInfoForm';
import { useEvaluationQuickAdd } from '../../hooks/useEvaluationQuickAdd';
import { useCreateEvaluationRouterContext } from '../../QuickAddEvaluationApptDrawer.router';
import { EvaluationDateTimeInfo } from '../QuickAddInfoPanel/EvaluationDateTimeInfo';
import { MultiplePetItem } from './MultiplePetItem';

export const QuickAddSelectPetAndEvaluation: React.FC = () => {
  const { customerId, evaluationInfo, petIds: defaultPetIds, setQuickAddFields } = useEvaluationQuickAdd();
  const [evaluationMap] = useSelector(businessEvaluationMapBox);
  const [petIds, setPetIds] = useState(defaultPetIds);
  const [evaluationDateTime, setEvaluationDateTime] = useState<{ date?: string; time?: number }>({
    date: evaluationInfo?.startDate,
    time: evaluationInfo?.startTime,
  });
  const form = useForm<Record<string, EvaluationItemFormFields>>();
  const { isValid } = useFormState({
    control: form.control,
  });
  const isEvaluationTimeSaveDisabled = useBool(true);
  const drawerRouter = useCreateEvaluationRouterContext();
  // 监听 petIds 变化，移除 form 中多余的字段
  useEffect(() => {
    const values = form.getValues();
    Object.keys(values).forEach((key) => {
      if (!petIds.map(String).includes(key)) {
        form.unregister(key);
      }
    });
  }, [petIds, form]);

  const isDisabled = !isNormal(petIds[0]) || !isValid || isEvaluationTimeSaveDisabled.value;

  const handleSubmit = useSerialCallback(async () => {
    const values = form.getValues();

    if (Object.keys(values).length > 1) {
      reportData(ReportActionName.evaluationCreateMultiplePets, {
        petsLength: Object.keys(values).length,
      });
    } else {
      reportData(ReportActionName.evaluationCreateSinglePet);
    }

    setQuickAddFields({
      petIds,
      evaluationInfo: {
        ...evaluationInfo,
        startDate: evaluationDateTime.date,
        startTime: evaluationDateTime.time,
        petServiceList: Object.keys(values).map((key) => {
          const evaluationDetails = evaluationMap.mustGetItem(values[key].serviceId);
          return {
            ...values[key],
            petId: key,
            serviceTime: evaluationDetails.duration || 0,
            servicePrice: evaluationDetails.price || 0,
            startDate: evaluationDateTime.date,
            startTime: evaluationDateTime.time,
          };
        }),
      },
    });
    drawerRouter.back();
  });

  const handleCancel = useSerialCallback(() => {
    drawerRouter.back();
  });

  return (
    <div className="moe-flex moe-flex-col moe-flex-1 moe-min-w-0 moe-min-h-0">
      <DrawerHeader title="Select pet and service" onClick={handleCancel} />
      <FlexGrowFixedHeightBox>
        <Scroll
          className="moe-w-full moe-h-full"
          classNames={{
            scrollbar: 'moe-w-[4px] moe-p-none',
            viewport: 'moe-pb-m moe-h-full',
          }}
        >
          <div className="moe-flex moe-flex-col moe-gap-y-m moe-px-[32px]">
            <PetPicker
              autoFocus
              isMultiplePets
              clientId={Number(customerId)}
              value={petIds}
              onChange={setPetIds}
              footerHidden
              defaultVisible
            />
            <Condition if={petIds?.length}>
              <EvaluationDateTimeInfo
                onChange={setEvaluationDateTime}
                setIsSaveDisabled={isEvaluationTimeSaveDisabled.as}
              />
              <Form form={form} footer={null}>
                {petIds.map((petId, index) => (
                  <MultiplePetItem
                    key={petId}
                    petId={petId}
                    petIds={petIds.map(String)}
                    form={form}
                    index={index}
                    startDate={evaluationDateTime.date}
                    startTime={evaluationDateTime.time}
                  />
                ))}
              </Form>
            </Condition>
          </div>
        </Scroll>
      </FlexGrowFixedHeightBox>
      <EditPetAndServiceFooter
        disabled={isDisabled}
        onSubmit={async () => {
          if (isDisabled) return;
          handleSubmit();
        }}
        onCancel={handleCancel}
      />
    </div>
  );
};

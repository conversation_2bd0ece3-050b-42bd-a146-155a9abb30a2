import { ColorPicker } from '@moego/ui';
import React from 'react';
import { DEFAULT_PRESET_COLORS } from '../../../../../../components/ColorPicker/ColorCodePicker';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useEvaluationQuickAdd } from '../../hooks/useEvaluationQuickAdd';

export interface EvaluationColorCodeProps {}

export const EvaluationColorCode: React.FC<EvaluationColorCodeProps> = () => {
  const { colorCode, setQuickAddFields } = useEvaluationQuickAdd();

  const onChangeColorCode = useLatestCallback((nextColorCode: string) => {
    setQuickAddFields({
      colorCode: nextColorCode,
      isDirty: true,
    });
  });

  return <ColorPicker value={colorCode} onChange={onChangeColorCode} presetList={DEFAULT_PRESET_COLORS} />;
};

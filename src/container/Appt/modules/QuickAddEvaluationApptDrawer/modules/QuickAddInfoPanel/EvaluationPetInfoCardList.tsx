import { MajorPlusOutlined } from '@moego/icons-react';
import { But<PERSON> } from '@moego/ui';
import React from 'react';
import { CommentNoteType } from '../../../../../../store/calendarLatest/calendar.utils';
import { getDefaultEvaluationInfo } from '../../../../../../store/evaluation/evaluation.types';
import { toNumber } from '../../../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useAddNewPet } from '../../../../hooks/useAddNewPet';
import { useAutoSelectSinglePet } from '../../hooks/useAutoSelectSinglePet';
import { useEvaluationQuickAdd } from '../../hooks/useEvaluationQuickAdd';
import { CreateEvaluationRouteName, useCreateEvaluationRouterContext } from '../../QuickAddEvaluationApptDrawer.router';
import { EvaluationPetInfoCard } from './EvaluationPetInfoCard';

export const EvaluationPetInfoCardList = () => {
  const { evaluationInfo, setQuickAddFields, customerId, petIds = [] } = useEvaluationQuickAdd();
  const addNewPet = useAddNewPet(customerId, petIds);
  const drawerRouter = useCreateEvaluationRouterContext();

  const handleEditEvaluationInfo = useLatestCallback((petId: number) => {
    drawerRouter.go(CreateEvaluationRouteName.EditPetEvaluation, { petId });
  });

  const addNewPetServices = async () => {
    await addNewPet({
      onAddPet: () => {
        drawerRouter.go(CreateEvaluationRouteName.AddNewPet);
      },
    });
  };

  const handleDeleteEvaluationInfo = useLatestCallback((petId: number) => {
    const petServiceList = evaluationInfo.petServiceList;

    if (petServiceList.length <= 1) {
      setQuickAddFields({
        ...evaluationInfo,
        petIds: [],
        evaluationInfo: {
          ...getDefaultEvaluationInfo(),
        },
      });
    }

    setQuickAddFields({
      evaluationInfo: {
        ...evaluationInfo,
        petServiceList: petServiceList.filter((item) => item.petId !== petId.toString()),
      },
      petIds: petIds.filter((id) => id !== petId),
    });
  });

  useAutoSelectSinglePet();

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-[12px]">
      {evaluationInfo.petServiceList.map((item) => (
        <EvaluationPetInfoCard
          key={item.petId}
          petId={toNumber(item.petId)}
          evaluationInfo={item}
          startTime={evaluationInfo.startTime}
          startDate={evaluationInfo.startDate}
          endDate={evaluationInfo.endDate}
          endTime={evaluationInfo.endTime}
          onEdit={handleEditEvaluationInfo}
          onDelete={handleDeleteEvaluationInfo}
          onGo2PetNotes={() => {
            drawerRouter.go(CreateEvaluationRouteName.Comment, {
              tab: CommentNoteType.PetNotes,
              petId: toNumber(item.petId),
            });
          }}
        />
      ))}
      <div className="moe-flex moe-justify-start">
        <Button variant="tertiary" align="start" icon={<MajorPlusOutlined />} onPress={addNewPetServices}>
          Add pet
        </Button>
      </div>
    </div>
  );
};

import { useDispatch } from 'amos';
import React, { useEffect } from 'react';
import { ClientSearchInput } from '../../../../../../components/ClientPicker/ClientSearchInput';
import { CommentNoteType } from '../../../../../../store/calendarLatest/calendar.utils';
import { getCustomerList } from '../../../../../../store/customer/customer.actions';
import { isNormal } from '../../../../../../store/utils/identifier';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { CreateEvaluationRouteName, useCreateEvaluationRouterContext } from '../../QuickAddEvaluationApptDrawer.router';
import { EvaluationClientInfoCard } from './EvaluationClientInfoCard';

/**
 * 参考 Appt 的 QuickAdd 的 ClientInfo，但这里不做特殊逻辑
 * 长期需考虑再进一步抽象变成业务通用组件（copy 代码太多啦！）
 */
export interface EvaluationClientInfoProps {
  className?: string;
  loadingClient?: boolean;
  clientId?: number;
  onClearClient?: () => void;
  onSelectClient?: (newClientId: number) => void;
}

export const EvaluationClientInfo: React.FC<EvaluationClientInfoProps> = (props) => {
  const { className, loadingClient, clientId, onSelectClient, onClearClient } = props;
  const dispatch = useDispatch();
  const drawerRouter = useCreateEvaluationRouterContext();

  const getTop10 = useSerialCallback(async () => {
    await dispatch(getCustomerList({ queries: { keyword: '' }, filters: undefined }));
  });

  useEffect(() => {
    getTop10();
  }, []);

  if (!isNormal(clientId)) {
    return <ClientSearchInput loadingTop10={getTop10.isBusy()} onChange={onSelectClient} />;
  }

  return (
    <EvaluationClientInfoCard
      className={className}
      clientId={clientId}
      loadingClient={loadingClient}
      onClearClient={onClearClient}
      onGo2ClientNotes={() => {
        drawerRouter.go(CreateEvaluationRouteName.Comment, {
          tab: CommentNoteType.ClientNotes,
        });
      }}
    />
  );
};

import { Heading, Spin, Text } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import { isNumber } from 'lodash';
import React, { useMemo } from 'react';
import { useAsync } from 'react-use';
import { AddPetAndServices } from '../../../../../../components/PetAndServicePicker/components/AddPetAndServices';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { getAllClientInfo } from '../../../../../../store/customer/customer.actions';
import { getDefaultEvaluationInfo } from '../../../../../../store/evaluation/evaluation.types';
import { getLodgingUnitList } from '../../../../../../store/lodging/actions/public/lodgingUnit.actions';
import { getCompanyStaffList } from '../../../../../../store/staff/staff.actions';
import { ID_ANONYMOUS, isNormal } from '../../../../../../store/utils/identifier';
import { useQuery } from '../../../../../../store/utils/useQuery';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { DrawerLayout } from '../../../../../Calendar/latest/ApptCalendar/components/DrawerLayout';
import { ApptDateTime } from '../../../../components/ApptDateTime';
import { SectionInfo } from '../../../../components/SectionInfo';
import { useEvaluationCustomerOverview } from '../../../../hooks/useEvaluationCustomerOverview';
import { useAutoSelectSinglePet } from '../../hooks/useAutoSelectSinglePet';
import { useEvaluationQuickAdd } from '../../hooks/useEvaluationQuickAdd';
import { CreateEvaluationRouteName, useCreateEvaluationRouterContext } from '../../QuickAddEvaluationApptDrawer.router';
import { EvaluationClientAlertNotes } from './EvaluationClientAlertNotes';
import { EvaluationClientInfo } from './EvaluationClientInfo';
import { EvaluationColorCode } from './EvaluationColorCode';
import { EvaluationPetInfoCardList } from './EvaluationPetInfoCardList';

export interface QuickAddInfoPanelProps {
  footer?: React.ReactNode;
}

export const QuickAddInfoPanel: React.FC<QuickAddInfoPanelProps> = (props) => {
  const { footer } = props;
  const dispatch = useDispatch();
  const { customerId, evaluationInfo, setQuickAddFields } = useEvaluationQuickAdd();
  const [business] = useSelector(selectCurrentBusiness);
  const apptStartDate = dayjs(evaluationInfo.startDate);

  useEvaluationCustomerOverview();
  useQuery(getLodgingUnitList({ businessId: business.id?.toString() }));
  useQuery(getCompanyStaffList());
  const drawerRouter = useCreateEvaluationRouterContext();

  const handleClearClient = useLatestCallback(() => {
    setQuickAddFields({
      customerId: ID_ANONYMOUS,
      isDirty: false,
    });
  });

  const handleSelectClient = useLatestCallback((newClientId: number) => {
    setQuickAddFields({
      customerId: newClientId,
      petIds: [],
      isDirty: true,
      evaluationInfo: getDefaultEvaluationInfo(),
    });
  });

  const { loading: loadingClient } = useAsync(async () => {
    if (isNormal(customerId)) {
      await dispatch(getAllClientInfo(customerId));
    }
  }, [customerId]);

  useAutoSelectSinglePet();

  const hasPetService = useMemo(() => Boolean(evaluationInfo.petServiceList.length), [evaluationInfo]);

  return (
    <DrawerLayout footerClassName="shadow" footer={footer}>
      <Spin isLoading={false} classNames={{ base: 'moe-h-full' }}>
        <Heading
          size="3"
          className="moe-bg-neutral-sunken-0 moe-px-m moe-pt-l moe-pb-m moe-text-primary moe-sticky moe-top-0 moe-z-[1]"
        >
          New evaluation
        </Heading>
        <div
          className="moe-px-m moe-mb-m"
          style={{ background: 'linear-gradient(to bottom, #F3F3F3, #F3F3F3 40px, #fff 40px, #fff)' }}
        >
          <EvaluationClientInfo
            clientId={customerId}
            onSelectClient={handleSelectClient}
            onClearClient={handleClearClient}
            loadingClient={loadingClient}
          />
        </div>

        {isNormal(customerId) && (
          <div className="moe-relative moe-flex moe-flex-col moe-gap-y-m moe-pb-[20px] moe-px-m">
            <div className="moe-flex moe-flex-col moe-gap-y-m">
              {hasPetService && (
                <ApptDateTime
                  className="moe-px-xs"
                  hiddenRepeatDetailText
                  customApptTimeTxtBlock={
                    <Text variant="regular" className="moe-text-primary">
                      {apptStartDate.format('dddd')}, {business.formatDate(apptStartDate)}{' '}
                      {isNumber(evaluationInfo.startTime)
                        ? business.formatTime(apptStartDate.add(evaluationInfo.startTime, 'minute'))
                        : null}
                    </Text>
                  }
                  onEdit={() => {
                    drawerRouter.go(CreateEvaluationRouteName.RescheduleDateAndTime);
                  }}
                />
              )}
              <EvaluationClientAlertNotes />

              {hasPetService ? (
                <>
                  <EvaluationPetInfoCardList />
                  <SectionInfo label="Color code" flexDirection="row">
                    <EvaluationColorCode />
                  </SectionInfo>
                </>
              ) : (
                <AddPetAndServices
                  disabledOverlay
                  onClick={() => {
                    drawerRouter.go(CreateEvaluationRouteName.SelectPetAndEvaluation);
                  }}
                />
              )}
            </div>
          </div>
        )}
      </Spin>
    </DrawerLayout>
  );
};

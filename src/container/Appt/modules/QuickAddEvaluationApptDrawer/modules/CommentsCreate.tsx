import React, { memo } from 'react';
import { Comments, type CommentsProps } from '../../../components/Comments';
import { CreateEvaluationRouteName, useCreateEvaluationRouterContext } from '../QuickAddEvaluationApptDrawer.router';
import { useEvaluationQuickAdd } from '../hooks/useEvaluationQuickAdd';

export const CommentsCreate = memo<Pick<CommentsProps, 'clientId'>>((props) => {
  const { clientId } = props;
  const drawerRouter = useCreateEvaluationRouterContext();
  const { tab, petId } = drawerRouter.getParams(CreateEvaluationRouteName.Comment) || {};
  const { ticketComment, setQuickAddFields } = useEvaluationQuickAdd();

  return (
    <Comments
      clientId={clientId}
      activeTab={tab}
      petId={petId}
      comments={ticketComment}
      onCommentsChange={(value) => setQuickAddFields({ ticketComment: value })}
    />
  );
});

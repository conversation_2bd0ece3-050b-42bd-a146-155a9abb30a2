import React, { useMemo } from 'react';
import { CreateApptNavigationTabs } from '../../../../../store/calendarLatest/calendar.utils';
import { isNormal } from '../../../../../store/utils/identifier';
import { DrawerLeftNav } from '../../../components/DrawerLeftNav/DrawerLeftNav';
import { useEvaluationQuickAdd } from '../hooks/useEvaluationQuickAdd';
import { CreateEvaluationRouteName, useCreateEvaluationRouterContext } from '../QuickAddEvaluationApptDrawer.router';

export const QuickAddLeftNav = () => {
  const { customerId, petIds } = useEvaluationQuickAdd();
  const drawerRouter = useCreateEvaluationRouterContext();

  const isSelectedClient = isNormal(customerId);
  const isSelectedPet = petIds?.length > 0;

  const currentActiveNavTab = useMemo(() => {
    switch (drawerRouter.current.name) {
      case CreateEvaluationRouteName.PetBelongings:
        return CreateApptNavigationTabs.PET_BELONGINGS;
      case CreateEvaluationRouteName.Comment:
        return CreateApptNavigationTabs.TICKET_COMMENT;
      default:
        return CreateApptNavigationTabs.INFO;
    }
  }, [drawerRouter]);

  const handleChangeNavTab = (v: number) => {
    switch (v) {
      case CreateApptNavigationTabs.INFO:
        drawerRouter.go(CreateEvaluationRouteName.Home);
        break;
      case CreateApptNavigationTabs.TICKET_COMMENT:
        drawerRouter.go(CreateEvaluationRouteName.Comment);
        break;
      case CreateApptNavigationTabs.PET_BELONGINGS:
        drawerRouter.go(CreateEvaluationRouteName.PetBelongings);
        break;
    }
  };

  const disabledTabs = useMemo(() => {
    const disabledList = [];
    if (!isSelectedClient) {
      disabledList.push(CreateApptNavigationTabs.TICKET_COMMENT);
    }
    if (!isSelectedPet) {
      disabledList.push(CreateApptNavigationTabs.PET_BELONGINGS);
    }
    return disabledList;
  }, [isSelectedClient, isSelectedPet]);

  const options = useMemo(() => {
    return CreateApptNavigationTabs.values
      .filter((v) => v !== CreateApptNavigationTabs.FeedingMedication)
      .map((tab) => {
        const isActive = tab === currentActiveNavTab;
        const isDisabled = disabledTabs.includes(tab);
        return {
          ...CreateApptNavigationTabs.mapLabels[tab],
          value: tab,
          isActive,
          isDisabled,
        };
      });
  }, [disabledTabs, currentActiveNavTab]);

  return <DrawerLeftNav options={options} value={currentActiveNavTab} onChange={handleChangeNavTab} />;
};

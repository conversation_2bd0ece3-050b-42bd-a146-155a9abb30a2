import { Button } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useMemo, useState } from 'react';
import { toastA<PERSON> } from '../../../../../components/Toast/Toast';
import { petMapBox } from '../../../../../store/pet/pet.boxes';
import { type PetBelongsRecordModel } from '../../../../../store/pet/petBelongs.boxes';
import { DrawerHead } from '../../../components/DrawerHead/DrawerHead';
import { PetBelongings } from '../../../components/PetBelongings/PetBelongings';
import { useEvaluationQuickAdd } from '../hooks/useEvaluationQuickAdd';

export interface QuickAddPetBelongingsProps {
  className?: string;
}

export const QuickAddPetBelongings: React.FC<QuickAddPetBelongingsProps> = (props) => {
  const { petIds, petBelongings = {}, setQuickAddFields } = useEvaluationQuickAdd();
  const [petMap] = useSelector(petMapBox);
  const [localBelonging, setLocalBelonging] = useState<Partial<PetBelongsRecordModel>>({});

  const petsDetails = useMemo(() => petIds.map((id) => petMap.mustGetItem(id)), [petIds, petMap]);

  const handleEdit = async (params: Partial<PetBelongsRecordModel>) => {
    if (!params.petId) {
      return;
    }
    const [originBelongings] = petBelongings?.[params.petId] || [];

    setQuickAddFields({
      petBelongings: {
        ...petBelongings,
        [params.petId]: [
          {
            ...originBelongings,
            ...params,
          },
        ],
      },
    });
  };

  const handleLocalEdit = async (params: Partial<PetBelongsRecordModel>) => {
    setLocalBelonging(params);
  };

  const handleSave = () => {
    handleEdit(localBelonging);
    toastApi.success('Update successfully');
  };

  if (!petIds?.length) {
    return null;
  }

  return (
    <div className="moe-flex moe-flex-col moe-w-full moe-h-full moe-overflow-auto">
      <DrawerHead title={'Pet belongings'} />
      <div className="moe-flex moe-flex-col moe-flex-1 moe-p-[24px] moe-gap-y-[8px] moe-overflow-auto">
        <PetBelongings
          {...props}
          onEdit={handleLocalEdit}
          defaultPetId={petIds[0]}
          pets={petsDetails}
          belongsMap={petBelongings}
          isBusy={false}
          withoutSummary
        />
      </div>
      <div className="moe-py-[16px] moe-px-[24px]">
        <Button className="moe-w-full" onPress={handleSave} isDisabled={!localBelonging?.name}>
          Save
        </Button>
      </div>
    </div>
  );
};

import React, { useMemo } from 'react';
import { useBusinessApplicableEvaluation } from '../../../../../../store/evaluation/evaluation.hooks';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useEvaluationQuickAdd } from '../../hooks/useEvaluationQuickAdd';
import { CreateEvaluationRouteName, useCreateEvaluationRouterContext } from '../../QuickAddEvaluationApptDrawer.router';
import { type EvaluationEditData, EvaluationEditUI } from './EvaluationEditUI';
export interface EvaluationQuickAddEvaluationEditProps {}

export const QuickAddEvaluationEdit: React.FC<EvaluationQuickAddEvaluationEditProps> = () => {
  const { evaluationInfo, currentServiceItemType, setQuickAddFields } = useEvaluationQuickAdd();
  const drawerRouter = useCreateEvaluationRouterContext();
  const { petId } = drawerRouter.getParams(CreateEvaluationRouteName.EditPetEvaluation);
  const { evaluationList } = useBusinessApplicableEvaluation(currentServiceItemType);
  const targetEvaluationInfo = useMemo(() => {
    const evaluation = evaluationInfo.petServiceList.find((petService) => +petService.petId === petId);
    if (!evaluation) {
      return;
    }

    return {
      petId,
      evaluationId: evaluation?.serviceId,
      price: evaluation?.servicePrice,
      duration: evaluation?.serviceTime,
      staffId: evaluation?.staffId,
      lodgingId: evaluation?.lodgingId,
    };
  }, [evaluationInfo.petServiceList, petId]);

  const handleCancelEditPet = useLatestCallback(() => {
    drawerRouter.back();
  });

  const handleSubmit = useLatestCallback((submitData: EvaluationEditData) => {
    const targetEvaluation = evaluationList.find((evaluation) => evaluation.id === submitData.evaluationId);

    setQuickAddFields({
      evaluationInfo: {
        ...evaluationInfo,
        petServiceList: evaluationInfo.petServiceList.map((petService) => {
          if (+petService.petId === petId) {
            return {
              ...petService,
              ...targetEvaluation?.toService(),
              servicePrice: submitData.price,
              serviceTime: submitData.duration,
              staffId: submitData.staffId,
              lodgingId: submitData.lodgingId,
            };
          }
          return petService;
        }),
      },
      isDirty: true,
    });
    drawerRouter.back();
  });

  if (!targetEvaluationInfo) {
    return null;
  }

  return (
    <EvaluationEditUI
      defaultEvaluationInfo={targetEvaluationInfo}
      onCancelEditPet={handleCancelEditPet}
      onSubmit={handleSubmit}
      onChange={() => {
        setQuickAddFields({
          isDirty: true,
        });
      }}
    />
  );
};

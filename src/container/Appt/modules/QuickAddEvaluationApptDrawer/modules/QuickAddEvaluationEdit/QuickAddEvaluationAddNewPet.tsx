import { useSelector } from 'amos';
import { isNumber } from 'lodash';
import React, { useRef, useState } from 'react';
import { businessEvaluationMapBox } from '../../../../../../store/evaluation/evaluation.boxes';
import { ID_ANONYMOUS } from '../../../../../../store/utils/identifier';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { EvaluationAddNewPet } from '../../../../components/Evaluation/EvaluationAddNewPet';
import { type PetEvaluationInfoFormRef } from '../../components/PetEvaluationInfoForm';
import { useEvaluationQuickAdd } from '../../hooks/useEvaluationQuickAdd';
import { useCreateEvaluationRouterContext } from '../../QuickAddEvaluationApptDrawer.router';

export const QuickAddEvaluationAddNewPet: React.FC = () => {
  const { customerId, evaluationInfo, petIds: defaultPetIds, setQuickAddFields } = useEvaluationQuickAdd();
  const [evaluationMap] = useSelector(businessEvaluationMapBox);
  const [petId, setPetId] = useState(ID_ANONYMOUS);
  const formRef = useRef<PetEvaluationInfoFormRef>(null);
  const targetEvaluationInfo = evaluationMap.mustGetItem(formRef.current?.getValues().serviceId);
  const drawerRouter = useCreateEvaluationRouterContext();

  const handleSubmit = useSerialCallback(async () => {
    const values = formRef?.current?.getValues();

    if (!values) return;

    setQuickAddFields({
      petIds: [...defaultPetIds, petId],
      evaluationInfo: {
        ...evaluationInfo,
        petServiceList: [
          ...evaluationInfo.petServiceList,
          {
            petId: petId.toString(),
            lodgingId: values.lodgingId,
            serviceId: values.serviceId,
            staffId: values.staffId,
            serviceTime: targetEvaluationInfo.duration || 0,
            servicePrice: targetEvaluationInfo.price || 0,
          },
        ],
      },
    });
    drawerRouter.back();
  });

  const handleCancel = useSerialCallback(() => {
    drawerRouter.back();
  });

  if (!evaluationInfo.startDate || !isNumber(evaluationInfo.startTime)) {
    return null;
  }

  return (
    <EvaluationAddNewPet
      startDate={evaluationInfo.startDate}
      startTime={evaluationInfo.startTime}
      petIds={defaultPetIds}
      customerId={customerId.toString()}
      onSubmit={handleSubmit}
      onCancel={handleCancel}
      ref={formRef}
      petId={petId}
      setPetId={setPetId}
    />
  );
};

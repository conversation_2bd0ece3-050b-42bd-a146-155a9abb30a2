import React, { useEffect, useRef, useState } from 'react';
import { DrawerHeader } from '../../../../../../components/Drawer/DrawerHeader';
import { ScrollerProvider } from '../../../../../../layout/components/ScrollerProvider';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { EditPetAndServiceFooter } from '../../../../components/EditPetServiceList/EditPetAndServiceFooter';
import { EvaluationDateTime } from '../../components/EvaluationDateTime';
import { useEvaluationQuickAdd } from '../../hooks/useEvaluationQuickAdd';
import { useCreateEvaluationRouterContext } from '../../QuickAddEvaluationApptDrawer.router';

export const QuickAddEvaluationReschedule = () => {
  const { evaluationInfo, setQuickAddFields } = useEvaluationQuickAdd();
  const [evaluationDateTime, setEvaluationDateTime] = useState<{ date?: string; time?: number }>({
    date: evaluationInfo?.startDate,
    time: evaluationInfo?.startTime,
  });
  const formRef = useRef<{ isValid: boolean }>(null);
  const isSaveDisabled = useBool(true);
  const drawerRouter = useCreateEvaluationRouterContext();

  const handleCancelEditPet = useLatestCallback(() => {
    drawerRouter.back();
  });

  const handleSubmit = useLatestCallback(() => {
    if (!formRef.current?.isValid) {
      return;
    }

    setQuickAddFields({
      evaluationInfo: {
        ...evaluationInfo,
        startDate: evaluationDateTime.date,
        startTime: evaluationDateTime.time,
      },
    });
    drawerRouter.back();
  });

  useEffect(() => {
    setEvaluationDateTime({
      date: evaluationInfo?.startDate,
      time: evaluationInfo?.startTime,
    });
  }, [evaluationInfo?.startDate, evaluationInfo?.startTime]);

  if (!evaluationInfo) {
    return null;
  }

  return (
    <div className={'moe-flex moe-flex-col moe-flex-1 moe-min-w-0 moe-min-h-0'}>
      <DrawerHeader title="Edit schedule" onClick={handleCancelEditPet} />
      <ScrollerProvider className="moe-flex moe-flex-col moe-flex-1 moe-py-[10px] moe-px-[32px] moe-overflow-auto moe-min-w-0">
        <EvaluationDateTime
          ref={formRef}
          setIsSaveDisabled={isSaveDisabled.as}
          value={{
            date: evaluationInfo.startDate,
            time: evaluationInfo.startTime,
          }}
          onChange={setEvaluationDateTime}
        />
      </ScrollerProvider>
      <EditPetAndServiceFooter disabled={isSaveDisabled.value} onSubmit={handleSubmit} onCancel={handleCancelEditPet} />
    </div>
  );
};

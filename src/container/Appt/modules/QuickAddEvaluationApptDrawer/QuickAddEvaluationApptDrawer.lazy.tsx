import { Drawer, Spin } from '@moego/ui';
import React, { lazy, memo, Suspense } from 'react';
import { useEvaluationQuickAddLeaveConfirm } from './hooks/useEvaluationQuickAddLeaveConfirm';
import { type QuickAddEvaluationApptDrawerProps } from './QuickAddEvaluationApptDrawer';

const LazyComponent = lazy(() =>
  import('./QuickAddEvaluationApptDrawer').then(({ QuickAddEvaluationApptDrawer }) => ({
    default: QuickAddEvaluationApptDrawer,
  })),
);

export interface DrawerWrapperProps
  extends Pick<QuickAddEvaluationApptDrawerProps, 'onClose' | 'onCreated' | 'onReady'> {}

export const DrawerLazyWrapper = memo((props: DrawerWrapperProps) => {
  const { onClose, onReady, onCreated } = props;

  const handleClose = useEvaluationQuickAddLeaveConfirm(onClose);

  return (
    <Drawer
      size="l"
      isOpen
      isDismissable={false}
      title={null}
      header={null}
      footer={null}
      {...props}
      classNames={{
        body: 'moe-flex moe-p-0 moe-rounded-8px-300 moe-border',
        container: 'moe-max-h-none',
        header: 'moe-hidden',
      }}
    >
      <Suspense
        fallback={
          <div className="moe-w-full moe-h-full moe-flex moe-justify-center moe-items-center">
            <Spin isLoading />
          </div>
        }
      >
        <LazyComponent onClose={handleClose} onCreated={onCreated} onReady={onReady} />
      </Suspense>
    </Drawer>
  );
});

import { createContext, useContext } from 'react';
import { type CommentNoteType } from '../../../../store/calendarLatest/calendar.utils';
import { type RouteItem, useDrawerRouter } from '../../../../utils/hooks/useDrawerRouter';

export enum CreateEvaluationRouteName {
  Home = 'Home',
  EditPetEvaluation = 'EditPetEvaluation',
  SelectPetAndEvaluation = 'SelectPetAndEvaluation',
  RescheduleDateAndTime = 'RescheduleDateAndTime',
  AddNewPet = 'AddNewPet',
  Comment = 'Comment',
  PetBelongings = 'PetBelongings',
}

type DrawerRoute = {
  [CreateEvaluationRouteName.EditPetEvaluation]: { petId: number };
  [CreateEvaluationRouteName.Comment]?: { tab: CommentNoteType; petId?: number };
};

const fallbackRoute: RouteItem<CreateEvaluationRouteName, DrawerRoute> = {
  name: CreateEvaluationRouteName.Home,
};

export const useCreateEvaluationRouter = () => {
  return useDrawerRouter<CreateEvaluationRouteName, DrawerRoute>(fallbackRoute);
};

export type DrawerRouterType = ReturnType<typeof useCreateEvaluationRouter>;
export const DrawerScopeContext = createContext<DrawerRouterType | null>(null);
export const DrawerRouterProvider = DrawerScopeContext.Provider;

export function useCreateEvaluationRouterContext() {
  const ctx = useContext(DrawerScopeContext);
  if (!ctx && __DEV__) {
    throw new Error('useCreateEvaluationRouterContext must be used within a DrawerRouterProvider');
  }
  return ctx!;
}

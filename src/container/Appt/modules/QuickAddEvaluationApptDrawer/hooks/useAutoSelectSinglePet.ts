import { useSelector } from 'amos';
import { useEffect, useMemo } from 'react';
import { toastApi } from '../../../../../components/Toast/Toast';
import { useBusinessApplicableEvaluation } from '../../../../../store/evaluation/evaluation.hooks';
import { customerPetListBox, petMapBox } from '../../../../../store/pet/pet.boxes';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { EvaluationNotFoundError } from '../utils/errors';
import { useEvaluationQuickAdd } from './useEvaluationQuickAdd';

/**
 * 若只有一个宠物，自动填充，需要区分 single / multiple evaluation 模式
 */
export const useAutoSelectSinglePet = () => {
  const { customerId, evaluationInfo, currentServiceItemType, setQuickAddFields } = useEvaluationQuickAdd();
  const { currentEvaluation } = useBusinessApplicableEvaluation(currentServiceItemType);
  const [customerPetList, petMap] = useSelector(customerPetListBox, petMapBox);

  const changePetIdSingle = useLatestCallback((newPetId: number) => {
    if (!currentEvaluation) {
      toastApi.error('Evaluation is inactive based on your settings.');
      throw new EvaluationNotFoundError();
    }
    const { id: serviceId = '', price: servicePrice = 0, duration: serviceTime = 0 } = currentEvaluation.toJSON();
    setQuickAddFields({
      petIds: [newPetId],
      evaluationInfo: {
        ...evaluationInfo,
        petServiceList: [
          {
            petId: newPetId.toString(),
            serviceId,
            servicePrice,
            serviceTime,
          },
        ],
      },
      isDirty: true,
    });
  });

  const allPets = customerPetList.getList(customerId);
  const alivePets = useMemo(() => {
    const alivePets = allPets
      .filter((petId) => {
        const pet = petMap.mustGetItem(petId);
        const isAlive = pet.isLive();
        return isAlive;
      })
      .toArray();
    return alivePets.map((petId) => ({
      value: petId,
      label: String(petId),
    }));
  }, [allPets, petMap]);
  const defaultTargetPetId = alivePets[0]?.value;
  const petNum = alivePets.length;

  useEffect(() => {
    if (defaultTargetPetId && petNum === 1) {
      changePetIdSingle(defaultTargetPetId);
    }
  }, [defaultTargetPetId, petNum]);
};

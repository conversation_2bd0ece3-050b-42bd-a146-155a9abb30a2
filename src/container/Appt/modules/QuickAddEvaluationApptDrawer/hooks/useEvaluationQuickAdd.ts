import { useDispatch, useSelector } from 'amos';
import {
  type UpdateEvaluationQuickAddInput,
  updateEvaluationQuickAddFields,
} from '../../../../../store/evaluation/evaluation.actions';
import { evaluationQuickAddFieldsBox } from '../../../../../store/evaluation/evaluation.boxes';

/**
 * 针对 evaluation quick add 的 state / action 封装
 */
export const useEvaluationQuickAdd = () => {
  const dispatch = useDispatch();
  const [config] = useSelector(evaluationQuickAddFieldsBox);
  const setQuickAddFields = (input: UpdateEvaluationQuickAddInput) => {
    dispatch(updateEvaluationQuickAddFields(input));
  };

  return {
    ...config,
    setQuickAddFields,
  };
};

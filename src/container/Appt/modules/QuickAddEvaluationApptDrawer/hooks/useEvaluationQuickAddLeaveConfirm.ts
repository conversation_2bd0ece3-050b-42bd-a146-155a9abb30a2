import { useMemo } from 'react';
import { useBeforeUnload } from 'react-use';
import { useUnsavedConfirmV2 } from '../../../../../utils/hooks/useUnsavedConfirmV2';
import { useDraftConfirmConfig } from '../../QuickAddAppt/hooks/useQuickAddLeaveConfirm';
import { useEvaluationQuickAdd } from './useEvaluationQuickAdd';

export const useEvaluationQuickAddLeaveConfirm = (onCancel?: () => void) => {
  const { isDirty } = useEvaluationQuickAdd();
  const { getDoubleConfirmConfig } = useDraftConfirmConfig();

  const props = useMemo(() => {
    const config = getDoubleConfirmConfig();
    return {
      ...config,
      content: "The evaluation that you're currently creating has unsaved changes.",
      confirmText: config.okText,
      onCancel,
      onClose() {},
      onConfirm() {
        return Promise.reject();
      },
    };
  }, [getDoubleConfirmConfig]);

  const showConfirm = !!isDirty;

  useBeforeUnload(showConfirm, 'You have unsaved changes, are you sure?');

  const handleCloseTrigger = useUnsavedConfirmV2(showConfirm, onCancel, () => false, undefined, props);
  return handleCloseTrigger;
};

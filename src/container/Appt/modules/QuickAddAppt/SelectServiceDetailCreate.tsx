import React, { memo } from 'react';
import { SelectServiceDetail } from '../../components/SelectServiceDetail/SelectServiceDetail';
import { CreateApptRouteName, useCreateApptRouterContext } from './QuickAddApptDrawer.router';

export const SelectServiceDetailCreate = memo(() => {
  const drawerRouter = useCreateApptRouterContext();
  const payload = drawerRouter.getParams(CreateApptRouteName.SelectServiceDetail);
  return <SelectServiceDetail payload={payload} />;
});

import { <PERSON><PERSON>, <PERSON><PERSON>, Spin, Text } from '@moego/ui';
import { useDispatch, useSelector, useStore } from 'amos';
import React, { memo, type PropsWithChildren } from 'react';
import { useAsync } from 'react-use';
import { ClientPicker } from '../../../../components/ClientPicker/ClientPicker';
import { Condition } from '../../../../components/Condition';
import { WithMoeGoPay } from '../../../../components/MoeGoPay/WithMoeGoPay';
import { MoeNewTag } from '../../../../components/Tag/MoeTag.style';
import { ApptTestIds } from '../../../../config/testIds/apptDrawer';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { CommentNoteType } from '../../../../store/calendarLatest/calendar.utils';
import { getAllClientInfo } from '../../../../store/customer/customer.actions';
import { emitFitStaffRouteOnSelectClient } from '../../../../store/mapView/actions/public/mapView.actions';
import { PRE_AUTH_FEATURE_NAME } from '../../../../store/stripe/preAuth.boxes';
import { ID_ANONYMOUS, isNormal } from '../../../../store/utils/identifier';
import { useBool } from '../../../../utils/hooks/useBool';
import { useEnableFulfillmentFlow } from '../../../../utils/hooks/useEnableFulfillmentFlow';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { PreAuthContextProvider } from '../../../Calendar/Grooming/PreAuthForAppt/PreAuthContext';
import { PreAuthSwitchBase } from '../../../Calendar/Grooming/PreAuthForAppt/PreAuthSwitchBase';
import { PreAuthSwitchForDrawerAdd } from '../../../Calendar/Grooming/PreAuthForAppt/PreAuthSwitchForDrawerAdd';
import { PreAuthSwitchTooltip } from '../../../Calendar/Grooming/PreAuthForAppt/PreAuthSwitchTooltip';
import { usePreAuthOnboarding } from '../../../Calendar/Grooming/PreAuthForAppt/usePreAuthOnboarding';
import { usePreAuthSwitch } from '../../../Calendar/Grooming/PreAuthForAppt/usePreAuthSwitch';
import { DrawerLayout } from '../../../Calendar/latest/ApptCalendar/components/DrawerLayout';
import { SectionInfo } from '../../components/SectionInfo';
import { clearExistPets } from '../../store/appt.actions';
import {
  selectApptStartAndEndTime,
  selectPetsInAppt,
  selectPetsServiceAddonDateInfo,
} from '../../store/appt.selectors';
import { ApptTime } from './ApptTime';
import { ClientAddress } from './ClientAddress';
import { AppointmentBusiness } from './components/AppointmentBusiness';
import { ApptColorCode } from './components/ApptColorCode';
import { BySlotWarningBanner } from './components/BySlotWarningBanner';
import { ClientAlertNotes } from './components/ClientAlertNotes';
import { ClientGroomingNotes } from './components/ClientGroomingNotes';
import { PreferBizAlert } from './components/PreferBizAlert';
import { useCustomerOverview } from './hooks/useCustomerOverview';
import { useQuickAddConfig } from './hooks/useQuickAddConfig';
import { type SubmitQuickAddParams } from './hooks/useSubmitQuickAdd';
import { CreateApptRouteName, useCreateApptRouterContext } from './QuickAddApptDrawer.router';
import { SelectPetAndService } from './SelectPetAndService/SelectPetAndService';

export interface StepHomeProps extends SubmitQuickAddParams {}

export const StepHome = memo((props: PropsWithChildren<{}>) => {
  const { children } = props;
  const dispatch = useDispatch();
  const store = useStore();
  const [{ clientId, ticketComment }, setQuickAddConfig] = useQuickAddConfig();
  const [business, pets, initPetDates, { startDateTime }] = useSelector(
    selectCurrentBusiness(),
    selectPetsInAppt(),
    selectPetsServiceAddonDateInfo(),
    selectApptStartAndEndTime(),
  );
  const loading = useBool();
  const enableFulfillmentFlow = useEnableFulfillmentFlow();
  const drawerRouter = useCreateApptRouterContext();

  const isMobile = business.isMobileGrooming();
  const validateClient = isNormal(clientId);
  const isSelectedPetAndService = pets.length > 0;

  const onClearClient = useLatestCallback(() => {
    setQuickAddConfig({
      clientId: ID_ANONYMOUS,
      alertNotes: '',
      lastAppointment: undefined,
      lastAppointmentVisible: false,
    });
    if (isSelectedPetAndService) {
      dispatch(clearExistPets());
    }
  });

  // 选中client后，判断是否有last appointment
  const onSelectClient = useLatestCallback(async (clientId) => {
    onClearClient();
    setQuickAddConfig({
      disableViewClientLastAppt: false,
      clientId,
    });
    dispatch(emitFitStaffRouteOnSelectClient(clientId));
  });

  const handleEditSchedule = () => {
    const { startDateTime } = store.select(selectApptStartAndEndTime());
    if (startDateTime) {
      drawerRouter.go(CreateApptRouteName.EditSchedule, { initDateTime: startDateTime, initDates: initPetDates });
    }
  };

  const { loading: loadingClient } = useAsync(async () => {
    if (isNormal(clientId)) {
      await dispatch(getAllClientInfo(clientId));
    }
  }, [clientId]);

  const { loadingClientLastAppt, customerPackages } = useCustomerOverview();

  //  正常来说 usePreAuthOnboarding 这个 hook 是在 HintFor* 系列组件内部调用，并且通过 autoIncrement 参数来实现使用的自增。但是由于
  //  hook 内部的设计不幂等，导致如果 HintFor* 组件（或其上层某个父组件）被条件渲染，就会相当于这个组件被显示 - 隐藏 - 再显示了，后续的显示
  //  会被判定为非首次展示，导致 onboarding 出不来。
  //  未来建议的优化思路：将 hook 的实现修改为幂等的，需要在条件渲染期间稳定保持的状态放到 Context 里去。
  const preAuthOnboardingState = usePreAuthOnboarding({ name: 'CreateTicket', autoIncrement: true });

  const preAuthManager = usePreAuthSwitch({
    isFromOB: false,
    date: startDateTime,
    time: startDateTime,
    isRepeat: false,
    isBookAgain: false,
    customerId: clientId,
    onChange: (preAuthParams) => {
      setQuickAddConfig({ preAuthParams });
    },
  });

  return (
    <DrawerLayout footerClassName="shadow" footer={children}>
      <Spin isLoading={loading.value} classNames={{ base: 'moe-h-full' }}>
        <Heading
          size="3"
          className="moe-bg-neutral-sunken-0 moe-px-m moe-pt-l moe-pb-m moe-text-primary moe-sticky moe-top-0 moe-z-[1]"
        >
          New appointment
        </Heading>
        <div
          className="moe-px-m moe-mb-m"
          style={{ background: 'linear-gradient(to bottom, #F3F3F3, #F3F3F3 40px, #fff 40px, #fff)' }}
        >
          <ClientPicker
            loadingClient={loadingClient || loadingClientLastAppt}
            clientId={clientId}
            onChange={onSelectClient}
            customerPackages={customerPackages}
            go2ClientNotes={() => {
              drawerRouter.go(CreateApptRouteName.Comment, { tab: CommentNoteType.ClientNotes });
            }}
          />
        </div>
        <Condition if={validateClient}>
          {() => (
            <div className="moe-relative moe-flex moe-flex-col moe-gap-y-m moe-pb-[20px] moe-px-m">
              <BySlotWarningBanner />
              <ClientAlertNotes />
              <Condition if={!isSelectedPetAndService && !enableFulfillmentFlow}>
                <PreferBizAlert businessId={business.id} customerId={clientId} setLoading={loading.as} />
              </Condition>
              <Condition if={isMobile || isSelectedPetAndService}>
                <div className="empty:moe-hidden">
                  <Condition if={isSelectedPetAndService}>
                    <ApptTime className="moe-px-xs" onEditTime={handleEditSchedule} />
                  </Condition>
                  <Condition if={isMobile}>
                    <ClientAddress clientId={clientId} />
                  </Condition>
                </div>
              </Condition>
              <AppointmentBusiness customerId={clientId} />
              <Condition if={!loadingClientLastAppt}>
                <SelectPetAndService />
                <Condition if={isSelectedPetAndService}>
                  <SectionInfo
                    className="moe-gap-y-[2px]"
                    label={
                      <div className="moe-flex moe-items-center moe-justify-between">
                        <Heading size="6" className="moe-text-primary">
                          Ticket comments
                        </Heading>
                        <Button
                          variant="tertiary"
                          align="end"
                          size="s"
                          onPress={() => {
                            drawerRouter.go(CreateApptRouteName.Comment, { tab: CommentNoteType.TicketComments });
                          }}
                          data-testid={ApptTestIds.ApptTicketCommentsViewAllBtn}
                        >
                          View all
                        </Button>
                      </div>
                    }
                  >
                    <ClientGroomingNotes
                      ticketComments={ticketComment}
                      onChange={(val) => {
                        setQuickAddConfig({ ticketComment: val });
                      }}
                      data-testid={ApptTestIds.ApptTicketCommentsBtn}
                    />
                    <Text variant="small" className="moe-text-tertiary moe-h-[30px] moe-mt-[8px]">
                      Private to business only
                    </Text>
                  </SectionInfo>
                  <PreAuthContextProvider value={preAuthManager}>
                    <WithMoeGoPay>
                      {/* An HTML tag is required as the direct child of the Popover. */}
                      <div>
                        <SectionInfo
                          label={
                            <div className="moe-flex moe-items-center moe-gap-x-[16px]">
                              <div className="moe-flex moe-items-center">
                                <Heading size="6" className="moe-text-primary">
                                  {PRE_AUTH_FEATURE_NAME}
                                </Heading>
                                <PreAuthSwitchTooltip />
                                {preAuthOnboardingState.onboarding.isStrictValid() && <MoeNewTag>New</MoeNewTag>}
                              </div>
                              <PreAuthSwitchBase />
                            </div>
                          }
                          noGap
                        >
                          <PreAuthSwitchForDrawerAdd />
                        </SectionInfo>
                      </div>
                    </WithMoeGoPay>
                  </PreAuthContextProvider>
                  <SectionInfo
                    label={
                      <Heading size="6" className="moe-text-primary">
                        Color code
                      </Heading>
                    }
                    flexDirection="row"
                  >
                    <ApptColorCode />
                  </SectionInfo>
                </Condition>
              </Condition>
            </div>
          )}
        </Condition>
      </Spin>
    </DrawerLayout>
  );
});

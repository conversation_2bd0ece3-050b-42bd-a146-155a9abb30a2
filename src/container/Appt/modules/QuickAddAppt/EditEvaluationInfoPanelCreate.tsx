import React, { memo } from 'react';
import { EditEvaluationInfoPanel } from '../../components/Evaluation/EditEvaluationInfoPanel';
import { CreateApptRouteName, useCreateApptRouterContext } from './QuickAddApptDrawer.router';

export const EditEvaluationInfoPanelCreate = memo(() => {
  const drawerRouter = useCreateApptRouterContext();
  const { payload } = drawerRouter.getParams(CreateApptRouteName.EditPetEvaluation);
  return <EditEvaluationInfoPanel payload={payload} />;
});

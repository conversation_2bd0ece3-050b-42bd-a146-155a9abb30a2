import { AlertDialog } from '@moego/ui';
import { useSelector } from 'amos';
import { type ModalFuncProps } from 'antd/es/modal/Modal';
import { useMemo } from 'react';
import { ApptTestIds } from '../../../../../config/testIds/apptDrawer';
import {
  PATH_GROOMING_CALENDAR,
  PATH_HOME_OVERVIEW,
  PATH_LODGING_CALENDAR,
  PATH_MAP_VIEW,
  PATH_TICKET_ADD,
} from '../../../../../router/paths';
import { useBool } from '../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { useUnsavedConfirmGlobalV2 } from '../../../../../utils/hooks/useUnsavedConfirmGlobalV2';
import { selectTicketRepeatPreviewState } from '../../../components/RepeatSeries/store/repeatSeries.selectors';
import { useQuickAddConfig } from './useQuickAddConfig';

const noNeedConfirmPath = [
  PATH_TICKET_ADD.path,
  PATH_LODGING_CALENDAR.path,
  PATH_GROOMING_CALENDAR.path,
  PATH_HOME_OVERVIEW.path,
  PATH_MAP_VIEW.path,
];

const baseConfig = {
  type: 'success',
  width: 480,
  title: 'Are you sure you’re ready to leave?',
  content: 'The appointment that you’re currently creating has unsaved changes.',
  okText: 'Back to edit',
  cancelText: 'Leave anyway',
  centered: true,
  icon: null,
  cancelButtonProps: {
    className: '!moe-rounded-[56px] !moe-font-bold !moe-text-[#333] !moe-text-[14px]',
    style: {
      textShadow: 'none',
    },
  },
  okButtonProps: {
    className:
      '!moe-rounded-[56px] !moe-font-bold !moe-text-white !moe-text-[14px] hover:!moe-shadow-[0_10px_10px_-10px_rgba(0,0,0,0.5)]',
    style: {
      textShadow: 'none',
    },
  },
};

export const useDraftConfirmConfig = () => {
  const getDoubleConfirmConfig = useLatestCallback(() => ({ ...baseConfig }));
  const asyncDoubleConfirm = useLatestCallback((params: ModalFuncProps) => {
    const finalConfig = {
      ...getDoubleConfirmConfig(),
      ...params,
    };
    AlertDialog.open({
      title: finalConfig.title,
      content: finalConfig.content as string,
      confirmText: finalConfig.okText,
      cancelText: finalConfig.cancelText,
      cancelButtonProps: {
        'data-testid': ApptTestIds.ApptPopupLeaveAnywayBtn,
      },
      confirmButtonProps: {
        'data-testid': ApptTestIds.ApptPopupBackEditBtn,
      },
      onCancel: () => {
        params.onCancel?.();
      },
      onConfirm: () => {
        params.onOk?.();
      },
    });
  });
  return {
    getDoubleConfirmConfig,
    asyncDoubleConfirm,
  };
};

/*
 * quick add 抽屉编辑态下离开页面时候的拦截
 */
export const useQuickAddLeaveConfirm = (onCancel?: () => void) => {
  const [{ drawerVisible: repeatDrawerVisible }] = useSelector(selectTicketRepeatPreviewState);
  // 当触发过一次 confirm leave 拦截后，需要重置一下这个值，否则下次跳转路由的时候无法拦截
  const updateConfirmLeaveTrigger = useBool();
  const { getDoubleConfirmConfig } = useDraftConfirmConfig();
  const [{ isDirty }] = useQuickAddConfig();
  const props = useMemo(() => {
    const config = getDoubleConfirmConfig();
    return {
      ...config,
      confirmText: config.okText,
      onCancel,
      onClose() {
        updateConfirmLeaveTrigger.toggle();
      },
      onConfirm() {
        updateConfirmLeaveTrigger.toggle();
        return Promise.reject();
      },
    };
  }, [getDoubleConfirmConfig]);
  const showConfirm = !!isDirty && !repeatDrawerVisible;
  useUnsavedConfirmGlobalV2({
    showConfirm: showConfirm,
    modalProps: props,
    noNeedConfirmPath,
    alwaysRun: true,
  });
};

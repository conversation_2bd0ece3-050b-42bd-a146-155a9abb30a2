import {
  type CreateAppointmentParams,
  type CreateAppointmentParamsPetBelonging,
} from '@moego/api-web/moego/api/appointment/v1/appointment_api';
import { AppointmentNoteType } from '@moego/api-web/moego/models/appointment/v1/appointment_note_enums';
import { useDispatch, useSelector } from 'amos';
import { useMemo } from 'react';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { customerMapBox } from '../../../../../store/customer/customer.boxes';
import { GroomingTicketSource } from '../../../../../store/grooming/grooming.boxes';
import { isNormal } from '../../../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../../../../utils/DateTimeUtil';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { apptReporter } from '../../../../../utils/reportData/reporter/apptReporter';
import { ReportActionName } from '../../../../../utils/reportType';
import { reportData } from '../../../../../utils/tracker';
import { usePreAuthOnboarding } from '../../../../Calendar/Grooming/PreAuthForAppt/usePreAuthOnboarding';
import {
  buildPreviewParamsForQuickAdd,
  buildSaveApptParamsFromQuickAdd,
} from '../../../components/RepeatSeries/adapters/ApptParamAdaptersForQuickAdd';
import { useRepeatSeriesState } from '../../../components/RepeatSeries/hooks/useRepeatSeriesState';
import { useGetPetsInfoForApi } from '../../../hooks/useGetPetsInfoForApi';
import { useGetApptServiceList } from '../../../hooks/useGetServiceListByPet';
import { clearCreateApptData } from '../../../store/appt.actions';
import { createAppointment } from '../../../store/appt.api';
import { matchApptFlowScenes } from '../../../store/appt.options';
import {
  selectApptComputedInfo,
  selectApptStartAndEndTime,
  selectMainServiceInAppt,
  selectPetsInAppt,
  selectRepeatSaveApptParams,
} from '../../../store/appt.selectors';
import { ApptFlowScene, CreateApptId } from '../../../store/appt.types';
import { useQuickAddConfig } from './useQuickAddConfig';

export interface SubmitQuickAddParams {
  onCreated?: (params: {
    ticketId: number;
    clientId: number;
    quickAddParams: CreateAppointmentParams;
    apptStartDate?: string;
  }) => Promise<void>;
}

export function useSubmitQuickAdd(params: SubmitQuickAddParams) {
  const { onCreated } = params;
  const [business, pets, { startDateTime }] = useSelector(
    selectCurrentBusiness,
    selectPetsInAppt(),
    selectApptStartAndEndTime,
  );
  const [
    {
      clientId,
      alertNotes,
      ticketComment,
      colorCode,
      allPetsStartAtSameTime,
      preAuthParams,
      petBelongings: OriginBelongings,
    },
    setQuickAddConfig,
  ] = useQuickAddConfig();
  const dispatch = useDispatch();
  const [{ serviceList }, { isDaycare }, { serviceItemType }] = useSelector(
    selectRepeatSaveApptParams(),
    selectApptComputedInfo(),
    selectMainServiceInAppt(),
  );
  const [enableRepeatPreview, staffValid] = matchApptFlowScenes(
    [ApptFlowScene.RepeatPreview, ApptFlowScene.StaffValid],
    serviceItemType,
  );
  const getPetsInfoForApi = useGetPetsInfoForApi();
  const apptServiceList = useGetApptServiceList();

  const petBelongings = useMemo(() => {
    return pets
      ?.map((pet) => {
        const [belongings] = OriginBelongings?.[Number(pet.petId)] || [];
        if (belongings && belongings?.name && isNormal(pet.petId)) {
          return {
            petId: pet.petId,
            name: belongings.name,
            area: belongings.area,
            photoUrl: belongings.photoUrl,
          };
        }
        return undefined;
      })
      .filter(Boolean);
  }, [pets, OriginBelongings]) as CreateAppointmentParamsPetBelonging[];

  const isValidStaff = useMemo(
    () => (staffValid ? apptServiceList.every((s) => isNormal(s.staffId)) : true),
    [apptServiceList, staffValid],
  );
  const validateForm = useMemo(() => {
    return pets.length > 0 && isValidStaff && isNormal(clientId);
  }, [pets, clientId, isValidStaff]);

  const { markUsedPreAuth } = usePreAuthOnboarding();

  const [mainService] = useSelector(selectMainServiceInAppt(CreateApptId));
  // repeat preview 流程
  const { isRepeatPreviewMode, previewRepeat, openPreviewDrawer, checkAndJumpToCalendar } = useRepeatSeriesState();
  const [currentCustomer] = useSelector(customerMapBox.mustGetItem(clientId));
  const handlePreviewRepeat = useLatestCallback(async (inputParams: CreateAppointmentParams) => {
    const previewParams = buildPreviewParamsForQuickAdd(serviceList);
    const saveApptParams = buildSaveApptParamsFromQuickAdd(inputParams, {
      customerAddressId: currentCustomer.primaryAddressId,
      customerId: clientId,
      serviceList,
      preAuthParams,
      mainService,
    });
    await previewRepeat({ previewParams, saveApptParams });
    openPreviewDrawer('quick', { supportRepeatWeekByDays: isDaycare });
    enableRepeatPreview && checkAndJumpToCalendar();
  });

  const handleSubmit = useSerialCallback(async () => {
    if (!isValidStaff) {
      // TODO 优化提示，在new ui后，这个交互逻辑缺失了
      setQuickAddConfig({ showStaffEmptyError: true });
      return;
    }
    if (!validateForm) {
      return;
    }
    const params: CreateAppointmentParams = {
      businessId: String(business.id),
      appointment: {
        customerId: String(clientId),
        source: GroomingTicketSource.Web,
        colorCode,
        allPetsStartAtSameTime,
      },
      petDetails: getPetsInfoForApi(CreateApptId),
      preAuth: {
        enable: preAuthParams.preAuthEnable,
        paymentMethodId: preAuthParams.preAuthPaymentMethod,
        cardBrandLast4: preAuthParams.preAuthCardNumber,
      },
      notes: [
        {
          note: alertNotes,
          type: AppointmentNoteType.ALERT_NOTES,
        },
        {
          note: ticketComment,
          type: AppointmentNoteType.COMMENT,
        },
      ].filter((note) => note.note),
      petBelongings,
    };
    if (isRepeatPreviewMode) {
      reportData(ReportActionName.RepeatQuickAddPreview);
      await handlePreviewRepeat(params);
      return;
    }
    apptReporter.setCreateApptFrom('quickAdd');
    const { appointmentId: ticketId } = await dispatch(createAppointment(params));
    dispatch(clearCreateApptData());
    if (preAuthParams.preAuthEnable) {
      markUsedPreAuth();
    }

    await onCreated?.({
      ticketId: Number(ticketId),
      clientId,
      quickAddParams: params,
      apptStartDate: startDateTime?.format(DATE_FORMAT_EXCHANGE),
    });
  });

  return {
    /** 是否可以提交Quick Add表单的数据 */
    validateForm,
    /** 提交数据 */
    handleSubmit,
  };
}

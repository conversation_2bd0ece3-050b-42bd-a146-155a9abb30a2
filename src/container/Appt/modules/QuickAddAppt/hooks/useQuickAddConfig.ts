import { useDispatch, useSelector } from 'amos';
import { calendarQuickAddApptFields } from '../../../../../store/calendarLatest/calendar.boxes';
import { type UpdateQuickAddConfigParams, updateQuickAddConfig } from '../../../store/appt.actions';

export type UseQuickAddConfigReturnType = [
  UpdateQuickAddConfigParams,
  (input: Partial<UpdateQuickAddConfigParams>) => void,
];

export function useQuickAddConfig(): UseQuickAddConfigReturnType {
  const dispatch = useDispatch();
  const [{ staffId, appointmentDate, ...rest }] = useSelector(calendarQuickAddApptFields);

  const setQuickAddConfig = (input: Partial<UpdateQuickAddConfigParams>) => {
    dispatch(updateQuickAddConfig(input));
  };

  return [rest, setQuickAddConfig];
}

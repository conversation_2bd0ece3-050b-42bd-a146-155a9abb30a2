import { type Action, useDispatch, useSelector, useStore } from 'amos';
import { useEffect } from 'react';
import { currentBusinessIdBox } from '../../../../../store/business/business.boxes';
import { getCalendarConfig } from '../../../../../store/calendarLatest/actions/private/calendar.actions';
import { getLodgingUnitList } from '../../../../../store/lodging/actions/public/lodgingUnit.actions';
import { selectLodgingUnitIdListWithoutCategory } from '../../../../../store/lodging/lodgingUnit.selectors';
import { getOnlineBookingPreference } from '../../../../../store/onlineBooking/actions/private/onlineBookingPreference.actions';
import { getGroomingServiceAvailability } from '../../../../../store/onlineBooking/actions/private/onlineBookingSettings.actions';
import { getPetCodeList } from '../../../../../store/pet/petCode.actions';
import { getAllBusinessBasicServiceInfoList } from '../../../../../store/service/actions/public/service.actions';
import { selectAllServiceList } from '../../../../../store/service/service.selectors';
import { getBusinessServiceArea } from '../../../../../store/serviceArea/serviceArea.actions';
import { selectBusinessServiceAreaIdList } from '../../../../../store/serviceArea/serviceArea.selectors';
import { getStaffList } from '../../../../../store/staff/staff.actions';
import { selectBusinessStaffs } from '../../../../../store/staff/staff.selectors';
import { isNormal } from '../../../../../store/utils/identifier';
import { truly } from '../../../../../store/utils/utils';
import { useEnableMultiPetBySlotFeature } from '../../../../Calendar/latest/components/SlotCalendar/hooks/useSlotCalendarFeature';

/** 获取抽屉打开后 需要的基本数据，比如staff数据，service数据等 */
export function useQuickAddFetchData() {
  const dispatch = useDispatch();
  const store = useStore();
  const [businessId] = useSelector(currentBusinessIdBox);
  const isEnableCalendarSlot = useEnableMultiPetBySlotFeature();

  useEffect(() => {
    if (isNormal(businessId)) {
      const staffList = store.select(selectBusinessStaffs());
      const serviceList = store.select(selectAllServiceList());
      const lodgingUnitList = store.select(selectLodgingUnitIdListWithoutCategory(String(businessId)));
      const serviceAreaList = store.select(selectBusinessServiceAreaIdList(businessId));

      const actions: Action<Promise<unknown>, any>[] = [
        !serviceList.size && getAllBusinessBasicServiceInfoList(),
        !staffList.size && getStaffList(),
        !lodgingUnitList.size && getLodgingUnitList({ businessId: String(businessId) }),
        !serviceAreaList.size && getBusinessServiceArea(businessId),
        getPetCodeList(),
        ...(isEnableCalendarSlot
          ? [
              getGroomingServiceAvailability({ businessId: String(businessId) }),
              getCalendarConfig(businessId),
              getOnlineBookingPreference(),
            ]
          : []),
      ].filter(truly);

      dispatch(actions);
    }
  }, [businessId]);
}

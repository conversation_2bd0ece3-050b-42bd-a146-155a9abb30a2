import { useSelector, useStore } from 'amos';
import { alertApi } from '../../../../../components/Alert/AlertApi';
import { history } from '../../../../../history';
import { PATH_TICKET_ADD, type TicketFormType } from '../../../../../router/paths';
import { type ServiceModel, serviceMapBox } from '../../../../../store/service/service.boxes';
import { isNormal } from '../../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { ReportActionName } from '../../../../../utils/reportType';
import { reportData } from '../../../../../utils/tracker';
import { useRepeatSeriesState } from '../../../components/RepeatSeries/hooks/useRepeatSeriesState';
import {
  selectApptStartAndEndTime,
  selectMainServiceInAppt,
  selectPetsInAppt,
  selectServiceListWithPet,
} from '../../../store/appt.selectors';
import { CreateApptId } from '../../../store/appt.types';
import { useQuickAddConfig } from './useQuickAddConfig';

export function useAdvancedJump() {
  const store = useStore();
  const appointmentId = CreateApptId;
  const [{ clientId, alertNotes, ticketComment, colorCode, preAuthParams, allPetsStartAtSameTime }] =
    useQuickAddConfig();

  const [apptPets, mainService, { startDateTime: appointmentDate }] = useSelector(
    selectPetsInAppt(appointmentId),
    selectMainServiceInAppt(),
    selectApptStartAndEndTime(),
  );
  const { isOpen: isRepeatOpen } = useRepeatSeriesState();
  const handleJumpToAdvancedEdit = useLatestCallback(() => {
    reportData(ReportActionName.CalendarQuickAddAdvancedEdit);
    if (!isNormal(clientId)) {
      return alertApi.warning('Please select a client before advanced edit');
    }
    if (!isNormal(mainService.staffId) || !appointmentDate) {
      return alertApi.warning('Appointment info is invalid');
    }

    const staffId = Number(mainService.staffId);

    const petServices =
      apptPets?.map((pet) => ({
        petId: Number(pet.petId),
        serviceList: store.select(selectServiceListWithPet(pet, appointmentId)),
      })) ?? [];

    // 如果没有传入pets，那么就是从quickAddConfig中获取petServices
    // 传入了pets的情况是，是在select pet & services 界面直接点击advanced edit
    const petAndServices: TicketFormType['petAndServices'] = petServices.map((petService) => {
      const { petId, serviceList } = petService;
      const list = serviceList.map((service) => {
        const { serviceId, servicePrice, serviceTime } = service;
        const s = store.select(serviceMapBox.mustGetItem(serviceId));
        return {
          ...(s.toJS() as ServiceModel),
          ...service,
          serviceId,
          price: servicePrice,
          duration: serviceTime,
          staffId: isNormal(service.staffId) ? service.staffId : staffId,
          // src/container/CreateTicket/hooks/useInitTicket.ts#109
          // 这个value就是service的Id 😭
          value: serviceId,
        };
      });
      return {
        pet: petId,
        serviceItem: list,
      };
    });

    const statedCreate = PATH_TICKET_ADD.stated({
      date: appointmentDate.valueOf(),
      time: appointmentDate.valueOf(),
      clientId,
      staffId,
      presetInfo: {
        client: {
          value: clientId,
        },
        alertNotes,
        ticketComments: ticketComment,
        colorCode,
        petAndServices,
        allPetsStartAtSameTime,
        preAuthValues: preAuthParams,
      },
      reuseRepeatState: isRepeatOpen,
    });
    history.push(statedCreate);
  });

  return {
    handleJumpToAdvancedEdit,
  };
}

import { type ServiceDetail } from '@moego/api-web/moego/api/appointment/v1/appointment_api';
import { type CustomerPackageView } from '@moego/api-web/moego/api/appointment/v1/appointment_view';
import { useDispatch, useSelector, useStore } from 'amos';
import { useEffect, useState } from 'react';
import { AppointmentScheduleClient } from '../../../../../middleware/clients';
import { currentBusinessIdBox } from '../../../../../store/business/business.boxes';
import { type CalendarQuickAddApptFields } from '../../../../../store/calendarLatest/calendar.types';
import { petMapBox } from '../../../../../store/pet/pet.boxes';
import { getSavedPriceList } from '../../../../../store/pet/petSavedPrice.actions';
import { isNormal } from '../../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { useResolveStaff } from '../../../hooks/useResolveStaff';
import { getCustomerLastAppointment } from '../../../store/appt.api';
import { selectApptStartAndEndTime } from '../../../store/appt.selectors';
import { useQuickAddConfig } from './useQuickAddConfig';

export function useCustomerOverview() {
  const dispatch = useDispatch();
  const store = useStore();
  const [businessId] = useSelector(currentBusinessIdBox);
  const [customerPackages, setCustomerPackages] = useState<CustomerPackageView[]>([]);

  const [{ clientId, disableViewClientLastAppt }, setQuickAddConfig] = useQuickAddConfig();
  const { getAvailableStaff } = useResolveStaff();

  // last appointment 校验合法性，是否可以book again
  const checkLastAppointmentValidate = useLatestCallback((serviceDetails: ServiceDetail[]) => {
    if (!serviceDetails || serviceDetails.length === 0) {
      return false;
    }
    const hasValidatePet = serviceDetails.some((service) => {
      const {
        pet: { id: petId },
      } = service;
      const validatePet = store.select(petMapBox.mustGetItem(Number(petId))).isLive();
      return validatePet;
    });
    return hasValidatePet;
  });

  const fetchClientLastApptInfo = useSerialCallback(async () => {
    const config: Partial<CalendarQuickAddApptFields> = {
      isDirty: true,
    };
    const { startDateTime: appointmentDate } = store.select(selectApptStartAndEndTime());
    const [result, showOnCalendarStaffs] = await Promise.all([
      dispatch(getCustomerLastAppointment({ customerId: String(clientId), selectedBusinessId: String(businessId) })),
      appointmentDate && (await getAvailableStaff(appointmentDate, true)),
    ]);

    const {
      customer: {
        customerProfile,
        lastAlertNote: { note = '' } = {},
        customerPackages,
      },
      appointment,
      serviceDetail,
      hasLastAppointment,
    } = result;

    const preferredGroomerId = Number(customerProfile.preferredGroomerId);

    if (hasLastAppointment) {
      const appointmentId = appointment.id;
      const pets = serviceDetail.map((s) => s.pet.id);

      const [petFeeding] = await Promise.all([
        AppointmentScheduleClient.getPetFeedingMedicationSchedules({
          appointmentId: appointmentId,
        }),
        ...pets.map((id) => dispatch(getSavedPriceList(Number(id)))),
      ]);
      const lastAppointmentVisible = checkLastAppointmentValidate(serviceDetail);
      Object.assign(config, {
        lastAppointmentVisible,
        lastAppointment: {
          info: result,
          extraInfo: petFeeding,
          availableStaffs: showOnCalendarStaffs,
        },
      });
    }

    Object.assign(config, {
      colorCode: customerProfile.clientColor,
      alertNotes: note,
      preferredStaffId: preferredGroomerId,
      showOnCalendarStaffs,
    });
    setCustomerPackages(customerPackages);
    setQuickAddConfig(config);
  });

  useEffect(() => {
    if (disableViewClientLastAppt || !isNormal(clientId)) {
      return;
    }
    fetchClientLastApptInfo();
  }, [clientId, disableViewClientLastAppt]);

  return {
    loadingClientLastAppt: fetchClientLastApptInfo.isBusy(),
    customerPackages,
  };
}

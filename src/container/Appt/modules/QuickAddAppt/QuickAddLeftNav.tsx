import { useSelector } from 'amos';
import React, { useMemo } from 'react';
import { BDOnlyTabs, CreateApptNavigationTabs } from '../../../../store/calendarLatest/calendar.utils';
import { selectBDFeatureEnable } from '../../../../store/company/company.selectors';
import { isNormal } from '../../../../store/utils/identifier';
import { DrawerLeftNav } from '../../components/DrawerLeftNav/DrawerLeftNav';
import { matchApptFlowScene } from '../../store/appt.options';
import { selectMainServiceInAppt, selectPetsInAppt } from '../../store/appt.selectors';
import { ApptFlowScene } from '../../store/appt.types';
import { useQuickAddConfig } from './hooks/useQuickAddConfig';
import { CreateApptRouteName, useCreateApptRouterContext } from './QuickAddApptDrawer.router';

export interface QuickAddLeftNavProps {
  className?: string;
}

export function QuickAddLeftNav(props: QuickAddLeftNavProps) {
  const { className } = props;
  const [enableBD, { serviceItemType }, pets] = useSelector(
    selectBDFeatureEnable,
    selectMainServiceInAppt(),
    selectPetsInAppt(),
  );
  const [{ clientId }] = useQuickAddConfig();
  const drawerRouter = useCreateApptRouterContext();

  const currentActiveNavTab = useMemo(() => {
    switch (drawerRouter.current.name) {
      case CreateApptRouteName.FeedingMedication:
        return CreateApptNavigationTabs.FeedingMedication;
      case CreateApptRouteName.PetBelongings:
        return CreateApptNavigationTabs.PET_BELONGINGS;
      case CreateApptRouteName.Comment:
        return CreateApptNavigationTabs.TICKET_COMMENT;
      default:
        return CreateApptNavigationTabs.INFO;
    }
  }, [drawerRouter]);

  const handleChangeNavTab = (v: number) => {
    switch (v) {
      case CreateApptNavigationTabs.INFO:
        drawerRouter.go(CreateApptRouteName.Home);
        break;
      case CreateApptNavigationTabs.TICKET_COMMENT:
        drawerRouter.go(CreateApptRouteName.Comment);
        break;
      case CreateApptNavigationTabs.FeedingMedication:
        drawerRouter.go(CreateApptRouteName.FeedingMedication);
        break;
      case CreateApptNavigationTabs.PET_BELONGINGS:
        drawerRouter.go(CreateApptRouteName.PetBelongings);
        break;
    }
  };

  const disabledTabs = useMemo(() => {
    const disabledList = [];
    if (!isNormal(clientId)) {
      disabledList.push(CreateApptNavigationTabs.TICKET_COMMENT);
    }
    if (!pets.length) {
      disabledList.push(...BDOnlyTabs);
    }
    return disabledList;
  }, [clientId, pets.length]);

  const options = useMemo(() => {
    return CreateApptNavigationTabs.values
      .filter((v) => {
        if (BDOnlyTabs.includes(v)) {
          return enableBD && matchApptFlowScene(ApptFlowScene.FeedingMedication, serviceItemType);
        }
        return true;
      })
      .map((tab) => {
        const isActive = tab === currentActiveNavTab;
        const isDisabled = disabledTabs.includes(tab);
        return {
          ...CreateApptNavigationTabs.mapLabels[tab],
          value: tab,
          isActive,
          isDisabled,
        };
      });
  }, [disabledTabs, currentActiveNavTab, enableBD, serviceItemType]);

  return (
    <DrawerLeftNav className={className} options={options} value={currentActiveNavTab} onChange={handleChangeNavTab} />
  );
}

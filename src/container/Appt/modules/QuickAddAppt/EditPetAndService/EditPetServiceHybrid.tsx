import { cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo } from 'react';
import { useSetState } from 'react-use';
import { DrawerHeader } from '../../../../../components/Drawer/DrawerHeader';
import { type ServiceEntry } from '../../../../../components/ServiceApplicablePicker/types/serviceEntry';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { optionalFunction } from '../../../../../utils/utils';
import { useAddSelectedCalendarStaff } from '../../../components/EditPetServiceList/hooks/useAddSelectedCalendarStaff';
import { EditPetServiceListHybrid } from '../../../components/EditPetServiceListHybrid/components/EditPetServiceListHybrid';
import { useRepeatSeriesState } from '../../../components/RepeatSeries/hooks/useRepeatSeriesState';
import { useApplySplitLodgingsToOtherPets } from '../../../hooks/useApplySplitLodgingsToOtherPets';
import { addApptPetEvaluations, addApptPetService, removeApptPet } from '../../../store/appt.actions';
import { selectPetsInAppt } from '../../../store/appt.selectors';
import { type ApptPetEvaluationInfo, CreateApptId } from '../../../store/appt.types';
import { useQuickAddConfig } from '../hooks/useQuickAddConfig';
import { CreateApptRouteName, useCreateApptRouterContext } from '../QuickAddApptDrawer.router';

interface EditPetServiceHybridState {
  petId: number;
  services: ServiceEntry[];
  evaluations: ApptPetEvaluationInfo[];
}

export const EditPetServiceHybrid = memo(() => {
  const drawerRouter = useCreateApptRouterContext();
  const payload = drawerRouter.getParams(CreateApptRouteName.EditPetHybridService);
  const dispatch = useDispatch();
  const { disabledPetIds, petId: originPetId } = payload;
  const [{ clientId, starStaffId }, setQuickAddConfig] = useQuickAddConfig();
  const [pets] = useSelector(selectPetsInAppt);
  const { isOpen: isRepeatSeriesOpen } = useRepeatSeriesState();
  const [state, setState] = useSetState<EditPetServiceHybridState>({
    petId: originPetId!,
    services: payload.serviceList!,
    evaluations: [],
  });
  const addSelectedStaffs = useAddSelectedCalendarStaff();
  const applySplitLodgingsToOtherPets = useApplySplitLodgingsToOtherPets();
  const { petId, services, evaluations } = state;

  const onCancel = useLatestCallback(() => {
    drawerRouter.back();
  });

  const onDeletePet = useLatestCallback(() => {
    dispatch(removeApptPet(originPetId!));
    drawerRouter.back();
  });

  const onSave = useLatestCallback(() => {
    dispatch(addApptPetService(petId, services, undefined, true));
    evaluations && dispatch(addApptPetEvaluations(petId.toString(), evaluations, CreateApptId));

    const applySplitToOtherPetsService = services.find((service) => service.isApplySplitLodgingsToAllPets);
    if (applySplitToOtherPetsService) {
      applySplitLodgingsToOtherPets({
        apptId: CreateApptId,
        petId: String(petId),
        applyToOtherPetsService: applySplitToOtherPetsService,
      });
    }

    drawerRouter.back();
    addSelectedStaffs(services);
  });

  const onBack = useLatestCallback(() => {
    drawerRouter.back();
  });

  const onChangePet = useLatestCallback((petId: number) => {
    if (state.petId === petId) return;
    drawerRouter.go(CreateApptRouteName.SelectPetService, { petIds: [petId] });
  });

  return (
    <div className={cn('moe-flex moe-flex-col moe-flex-1 moe-min-w-0 moe-min-h-0')}>
      <DrawerHeader title="Edit service" onClick={onBack} />
      <EditPetServiceListHybrid
        // 这里是new appt的时候，所以appointmentId是ID_ANONYMOUS
        appointmentId={Number(CreateApptId)}
        value={state}
        onChange={setState}
        disabledPetIds={disabledPetIds}
        clientId={clientId}
        onSave={onSave}
        onDeletePet={optionalFunction(onDeletePet, pets.length > 1)}
        starStaffId={starStaffId}
        onCancel={onCancel}
        onChangeStarStaff={(starStaffId) => {
          setQuickAddConfig({ starStaffId });
        }}
        onChangePet={onChangePet}
        disabledSavedPriceAndDuration={isRepeatSeriesOpen}
      />
    </div>
  );
});

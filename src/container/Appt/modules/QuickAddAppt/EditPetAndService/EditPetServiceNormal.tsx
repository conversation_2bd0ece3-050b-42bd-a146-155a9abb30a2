import { cn } from '@moego/ui';
import { useDispatch, useSelector, useStore } from 'amos';
import { debounce } from 'lodash';
import React, { memo, useEffect, useMemo } from 'react';
import { useSetState } from 'react-use';
import { DrawerHeader } from '../../../../../components/Drawer/DrawerHeader';
import { type ServiceEntry } from '../../../../../components/ServiceApplicablePicker/types/serviceEntry';
import { useCancelableCallback } from '../../../../../utils/hooks/useCancelableCallback';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { optionalFunction } from '../../../../../utils/utils';
import { EditPetServiceList } from '../../../components/EditPetServiceList/EditPetServiceList';
import { useAddSelectedCalendarStaff } from '../../../components/EditPetServiceList/hooks/useAddSelectedCalendarStaff';
import { useRepeatSeriesState } from '../../../components/RepeatSeries/hooks/useRepeatSeriesState';
import { isValidServiceList, useCalcGroomingOnlySchedule } from '../../../hooks/useCalcGroomingOnlySchedule';
import { useGetServiceListByPet } from '../../../hooks/useGetServiceListByPet';
import { removeApptPet } from '../../../store/appt.actions';
import { selectPetsInAppt } from '../../../store/appt.selectors';
import { CreateApptId } from '../../../store/appt.types';
import { useQuickAddConfig } from '../hooks/useQuickAddConfig';
import { CreateApptRouteName, useCreateApptRouterContext } from '../QuickAddApptDrawer.router';

export const EditPetServiceNormal = memo(() => {
  const drawerRouter = useCreateApptRouterContext();
  const payload = drawerRouter.getParams(CreateApptRouteName.EditPetService);
  const dispatch = useDispatch();
  const store = useStore();
  const { disabledPetIds, petId: originPetId } = payload;
  const [{ clientId, starStaffId, allPetsStartAtSameTime }, setQuickAddConfig] = useQuickAddConfig();
  const [pets] = useSelector(selectPetsInAppt());
  const { isOpen: isRepeatSeriesOpen } = useRepeatSeriesState();
  const [state, setState] = useSetState<{ petId: number; services: ServiceEntry[] }>({
    petId: originPetId!,
    services: payload.serviceList!,
  });
  const addSelectedStaffs = useAddSelectedCalendarStaff();
  const calcGroomingOnlySchedule = useCalcGroomingOnlySchedule();
  const getServiceList = useGetServiceListByPet();

  const handleSyncPetServices = useCancelableCallback(async (signal, petId: number, serviceList: ServiceEntry[]) => {
    // 同步新的service内容到box 并且重新计算schedule
    await calcGroomingOnlySchedule({
      petIdsServiceList: [{ petId, serviceList }],
      allPetsStartAtSameTime,
      customerId: String(clientId),
      signal,
    });
    addSelectedStaffs(serviceList);
  });

  const handleServiceChange = useCancelableCallback(async (_signal, petId: number, serviceList: ServiceEntry[]) => {
    await handleSyncPetServices(petId, serviceList);
    const newPet = originPetId !== petId;
    if (newPet && originPetId) {
      // 先加再删，因为我们会全局监听pet的变化，当pet被删到length 0之后
      // 会把placeholder service重新添加到 appt box中
      // 所以这里需要先加再删
      await handleDeletePet(originPetId);
    }
  });

  const handleServiceChangeDebounced = useMemo(() => debounce(handleServiceChange, 300), []);

  const handleDeletePet = useSerialCallback(async (petId: number) => {
    await dispatch(removeApptPet(petId));
    const pets = store.select(selectPetsInAppt());
    if (pets.length) {
      await calcGroomingOnlySchedule({
        allPetsStartAtSameTime,
        customerId: String(clientId),
      });
    }
  });

  const onCancel = useSerialCallback(async () => {
    // reset 回去之前的内容
    await handleSyncPetServices(originPetId!, payload.serviceList!);
    drawerRouter.back();
  });

  const onDeletePet = useSerialCallback(async () => {
    await handleDeletePet(originPetId!);
    drawerRouter.back();
  });

  const onSave = useSerialCallback(async () => {
    drawerRouter.back();
  });

  const petInfo = useMemo(() => pets.find((p) => p.petId === String(state.petId)), [pets, state.petId]);

  useEffect(() => {
    // 同步外部的calendar卡片拖动，主要是staff修改
    if (!petInfo) return;
    const newServiceList = getServiceList(petInfo, CreateApptId);
    setState((preState) => ({
      ...preState,
      services: preState.services.map((s) => {
        const newService = newServiceList.find((ns) => ns.serviceId === s.serviceId);
        return newService
          ? {
              ...s,
              staffId: newService.staffId,
              startDate: newService.startDate,
              startTime: newService.startTime,
              endDate: newService.endDate,
              endTime: newService.endTime,
              operationList: newService.operationList,
            }
          : s;
      }),
    }));
  }, [getServiceList, petInfo]);

  return (
    <div className={cn('moe-flex moe-flex-col moe-flex-1 moe-min-w-0 moe-min-h-0')}>
      <DrawerHeader title="Edit pet and service" onClick={onCancel} />
      <EditPetServiceList
        value={state}
        onChange={(newVal) => {
          setState(newVal);
          if (isValidServiceList(newVal.services)) {
            handleServiceChangeDebounced(newVal.petId, newVal.services);
          }
        }}
        disabledPetIds={disabledPetIds}
        clientId={clientId}
        btnLoading={{
          primary: handleServiceChange.isBusy(),
          secondary: onCancel.isBusy(),
          extra: onDeletePet.isBusy(),
        }}
        onSave={onSave}
        onDeletePet={optionalFunction(onDeletePet, pets.length > 1)}
        starStaffId={starStaffId}
        onCancel={onCancel}
        onChangeStarStaff={(starStaffId) => {
          setQuickAddConfig({ starStaffId });
        }}
        disabledSavedPriceAndDuration={isRepeatSeriesOpen}
      />
    </div>
  );
});

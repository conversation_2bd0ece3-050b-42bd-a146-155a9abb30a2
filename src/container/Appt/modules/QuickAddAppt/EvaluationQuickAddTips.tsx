import { useDispatch, useStore } from 'amos';
import React, { memo } from 'react';
import { EvaluationTip } from '../../components/EvaluationCombined/EvaluationTip';
import { addApptPetEvaluations } from '../../store/appt.actions';
import { selectPetInAppt } from '../../store/appt.selectors';
import { CreateApptRouteName, useCreateApptRouterContext } from './QuickAddApptDrawer.router';

export const EvaluationQuickAddTips = memo(function EvaluationQuickAddTips(props: {
  petId: string;
  appointmentId: string;
}) {
  const { petId, appointmentId } = props;
  const drawerRouter = useCreateApptRouterContext();
  const dispatch = useDispatch();
  const store = useStore();

  const handleEdit = () => {
    drawerRouter.go(CreateApptRouteName.EditPetEvaluation, {
      payload: {
        petId,
        appointmentId,
        onDelete: () => {
          dispatch(addApptPetEvaluations(petId, [], appointmentId));
          drawerRouter.go(CreateApptRouteName.Home);
        },
        onConfirm: (value) => {
          const petInfo = store.select(selectPetInAppt(petId, appointmentId));
          const currentPetEvaluation = petInfo?.evaluations?.[0];
          if (!currentPetEvaluation) {
            return;
          }

          const { price, duration, staffId, lodgingId, startTime } = value;
          const newData = {
            price: +(price || 0),
            duration: +(duration || 0),
            petId,
            evaluationId: currentPetEvaluation.evaluationId,
            staffId,
            lodgingId,
            startTime: startTime?.getMinutes() || 0,
            id: currentPetEvaluation.id,
          };

          dispatch(addApptPetEvaluations(petId, [newData], appointmentId));
          drawerRouter.back();
        },
        onCancel: () => {
          drawerRouter.back();
        },
      },
    });
  };

  return <EvaluationTip petId={petId} appointmentId={appointmentId} onEdit={handleEdit} />;
});

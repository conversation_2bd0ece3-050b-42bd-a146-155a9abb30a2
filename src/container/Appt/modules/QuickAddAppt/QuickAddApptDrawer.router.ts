import { type Dayjs } from 'dayjs';
import { createContext, useContext } from 'react';
import {
  type CombinedPetEvaluationPayload,
  type QuickAddPetServicePayload,
  type QuickAddSelectServiceDetailPayload,
} from '../../../../store/calendarLatest/calendar.types';
import { type CommentNoteType } from '../../../../store/calendarLatest/calendar.utils';
import { type RouteItem, useDrawerRouter } from '../../../../utils/hooks/useDrawerRouter';
import { type ServiceAddOnDateInfo } from '../../store/appt.types';

export const enum CreateApptRouteName {
  Home = 'Home',
  SelectPetService = 'SelectPetService',
  EditSchedule = 'EditSchedule',
  EditPetService = 'EditPetService',
  EditPetHybridService = 'EditPetHybridService',
  SelectServiceDetail = 'SelectServiceDetail',
  EditPetEvaluation = 'EditPetEvaluation',
  Comment = 'Comment',
  PetBelongings = 'PetBelongings',
  FeedingMedication = 'FeedingMedication',
}

type DrawerRoute = {
  [CreateApptRouteName.Home]: undefined;
  [CreateApptRouteName.EditSchedule]: {
    initDateTime: Dayjs;
    /** daycare 使用的 service开始时间，快照时间 */
    initDates?: ServiceAddOnDateInfo[];
  };
  [CreateApptRouteName.EditPetService]: QuickAddPetServicePayload;
  [CreateApptRouteName.EditPetHybridService]: QuickAddPetServicePayload;
  [CreateApptRouteName.SelectServiceDetail]: QuickAddSelectServiceDetailPayload;
  [CreateApptRouteName.EditPetEvaluation]: {
    isSubmitLoading?: boolean;
    payload: CombinedPetEvaluationPayload;
    CustomBlock?: React.ReactNode;
    isFinishedOrCancelled?: boolean;
  };
  [CreateApptRouteName.Comment]?: { tab: CommentNoteType; petId?: number };
  [CreateApptRouteName.PetBelongings]: undefined;
  [CreateApptRouteName.FeedingMedication]: undefined;
  [CreateApptRouteName.SelectPetService]: QuickAddPetServicePayload & { petIds?: number[] };
};

const fallbackRoute: RouteItem<CreateApptRouteName, DrawerRoute> = {
  name: CreateApptRouteName.Home,
};

export const useCreateApptRouter = () => {
  return useDrawerRouter<CreateApptRouteName, DrawerRoute>(fallbackRoute);
};

export type DrawerRouterType = ReturnType<typeof useCreateApptRouter>;
export const DrawerScopeContext = createContext<DrawerRouterType | null>(null);
export const DrawerRouterProvider = DrawerScopeContext.Provider;

export function useCreateApptRouterContext() {
  const ctx = useContext(DrawerScopeContext);
  if (!ctx && __DEV__) {
    throw new Error('useCreateDrawerRouterContext must be used within a DrawerRouterProvider');
  }

  return ctx!;
}

const drawerRouterRef: { ref: DrawerRouterType | null } = { ref: null };

export const createApptRouter = {
  set(v: DrawerRouterType | null) {
    drawerRouterRef.ref = v;
  },
  get current() {
    return drawerRouterRef.ref;
  },
};

import { cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { forwardRef, memo, useImperativeHandle, useMemo, useRef } from 'react';
import { calendarSelectedDate } from '../../../../../../store/calendarLatest/calendar.boxes';
import { useCheckDateInSelectedRangeDates } from '../../../../../Calendar/latest/hooks/useSelectedRangeDates';
import { RepeatSeriesSection } from '../../../../components/RepeatSeries/RepeatSeriesSection';
import { type RepeatSeriesSectionRef } from '../../../../components/RepeatSeries/RepeatSeriesSection.options';
import { SectionInfo } from '../../../../components/SectionInfo';
import { TimeSlotWrapper } from '../../../../components/SelectServiceDetail/components/Service/TimeSlot/TimeSlotWrapper';
import { setGroomingOnlyNewSchedule } from '../../../../store/appt.actions';
import { selectApptStartAndEndTime } from '../../../../store/appt.selectors';
import { CreateApptId } from '../../../../store/appt.types';
import { SelectQuickApptDateTime } from '../../components/SelectDateTime';
import { useQuickAddConfig } from '../../hooks/useQuickAddConfig';
import { useCreateApptRouterContext } from '../../QuickAddApptDrawer.router';
import { type ApptTimeRescheduleWithRepeatProps, type RescheduleActionRef } from '../ApptTimeRescheduleWithRepeat.type';

export interface RescheduleNormalProps extends ApptTimeRescheduleWithRepeatProps {
  supportRepeatWeekByDays?: boolean;
}

export const RescheduleNormal = memo(
  forwardRef<RescheduleActionRef, RescheduleNormalProps>((props, ref) => {
    const dispatch = useDispatch();
    const {
      appointmentId = CreateApptId,
      payload,
      supportRepeatWeekByDays = false,
      showAvailableTimeSlotPicker,
    } = props;
    const repeatSectionRef = useRef<RepeatSeriesSectionRef>(null);
    const [{ startDateTime }] = useSelector(selectApptStartAndEndTime(appointmentId));

    const [{ clientId: customerId }] = useQuickAddConfig();
    const checkDateInSelectedRangeDates = useCheckDateInSelectedRangeDates();
    const drawerRouter = useCreateApptRouterContext();

    const now = useMemo(() => dayjs().startOf('day'), []);

    useImperativeHandle(ref, () => ({
      async cancel() {
        const { initDateTime } = payload;
        const nextDate = initDateTime.clone();
        const isSameDayWithCalendarDate = checkDateInSelectedRangeDates(nextDate);

        /**
         * 回退数据到用户修改前，否则，用户点击 cancel 后，日历和 New appointment 里面时间是最修改后的数据。
         */
        await dispatch(
          setGroomingOnlyNewSchedule({
            appointmentDate: nextDate,
          }),
        );

        if (!isSameDayWithCalendarDate) {
          dispatch(calendarSelectedDate.setState(nextDate.startOf('date')));
        }
        drawerRouter.back();
      },
      async save() {
        await repeatSectionRef.current?.save();
        drawerRouter.back();
      },
    }));

    return (
      <TimeSlotWrapper appointmentId={appointmentId} showAvailableTimeSlotPicker={showAvailableTimeSlotPicker}>
        {({ petIndex, isMultiPet, isShowRepeat, serviceIndex, serviceEntry, petId }) => {
          return (
            <>
              <SelectQuickApptDateTime
                className="moe-flex-col moe-gap-s moe-items-stretch"
                appointmentId={appointmentId}
                showAvailableTimeSlotPicker={serviceIndex === 0 && petIndex === 0}
                isDateDisabled={(isMultiPet && petIndex > 0) || serviceIndex > 0}
                isTimeDisabled={(isMultiPet && petIndex > 0) || serviceIndex > 0}
                serviceEntry={serviceEntry}
                petId={petId}
              />
              {isShowRepeat && (
                <SectionInfo
                  label="Repeat"
                  className={cn('moe-mt-[24px]', { 'moe-mt-0': showAvailableTimeSlotPicker })}
                  childClassName="moe-flex moe-flex-col moe-gap-y-[8px]"
                >
                  {
                    <RepeatSeriesSection
                      supportRepeatWeekByDays={supportRepeatWeekByDays}
                      ref={repeatSectionRef}
                      startOnDate={startDateTime || now}
                      customerId={customerId}
                      isDisabled={isMultiPet && petIndex > 0}
                    />
                  }
                </SectionInfo>
              )}
            </>
          );
        }}
      </TimeSlotWrapper>
    );
  }),
);

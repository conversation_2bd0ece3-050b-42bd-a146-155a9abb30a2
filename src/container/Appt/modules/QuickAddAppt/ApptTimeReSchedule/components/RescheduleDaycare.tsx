import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { forwardRef, memo, useImperativeHandle, useMemo, useRef } from 'react';
import { Carousel } from '../../../../../../components/Carousel/Carousel';
import { Condition } from '../../../../../../components/Condition';
import { ApptAddOnTime } from '../../../../components/ApptTimeReSchedule/ApptAddOnTime';
import { ApptServiceOnTime } from '../../../../components/ApptTimeReSchedule/ApptServiceOnTime';
import { DaycareTime } from '../../../../components/ApptTimeReSchedule/DaycareTime';
import { DaycareTimeTxt } from '../../../../components/ApptTimeReSchedule/DaycareTimeTxt';
import { PetAvatar } from '../../../../components/PetAvatar/PetAvatar';
import { RepeatSeriesSection } from '../../../../components/RepeatSeries/RepeatSeriesSection';
import { type RepeatSeriesSectionRef } from '../../../../components/RepeatSeries/RepeatSeriesSection.options';
import { SectionInfo } from '../../../../components/SectionInfo';
import { TimeSlotItemWrapper } from '../../../../components/SelectServiceDetail/components/Service/TimeSlot/TimeSlotItemWrapper';
import { TimeSlotWrapperNormal } from '../../../../components/SelectServiceDetail/components/Service/TimeSlot/TimeSlotWrapperNormal';
import { useRescheduleDaycareData } from '../../../../hooks/useReschedule';
import { savePetsServiceAddonDates } from '../../../../store/appt.actions';
import { selectApptStartAndEndTime } from '../../../../store/appt.selectors';
import { CreateApptId } from '../../../../store/appt.types';
import { useQuickAddConfig } from '../../hooks/useQuickAddConfig';
import { useCreateApptRouterContext } from '../../QuickAddApptDrawer.router';
import { type ApptTimeRescheduleWithRepeatProps, type RescheduleActionRef } from '../ApptTimeRescheduleWithRepeat.type';

export interface RescheduleDaycareProps extends ApptTimeRescheduleWithRepeatProps {}

export const RescheduleDaycare = memo(
  forwardRef<RescheduleActionRef, RescheduleDaycareProps>((props, ref) => {
    const dispatch = useDispatch();
    const { appointmentId = CreateApptId, payload, showAvailableTimeSlotPicker } = props;
    const { pets, isActiveMainPet, activePetId, addOnOwnIds, isAllPetHasDaycare, allPetIds, setActivePetId } =
      useRescheduleDaycareData(appointmentId);
    const [{ startDateTime }] = useSelector(selectApptStartAndEndTime(appointmentId));

    const [{ clientId: customerId }] = useQuickAddConfig();
    const drawerRouter = useCreateApptRouterContext();

    const now = useMemo(() => dayjs().startOf('day'), []);

    const repeatSectionRef = useRef<RepeatSeriesSectionRef>(null);

    useImperativeHandle(ref, () => ({
      async cancel() {
        const { initDates } = payload;
        if (initDates) {
          dispatch(savePetsServiceAddonDates(initDates));
        }
        drawerRouter.back();
      },
      async save() {
        await repeatSectionRef.current?.save();
        drawerRouter.back();
      },
    }));

    return (
      <>
        {pets.length > 1 && (
          <Carousel className="moe-mb-[20px]">
            {pets.map((item) => {
              return (
                <PetAvatar
                  isActive={item.petId === activePetId}
                  className="moe-flex-1 moe-min-w-[240px]"
                  key={item.petId}
                  petId={item.petId}
                  onClick={() => {
                    setActivePetId(item.petId);
                  }}
                />
              );
            })}
          </Carousel>
        )}

        <TimeSlotWrapperNormal
          appointmentId={appointmentId}
          isEnableSlotCalender={!!showAvailableTimeSlotPicker}
          serviceItemType={ServiceItemType.DAYCARE}
        >
          {({ showServiceCategoryName }) => {
            return (
              <>
                {isActiveMainPet ? (
                  <DaycareTime
                    serviceNameClassName={showServiceCategoryName ? 'moe-text-primary' : ''}
                    className={showServiceCategoryName ? 'moe-gap-y-s' : ''}
                    petId={activePetId}
                    allPetIds={allPetIds}
                    appointmentId={appointmentId}
                  />
                ) : (
                  <DaycareTimeTxt petId={activePetId} appointmentId={appointmentId} withoutTime={!isAllPetHasDaycare} />
                )}
                <Condition if={isAllPetHasDaycare}>
                  <SectionInfo
                    label="Repeat"
                    className={cn('moe-mt-[24px]', !isActiveMainPet && 'moe-hidden', {
                      'moe-mt-s': showServiceCategoryName,
                    })}
                    childClassName="moe-flex moe-flex-col moe-gap-y-[8px]"
                  >
                    <RepeatSeriesSection
                      supportRepeatWeekByDays={true}
                      ref={repeatSectionRef}
                      startOnDate={startDateTime || now}
                      customerId={customerId}
                    />
                  </SectionInfo>
                </Condition>
              </>
            );
          }}
        </TimeSlotWrapperNormal>

        <Condition if={addOnOwnIds.length}>
          <div className="moe-mt-m moe-h-0 moe-border-t moe-border-dashed moe-border-[#E6E6E6]" />
        </Condition>

        <div className="moe-mt-m moe-flex moe-flex-col moe-gap-y-[20px]">
          <TimeSlotWrapperNormal
            appointmentId={appointmentId}
            isEnableSlotCalender={!!showAvailableTimeSlotPicker}
            className="moe-mb-0"
            serviceItemType={ServiceItemType.GROOMING}
          >
            {({ hasGroomingService }) => {
              return (
                <>
                  {addOnOwnIds.map(({ ownId, serviceType }, index) => {
                    const last = index === addOnOwnIds.length - 1;
                    const isAddon = serviceType === ServiceType.ADDON;

                    const renderServiceContent = ({ showServiceName }: { showServiceName: boolean }) => {
                      return isAddon ? (
                        <ApptAddOnTime
                          showAvailableTimeSlotPicker={showAvailableTimeSlotPicker}
                          appointmentId={appointmentId}
                          key={ownId}
                          addOnOwnId={ownId}
                          line={!last}
                          showServiceName={showServiceName}
                        />
                      ) : (
                        <ApptServiceOnTime
                          showAvailableTimeSlotPicker={showAvailableTimeSlotPicker}
                          appointmentId={appointmentId}
                          key={ownId}
                          serviceOwnId={ownId}
                          line={!last}
                          showServiceName={showServiceName}
                        />
                      );
                    };

                    if (!showAvailableTimeSlotPicker || !hasGroomingService) {
                      return renderServiceContent({ showServiceName: true });
                    }

                    return (
                      <TimeSlotItemWrapper
                        key={ownId}
                        serviceType={serviceType}
                        ownId={ownId}
                        appointmentId={appointmentId}
                      >
                        {renderServiceContent({ showServiceName: false })}
                      </TimeSlotItemWrapper>
                    );
                  })}
                </>
              );
            }}
          </TimeSlotWrapperNormal>
        </div>
      </>
    );
  }),
);

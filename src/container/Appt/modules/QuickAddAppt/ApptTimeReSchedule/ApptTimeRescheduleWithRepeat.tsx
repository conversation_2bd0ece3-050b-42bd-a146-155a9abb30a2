import { Button, cn } from '@moego/ui';
import { useSelector, useStore } from 'amos';
import React, { memo, useRef } from 'react';
import { DrawerHeader } from '../../../../../components/Drawer/DrawerHeader';
import { Switch } from '../../../../../components/SwitchCase';
import { selectIsEnableSlotCalender } from '../../../../../store/calendarLatest/calendar.selectors';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { ReportActionName } from '../../../../../utils/reportType';
import { reportData } from '../../../../../utils/tracker';
import { DrawerFooter } from '../../../../Calendar/latest/AwesomeCalendar.style';
import { useRepeatSeriesState } from '../../../components/RepeatSeries/hooks/useRepeatSeriesState';
import { useTimeSlotEditPetService } from '../../../components/SelectServiceDetail/components/Service/TimeSlot/hooks/useTimeSlot.hooks';
import { useCheckConflictAlert } from '../../../hooks/useCheckConflictAlert';
import { apptServiceMapBox } from '../../../store/appt.boxes';
import { selectApptComputedInfo, selectMainServiceInAppt, selectPetsInAppt } from '../../../store/appt.selectors';
import { CreateApptId } from '../../../store/appt.types';
import { useDisabledReschedule } from '../hooks/useDisabledReschedule';
import { type ApptTimeRescheduleWithRepeatProps, type RescheduleActionRef } from './ApptTimeRescheduleWithRepeat.type';
import { RescheduleDaycare } from './components/RescheduleDaycare';
import { RescheduleNormal } from './components/RescheduleNormal';

export const ApptTimeRescheduleWithRepeat = memo<ApptTimeRescheduleWithRepeatProps>((props) => {
  const { className, payload, appointmentId = CreateApptId } = props;
  const store = useStore();
  const [{ isDaycare, isDogWalking }, isEnableSlotCalender] = useSelector(
    selectApptComputedInfo(appointmentId),
    selectIsEnableSlotCalender,
  );

  const { isRepeatPreviewMode } = useRepeatSeriesState();
  const handleReportRuleSave = useLatestCallback(() => {
    if (isRepeatPreviewMode) {
      reportData(ReportActionName.RepeatQuickAddEditRepeatRule);
    }
  });

  const checkConflictAlert = useCheckConflictAlert();
  const { handleClearTimeSlotPetService } = useTimeSlotEditPetService(appointmentId);

  const disabled = useDisabledReschedule();

  const ref = useRef<RescheduleActionRef>(null);

  const handleCancel = useSerialCallback(async () => {
    if (isEnableSlotCalender) {
      handleClearTimeSlotPetService();
    }

    await ref.current?.cancel();
  });

  const handleSave = useSerialCallback(async () => {
    const pets = store.select(selectPetsInAppt(appointmentId));
    const mainService = store.select(selectMainServiceInAppt(appointmentId));
    const { startDate: startDateStr, endDate: endDateStr } = store.select(
      apptServiceMapBox.mustGetItem(mainService.ownId),
    );

    await checkConflictAlert({
      appointmentId,
      startDateStr,
      endDateStr,
      petIds: pets.map((i) => i.petId),
    });
    await ref.current?.save();
    handleReportRuleSave();
  });

  return (
    <div className={cn(className, 'moe-flex moe-flex-col moe-flex-1 moe-min-w-0')}>
      <DrawerHeader title="Edit schedule" onClick={handleCancel} />
      <div className="moe-flex-1 moe-min-w-0 moe-py-[20px] moe-px-[32px] moe-overflow-auto">
        <Switch shortCircuit>
          <Switch.Case if={isDaycare}>
            <RescheduleDaycare
              showAvailableTimeSlotPicker={isEnableSlotCalender}
              ref={ref}
              payload={payload}
              appointmentId={appointmentId}
            />
          </Switch.Case>
          <Switch.Case if={isDogWalking}>
            <RescheduleNormal
              showAvailableTimeSlotPicker={isEnableSlotCalender}
              ref={ref}
              payload={payload}
              appointmentId={appointmentId}
              supportRepeatWeekByDays
            />
          </Switch.Case>
          <Switch.Case else>
            <RescheduleNormal
              showAvailableTimeSlotPicker={isEnableSlotCalender}
              ref={ref}
              payload={payload}
              appointmentId={appointmentId}
            />
          </Switch.Case>
        </Switch>
      </div>
      <DrawerFooter className="border">
        <Button className="moe-flex-1" variant="secondary" isLoading={handleCancel.isBusy()} onPress={handleCancel}>
          Cancel
        </Button>
        <Button
          className="moe-flex-1"
          isDisabled={disabled}
          variant="primary"
          isLoading={handleSave.isBusy()}
          onPress={handleSave}
        >
          Save
        </Button>
      </DrawerFooter>
    </div>
  );
});

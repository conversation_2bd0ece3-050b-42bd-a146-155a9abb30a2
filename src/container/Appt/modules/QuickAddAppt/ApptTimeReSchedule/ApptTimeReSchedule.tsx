import { useSelector } from 'amos';
import React, { memo } from 'react';
import { Switch } from '../../../../../components/SwitchCase';
import { BoardingStartTime } from '../../../components/ApptTimeReSchedule/BoardingStartTime';
import { selectApptComputedInfo } from '../../../store/appt.selectors';
import { CreateApptRouteName, useCreateApptRouterContext } from '../QuickAddApptDrawer.router';
import { ApptTimeRescheduleWithRepeat } from './ApptTimeRescheduleWithRepeat';

export const ApptTimeReschedule = memo(() => {
  const drawerRouter = useCreateApptRouterContext();
  const payload = drawerRouter.getParams(CreateApptRouteName.EditSchedule);
  const [{ isBoarding }] = useSelector(selectApptComputedInfo());

  return (
    <Switch shortCircuit>
      <Switch.Case if={isBoarding}>
        <BoardingStartTime payload={payload} />
      </Switch.Case>
      <Switch.Case else>
        <ApptTimeRescheduleWithRepeat payload={payload} />
      </Switch.Case>
    </Switch>
  );
});

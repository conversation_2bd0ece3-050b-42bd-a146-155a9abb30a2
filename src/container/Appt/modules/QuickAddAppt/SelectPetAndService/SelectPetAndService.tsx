import { MajorPlusOutlined } from '@moego/icons-react';
import { But<PERSON> } from '@moego/ui';
import { useDispatch, useSelector, useStore } from 'amos';
import React, { memo } from 'react';
import { Condition } from '../../../../../components/Condition';
import { PetAndServicePicker } from '../../../../../components/PetAndServicePicker/PetAndServicePicker';
import { ApptTestIds } from '../../../../../config/testIds/apptDrawer';
import { type QuickAddPetServicePayload } from '../../../../../store/calendarLatest/calendar.types';
import { CommentNoteType } from '../../../../../store/calendarLatest/calendar.utils';
import { isNormal } from '../../../../../store/utils/identifier';
import { PricingRuleRender } from '../../../components/PricingRuleRender/PricingRuleRender';
import { useCheckAddNewServiceType } from '../../../hooks/useCheckAddNewServiceType';
import { useGetServiceListByPet } from '../../../hooks/useGetServiceListByPet';
import { useStartSameTimeInAddPet } from '../../../hooks/useStartSameTimeInAddPet';
import { removeApptPet, setGroomingOnlyNewSchedule } from '../../../store/appt.actions';
import { matchApptFlowScenes } from '../../../store/appt.options';
import { selectApptStartAndEndTime, selectMainServiceInAppt, selectPetsInAppt } from '../../../store/appt.selectors';
import { ApptFlowScene, CreateApptId } from '../../../store/appt.types';
import { ClientLastApptCard } from '../components/ClientLastApptCard';
import { EvaluationQuickAddTips } from '../EvaluationQuickAddTips';
import { useQuickAddConfig } from '../hooks/useQuickAddConfig';
import { CreateApptRouteName, useCreateApptRouterContext } from '../QuickAddApptDrawer.router';

export const SelectPetAndService = memo(() => {
  const store = useStore();
  const dispatch = useDispatch();
  const [pets, { serviceItemType, endDate }] = useSelector(selectPetsInAppt(), selectMainServiceInAppt);

  const [{ clientId, allPetsStartAtSameTime, lastAppointment }, setQuickAddConfig] = useQuickAddConfig();
  const getServiceListByPet = useGetServiceListByPet();
  const checkAddNewServiceType = useCheckAddNewServiceType();

  const hasClientLastAppointment = isNormal(clientId) && lastAppointment?.info.hasLastAppointment;
  const [startAtSameTime, editPetService] = matchApptFlowScenes(
    [ApptFlowScene.StartAtSameTime, ApptFlowScene.EditPetService],
    serviceItemType,
  );
  const isStartSameTimeVisible = pets.length > 1 && startAtSameTime;
  const defaultStartAtSameTime = useStartSameTimeInAddPet();
  const drawerRouter = useCreateApptRouterContext();

  const getServiceList = (petId: string) => {
    const pet = pets.find((pet) => pet.petId === petId);
    if (!pet) {
      return [];
    }
    return getServiceListByPet(pet, CreateApptId);
  };

  const handleOnAdd = (payload: QuickAddPetServicePayload) => {
    drawerRouter.go(CreateApptRouteName.SelectPetService, payload);

    // dog walking 的特殊逻辑
    const extra = defaultStartAtSameTime ? { allPetsStartAtSameTime: true } : undefined;
    setQuickAddConfig({ ...extra });
  };

  return (
    <PetAndServicePicker
      renderExtraTips={(petId: string) => <EvaluationQuickAddTips petId={petId} appointmentId={CreateApptId} />}
      clientId={clientId}
      getServiceListByPet={getServiceList}
      currentPetIds={pets.map((pet) => pet.petId)}
      appointmentEndDate={endDate}
      overlay={
        <Condition if={hasClientLastAppointment}>
          <ClientLastApptCard />
        </Condition>
      }
      priceContent={(petId: string, serviceId, defaultContent) => (
        <PricingRuleRender
          appointmentId={CreateApptId}
          petId={petId}
          serviceId={serviceId}
          defaultContent={defaultContent}
        />
      )}
      disabledOverlay={!hasClientLastAppointment}
      isStartSameTime={allPetsStartAtSameTime}
      isStartSameTimeVisible={isStartSameTimeVisible}
      onAdd={handleOnAdd}
      onDelete={async (petId) => {
        const { startDateTime } = store.select(selectApptStartAndEndTime());
        await dispatch(removeApptPet(petId));
        if (startDateTime) {
          dispatch(
            setGroomingOnlyNewSchedule({
              appointmentDate: startDateTime,
            }),
          );
        }
      }}
      onEdit={(petId) => {
        const serviceList = getServiceList(petId);
        const { startDateTime } = store.select(selectApptStartAndEndTime());
        const disabledPetIds = pets.map((pet) => Number(pet.petId));

        if (editPetService) {
          drawerRouter.go(CreateApptRouteName.EditPetService, {
            petId: Number(petId),
            serviceList,
            disabledPetIds,
            petStartTime: startDateTime || undefined,
          });
          return;
        }
        drawerRouter.go(CreateApptRouteName.EditPetHybridService, {
          petId: Number(petId),
          serviceList,
          disabledPetIds,
        });
      }}
      onGo2PetNotes={(petId) => {
        drawerRouter.go(CreateApptRouteName.Comment, { tab: CommentNoteType.PetNotes, petId: +petId });
      }}
      onStartSameTimeChange={async (e) => {
        const { startDateTime } = store.select(selectApptStartAndEndTime());
        setQuickAddConfig({ allPetsStartAtSameTime: e });
        if (startDateTime) {
          await dispatch(
            setGroomingOnlyNewSchedule({
              appointmentDate: startDateTime,
            }),
          );
        }
      }}
    >
      {(petId) => {
        const showAnotherCareType = checkAddNewServiceType(petId);

        return (
          <Condition if={showAnotherCareType}>
            <div>
              <Button
                size="s"
                icon={<MajorPlusOutlined />}
                variant="tertiary-legacy"
                align="start"
                onPress={() => {
                  handleOnAdd({
                    petIds: [Number(petId)],
                    isDisabled: true,
                    isMultiplePets: false,
                  });
                }}
                data-testid={ApptTestIds.ApptPetServiceAddCareType}
              >
                Add another care type
              </Button>
            </div>
          </Condition>
        );
      }}
    </PetAndServicePicker>
  );
});

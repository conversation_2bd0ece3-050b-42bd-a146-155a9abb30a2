import { Button, Text } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { useEffect, useState } from 'react';
import { useSetState } from 'react-use';
import { Condition } from '../../../../../components/Condition';
import {
  type TicketCommentsParamsV2,
  type TicketCommentsV2DTO,
  getTicketCommentsV2,
} from '../../../../../store/calendarLatest/actions/private/calendar.actions';
import { isNormal } from '../../../../../store/utils/identifier';
import { useBool } from '../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { CommentHistory } from '../../ApptDetailDrawer/ApptTicketCommentPanel/CommentHistory';
import { ClientGroomingNotes } from './ClientGroomingNotes';

export interface ClientGroomingNotesAndHistoryProps {
  ticketComments: string;
  onTicketCommentsChange?: (value: string) => void;
  clientId: number;
}

export function ClientGroomingNotesAndHistory(props: ClientGroomingNotesAndHistoryProps) {
  const { ticketComments, onTicketCommentsChange, clientId } = props;
  const dispatch = useDispatch();
  const loading = useBool();
  const isDirty = useBool();
  const [currentComment, setCurrentComment] = useState(ticketComments);
  const [state, setState] = useSetState<{
    historyComments: TicketCommentsV2DTO[];
  }>({
    historyComments: [],
  });
  const getComments = useLatestCallback(async (shouldLoading = true) => {
    if (!isNormal(clientId)) {
      return;
    }
    if (shouldLoading) {
      loading.open();
    }
    const params: TicketCommentsParamsV2 = {
      customerId: clientId,
    };
    try {
      const res = await dispatch(getTicketCommentsV2(params));
      const { historyComments } = res;
      setState({
        historyComments: historyComments?.sort((a, b) => +b.updateTime - +a.updateTime) || [],
      });
    } finally {
      loading.close();
    }
  });

  useEffect(() => {
    getComments();
  }, [clientId]);

  const handleChange = (value: string) => {
    setCurrentComment(value);
    isDirty.open();
  };

  return (
    <div className="moe-h-full moe-flex moe-flex-col moe-gap-y-[24px] moe-overflow-auto">
      <div className="moe-text-[14px]">
        <ClientGroomingNotes ticketComments={currentComment} onChange={handleChange} />
        <div className="moe-flex moe-items-start moe-justify-between moe-mt-[8px]">
          <Text variant="small" className="moe-text-tertiary moe-h-[30px]">
            Private to business only
          </Text>
          <Condition if={isDirty.value}>
            <div className="moe-flex moe-items-center moe-gap-x-[16px] ">
              <Button
                size="s"
                variant="secondary"
                onPress={() => {
                  onTicketCommentsChange?.('');
                  isDirty.close();
                }}
              >
                Cancel
              </Button>
              <Button
                size="s"
                onPress={async () => {
                  onTicketCommentsChange?.(currentComment);
                  isDirty.close();
                }}
              >
                Save
              </Button>
            </div>
          </Condition>
        </div>
      </div>
      <CommentHistory className="moe-flex moe-flex-col moe-gap-y-[24px]" historyComments={state.historyComments} />
    </div>
  );
}

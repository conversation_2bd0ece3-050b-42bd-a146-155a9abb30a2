import { DatePicker, TimePicker, cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs, { type Dayjs } from 'dayjs';
import React from 'react';
import { ApptTestIds } from '../../../../../config/testIds/apptDrawer';
import { calendarSelectedDate } from '../../../../../store/calendarLatest/calendar.boxes';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { useCheckDateInSelectedRangeDates } from '../../../../Calendar/latest/hooks/useSelectedRangeDates';
import { BusinessInfoRender } from '../../../components/BusinessInfoRender';
import { SectionInfo } from '../../../components/SectionInfo';
import { setGroomingOnlyNewSchedule } from '../../../store/appt.actions';
import { selectApptStartAndEndTime } from '../../../store/appt.selectors';
import { type BusinessRecord } from '../../../../../store/business/business.boxes';
import { DATE_FORMAT_EXCHANGE } from '@moego/reporting';
import { TimeSlot } from '../../../components/SelectServiceDetail/components/Service/TimeSlot/TimeSlot';
import { type ServiceEntry } from '../../../../../components/ServiceApplicablePicker/types/serviceEntry';
import { reportApptEditTimeBeforeToday } from '../../../../../utils/reportData/reporter/apptReporter';

export interface SelectQuickApptDateTimeProps {
  className?: string;
  showAvailableTimeSlotPicker?: boolean;
  appointmentId: string;
  isDateDisabled?: boolean;
  isTimeDisabled?: boolean;
  serviceEntry?: ServiceEntry;
  petId: string;
}

export function SelectQuickApptDateTime(props: SelectQuickApptDateTimeProps) {
  const { className, showAvailableTimeSlotPicker, appointmentId, isDateDisabled, isTimeDisabled, serviceEntry, petId } =
    props;
  const dispatch = useDispatch();
  const checkDateInSelectedRangeDates = useCheckDateInSelectedRangeDates();
  const [{ startDateTime }] = useSelector(selectApptStartAndEndTime());

  const onDateTimeChange = useLatestCallback(async (nextDate: Dayjs | null) => {
    if (nextDate) {
      const isSameDayWithCalendarDate = checkDateInSelectedRangeDates(nextDate);
      dispatch(
        setGroomingOnlyNewSchedule({
          appointmentDate: nextDate,
        }),
      );

      if (!isSameDayWithCalendarDate) {
        dispatch(calendarSelectedDate.setState(nextDate.startOf('date')));
      }
    }
  });

  const handleSelectTime = useLatestCallback((startTime: number) => {
    if (startDateTime) {
      const nextDate = startDateTime.setMinutes(startTime);
      onDateTimeChange(nextDate);
    }
  });

  const renderDate = (business: BusinessRecord) => {
    return (
      <SectionInfo label="Date" className="moe-flex-1" isRequired>
        <div data-testid={ApptTestIds.ApptEditScheduleDateBtn}>
          <DatePicker
            isClearable={false}
            format={business.dateFormat}
            value={startDateTime}
            isDisabled={isDateDisabled}
            onChange={(newVal) => {
              if (newVal) {
                // 这里 startDateTime 应该一定会存在才对，但是仍然用一个兜底写法，也使得类型安全
                const nextDate = startDateTime ? newVal.setMinutes(startDateTime.getMinutes()) : newVal;
                onDateTimeChange(nextDate);

                startDateTime &&
                  reportApptEditTimeBeforeToday({
                    originalDate: startDateTime,
                    newDate: newVal,
                  });
              }
            }}
          />
        </div>
      </SectionInfo>
    );
  };

  const renderTime = (business: BusinessRecord) => {
    return (
      <SectionInfo label="Time" className="moe-flex-1" isRequired>
        <div data-testid={ApptTestIds.ApptEditScheduleTimeBtn}>
          <TimePicker
            isDisabled={isTimeDisabled}
            isClearable={false}
            value={serviceEntry?.startTime ? dayjs().setMinutes(serviceEntry.startTime) : startDateTime}
            format={business.timeFormat()}
            minuteStep={5}
            onChange={(time) => {
              if (time) {
                const nextDate = (startDateTime || time).setMinutes(time.getMinutes());
                onDateTimeChange(nextDate);
              }
            }}
          />
        </div>
      </SectionInfo>
    );
  };

  return (
    <BusinessInfoRender>
      {({ business }) => (
        <div
          className={cn(
            'moe-flex',
            {
              'moe-flex-col moe-gap-s': showAvailableTimeSlotPicker,
              'moe-items-center moe-gap-x-[8px]': !showAvailableTimeSlotPicker,
            },
            className,
          )}
        >
          {showAvailableTimeSlotPicker && serviceEntry && startDateTime ? (
            <>
              {renderDate(business)}
              {showAvailableTimeSlotPicker && (
                <TimeSlot
                  className="moe-mt-[-16px]"
                  value={{
                    id: serviceEntry.id,
                    startTime: serviceEntry.startTime ?? 0,
                    appointmentId,
                    staffId: String(serviceEntry.staffId),
                    startDate: startDateTime.format(DATE_FORMAT_EXCHANGE),
                    petId,
                  }}
                  isEnableSlotCalender
                  business={business}
                  onSelect={handleSelectTime}
                />
              )}
              {renderTime(business)}
            </>
          ) : (
            <>
              {renderDate(business)}
              {renderTime(business)}
            </>
          )}
        </div>
      )}
    </BusinessInfoRender>
  );
}

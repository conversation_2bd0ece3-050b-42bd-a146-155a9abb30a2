import { <PERSON><PERSON>, Spin } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useEffect, useMemo } from 'react';
import { ADCommonTestIds } from '../../../../config/testIds/apptDrawer';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { globalEvent } from '../../../../utils/events/events';
import { apptReporter } from '../../../../utils/reportData/reporter/apptReporter';
import { getOBReportApptData } from '../../../../utils/reportData/reporter/onlineBookingReporter';
import { useRepeatSeriesState } from '../../components/RepeatSeries/hooks/useRepeatSeriesState';
import { matchApptFlowScene } from '../../store/appt.options';
import { selectMainServiceInAppt, selectPetsInAppt } from '../../store/appt.selectors';
import { ApptFlowScene } from '../../store/appt.types';
import { useAdvancedJump } from './hooks/useAdvancedJump';
import { useApptTotalPrice } from './hooks/useApptTotalPrice';
import { type SubmitQuickAddParams, useSubmitQuickAdd } from './hooks/useSubmitQuickAdd';

export interface QuickAddFooterProps extends SubmitQuickAddParams {
  onClose?: (checkDirty?: boolean) => void;
}

const { AdvancedEditBtn } = ADCommonTestIds;

export const QuickAddFooter = memo<QuickAddFooterProps>(function QuickAddFooter(props) {
  const { onClose, onCreated } = props;
  const [business, { serviceItemType }, pets] = useSelector(
    selectCurrentBusiness(),
    selectMainServiceInAppt(),
    selectPetsInAppt(),
  );
  const { validateForm, handleSubmit } = useSubmitQuickAdd({ onCreated });
  const { handleJumpToAdvancedEdit } = useAdvancedJump();
  const { isRepeatPreviewMode } = useRepeatSeriesState();
  const { loading, total } = useApptTotalPrice();

  useEffect(() => {
    const dispose = globalEvent.createdAppt.on((payload) => {
      const { apptId } = payload || {};
      if (!apptId) return;
      apptReporter.reportCreateApptDuration(getOBReportApptData(Number(apptId)));
    });
    return dispose;
  }, []);

  const buttonProps = useMemo(() => {
    return matchApptFlowScene(ApptFlowScene.AdvanceEdit, serviceItemType) && pets.length
      ? {
          onPress: () => {
            handleJumpToAdvancedEdit();
            onClose?.(false);
          },
          text: 'Advanced edit',
          testId: AdvancedEditBtn,
        }
      : { onPress: onClose, text: 'Cancel' };
  }, [serviceItemType, handleJumpToAdvancedEdit, onClose, pets]);

  return (
    <div className="moe-w-full moe-flex moe-flex-col moe-gap-y-[16px]">
      <div className="moe-flex moe-items-center moe-justify-between moe-gap-x-[8px]">
        <div className="moe-text-[#333] moe-font-medium moe-text-base">Estimated total</div>
        <div className="moe-text-[20px] meo-text-[#333] moe-font-bold moe-leading-[24px]">
          <Spin classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }} isLoading={loading}>
            {business.formatAmount(total)}
          </Spin>
        </div>
      </div>
      <div className="moe-flex moe-justify-between moe-gap-[16px]">
        <Button
          variant="secondary"
          className="moe-flex-1"
          onPress={() => {
            buttonProps.onPress?.();
          }}
          data-testid={buttonProps.testId}
        >
          {buttonProps.text}
        </Button>
        <Button
          variant="primary"
          className="moe-flex-1"
          onPress={handleSubmit}
          isLoading={handleSubmit.isBusy()}
          isDisabled={!validateForm}
        >
          {isRepeatPreviewMode ? 'Preview' : 'Book now'}
        </Button>
      </div>
    </div>
  );
});

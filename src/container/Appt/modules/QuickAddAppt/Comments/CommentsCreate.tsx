import React, { memo } from 'react';
import { Comments, type CommentsProps } from '../../../components/Comments';
import { useQuickAddConfig } from '../hooks/useQuickAddConfig';
import { CreateApptRouteName, useCreateApptRouterContext } from '../QuickAddApptDrawer.router';

export const CommentsCreate = memo<Pick<CommentsProps, 'clientId'>>((props) => {
  const { clientId } = props;
  const drawerRouter = useCreateApptRouterContext();
  const { tab, petId } = drawerRouter.getParams(CreateApptRouteName.Comment) || {};
  const [{ ticketComment }, setQuickAddFields] = useQuickAddConfig();

  return (
    <Comments
      clientId={clientId}
      activeTab={tab}
      petId={petId}
      comments={ticketComment}
      onCommentsChange={(value) => setQuickAddFields({ ticketComment: value })}
    />
  );
});

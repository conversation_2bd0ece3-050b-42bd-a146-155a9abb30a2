import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Button, cn } from '@moego/ui';
import { useDispatch, useSelector, useStore } from 'amos';
import classNames from 'classnames';
import dayjs from 'dayjs';
import React, { useEffect, useMemo } from 'react';
import { useSetState } from 'react-use';
import { Condition } from '../../../../../components/Condition';
import { DrawerHeader } from '../../../../../components/Drawer/DrawerHeader';
import { PetNameInfo } from '../../../../../components/PetInfo/PetNameInfo';
import { PetPicker } from '../../../../../components/PetPicker/PetPicker';
import { ServiceApplicablePicker } from '../../../../../components/ServiceApplicablePicker/ServiceApplicablePicker';
import { ApptTestIds } from '../../../../../config/testIds/apptDrawer';
import { ScrollerProvider } from '../../../../../layout/components/ScrollerProvider';
import { selectBDFeatureEnable } from '../../../../../store/company/company.selectors';
import { DATE_FORMAT_EXCHANGE } from '../../../../../utils/DateTimeUtil';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { DrawerFooter } from '../../../../Calendar/latest/AwesomeCalendar.style';
import { SelectServiceItemType } from '../../../components/SelectServiceItemType/SelectServiceItemType';
import { useBundleService } from '../../../hooks/useBundleService';
import { usePickServiceTabs } from '../../../hooks/usePickServiceTabs';
import { setMultiplePets, setPlaceholderService } from '../../../store/appt.actions';
import { selectPetsInAppt, selectPlaceholderServiceInAppt } from '../../../store/appt.selectors';
import { CreateApptId } from '../../../store/appt.types';
import { CreateApptRouteName, useCreateApptRouterContext } from '../QuickAddApptDrawer.router';
import { useItemTypeOptions, useSubmitService } from '../StepSelectPet.hook';
import { type StepSelectMultiplePetState } from '../StepSelectPet.type';
import { useQuickAddConfig } from '../hooks/useQuickAddConfig';
import { useMultiplePetsData, useMultiplePetsVerify } from './useStepSelectMultiplePets';

export function StepSelectMultiplePets() {
  const drawerRouter = useCreateApptRouterContext();
  const payload = drawerRouter.getParams(CreateApptRouteName.SelectPetService);
  const dispatch = useDispatch();
  const store = useStore();
  const [{ clientId }] = useQuickAddConfig();

  const [state, setState] = useSetState<StepSelectMultiplePetState>({
    petIds: payload?.petIds ?? [],
    serviceList: payload?.serviceList?.slice() ?? [],
    serviceItemType: undefined,
  });
  const { petIds, serviceList, serviceItemType } = state;

  const [pets, placeholderService, isBD] = useSelector(
    selectPetsInAppt(CreateApptId),
    selectPlaceholderServiceInAppt,
    selectBDFeatureEnable(),
  );
  const isMultipleSelect = payload?.isMultiplePets !== false && isBD;

  const prevSelectedServiceIds = useMemo(() => {
    if (!pets.length) return [];
    return pets.flatMap((pet) =>
      petIds.includes(Number(pet.petId))
        ? pet.services.filter((s) => !s.associatedId).map((s) => Number(s.serviceId))
        : [],
    );
  }, [pets, petIds.length]);

  const tabs = usePickServiceTabs({
    serviceItemType,
    serviceList,
  });

  const {
    disabledIds,
    preventAutoSelectPet,
    isPetDisabled: isPetSelectDisabled,
  } = useMultiplePetsData({ payload, petIds, onChange: setState });

  const { validateForm, validatePetIds } = useMultiplePetsVerify(state);

  const submitService = useSubmitService();

  const { resolveServiceList, isLoading } = useBundleService();

  const handleServiceItemTypeChange = (newVal?: ServiceItemType) => {
    setState({ serviceItemType: newVal });
    const pets = store.select(selectPetsInAppt());
    if (!pets.length && newVal) {
      const { startDate, endDate } = placeholderService;
      let newEndDate = endDate;
      if (newVal === ServiceItemType.BOARDING) {
        if (startDate === endDate) {
          newEndDate = dayjs(startDate).add(1, 'day').format(DATE_FORMAT_EXCHANGE);
        }
      } else {
        newEndDate = startDate;
      }
      dispatch(setPlaceholderService({ serviceItemType: newVal, endDate: newEndDate }));
    }
  };

  const handleSubmit = useSerialCallback(async () => {
    if (validateForm && serviceItemType) {
      await submitService({ serviceList, serviceItemType, petIds });
    }
  });

  const handlePetChange = useLatestCallback((newVal: number[] | number) => {
    const petIds = [newVal].flat();
    setState({ petIds, serviceList: [] });
  });

  const { serviceItemTypeOptions, primaryBtnText } = useItemTypeOptions({
    payload,
    petIds,
    serviceItemType,
    onChange: (v) => handleServiceItemTypeChange(v.serviceItemType),
  });

  useEffect(() => {
    dispatch(setMultiplePets(petIds));
  }, [petIds.length]);

  return (
    <div className={cn('moe-flex-1 moe-min-w-0 moe-h-full moe-flex moe-flex-col')}>
      <DrawerHeader
        title={isPetSelectDisabled ? 'Select service' : 'Select pet and service'}
        onClick={() => {
          drawerRouter.back();
        }}
      />
      <ScrollerProvider
        className={classNames('moe-flex-1 moe-min-w-0 moe-min-h-0 moe-py-m moe-px-l moe-flex moe-flex-col')}
      >
        <PetPicker
          autoFocus={isMultipleSelect}
          isMultiplePets={isMultipleSelect} // BD 用户才显示 Multiple select
          footerHidden={isMultipleSelect}
          clientId={clientId}
          defaultVisible={preventAutoSelectPet}
          value={isMultipleSelect ? petIds : petIds[0]}
          onChange={handlePetChange}
          isDisabled={isPetSelectDisabled}
          disabledPetIds={disabledIds}
          renderPetName={(pet) => (
            <PetNameInfo petId={pet.petId} showVaccine={true} endDate={placeholderService.endDate} />
          )}
        />

        <Condition if={validatePetIds}>
          <SelectServiceItemType
            options={serviceItemTypeOptions}
            value={serviceItemType}
            onChange={handleServiceItemTypeChange}
          />
          <Condition if={serviceItemType && petIds.length}>
            <ServiceApplicablePicker
              tabs={tabs}
              className="moe-mt-[24px]"
              serviceItemType={serviceItemType}
              value={serviceList}
              petIds={petIds.map(String)}
              selectedLodgingUnitId={placeholderService?.lodgingId}
              prevSelectedServiceIds={prevSelectedServiceIds}
              onChange={(services) => {
                if (!services) {
                  return;
                }
                setState({
                  serviceList: services,
                });
              }}
              onServiceChange={async (nextServices, extra) => {
                const bundles = await resolveServiceList({ ...extra, nextServices, petIds });
                bundles &&
                  setState({
                    serviceList: bundles,
                  });
              }}
            />
          </Condition>
        </Condition>
      </ScrollerProvider>
      <DrawerFooter className="border">
        <Button
          variant="secondary"
          isDisabled={!validatePetIds}
          className="moe-flex-1"
          onPress={() => {
            drawerRouter.back();
          }}
          data-testid={ApptTestIds.ApptCancelBtn}
        >
          Cancel
        </Button>
        <Button
          isDisabled={!validateForm}
          isLoading={handleSubmit.isBusy() || isLoading}
          className="moe-flex-1"
          onPress={handleSubmit}
          data-testid={ApptTestIds.ApptSaveBtn}
        >
          {primaryBtnText}
        </Button>
      </DrawerFooter>
    </div>
  );
}

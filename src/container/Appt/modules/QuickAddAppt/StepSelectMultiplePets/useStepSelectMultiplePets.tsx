import { useEffect, useMemo } from 'react';
import { useClientPets } from '../../../../../components/PetInfo/hooks/useClientPets';
import { isNormal } from '../../../../../store/utils/identifier';
import { type StepSelectMultiplePetState, type UseMultiplePetsDataProps } from '../StepSelectPet.type';
import { useQuickAddConfig } from '../hooks/useQuickAddConfig';

export const useMultiplePetsVerify = (state: StepSelectMultiplePetState) => {
  const { petIds, serviceList } = state;

  const validatePetIds = petIds.every(isNormal);
  const validateServiceIds = serviceList.length > 0;
  const validateForm = validatePetIds && validateServiceIds;

  return {
    validatePetIds,
    validateServiceIds,
    validateForm,
  };
};

export const useMultiplePetsData = (prams: UseMultiplePetsDataProps) => {
  const { payload, petIds, onChange } = prams;
  const { disabledPetIds, preventAutoSelectPet, isDisabled: isPetDisabled } = payload || {};
  const [{ clientId, source }] = useQuickAddConfig();
  const alivePets = useClientPets(clientId);

  const disabledIds = useMemo(() => (Array.isArray(disabledPetIds) ? disabledPetIds : []), [disabledPetIds]);

  useEffect(() => {
    if (preventAutoSelectPet) {
      return;
    }
    const emptyPet = !petIds.filter(isNormal).length;
    const availablePets = alivePets.filter((petId) => !disabledIds.includes(petId));
    // 仅当只有一直pet的时候，默认选中
    if (emptyPet && availablePets.length === 1) {
      onChange({ petIds: [availablePets[0]] });
    }
  }, [disabledIds, alivePets, petIds.length, preventAutoSelectPet]);

  useEffect(() => {
    onChange({
      serviceList: payload?.serviceList?.slice() ?? [],
    });
  }, [petIds.length, clientId, source]);

  return {
    disabledIds,
    preventAutoSelectPet,
    isPetDisabled,
  };
};

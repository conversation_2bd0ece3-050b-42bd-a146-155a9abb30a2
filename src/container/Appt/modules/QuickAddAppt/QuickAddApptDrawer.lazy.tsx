import { Spin } from '@moego/ui';
import React, { lazy, memo, Suspense, useEffect } from 'react';
import { globalEvent } from '../../../../utils/events/events';
import { CalendarDrawer } from '../../../Calendar/latest/ApptCalendar/components/CalendarDrawer';
import { type QuickAddApptDrawerProps } from './QuickAddApptDrawer';
import { useQuickAddConfig } from './hooks/useQuickAddConfig';
import { useDraftConfirmConfig, useQuickAddLeaveConfirm } from './hooks/useQuickAddLeaveConfirm';

const LazyComponent = lazy(() =>
  import('./QuickAddApptDrawer').then(({ QuickAddApptDrawer }) => ({
    default: QuickAddApptDrawer,
  })),
);

export interface DrawerWrapperProps extends Pick<QuickAddApptDrawerProps, 'onClose' | 'onCreated' | 'onReady'> {}

export const DrawerLazyWrapper = memo((props: DrawerWrapperProps) => {
  const { onClose, onReady, onCreated } = props;
  const { asyncDoubleConfirm } = useDraftConfirmConfig();
  const [{ isDirty }] = useQuickAddConfig();

  const handleClose = (checkDirty = true) => {
    if (checkDirty && isDirty) {
      asyncDoubleConfirm({
        closable: true,
        onOk: () => Promise.resolve(),
        onCancel: onClose,
      });
      return;
    }
    onClose?.();
  };

  useQuickAddLeaveConfirm(handleClose);

  useEffect(() => {
    // TODO(vision,p1) 临时fix 目前这里不支持关闭 created appt，我们先通过事件去实现
    // repeat drawer 应该通过 useModal 替换
    return globalEvent.closeCreateAppt.on((v) => {
      handleClose(v?.doubleConfirm);
    });
  }, []);

  return (
    <CalendarDrawer isOpen onClose={handleClose}>
      <Suspense
        fallback={
          <div className="moe-w-full moe-h-full moe-flex moe-justify-center moe-items-center">
            <Spin isLoading />
          </div>
        }
      >
        <LazyComponent onClose={handleClose} onCreated={onCreated} onReady={onReady} />
      </Suspense>
    </CalendarDrawer>
  );
});

import {
  type AddOnComposite,
  type EvaluationServiceComposite,
  type ServiceComposite,
  type ServiceDetail,
} from '@moego/api-web/moego/api/appointment/v1/appointment_api';
import { type GetPetFeedingMedicationSchedulesResult } from '@moego/api-web/moego/api/appointment/v1/appointment_schedule_api';
import {
  type FeedingDetail,
  type MedicationDetail,
} from '@moego/api-web/moego/api/online_booking/v1/booking_request_api';
import { type AppointmentPetFeedingScheduleDef } from '@moego/api-web/moego/models/appointment/v1/appointment_pet_feeding_schedule_defs';
import { type AppointmentPetMedicationScheduleDef } from '@moego/api-web/moego/models/appointment/v1/appointment_pet_medication_schedule_defs';
import { type ServiceOperationModel } from '@moego/api-web/moego/models/appointment/v1/service_operation_models';
import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type ServiceOperationEntry } from '../../../components/ServiceApplicablePicker/types/serviceOperation';
import { type ApptInfoClientPetInfo, type ApptInfoPetServiceInfo } from '../../../store/calendarLatest/calendar.types';
import { EvaluationServiceRecord } from '../../../store/evaluation/evaluation.boxes';
import { isNormal } from '../../../store/utils/identifier';
import { ApptAddOnRecord, ApptServiceRecord } from './appt.boxes';
import {
  type ApptAddOnInfoRecord,
  type ApptPetServiceItem,
  type ApptPetsListInfo,
  type ApptServiceInfoRecord,
} from './appt.types';
import { assignFeedingMedicationId } from './appt.utils';
import { getMainCareType } from '../../../components/PetAndServicePicker/utils/getMainCareType';

/** 将detail接口的数据转换成apptPetsMapbox中的数据 */
export const transformServiceDetail = (
  serviceDetail: ServiceDetail[],
  other?: GetPetFeedingMedicationSchedulesResult,
) => {
  const pets: ApptPetsListInfo['pets'] = [];
  const apptServices: ApptServiceInfoRecord[] = [];
  const apptAddOns: ApptAddOnInfoRecord[] = [];
  const evaluationServices: (EvaluationServiceComposite & { ownId: string })[] = [];

  for (const petItem of serviceDetail) {
    const { services, evaluations = [], addOns, petEvaluations, petCodes } = petItem;
    const isOnlyEvaluation = !!evaluations.length && !services.length && !addOns.length;
    const petId = petItem.pet.id;

    const servicesList: ApptPetServiceItem[] = [...services, ...addOns].map((data) => {
      const { serviceItemType, serviceType, id, serviceId } = data.serviceDetail;

      return {
        id,
        serviceType,
        serviceItemType,
        serviceId,
        isSlotFreeService: serviceType === ServiceType.ADDON ? false : (data as ServiceComposite).isSlotFreeService,
      };
    });

    isOnlyEvaluation &&
      evaluations?.forEach(({ id, serviceId }) => {
        servicesList.push({
          id,
          serviceType: ServiceType.SERVICE,
          serviceItemType: ServiceItemType.EVALUATION,
          serviceId: serviceId,
        });
      });

    const mainCareType = getMainCareType(services.map(({ serviceDetail }) => serviceDetail.serviceItemType));
    let flag = false;
    for (const service of services) {
      const {
        serviceDetail: { serviceItemType },
      } = service;

      let feedings: AppointmentPetFeedingScheduleDef[] = [];
      let medications: AppointmentPetMedicationScheduleDef[] = [];
      const schedule = other?.schedules?.find((i) => i.petId === petId);
      const isMainServiceItemType = serviceItemType === mainCareType;
      // 仅会赋值一次，后端不能保证顺序，所以需要判断是否mainServiceItemType
      if (
        !flag &&
        schedule &&
        isMainServiceItemType &&
        (serviceItemType === ServiceItemType.BOARDING || serviceItemType === ServiceItemType.DAYCARE)
      ) {
        feedings = schedule.feedings.map((v) => assignFeedingMedicationId(v));
        medications = schedule.medications.map((v) => assignFeedingMedicationId(v));
        flag = true;
      }
      const svc = ApptServiceRecord.createFromServiceDetail(service, { ...petItem, feedings, medications });
      apptServices.push(svc);
    }
    for (const addOn of addOns) {
      const { associatedServiceId } = addOn.serviceDetail;
      // 后端还不支持 associatedId 所以我们先做一层转换
      const associatedId = isNormal(associatedServiceId)
        ? services.find((i) => i.serviceDetail.serviceId === associatedServiceId)?.serviceDetail.id
        : undefined;
      apptAddOns.push(ApptAddOnRecord.createFromServiceDetail(addOn, petItem, associatedId));
    }

    for (const evaluation of evaluations) {
      evaluationServices.push({
        ...evaluation,
        ownId: EvaluationServiceRecord.createOwnId(petId, evaluation.serviceId),
      });
    }

    pets.push({
      petId,
      services: servicesList,
      evaluations: evaluations?.map(({ id, serviceId, lodgingId, staffId, startTime, serviceTime, servicePrice }) => ({
        id,
        evaluationId: serviceId,
        petId,
        startTime,
        lodgingId,
        staffId,
        duration: serviceTime,
        price: servicePrice,
        evaluationStatus: petEvaluations?.find((i) => i.evaluationId === serviceId)?.evaluationStatus,
      })),
      petCodes: petCodes?.map((petCode) => +petCode.id),
    });
  }

  return {
    apptAddOns,
    apptServices,
    pets,
    evaluations: evaluationServices,
  };
};

export function transformServiceOperationModel(options: ServiceOperationModel): ServiceOperationEntry {
  const { staffId, price, priceRatio, duration, startTime, operationName } = options;
  return {
    staffId: +staffId,
    price,
    priceRatio,
    duration,
    startTime,
    operationName,
  };
}

export function transformServiceCompositeToServiceEntity(s: ServiceComposite | AddOnComposite): ApptInfoPetServiceInfo {
  const { serviceDetail, operations } = s;
  const {
    serviceId,
    servicePrice,
    serviceTime,
    serviceType,
    scopeTypePrice,
    scopeTypeTime,
    staffId,
    enableOperation,
    workMode,
    id,
  } = serviceDetail;

  return {
    id,
    serviceId: +serviceId,
    servicePrice,
    serviceTime,
    serviceType,
    scopeTypePrice,
    scopeTypeTime,
    staffId: +staffId,
    enableOperation,
    operationList: (operations || []).map(transformServiceOperationModel),
    workMode,
  };
}

export const getTicketDetailPetsFromServiceDetail = (serviceDetail?: ServiceDetail[]): ApptInfoClientPetInfo[] => {
  if (!Array.isArray(serviceDetail) || !serviceDetail.length) {
    return [];
  }
  const pets: ApptInfoClientPetInfo[] = serviceDetail.map((petInfo) => {
    const { pet, services, addOns } = petInfo;
    const serviceList = services.map(transformServiceCompositeToServiceEntity);
    const addOnList = addOns.map(transformServiceCompositeToServiceEntity);
    return {
      petId: +pet.id,
      services: serviceList.concat(addOnList),
    };
  });
  return pets;
};

export const transformOBFeedingDetailToApptFeedingDetail = (
  feedingDetail: Partial<FeedingDetail>,
): Partial<AppointmentPetFeedingScheduleDef> => {
  const { time, amountStr, unit, foodType, foodSource, instruction, note } = feedingDetail;
  return {
    feedingTimes: time?.map((t) => ({
      scheduleTime: t.time,
      extraJson: { label: t.label },
    })),
    feedingAmount: amountStr,
    feedingUnit: unit,
    feedingType: foodType?.join(', '),
    feedingSource: foodSource,
    feedingInstruction: instruction,
    feedingNote: note,
  };
};

export const transformMedicationDetailToApptMedicationDetail = (
  medicationDetail: Partial<MedicationDetail>,
): Partial<AppointmentPetMedicationScheduleDef> => {
  const { time, amountStr, unit, medicationName, notes, selectedDate } = medicationDetail;
  return {
    medicationTimes: time?.map((t) => ({
      scheduleTime: t.time,
      extraJson: { label: t.label },
    })),
    medicationAmount: amountStr,
    medicationUnit: unit,
    medicationName: medicationName,
    medicationNote: notes,
    selectedDate,
  };
};

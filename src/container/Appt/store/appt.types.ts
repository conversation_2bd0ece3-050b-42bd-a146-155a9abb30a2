import { type GetAppointmentResult } from '@moego/api-web/moego/api/appointment/v1/appointment_api';
import { type AppointmentPetFeedingScheduleDef } from '@moego/api-web/moego/models/appointment/v1/appointment_pet_feeding_schedule_defs';
import { type AppointmentPetMedicationScheduleDef } from '@moego/api-web/moego/models/appointment/v1/appointment_pet_medication_schedule_defs';
import { type BoardingSplitLodgingScheduleDef } from '@moego/api-web/moego/models/appointment/v1/boarding_split_lodging_defs';
import {
  type SelectedAddOnDef,
  type SelectedServiceDef,
} from '@moego/api-web/moego/models/appointment/v1/pet_detail_defs';
import { type PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import {
  type ServiceItemType,
  type ServiceOverrideType,
  type ServicePriceUnit,
  type ServiceType,
} from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type OpenApiDefinitions } from '../../../openApi/schema';
import { type ServiceRecord } from '../../../store/service/service.boxes';

export const ApptValidOneDayMinutes = 24 * 60 - 15;
export interface ApptPetServiceItem {
  id: string;
  serviceType: ServiceType;
  // 当前它的值是和 main service 一致，后续需要考虑不同 service 和 addOn 的区分
  serviceItemType: ServiceItemType;
  serviceId: string;
  associatedId?: string;
  isAdditionalService?: boolean;
  isSlotFreeService?: boolean;
}

export type ApptPetIdsServiceList = { petId: number; serviceList: ApptPetServiceItem[] }[];

export interface PetServiceListWithMainService {
  petId: number;
  serviceList: ApptPetServiceItem[];
  mainServiceInfo: ServiceRecord;
  mainServiceDetail: ApptPetServiceItem;
}

export interface BelongServiceAddOnItem {
  id: string;
  ownId: string;
  serviceId: string;
  serviceItemType: ServiceItemType;
}

/** 跟ApptPetServiceItem属于反转关系 */
export interface ApptPetServiceWithAddon {
  /** appt Service/Addon MapBox里面的key */
  id: string;
  ownId: string;
  serviceId: string;
  serviceType: ServiceType;
  serviceItemType: ServiceItemType;
  /** 归属于这个service的addon列表，根据associatedServiceId来判断归属关系 */
  addOns?: BelongServiceAddOnItem[];
}

export interface ApptPetEvaluationInfo {
  id: string;
  evaluationId: string;
  startTime: number;
  staffId?: string;
  lodgingId?: string;
  petId: string;
  associatedId?: string;
  price?: number;
  duration?: number;
  evaluationStatus?: number;
}

/** appt下的pet和service相关信息 */
export interface ApptPetsListInfo {
  appointmentId: string;
  pets: ApptPetServiceInfo[];
}

/**
 * 某个pet下的service相关信息，包含了service和addon信息
 */
export interface ApptPetServiceInfo {
  petId: string;
  services: ApptPetServiceItem[];
  evaluations: ApptPetEvaluationInfo[];
  petCodes: number[];
}

export interface ApptTicketInfoModel {
  clientId: number;
  pets: ApptPetServiceInfo[];
}

export type ApptServiceInfoRecord =
  /** 由于后端历史设计的问题，service 和 addon 本该具有一致的dateType 枚举，这里属于是前端在类型层面做一个兼容 */
  Omit<SelectedServiceDef, 'dateType'> & {
    id: string;
    serviceItemType: ServiceItemType;
    serviceName: string;
    lodgingName?: string;
    ownId: string;
    petId: string;
    dateType?: PetDetailDateType;
    maxDuration?: number;
    priceOverrideType?: ServiceOverrideType;
    durationOverrideType?: ServiceOverrideType;
    isPriceModified?: boolean;
    isDurationModified?: boolean;
    quantityPerDay?: number;
    priceUnit: ServicePriceUnit;
    orderLineItemId?: string;
  };

export type ApptServiceInfoRecordTimeKeys = 'startTime' | 'endTime' | 'startDate' | 'endDate';

export type ApptAddOnInfoRecord = Omit<SelectedAddOnDef, 'dateType'> & {
  id: string;
  serviceItemType: ServiceItemType;
  serviceName: string;
  ownId: string;
  petId: string;
  dateType?: PetDetailDateType;
  /** 属于哪个主service下的addon */
  associatedId?: string;
  requireDedicatedStaff: boolean;
  priceOverrideType: ServiceOverrideType;
  durationOverrideType: ServiceOverrideType;
  priceUnit: ServicePriceUnit;
  endDate?: string;
  endTime?: number;
  orderLineItemId?: string;
};

/** 创建flow的appointmentId */
export const CreateApptId: string = '0';

export type AddOnDateInfo = {
  id: string;
  ownId: string;
  petId: string;
  serviceId: string;
  serviceType: ServiceType;
  dateType?: PetDetailDateType;
  /** @deprecated 直接冲service setting(serviceMapBox)中拿配置，不用快照数据 */
  requireDedicatedStaff?: boolean;
  // 指定staff，明确日期
  startDate?: string;
  startTime?: number;
  serviceTime?: number;
  // 范围
  specificDates?: string[];
  associatedId?: string;
  quantityPerDay?: number;
  extra?: {
    /**
     * 控制 date 下方是否展示 availableTimeSlotPicker by slots 适用场景
     */
    showAvailableTimeSlotPicker?: boolean;
  };
};

export type ServiceDateInfo = {
  ownId: string;
  id: string;
  petId: string;
  serviceId: string;
  serviceType: ServiceType;
  serviceItemType?: ServiceItemType;
  startDate?: string;
  startTime?: number;
  endDate?: string;
  endTime?: number;
  dateType?: PetDetailDateType;
  specificDates?: string[];
  quantityPerDay?: number;
  extra?: {
    /**
     * 控制 date 下方是否展示 availableTimeSlotPicker by slots 适用场景
     */
    showAvailableTimeSlotPicker?: boolean;
  };
};

export type ServiceAddOnDateInfo = AddOnDateInfo | ServiceDateInfo;

export enum ApptPetCardMode {
  DETAIL = 'DETAIL',
  CREATE = 'CREATE',
}

export type ApptDetailInfoRecord = GetAppointmentResult & { appointmentId: string };

export interface ServiceFeedingMedication {
  ownId: string;
  petId: string;
  serviceId: string;
  feedings: Partial<AppointmentPetFeedingScheduleDef>[];
  medications: Partial<AppointmentPetMedicationScheduleDef>[];
}

/** service时间范围 */
export interface ApptServiceTimeInfo {
  startTime: number;
  endTime?: number;
  serviceTime?: number;
  staffId?: number;
}

export interface PreviousApptPetService {
  petId: number;
  serviceList: OpenApiDefinitions['grooming']['com.moego.server.grooming.params.PetDetailParams'][];
}

export interface ApptMainServiceEntry {
  serviceId: string;
  serviceType: ServiceType;
  serviceItemType: ServiceItemType;
  startDate?: string;
  endDate?: string;
  startTime?: number;
  endTime?: number;
  petId: string;
  specificDates?: string[];
  lodgingId?: string;
  lodgingName?: string;
  splitLodgings?: BoardingSplitLodgingScheduleDef[];
  staffId?: string;
  ownId: string;
  serviceTime?: number;
}

// Appointment 级别
export enum ApptFlowScene {
  /** 是否自动计算 schedule */
  AutoCalc = 'AutoCalc',
  StartAtSameTime = 'StartAtSameTime',
  StaffValid = 'StaffValid',
  BookAgainAllService = 'BookAgainAllService',
  RepeatPreview = 'RepeatPreview',
  /** 是否进入 drawer的 edit pet service 页面，跟 edit pet hybrid service 区分开*/
  EditPetService = 'EditPetService',
  UpdateStaffDateTimeOnCalendar = 'UpdateStaffDateTimeOnCalendar',
  AdvanceEdit = 'AdvanceEdit',
  WaitList = 'WaitList',
  FeedingMedication = 'FeedingMedication',
  SmartSchedule = 'SmartSchedule',
  StayCard = 'StayCard',
  ApptLodging = 'ApptLodging',
  SyncMainServiceSchedule = 'SyncMainServiceSchedule',
  SingleToMultiDaysSource = 'SingleToMultiDaysSource',
  SingleToMultiDaysTarget = 'SingleToMultiDaysTarget',
  /** sync startDate when main service is daycare */
  SyncStartDate = 'SyncStartDate',
  AutoFillFeedings = 'AutoFillFeedings',
  EnableFeedingMedicationDate = 'EnableFeedingMedicationDate',
}

// Service 级别
export enum ApptServiceScene {
  /** 是否自动补充 service staff*/
  AutoFillStaff = 'AutoFillStaff',
  AutoFillStartDate = 'AutoFillStartDate',
  ServicePickerDuration = 'ServicePickerDuration',
  ServiceDateTimeWhenEdit = 'ServiceDateTimeWhenEdit',
  ServiceRequireStaff = 'ServiceRequireStaff',
  LodgingAssignment = 'LodgingAssignment',
  LodgingAssignmentRequired = 'LodgingAssignmentRequired',
  AddAssociatedService = 'AddAssociatedService',
  ServiceRequireTime = 'ServiceRequireTime',
  PickServiceOnly = 'PickServiceOnly',
  PickServiceSingle = 'PickServiceSingle',
  PickServiceFirst = 'PickServiceFirst',
  ServiceFeedingMedication = 'ServiceFeedingMedication',
  MultiSameService = 'MultiSameService',
  HiddenServiceDateWhenSingleDayAppt = 'HiddenServiceDateWhenSingleDayAppt',
  OnlyBD = 'OnlyBD',
  MainServiceSingleDay = 'MainServiceSingleDay',
  ServiceSplitLodgings = 'ServiceSplitLodgings',
  // 使用动态的 Service DateType 的时间，类似 Last Day / First Day 等
  ServiceUseDynamicDatePoint = 'UseDynamicServiceDateType',
  AddOnSupportQuantityPerDay = 'AddOnSupportQuantityPerDay',
}

export interface ApptCareTypeConfig {
  icon: React.ReactNode;
  /** 能继续选择的其他 careType */
  accept: ServiceItemType[] | ((filter: (item: ServiceItemType) => boolean) => ServiceItemType[]);
  /** 前端样式上的展示顺序 */
  order: number;
}

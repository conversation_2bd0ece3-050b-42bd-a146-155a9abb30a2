import { action } from 'amos';
import { merge } from 'lodash';
import { type DeepPartial } from 'utility-types';
import { apptInfoMapBox } from '../../appt.boxes';
import { type ApptDetailInfoRecord } from '../../appt.types';

/**
 * 直接修改 appt 的 store 信息，而不是通过 refresh appt的接口
 */
export const setApptStoreInfo = action(
  async (dispatch, select, apptId: string | number, params: DeepPartial<ApptDetailInfoRecord>) => {
    const appointmentId = String(apptId);
    const detail = select(apptInfoMapBox.mustGetItem(appointmentId));
    // TODO(vision,p2) 改 appt detail 时看看，这里应该用 updateItem 方法
    dispatch(apptInfoMapBox.mergeItem(appointmentId, merge(detail.toJSON(), params as ApptDetailInfoRecord)));
  },
);

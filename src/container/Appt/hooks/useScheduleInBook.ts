import { type AddOnComposite, type ServiceComposite } from '@moego/api-web/moego/api/appointment/v1/appointment_api';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useStore } from 'amos';
import dayjs from 'dayjs';
import { useCallback } from 'react';
import { DATE_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';
import { selectPlaceholderServiceInAppt } from '../store/appt.selectors';

const { GROOMING, BOARDING, DAYCARE } = ServiceItemType;

export const useGetPlaceholderSchedule = () => {
  const store = useStore();

  return (serviceItemType: ServiceItemType) => {
    const placeholder = store.select(selectPlaceholderServiceInAppt());
    if (!placeholder) return {};
    let { startDate, endDate, startTime, endTime } = placeholder;

    if (!startDate || !endDate) {
      const now = dayjs().format(DATE_FORMAT_EXCHANGE);
      startDate = now;
      endDate = now;
    }

    if (serviceItemType === BOARDING && startDate === endDate) {
      endDate = dayjs(startDate).add(1, 'day').format(DATE_FORMAT_EXCHANGE);
    }

    // daycare service book again 不能用 placeholder service 的 startTime & endTime
    if (serviceItemType === DAYCARE) {
      endDate = startDate;
      startTime = endTime = undefined;
    }

    return {
      startDate,
      endDate,
      startTime,
      endTime,
    };
  };
};

export const useServiceScheduleInBook = () => {
  const resolveSchedule = useGetPlaceholderSchedule();

  return useCallback((originService: ServiceComposite | AddOnComposite) => {
    const {
      serviceDetail: { serviceItemType },
    } = originService;

    const { startDate: sDate, endDate: eDate, startTime: sTime, endTime: eTime } = resolveSchedule(serviceItemType);
    const isCrossDaycareType = serviceItemType === BOARDING;

    const now = sDate ? dayjs(sDate) : dayjs();
    const defaultEndDate = isCrossDaycareType ? now.add(1, 'day') : now;
    const endDate = eDate ? dayjs(eDate) : defaultEndDate;

    const schedule: Record<string, string | number | undefined> = {
      startDate: now.format(DATE_FORMAT_EXCHANGE),
      endDate: endDate.format(DATE_FORMAT_EXCHANGE),
      startTime: sTime || originService.serviceDetail.startTime,
      endTime: eTime || originService.serviceDetail.endTime,
    };

    if (serviceItemType === GROOMING) {
      // 如果是有bd的情况，是不会进入这里的，因为bd不会把grooming 的内容放进book again里
      schedule.startTime = sTime || originService.serviceDetail.startTime || dayjs().getMinutes();
      // 如果是grooming only，结束时间我们做一个简单的预填
      schedule.endTime = schedule.startTime + 60;
      schedule.endDate = schedule.startDate;
    }

    return schedule;
  }, []);
};

export const useScheduleInBook = () => {
  const store = useStore();

  return useCallback((originService?: ServiceComposite | AddOnComposite) => {
    const placeholder = store.select(selectPlaceholderServiceInAppt());
    const now = placeholder?.startDate ? dayjs(placeholder.startDate) : dayjs();
    const startDate = now.format(DATE_FORMAT_EXCHANGE);
    return {
      startDate,
      startTime: placeholder?.startTime || originService?.serviceDetail?.startTime || now.getMinutes(),
    };
  }, []);
};

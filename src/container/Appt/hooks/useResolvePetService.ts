import { useStore } from 'amos';
import { type ServiceEntry } from '../../../components/ServiceApplicablePicker/types/serviceEntry';
import { selectPetsInAppt, selectServiceListWithPet } from '../store/appt.selectors';
import { CreateApptId } from '../store/appt.types';
import { sortServiceList } from '../store/appt.utils';
import { type GroomingOnlyScheduleProps } from './useCalcGroomingOnlySchedule';

/**
 * 组装 calcGroomingSchedule 需要的 serviceList
 * 如果有新的 petId 和 serviceList，则替换/添加
 * 否则，用原有的 serviceList 计算
 */
export const useResolvePetService = () => {
  const store = useStore();

  return (props: GroomingOnlyScheduleProps) => {
    const { appointmentId = CreateApptId, petIdsServiceList } = props;
    const pets = store.select(selectPetsInAppt(appointmentId));

    const petsServiceList = pets.map((pet) => {
      const serviceList = store.select(selectServiceListWithPet(pet, appointmentId));
      return {
        petId: pet.petId,
        services: sortServiceList(serviceList),
      };
    });

    if (!petIdsServiceList?.length) {
      // 不是新增 /替换 pet service。直接返回原有的serviceList
      return petsServiceList;
    }

    const updatePetServiceList = (petId: string, serviceList: ServiceEntry[]) => {
      const index = petsServiceList.findIndex((pet) => pet.petId === petId);
      const newPet = {
        petId,
        services: sortServiceList(serviceList),
      };
      // 保持 pet 顺序
      if (index !== -1) {
        petsServiceList.splice(index, 1, newPet);
      } else {
        petsServiceList.push(newPet);
      }
    };

    petIdsServiceList.forEach(({ petId, serviceList }) => {
      if (!serviceList || !petId) return;
      updatePetServiceList(String(petId), serviceList);
    });

    return petsServiceList;
  };
};

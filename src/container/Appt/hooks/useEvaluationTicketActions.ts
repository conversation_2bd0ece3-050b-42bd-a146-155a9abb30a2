import { useDispatch } from 'amos';
import { store } from '../../../provider';
import { selectCurrentPermissions } from '../../../store/business/role.selectors';
import {
  type ReadyForPickupConfirmModalMode,
  type SetTakePaymentModalOptions,
} from '../../../store/calendarLatest/calendar.boxes';
import {
  setEvaluationApptDetailDrawer,
  setEvaluationApptDetailModalState,
} from '../../../store/evaluation/evaluation.actions';
import {
  type EvaluationApptDetailDrawerBox,
  evaluationApptDetailDrawerBox,
} from '../../../store/evaluation/evaluation.boxes';
import { getInvoice } from '../../../store/payment/actions/private/payment.actions';
import { getPetOptions } from '../../../store/pet/pet.actions';
import { isNormal, toNumber } from '../../../store/utils/identifier';
import { uniq } from '../../../store/utils/utils';
import { globalEvent } from '../../../utils/events/events';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { useLazySelectors } from '../../../utils/unstable/createSelectAccessor';
import { getAppointment } from '../store/appt.api';

export function useEvaluationTicketActions(propTicketId?: EvaluationApptDetailDrawerBox['ticketId']) {
  const dispatch = useDispatch();
  const [permissions, apptDetail] = useLazySelectors(selectCurrentPermissions(), evaluationApptDetailDrawerBox);
  const ticketId = () => propTicketId ?? apptDetail().ticketId;
  const canProcessPayment = () => permissions().has('canProcessPayment');

  const setTakePaymentModal = useLatestCallback((visible: boolean, options?: SetTakePaymentModalOptions) => {
    if (!canProcessPayment()) return;
    dispatch(
      setEvaluationApptDetailModalState({
        takePaymentModal: {
          visible,
          isDeposit: !!options?.isDeposit,
          isViewOnly: !!options?.isViewOnly,
          isCheckout: !!options?.isCheckout,
        },
      }),
    );
  });

  const setReadyForPickupModal = useLatestCallback(
    (visible: boolean, mode: ReadyForPickupConfirmModalMode, markDone?: boolean) => {
      dispatch(
        setEvaluationApptDetailModalState({
          readyForPickupModal: {
            visible,
            mode,
            markDone,
          },
        }),
      );
    },
  );

  /** 刷新当前 ticket 详情，带 loading */
  const refreshTicket = useLatestCallback(async (inputTicketId?: number, reloadCalendarLodgingOverview = true) => {
    try {
      const appointmentId = inputTicketId ?? ticketId();
      dispatch(setEvaluationApptDetailDrawer({ loading: true }));
      await dispatch(getPetOptions());
      const res = await dispatch(getAppointment({ appointmentId: '' + appointmentId }));
      const { invoice, serviceDetail, appointment } = res;
      const petIds = serviceDetail?.map((item) => toNumber(item.pet.id));

      dispatch(
        setEvaluationApptDetailDrawer({
          petIds,
          customerId: toNumber(appointment.customerId),
          startDate: appointment.appointmentDate,
          startTime: appointment.appointmentStartTime,
          evaluationResults: serviceDetail.map((item) => ({
            petId: toNumber(item.pet.id),
            evaluationStatus: item.petEvaluations?.find((e) => e.petId === item.pet.id)?.evaluationStatus,
            petCodeIdList: uniq(item.petCodes.map((p) => toNumber(p.id))),
            evaluationId: toNumber(item.evaluations[0].serviceId),
          })),
        }),
      );
      if (isNormal(invoice?.invoiceId)) {
        await dispatch(getInvoice(+res.invoice.invoiceId, 'grooming'));
      }
      return res;
    } finally {
      dispatch(setEvaluationApptDetailDrawer({ loading: false }));
      if (reloadCalendarLodgingOverview) {
        globalEvent.refresh.emit();
      }
    }
  });

  const setChargeNoShowFeeModal = useLatestCallback((visible: boolean) => {
    dispatch(
      setEvaluationApptDetailModalState({
        chargeNoShowFeeModalVisible: visible,
      }),
    );
  });

  const setNoShowTakePaymentModal = useLatestCallback((visible: boolean) => {
    dispatch(
      setEvaluationApptDetailModalState({
        noShowTakePaymentModalVisible: visible,
      }),
    );
  });

  return {
    setCancelTicketModalVisible,
    setTakePaymentModal,
    refreshTicket,
    setReadyForPickupModal,
    setChargeNoShowFeeModal,
    setNoShowTakePaymentModal,
    setEditLodgingAssignmentModalVisible,
  };
}

export function setCancelTicketModalVisible(visible: boolean) {
  store.dispatch(
    setEvaluationApptDetailModalState({
      cancelApptModalVisible: visible,
    }),
  );
}

export function setEditLodgingAssignmentModalVisible(visible: boolean) {
  store.dispatch(
    setEvaluationApptDetailModalState({
      editLodgingAssignmentModalVisible: visible,
    }),
  );
}

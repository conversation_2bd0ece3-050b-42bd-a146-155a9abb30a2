import { useDispatch } from 'amos';
import { type Dayjs } from 'dayjs';
import React from 'react';
import { AutoMessageType } from '../../../store/autoMessage/autoMessage.boxes';
import { updateEvaluationQuickAddFields } from '../../../store/evaluation/evaluation.actions';
import { evaluationQuickAddFieldsBox, type EvaluationQuickAddSource } from '../../../store/evaluation/evaluation.boxes';
import { getDefaultEvaluationInfo, type EvaluationDetails } from '../../../store/evaluation/evaluation.types';
import { DATE_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';
import { globalEvent } from '../../../utils/events/events';
import { useCloseAllDrawer } from '../../../utils/hooks/useCloseAllDrawer';
import { useFloatableHost } from '../../../utils/hooks/useFloatableHost';
import { toggleIntercom } from '../../../utils/hooks/useToggleIntercom';
import {
  DrawerLazyWrapper,
  type DrawerWrapperProps,
} from '../modules/QuickAddEvaluationApptDrawer/QuickAddEvaluationApptDrawer.lazy';
import { useTicketAlerts } from './useTicketAlerts';

export interface OpenProps extends Pick<DrawerWrapperProps, 'onReady'> {
  clientId?: number;
  source?: EvaluationQuickAddSource;
  businessId?: number;
  preset?: {
    appointmentStart?: Dayjs;
    appointmentEnd?: Dayjs;
    petServiceList?: EvaluationDetails[];
  };
}

export const useCreateEvaluationDrawer = () => {
  const dispatch = useDispatch();
  const { mountDrawer } = useFloatableHost();
  const { closeAllDrawer } = useCloseAllDrawer();
  const openTicketAlerts = useTicketAlerts();

  const resolvePreset = (v?: OpenProps['preset']) => {
    if (!v) {
      return getDefaultEvaluationInfo();
    }
    const { appointmentStart, appointmentEnd, ...rest } = v;

    const start = appointmentStart
      ? { startDate: appointmentStart.format(DATE_FORMAT_EXCHANGE), startTime: appointmentStart.getMinutes() }
      : undefined;

    const end = appointmentEnd
      ? { endDate: appointmentEnd.format(DATE_FORMAT_EXCHANGE), endTime: appointmentEnd.getMinutes() }
      : undefined;

    return {
      ...getDefaultEvaluationInfo(),
      ...rest,
      ...start,
      ...end,
    };
  };

  const resolveProps = (v?: OpenProps) => {
    return {
      ...v,
      evaluationInfo: resolvePreset(v?.preset),
    };
  };

  const onClose = () => {
    toggleIntercom(false);
    dispatch([
      evaluationQuickAddFieldsBox.setState(evaluationQuickAddFieldsBox.initialState), // 清理默认 config
    ]);
  };

  const beforeOpen = async (v: ReturnType<typeof resolveProps>) => {
    closeAllDrawer();
    toggleIntercom(true);

    await dispatch([
      // 这里也可以通过给 drawer props 来实现，但是为了方便现有的逻辑，我们先保持
      // 如果用 props 来实现，那好处是不需要 reset，但是缺点是外部可能无法感知上下文
      updateEvaluationQuickAddFields(v),
    ]);
  };

  const open = async (p?: OpenProps) => {
    const props = resolveProps(p);

    await beforeOpen(props);

    const { promise, closeFloatable: closeModal } = mountDrawer(
      <DrawerLazyWrapper
        onClose={() => {
          onClose();
          closeModal();
        }}
        onCreated={async ({ apptId, customerId }) => {
          onClose();
          closeModal();

          openTicketAlerts({
            ticketId: Number(apptId),
            customerId,
            mode: AutoMessageType.AppointmentBooked,
          });

          globalEvent.createdAppt.emit({ apptId });
          globalEvent.refresh.emit();
        }}
        onReady={props.onReady}
      />,
    );

    return promise;
  };

  return open;
};

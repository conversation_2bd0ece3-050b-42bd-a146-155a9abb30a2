import { useSelector, useStore } from 'amos';
import { useCallback, useMemo } from 'react';
import { apptAddOnMapBox, apptServiceMapBox } from '../store/appt.boxes';
import { selectPetsInAppt, selectServiceListWithPet } from '../store/appt.selectors';
import { type ApptPetServiceInfo, CreateApptId } from '../store/appt.types';

/**
 * 此 hook 设计为当你需要在渲染层面，获取某个 pet 的 service list 时使用
 * 如果你只想在事件处理层面，请直接使用 store.select selectServiceListWithPet
 * @returns
 */
export const useGetServiceListByPet = () => {
  const store = useStore();
  const [apptServiceMap, apptAddOnMap] = useSelector(apptServiceMapBox, apptAddOnMapBox);

  return useCallback(
    (pet: ApptPetServiceInfo, appointmentId: string | number) =>
      store.select(selectServiceListWithPet(pet, appointmentId)),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [apptServiceMap, apptAddOnMap],
  );
};

export const useGetApptServiceList = (appointmentId = CreateApptId) => {
  const [pets] = useSelector(selectPetsInAppt(appointmentId));
  const getServiceListByPet = useGetServiceListByPet();

  return useMemo(
    () => pets.map((pet) => getServiceListByPet(pet, appointmentId)).reduce((acc, val) => acc.concat(val), []),
    [pets, getServiceListByPet, appointmentId],
  );
};

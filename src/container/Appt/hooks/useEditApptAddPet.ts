import { useDispatch, useSelector, useStore } from 'amos';
import { setApptDetailDrawerAdd } from '../../../store/calendarLatest/actions/private/calendar.actions';
import { type ApptDetailDrawerAddBox, apptDetailDrawerAddBox } from '../../../store/calendarLatest/calendar.boxes';
import { ID_ANONYMOUS } from '../../../store/utils/identifier';
import { apptPetMapBox } from '../store/appt.boxes';
import { selectServiceListWithPet } from '../store/appt.selectors';

export const useEditApptAddPet = () => {
  const [apptDetailPetMap] = useSelector(apptPetMapBox);

  const dispatch = useDispatch();
  const store = useStore();

  return (appointmentId: string, extra: Partial<ApptDetailDrawerAddBox>) => {
    const { pets } = apptDetailPetMap.mustGetItem(appointmentId);
    const lastPet = pets[pets.length - 1];
    const serviceList = store.select(selectServiceListWithPet(lastPet, appointmentId));
    const serviceInfo = serviceList[serviceList.length - 1];
    const lastServiceStaffId = serviceInfo?.staffId ? Number(serviceInfo?.staffId) : ID_ANONYMOUS;

    dispatch(
      setApptDetailDrawerAdd({
        ...apptDetailDrawerAddBox.initialState,
        ...extra,
        visible: true,
        lastServiceStaffId,
        disabledPetIds: pets.map((pet) => Number(pet.petId)),
        serviceList: [],
        isDisabled: false,
        defaultVisible: true,
      }),
    );
  };
};

import { type ServiceComposite } from '@moego/api-web/moego/api/appointment/v1/appointment_api';
import { useStore } from 'amos';
import { isNormal } from '../../../store/utils/identifier';
import { type ID } from '../../../types/common';
import { ApptAddOnRecord, ApptServiceRecord, apptAddOnMapBox, apptServiceMapBox } from '../store/appt.boxes';
import { selectApptPetAddon, selectApptPetService } from '../store/appt.selectors';
import { useGetLodgingInBook } from './useGetLodgingInBook';
import { useResolveStaff } from './useResolveStaff';
import { useScheduleInBook, useServiceScheduleInBook } from './useScheduleInBook';

interface AutoFillProps {
  petId: string;
  originApptId: string;
  newApptId: string;
  originService: ServiceComposite;
  availableStaffs?: number[];
}

export const useOverrideInfo = () => {
  const { resolveStaff } = useResolveStaff();

  return (props: { petId: ID; serviceId: ID; staffId?: ID; availableStaffs?: number[] }) => {
    const { staffId, availableStaffs } = props;

    return {
      // 如果本身是非法值，那么就是不需要staffId
      staffId: isNormal(staffId) ? String(resolveStaff(staffId, availableStaffs)) : staffId,
    };
  };
};

export const useAutoFillAddOn = () => {
  const store = useStore();
  const getSchedule = useScheduleInBook();
  const getOverrideInfo = useOverrideInfo();

  return (props: AutoFillProps) => {
    const { petId, originApptId, newApptId, originService, availableStaffs } = props;

    const info = store.select(selectApptPetAddon(originApptId, originService.serviceDetail.id)).toJSON();
    const { startDate, startTime, staffId, ...rest } = info;
    const schedule = getSchedule(originService);

    return apptAddOnMapBox.mergeItem(ApptAddOnRecord.createOwnId(newApptId, originService.serviceDetail.id), {
      ...rest,
      ...schedule,
      ...getOverrideInfo({ petId, serviceId: originService.serviceDetail.serviceId, staffId, availableStaffs }),
    });
  };
};

export const useAutoFillService = () => {
  const store = useStore();

  const getSchedule = useServiceScheduleInBook();
  const getOverrideInfo = useOverrideInfo();
  const getLodgingInBook = useGetLodgingInBook();

  return (props: AutoFillProps) => {
    const { petId, originApptId, newApptId, originService, availableStaffs } = props;
    const info = store.select(selectApptPetService(originApptId, originService.serviceDetail.id)).toJSON();
    const { startDate, endDate, startTime, endTime, staffId, ...rest } = info;
    const schedule = getSchedule(originService);
    const lodging = getLodgingInBook(originService);

    return apptServiceMapBox.mergeItem(ApptServiceRecord.createOwnId(newApptId, originService.serviceDetail.id), {
      ...rest,
      ...schedule,
      ...lodging,
      ...getOverrideInfo({ petId, serviceId: originService.serviceDetail.serviceId, staffId, availableStaffs }),
    });
  };
};

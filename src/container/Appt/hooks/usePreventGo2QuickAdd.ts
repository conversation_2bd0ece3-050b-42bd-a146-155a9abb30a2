import { useRouteMatch } from 'react-router';
import { PATH_TICKET_ADD, PATH_TICKET_EDIT, PATH_WAITING_ADD, PATH_WAITING_EDIT } from '../../../router/paths';

export function usePreventGo2QuickAdd() {
  const isInAdvancedEdit = useRouteMatch(PATH_TICKET_ADD.path);
  const isInWaitingAdd = useRouteMatch(PATH_WAITING_ADD.path);
  const isInWaitingEdit = useRouteMatch(PATH_WAITING_EDIT.path);
  const isInTicketEdit = useRouteMatch(PATH_TICKET_EDIT.path);

  return !!isInAdvancedEdit || !!isInWaitingAdd || !!isInWaitingEdit || !!isInTicketEdit;
}

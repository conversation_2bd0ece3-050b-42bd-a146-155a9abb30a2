import {
  type AddOnComposite,
  type ServiceComposite,
  type ServiceDetail,
} from '@moego/api-web/moego/api/appointment/v1/appointment_api';
import { type AppointmentCalendarView } from '@moego/api-web/moego/models/appointment/v1/appointment_models';
import { ServiceType, type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useStore } from 'amos';
import { useCallback } from 'react';
import { getMainCareType } from '../../../components/PetAndServicePicker/utils/getMainCareType';
import { lodgingUnitMapBox } from '../../../store/lodging/lodgingUnit.boxes';
import { selectLodgingUnitList } from '../../../store/lodging/lodgingUnit.selectors';
import { selectAllActiveServiceIdList } from '../../../store/service/service.selectors';
import { isNormal } from '../../../store/utils/identifier';
import { matchApptFlowScene, matchApptServiceScenes } from '../store/appt.options';
import { ApptFlowScene, ApptServiceScene, CreateApptId, type ApptPetServiceInfo } from '../store/appt.types';
import { useOverrideInfo } from './useAutoFill';
import { useResolveStaff } from './useResolveStaff';
import { useScheduleInBook, useServiceScheduleInBook } from './useScheduleInBook';

interface Props {
  serviceDetail: ServiceDetail[];
  appointment: AppointmentCalendarView;
  serviceItemTypes: ServiceItemType[];
  staffId?: number;
  availableStaffs?: number[];
}

export const useLastApptPetAndService = () => {
  const store = useStore();
  const getServiceSchedule = useServiceScheduleInBook();
  const getAddonSchedule = useScheduleInBook();
  const { resolveStaffs } = useResolveStaff();
  const getOverrideInfo = useOverrideInfo();

  return useCallback(
    (props: Props) => {
      const newApptId = CreateApptId;
      const { serviceDetail, staffId, availableStaffs, serviceItemTypes } = props;
      const activeIdList = store.select(selectAllActiveServiceIdList());

      const resolveServices = (services: ServiceComposite[] | AddOnComposite[]) => {
        const mainServiceItemType = getMainCareType(services.map((s) => s.serviceDetail.serviceItemType));
        const lodgingList = store.select(selectLodgingUnitList());
        return services
          .filter(
            (service) =>
              activeIdList.includes(Number(service.serviceDetail.serviceId)) &&
              !service.serviceDetail.isDeleted &&
              !service.serviceDetail.inactive,
          ) // 过滤不可用的
          .map((v) => {
            const schedule =
              v.serviceDetail.serviceType === ServiceType.SERVICE ? getServiceSchedule(v) : getAddonSchedule(v);

            const originServiceDetail = v.serviceDetail;
            const { petId, serviceId, staffId: originStaffId, lodgingId, serviceItemType } = originServiceDetail;

            const [isAutoFill, isNeedLodging] = matchApptServiceScenes(
              [ApptServiceScene.AutoFillStaff, ApptServiceScene.LodgingAssignment],
              {
                serviceItemType,
                mainServiceItemType,
              },
            );
            const finallyStaffId = isAutoFill ? resolveStaffs([staffId, originStaffId], availableStaffs) : undefined;

            const lodgingValid = store.select(lodgingUnitMapBox.getItem(lodgingId));
            const finallyLodgingId = isNormal(lodgingId) && lodgingValid ? lodgingId : lodgingList.first();
            const finallySplitLodgings =
              v.serviceDetail.serviceType === ServiceType.SERVICE ? (v as ServiceComposite).splitLodgings : [];
            const isSlotFreeService =
              v.serviceDetail.serviceType === ServiceType.SERVICE ? (v as ServiceComposite).isSlotFreeService : false;

            return {
              ...v,
              splitLodgings: finallySplitLodgings,
              serviceDetail: {
                ...originServiceDetail,
                ...getOverrideInfo({ petId, serviceId }),
                ...schedule,
                staffId: isNormal(finallyStaffId) ? String(finallyStaffId) : '',
                appointmentId: newApptId,
                lodgingId: isNeedLodging && isNormal(finallyLodgingId) ? finallyLodgingId : '',
              },
              isSlotFreeService,
            };
          });
      };

      const filterDeleteOrInactiveService = (
        services: ServiceComposite[] | AddOnComposite[],
        type: ServiceType,
        mainServiceItemType: ServiceItemType,
      ) => {
        const allService = matchApptFlowScene(ApptFlowScene.BookAgainAllService, mainServiceItemType);
        if (!allService && type === ServiceType.ADDON) {
          return [];
        }
        const mainService = services.filter(
          ({ serviceDetail }) =>
            serviceDetail.serviceType === ServiceType.SERVICE && serviceDetail.serviceItemType === mainServiceItemType,
        );
        return resolveServices(allService ? services : mainService);
      };

      const mainServiceItemType = getMainCareType(serviceItemTypes);
      return serviceDetail
        .map((detail) => {
          return {
            ...detail,
            addOns: filterDeleteOrInactiveService(detail.addOns, ServiceType.ADDON, mainServiceItemType),
            services: filterDeleteOrInactiveService(detail.services, ServiceType.SERVICE, mainServiceItemType),
          };
        })
        .filter(
          ({ pet, services, addOns }) =>
            isNormal(pet.id) && !pet.passedAway && !pet.deleted && (services.length || addOns.length),
        );
    },
    [getServiceSchedule, getAddonSchedule],
  );
};

export const transformToApptServices = (serviceDetail: ServiceDetail[]): ApptPetServiceInfo[] => {
  return serviceDetail.map((detail) => {
    const { pet, services, addOns } = detail;
    return {
      petId: pet.id,
      evaluations: [],
      petCodes: [],
      services: [
        ...services.map((s) => ({
          id: s.serviceDetail.id,
          serviceType: s.serviceDetail.serviceType,
          serviceItemType: s.serviceDetail.serviceItemType,
          serviceId: s.serviceDetail.serviceId,
          splitLodgings: s.splitLodgings,
        })),
        ...addOns.map((s) => ({
          id: s.serviceDetail.id,
          serviceType: s.serviceDetail.serviceType,
          serviceItemType: s.serviceDetail.serviceItemType,
          serviceId: s.serviceDetail.serviceId,
        })),
      ],
    };
  });
};

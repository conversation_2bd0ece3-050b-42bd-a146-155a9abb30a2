import { type RepeatAppointmentModifyScope } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useStore } from 'amos';
import { type ServiceEntry } from '../../../components/ServiceApplicablePicker/types/serviceEntry';
import { isNormal } from '../../../store/utils/identifier';
import { deletePetService, updatePetService } from '../store/appt.api';
import { matchApptFlowScene } from '../store/appt.options';
import {
  selectApptInfo,
  selectMainServiceInAppt,
  selectPetsInAppt,
  selectServiceListWithPet,
} from '../store/appt.selectors';
import { ApptFlowScene } from '../store/appt.types';
import { useCalcGroomingOnlySchedule } from './useCalcGroomingOnlySchedule';
import { useGetPetsInfoForApi, type UpdatePetServiceDetail } from './useGetPetsInfoForApi';
import { useStartSameTimeInAddPet } from './useStartSameTimeInAddPet';

const maybeString = (v: string | number) => (isNormal(v) ? String(v) : undefined);

type AutoFlowParams<T = Array<ServiceEntry>, O = Array<UpdatePetServiceDetail>> = {
  petId: string;
  repeatType?: RepeatAppointmentModifyScope;
  calcResolve: (v: T) => T;
  normalResolve: (v: O) => O;
};

export const useOperateInExistAppt = (appointmentId: string) => {
  const dispatch = useDispatch();
  const store = useStore();
  const getPetsInfoForApi = useGetPetsInfoForApi();
  const calcGroomingOnlySchedule = useCalcGroomingOnlySchedule();
  const newStartAtSameTime = useStartSameTimeInAddPet(appointmentId);

  const send = async (newPet?: UpdatePetServiceDetail, repeatType?: RepeatAppointmentModifyScope) => {
    if (!newPet) {
      return;
    }
    await dispatch(
      updatePetService({
        appointmentId,
        petDetails: [newPet],
        repeatAppointmentModifyScope: repeatType,
      }),
    );
  };

  const update = async (params: AutoFlowParams) => {
    const { petId, repeatType, calcResolve, normalResolve } = params;
    const mainService = store.select(selectMainServiceInAppt(appointmentId));
    const isAutoFill = matchApptFlowScene(ApptFlowScene.AutoCalc, mainService.serviceItemType);
    const {
      appointment: { startAtSameTime },
      customerId,
    } = store.select(selectApptInfo(appointmentId));

    if (isAutoFill) {
      const pet = store.select(selectPetsInAppt(appointmentId)).find((p) => p.petId === petId);
      if (!pet) return;
      const prevServiceList = store.select(selectServiceListWithPet(pet, appointmentId));
      await calcGroomingOnlySchedule({
        allPetsStartAtSameTime: newStartAtSameTime || startAtSameTime,
        customerId,
        appointmentId,
        petIdsServiceList: [{ petId: Number(petId), serviceList: calcResolve(prevServiceList) }],
      });
      const petDetails = getPetsInfoForApi(appointmentId);
      await send(
        petDetails.find((pet) => pet.petId === String(petId)),
        repeatType,
      );
    } else {
      const petDetails = getPetsInfoForApi(appointmentId);
      if (!petDetails?.length) return;
      const newPetDetails = normalResolve(petDetails);
      await send(
        newPetDetails.find((pet) => pet.petId === String(petId)),
        repeatType,
      );
    }
  };

  return {
    deletePet: async (petId: string) => {
      await dispatch(deletePetService({ appointmentId, petId }));
    },
    deleteService: async (petId: string, service: ServiceEntry, repeatType?: RepeatAppointmentModifyScope) => {
      const calcResolve: AutoFlowParams['calcResolve'] = (v) => v.filter((s) => s.id !== service.id);
      const normalResolve: AutoFlowParams['normalResolve'] = (v) =>
        v.map((p) => {
          if (p.petId === petId) {
            const newValue =
              service.serviceType === ServiceType.SERVICE
                ? { services: p.services.filter((s) => s.id !== service.id) }
                : { addOns: p.addOns.filter((s) => s.addOnId !== String(service.serviceId)) };
            return { ...p, ...newValue };
          }
          return p;
        });
      await update({ petId, repeatType, calcResolve, normalResolve });
    },
    /**
     * 暂时不支持evaluation
     * 暂时只支持price staff的修改
     */
    editService: async (petId: string, service: ServiceEntry, repeatType?: RepeatAppointmentModifyScope) => {
      const calcResolve: AutoFlowParams['calcResolve'] = (v) =>
        v.map((s) => {
          if (s.id === service.id) {
            return { ...s, ...service };
          }
          return s;
        });
      const normalResolve: AutoFlowParams['normalResolve'] = (v) =>
        v.map((p) => {
          if (p.petId === petId) {
            const newValue =
              service.serviceType === ServiceType.SERVICE
                ? {
                    services: p.services.map((s) => {
                      const { id, staffId, servicePrice } = service;
                      if (s.id === id) {
                        return { ...s, servicePrice, staffId: maybeString(staffId) };
                      }
                      return s;
                    }),
                  }
                : {
                    addons: p.addOns.map((s) => {
                      const { serviceId, staffId, servicePrice } = service;
                      if (s.addOnId === String(serviceId)) {
                        return { ...s, servicePrice, staffId: maybeString(staffId) };
                      }
                      return s;
                    }),
                  };
            return { ...p, ...newValue };
          }
          return p;
        });
      await update({ petId, repeatType, calcResolve, normalResolve });
    },
  };
};

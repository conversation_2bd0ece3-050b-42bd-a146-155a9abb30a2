import { type RepeatAppointmentModifyScope } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useStore } from 'amos';
import { useModifyServiceVariation } from '../../../components/ServiceApplicablePicker/hooks/useModifyServiceVariation';
import { type ServiceEntry } from '../../../components/ServiceApplicablePicker/types/serviceEntry';
import { updatePetService } from '../store/appt.api';
import {
  selectApptInfo,
  selectMainServiceInAppt,
  selectPetsInAppt,
  selectServiceListWithPet,
} from '../store/appt.selectors';
import { useCalcGroomingOnlySchedule } from './useCalcGroomingOnlySchedule';
import { useGetPetsInfoForApi } from './useGetPetsInfoForApi';
import { useIsSingleToMultiDaysApptCallback } from './useIsSingleToMultiDaysApptCallback';
import { useStartSameTimeInAddPet } from './useStartSameTimeInAddPet';

export interface AddServiceInExistApptParams {
  petId: string;
  serviceList: ServiceEntry[];
  serviceItemType: ServiceItemType;
  repeatType?: RepeatAppointmentModifyScope;
}

export const useAddServiceInExistAppt = (appointmentId: string) => {
  const dispatch = useDispatch();
  const store = useStore();
  const getPetsInfoForApi = useGetPetsInfoForApi();
  const calcGroomingOnlySchedule = useCalcGroomingOnlySchedule();
  const modifyServiceVariation = useModifyServiceVariation();
  const newStartAtSameTime = useStartSameTimeInAddPet(appointmentId);
  const isSingleToMultiDaysApptCallback = useIsSingleToMultiDaysApptCallback();

  return (params: AddServiceInExistApptParams) => {
    const { petId, serviceList, serviceItemType, repeatType } = params;

    const mainService = store.select(selectMainServiceInAppt(appointmentId));
    const {
      appointment: { startAtSameTime },
      customerId,
    } = store.select(selectApptInfo(appointmentId));

    const save = async () => {
      const isSingleToMultiDaysAppt = isSingleToMultiDaysApptCallback(appointmentId, serviceItemType);
      // single to multi days appointment, just update all pets services, otherwise, just update the current pet services
      const petDetailList = getPetsInfoForApi(appointmentId).filter(
        (item) => isSingleToMultiDaysAppt || item.petId === String(petId),
      );
      if (!petDetailList?.length) return;
      await dispatch(
        updatePetService({
          appointmentId,
          petDetails: petDetailList,
          repeatAppointmentModifyScope: repeatType,
        }),
      );
    };

    const saveWithAutoFill = async () => {
      const nextServices = serviceList.map((service) => {
        const staffId = Number(mainService.staffId);
        const nextVariation = modifyServiceVariation(service, staffId);
        return { ...service, ...nextVariation, staffId };
      });
      const pet = store.select(selectPetsInAppt(appointmentId)).find((p) => p.petId === petId);

      const petIdsServiceList = [
        {
          petId: Number(petId),
          serviceList: pet
            ? store.select(selectServiceListWithPet(pet, appointmentId)).concat(nextServices)
            : nextServices,
        },
      ];

      await calcGroomingOnlySchedule({
        allPetsStartAtSameTime: newStartAtSameTime || startAtSameTime,
        customerId,
        appointmentId,
        petIdsServiceList,
      });
      await save();
    };

    return { save, saveWithAutoFill };
  };
};

import React from 'react';
import { useFloatableHost } from '../../../utils/hooks/useFloatableHost';
import {
  TicketCreatedAlerts,
  type TicketCreatedAlertsProps,
} from '../../Calendar/Grooming/TicketCreatedAlerts/TicketCreatedAlerts';

export const useTicketAlerts = () => {
  const { mountModal } = useFloatableHost();

  return (props: TicketCreatedAlertsProps) => {
    const { closeFloatable } = mountModal(
      <TicketCreatedAlerts
        {...props}
        onClose={() => {
          props.onClose?.();
          closeFloatable();
        }}
      />,
    );
  };
};

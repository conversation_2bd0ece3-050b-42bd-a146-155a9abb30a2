import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useSelector } from 'amos';
import React from 'react';
import { serviceMapBox } from '../../../../store/service/service.boxes';
import { toNumber } from '../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { addApptPetEvaluations } from '../../store/appt.actions';
import { selectMainServiceInAppt, selectPetInAppt } from '../../store/appt.selectors';
import { type ApptPetEvaluationInfo } from '../../store/appt.types';
import { EvaluationCombined } from '../EvaluationCombined/EvaluationCombined';

export interface EditEvaluationCombinedProps {
  petId: string;
  associatedServiceIds?: string[];
  appointmentId: string;
}

export const EditEvaluationCombined: React.FC<EditEvaluationCombinedProps> = (props) => {
  const { petId, appointmentId, associatedServiceIds } = props;
  const [mainService, serviceMap, petInfo] = useSelector(
    selectMainServiceInAppt(appointmentId),
    serviceMapBox,
    selectPetInAppt(petId, appointmentId),
  );
  const isService = associatedServiceIds?.every(
    (id) => serviceMap.mustGetItem(toNumber(id)).type === ServiceType.SERVICE,
  );
  const dispatch = useDispatch();
  const isBoarding = mainService.serviceItemType === ServiceItemType.BOARDING;

  const onChangeEvaluation = useLatestCallback((evaluations: ApptPetEvaluationInfo[]) => {
    dispatch(addApptPetEvaluations(petId, evaluations, appointmentId));
  });

  // 该组件只用于 service，不考虑 addon
  if (!isService) {
    return null;
  }

  return (
    <EvaluationCombined
      appointmentId={appointmentId}
      petId={petId}
      associatedServiceIds={associatedServiceIds}
      startDate={mainService?.startDate || ''}
      startTime={mainService?.startTime || 0}
      isBoarding={isBoarding}
      onChange={onChangeEvaluation}
      prevEvaluations={petInfo?.evaluations}
    />
  );
};

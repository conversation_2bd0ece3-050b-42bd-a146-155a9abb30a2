import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, Form, TimePicker, cn, useForm, useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo, useEffect, useMemo } from 'react';
import { DrawerHeader } from '../../../../components/Drawer/DrawerHeader';
import { ApptTestIds } from '../../../../config/testIds/apptDrawer';
import { ScrollerProvider } from '../../../../layout/components/ScrollerProvider';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { type CombinedPetEvaluationPayload } from '../../../../store/calendarLatest/calendar.types';
import { type CombinedEvaluationCellData } from '../../../../store/evaluation/evaluation.types';
import { petMapBox } from '../../../../store/pet/pet.boxes';
import { isNormal, toNumber } from '../../../../store/utils/identifier';
import { useBusinessIsWorkingLocation } from '../../../../utils/BusinessUtil';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { EditPetAndServiceFooter } from '../../components/EditPetServiceList/EditPetAndServiceFooter';
import { useEvaluationAutoAssignStaff } from '../../hooks/useEvaluationAutoAssignStaff';
import { EditTicketPermissionTip } from '../../modules/ApptDetailDrawer/ApptInfoPanel/EditTicketPermissionTip';
import { selectApptPetInfo, selectMainServiceInAppt } from '../../store/appt.selectors';
import { EditEvaluationInfoCell } from './EditEvaluationInfoCell';

export interface EditEvaluationInfoPanelProps {
  isSubmitLoading?: boolean;
  payload: CombinedPetEvaluationPayload;
  CustomBlock?: React.ReactNode;
  isFinishedOrCancelled?: boolean;
}

export const EditEvaluationInfoPanel = memo((props: EditEvaluationInfoPanelProps) => {
  const { CustomBlock, payload, isFinishedOrCancelled } = props;
  const { petId, appointmentId, onConfirm, onCancel, onDelete } = payload;
  const [mainService, petInfo, business, pet] = useSelector(
    selectMainServiceInAppt(appointmentId),
    selectApptPetInfo(appointmentId),
    selectCurrentBusiness,
    petMapBox.mustGetItem(toNumber(petId)),
  );
  const evaluationForm = useForm<CombinedEvaluationCellData>();
  const [staffId, duration, startTime] = useWatch({
    control: evaluationForm.control,
    name: ['staffId', 'duration', 'startTime'],
  });
  const currentPetEvaluation = useMemo(() => {
    return petInfo?.pets.find((p) => p.petId === petId)?.evaluations?.[0];
  }, [petInfo, petId]);
  const isSubmitDisabled = !evaluationForm.formState.isDirty || !isNormal(currentPetEvaluation?.evaluationId);
  const isWorkingLocation = useBusinessIsWorkingLocation();

  useEffect(() => {
    evaluationForm.reset({
      startTime: dayjs(mainService.startDate).setMinutes(currentPetEvaluation?.startTime || 0),
      price: currentPetEvaluation?.price?.toString() || '0',
      duration: currentPetEvaluation?.duration?.toString() || '0',
      staffId: currentPetEvaluation?.staffId,
      lodgingId: currentPetEvaluation?.lodgingId,
    });
  }, [evaluationForm, currentPetEvaluation]);

  const handleSubmit = useSerialCallback(async () => {
    await evaluationForm.handleSubmit(async (value) => {
      await onConfirm?.(value);
    })();
  });

  const onStaffReady = useLatestCallback((staffId: string) => {
    evaluationForm.setValue('staffId', staffId, {
      shouldDirty: true,
    });
  });

  useEvaluationAutoAssignStaff({
    evaluationId: currentPetEvaluation?.evaluationId,
    defaultStaffId: currentPetEvaluation?.staffId,
    onStaffReady,
  });

  return (
    <div className={cn('moe-flex moe-flex-col moe-flex-1 moe-min-w-0 moe-min-h-0')}>
      <DrawerHeader title="Edit evaluation" onClick={onCancel} />
      <div className={'moe-flex-1 moe-flex moe-flex-col moe-min-w-0 moe-min-h-0'}>
        <ScrollerProvider className="moe-flex moe-flex-col moe-flex-1 moe-py-[20px] moe-px-[32px] moe-overflow-auto moe-min-w-0">
          <EditTicketPermissionTip>
            {(hasEditTicketPermission) => {
              const noPermissionEdit = !hasEditTicketPermission || !isWorkingLocation || isFinishedOrCancelled;
              return (
                <Form form={evaluationForm} footer={null}>
                  <Form.Item
                    name={'startTime'}
                    label="Start Time"
                    rules={{
                      required: {
                        value: true,
                        message: 'StartTime is required',
                      },
                    }}
                  >
                    <TimePicker
                      isDisabled={noPermissionEdit}
                      isRequired
                      data-testid={ApptTestIds.ApptEditScheduleTimeBtn}
                      isClearable={false}
                      format={business.timeFormat()}
                      minuteStep={5}
                    />
                  </Form.Item>

                  {mainService?.startDate && currentPetEvaluation?.evaluationId && (
                    <EditEvaluationInfoCell
                      form={evaluationForm}
                      evaluationId={currentPetEvaluation!.evaluationId}
                      petId={+petId}
                      startDate={mainService.startDate!}
                      startTime={startTime?.getMinutes() || 0}
                      duration={+duration!}
                      staffId={staffId}
                      noPermissionEdit={noPermissionEdit}
                    />
                  )}
                </Form>
              );
            }}
          </EditTicketPermissionTip>

          {CustomBlock}
        </ScrollerProvider>
        <EditPetAndServiceFooter
          LeftCustomBlock={
            <Button
              variant="tertiary"
              onPress={() => {
                AlertDialog.open({
                  variant: 'danger',
                  className: 'moe-w-[480px]',
                  title: 'Remove the evaluation?',
                  confirmText: 'Remove',
                  onConfirm: async () => {
                    await onDelete?.();
                  },
                  content: `Are you sure to remove the evaluation for ${pet.petName}? This action cannot be reversed.`,
                });
              }}
            >
              Remove evaluation
            </Button>
          }
          disabled={isSubmitDisabled}
          btnLoading={{
            primary: handleSubmit.isBusy(),
          }}
          onSubmit={handleSubmit}
          onCancel={onCancel}
        />
      </div>
    </div>
  );
});

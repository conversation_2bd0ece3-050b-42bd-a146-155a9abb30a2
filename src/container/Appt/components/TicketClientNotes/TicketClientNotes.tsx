import { useDispatch, useSelector } from 'amos';
import { Divider } from 'antd';
import classNames from 'classnames';
import React, { memo } from 'react';
import { useSetState } from 'react-use';
import { Condition } from '../../../../components/Condition';
import { customerMapBox } from '../../../../store/customer/customer.boxes';
import { addCustomerNote } from '../../../../store/customer/customerNote.actions';
import { customerNoteListBox, customerNoteMapBox } from '../../../../store/customer/customerNote.boxes';
import { jsonRecordMap } from '../../../../store/utils/utils';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { NoteInput } from '../../modules/ApptDetailDrawer/ApptTicketCommentPanel/NoteInput';
import { ClientNoteItem } from './ClientNoteItem';

export interface TicketClientNotesProps {
  className?: string;
  clientId: number;
  disabled?: boolean;
}

export const TicketClientNotes = memo<TicketClientNotesProps>(function TicketClientNotes(props) {
  const { className, clientId, disabled } = props;
  const dispatch = useDispatch();
  const [state, setState] = useSetState({
    notes: '',
  });
  const [noteList, noteMap, client] = useSelector(
    customerNoteListBox.getList(clientId),
    customerNoteMapBox,
    customerMapBox.mustGetItem(clientId),
  );
  const list = jsonRecordMap(noteMap, noteList);
  const onCreateClientNotes = useSerialCallback(async () => {
    await dispatch(addCustomerNote({ customerId: clientId, note: state.notes }));
    setState({ notes: '' });
  });

  return (
    <div className={classNames(className, 'moe-h-full moe-overflow-auto moe-flex moe-flex-col moe-gap-y-[20px]')}>
      <NoteInput
        disabled={disabled}
        loading={onCreateClientNotes.isBusy()}
        value={state.notes}
        onChange={(notes) => setState({ notes })}
        onCancel={() => setState({ notes: '' })}
        onSave={onCreateClientNotes}
        placeholder={`Leave a note for ${client.firstName}...`}
      />
      <div className="moe-flex moe-flex-col moe-gap-y-[16px]">
        {list.map((item, index) => {
          const isLast = index === list.length - 1;
          return (
            <React.Fragment key={item.customerNoteId}>
              <ClientNoteItem customerNoteId={item.customerNoteId} />
              <Condition if={!isLast}>
                <Divider dashed className="!moe-m-0" />
              </Condition>
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
});

import { useDispatch, useSelector } from 'amos';
import { Popconfirm } from 'antd';
import classNames from 'classnames';
import React, { memo, useRef } from 'react';
import { useSetState } from 'react-use';
import SvgIconDeleteNewSvg from '../../../../assets/svg/icon-delete-new.svg';
import SvgIconEditNewSvg from '../../../../assets/svg/icon-edit-new.svg';
import { alertApi } from '../../../../components/Alert/AlertApi';
import { Condition } from '../../../../components/Condition';
import { SvgIcon } from '../../../../components/Icon/Icon';
import { Switch } from '../../../../components/SwitchCase';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { computeNoteInfo } from '../../../../store/customer/customer.selectors';
import { removeCustomerNote, updateCustomerNote } from '../../../../store/customer/customerNote.actions';
import { type CustomerNoteRecord, customerNoteMapBox } from '../../../../store/customer/customerNote.boxes';
import { selectCurrentStaff } from '../../../../store/staff/staff.selectors';
import { useBusinessIsWorkingLocation } from '../../../../utils/BusinessUtil';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { NoteInput } from '../../modules/ApptDetailDrawer/ApptTicketCommentPanel/NoteInput';

export interface ClientNoteItemProps {
  className?: string;
  customerNoteId: number;
}

export const ClientNoteItem = memo<ClientNoteItemProps>(function NoteItem(props) {
  const { className, customerNoteId } = props;
  const [state, setState] = useSetState<{ editNote: CustomerNoteRecord | null; notes: string }>({
    editNote: null,
    notes: '',
  });
  const ref = useRef(null);
  const dispatch = useDispatch();
  const [note, staff, business] = useSelector(
    customerNoteMapBox.mustGetItem(customerNoteId),
    selectCurrentStaff,
    selectCurrentBusiness,
  );
  const isWorkingLocation = useBusinessIsWorkingLocation();

  const handleDelete = useSerialCallback(async () => {
    await dispatch(removeCustomerNote(note.customerNoteId));
    alertApi.warn('Client note deleted successfully!');
  });

  const onSaveEditNotes = useSerialCallback(async () => {
    if (!state.editNote) {
      return;
    }
    await dispatch(updateCustomerNote({ id: state.editNote.customerNoteId, note: state.notes }));
    setState({ editNote: null, notes: '' });
  });

  return (
    <Switch>
      <Switch.Case if={!!state.editNote}>
        <NoteInput
          allWaysShowSave
          loading={onSaveEditNotes.isBusy()}
          value={state.notes}
          onChange={(notes) => setState({ notes })}
          onCancel={() => setState({ notes: '', editNote: null })}
          onSave={onSaveEditNotes}
        />
      </Switch.Case>
      <Switch.Case else>
        <div
          ref={ref}
          className={classNames(
            className,
            'moe-group moe-flex moe-flex-col moe-gap-y-[4px] hover:moe-bg-[#F2F3F6] moe-cursor-pointer moe-p-[8px] moe-rounded-[8px]',
          )}
        >
          <div className="moe-text-[14px] moe-leading-[18px] moe-text-[#333] moe-font-medium moe-whitespace-pre-wrap">
            {note.note}
          </div>
          <div className="moe-flex moe-items-center moe-justify-between">
            <div className="moe-text-xs moe-text-[#999] moe-font-medium">
              <div>
                {computeNoteInfo(
                  'Created',
                  note.createTime,
                  staff.id === note.accountId ? 'you' : note.accountName,
                  business,
                ).join(' ')}
              </div>
              <Condition if={note.updateTime !== note.createTime}>
                <div>
                  {computeNoteInfo(
                    'Last edit',
                    note.updateTime,
                    staff.id === note.lastAccountId ? 'you' : note.lastAccountName,
                    business,
                  ).join(' ')}
                </div>
              </Condition>
            </div>
            <Condition if={isWorkingLocation}>
              <div className="moe-flex moe-items-center moe-h-[20px]">
                <SvgIcon
                  src={SvgIconEditNewSvg}
                  size={18}
                  onClick={() => {
                    setState({ editNote: note, notes: note.note });
                  }}
                  color="#666"
                  className="hover:!moe-text-brand group-hover:!moe-block !moe-hidden"
                />
                <Popconfirm
                  title="Are you sure to delete this note?"
                  onConfirm={async () => {
                    await handleDelete();
                  }}
                  okText="Yes"
                  okType="danger"
                  cancelText="No"
                  placement="topRight"
                  getPopupContainer={() => ref.current!}
                >
                  <SvgIcon
                    src={SvgIconDeleteNewSvg}
                    size={20}
                    color="#666"
                    className="group-hover:!moe-block !moe-hidden"
                  />
                </Popconfirm>
              </div>
            </Condition>
          </div>
        </div>
      </Switch.Case>
    </Switch>
  );
});

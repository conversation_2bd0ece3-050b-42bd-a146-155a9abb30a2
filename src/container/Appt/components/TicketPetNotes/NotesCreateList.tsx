import { useDispatch, useSelector } from 'amos';
import { Divider } from 'antd';
import classNames from 'classnames';
import React, { memo } from 'react';
import { useSetState } from 'react-use';
import { Condition } from '../../../../components/Condition';
import { petMapBox } from '../../../../store/pet/pet.boxes';
import { addPetNote } from '../../../../store/pet/petNote.actions';
import { petNoteListBox, petNoteMapBox } from '../../../../store/pet/petNote.boxes';
import { jsonRecordMap } from '../../../../store/utils/utils';
import { useBusinessIsWorkingLocation } from '../../../../utils/BusinessUtil';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { NoteInput } from '../../modules/ApptDetailDrawer/ApptTicketCommentPanel/NoteInput';
import { NoteItem } from './NoteItem';

export interface NotesCreateListProps {
  petId: number;
  className?: string;
  label?: string;
}

export const NotesCreateList = memo<NotesCreateListProps>(function NotesCreateList(props) {
  const { className, petId, label } = props;
  const dispatch = useDispatch();
  const [noteList, noteMap, pet] = useSelector(
    petNoteListBox.getList(petId),
    petNoteMapBox,
    petMapBox.mustGetItem(petId),
  );
  const list = jsonRecordMap(noteMap, noteList);
  const [state, setState] = useSetState({
    notes: '',
  });

  const onCreatePetNotes = useSerialCallback(async () => {
    await dispatch(addPetNote({ petId, note: state.notes }));
    setState({ notes: '' });
  });

  const isWorkingLocation = useBusinessIsWorkingLocation();

  return (
    <div className={classNames(className)}>
      <NoteInput
        label={label}
        loading={onCreatePetNotes.isBusy()}
        value={state.notes}
        onChange={(notes) => setState({ notes })}
        onCancel={() => setState({ notes: '' })}
        onSave={onCreatePetNotes}
        placeholder={`Leave a pet note for ${pet.petName}...`}
        disabled={!isWorkingLocation}
        className="moe-mb-s"
      />
      <div className="moe-flex moe-flex-col moe-gap-y-[16px]">
        {list.map((item, index) => {
          const isLast = index === list.length - 1;
          return (
            <React.Fragment key={item.petNoteId}>
              <NoteItem petNoteId={item.petNoteId} />
              <Condition if={!isLast}>
                <Divider dashed className="!moe-m-0" />
              </Condition>
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
});

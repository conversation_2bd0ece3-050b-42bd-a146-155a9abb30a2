import { MajorEditOutlined, MajorPinOutlined, Major<PERSON>rashOutlined, MinorPinFilled } from '@moego/icons-react';
import { Tooltip, cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { Popconfirm } from 'antd';
import classNames from 'classnames';
import React, { memo, useRef } from 'react';
import { useSetState } from 'react-use';
import { alertApi } from '../../../../components/Alert/AlertApi';
import { Condition } from '../../../../components/Condition';
import { Switch } from '../../../../components/SwitchCase';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { computeNoteInfo } from '../../../../store/customer/customer.selectors';
import { pinPetNote, removePetNote, unpinPetNote, updatePetNote } from '../../../../store/pet/petNote.actions';
import { type PetNoteRecord, petNoteMapBox } from '../../../../store/pet/petNote.boxes';
import { selectCurrentStaff } from '../../../../store/staff/staff.selectors';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { NoteInput } from '../../modules/ApptDetailDrawer/ApptTicketCommentPanel/NoteInput';

export interface NoteItemProps {
  className?: string;
  petNoteId: number;
}

const PET_NOTE_ICON_CLASS =
  'moe-w-[24px] !moe-h-[24px] moe-p-[2px] moe-ml-xs moe-rounded-[1000px] moe-text-gray-900 moe-bg-neutral-sunken-0 hover:moe-bg-neutral-sunken-1';
const HOVER_EFFECT_CLASS = 'moe-invisible group-hover:!moe-visible';

export const NoteItem = memo<NoteItemProps>(function NoteItem(props) {
  const { className, petNoteId } = props;
  const [state, setState] = useSetState<{ editNote: PetNoteRecord | null; notes: string }>({
    editNote: null,
    notes: '',
  });
  const ref = useRef(null);
  const dispatch = useDispatch();
  const [note, staff, business] = useSelector(
    petNoteMapBox.mustGetItem(petNoteId),
    selectCurrentStaff,
    selectCurrentBusiness,
  );
  const isPinned = note.isPinned;

  const handleDelete = useSerialCallback(async () => {
    await dispatch(removePetNote(note.petNoteId));
    alertApi.warn('Pet note deleted successfully!');
  });

  const onSaveEditNotes = useSerialCallback(async () => {
    if (!state.editNote) {
      return;
    }
    await dispatch(updatePetNote({ id: state.editNote.petNoteId, note: state.notes }));
    setState({ editNote: null, notes: '' });
  });

  return (
    <Switch>
      <Switch.Case if={!!state.editNote}>
        <NoteInput
          allWaysShowSave
          loading={onSaveEditNotes.isBusy()}
          value={state.notes}
          onChange={(notes) => setState({ notes })}
          onCancel={() => setState({ notes: '', editNote: null })}
          onSave={onSaveEditNotes}
        />
      </Switch.Case>
      <Switch.Case else>
        <div
          ref={ref}
          className={classNames(
            className,
            'moe-group moe-flex moe-gap-x-[8px] moe-items-start  moe-gap-y-[4px] hover:moe-bg-neutral-sunken-light moe-cursor-pointer moe-p-[8px] moe-rounded-[8px] moe-flex-1',
          )}
        >
          <Condition if={isPinned}>
            <MinorPinFilled className="!moe-w-[20px] !moe-h-[20px] moe-text-gray-400" />
          </Condition>
          <div className="moe-flex-1 moe-relative">
            <div className="moe-text-[14px] moe-leading-[18px] moe-text-[#333] moe-font-medium moe-whitespace-pre-wrap">
              <div>{note.note}</div>
              <div className="moe-text-xs moe-text-[#999] moe-font-medium moe-mt-[4px]">
                <div>
                  {computeNoteInfo(
                    'Created',
                    note.createTime,
                    staff.id === note.accountId ? 'you' : note.accountName,
                    business,
                  ).join(' ')}
                </div>
                <Condition if={note.updateTime !== note.createTime}>
                  <div>
                    {computeNoteInfo(
                      'Last edit',
                      note.updateTime,
                      staff.id === note.lastAccountId ? 'you' : note.lastAccountName,
                      business,
                    ).join(' ')}
                  </div>
                </Condition>
              </div>
            </div>
            <div className="moe-absolute moe-top-[50%] moe-translate-y-[-50%] moe-right-[8px] moe-flex moe-items-center moe-justify-between">
              <div className="moe-flex moe-items-center moe-h-[24px]">
                <Tooltip side="top" align="center" content={isPinned ? 'Remove pin' : 'Pin to top'}>
                  {isPinned ? (
                    <MinorPinFilled
                      className={cn(PET_NOTE_ICON_CLASS, HOVER_EFFECT_CLASS, 'moe-text-gray-400')}
                      onClick={() => {
                        dispatch(unpinPetNote({ id: String(note.petNoteId), isPinned: false }));
                      }}
                    />
                  ) : (
                    <MajorPinOutlined
                      className={cn(PET_NOTE_ICON_CLASS, HOVER_EFFECT_CLASS)}
                      onClick={() => {
                        dispatch(pinPetNote({ id: String(note.petNoteId), isPinned: true }));
                      }}
                    />
                  )}
                </Tooltip>
                <MajorEditOutlined
                  className={cn(PET_NOTE_ICON_CLASS, HOVER_EFFECT_CLASS)}
                  onClick={() => {
                    setState({ editNote: note, notes: note.note });
                  }}
                />
                <Popconfirm
                  title="Are you sure to delete this note?"
                  onConfirm={async () => {
                    await handleDelete();
                  }}
                  okText="Yes"
                  okType="danger"
                  cancelText="No"
                  placement="topRight"
                  getPopupContainer={() => ref.current!}
                >
                  <MajorTrashOutlined className={cn(PET_NOTE_ICON_CLASS, HOVER_EFFECT_CLASS)} />
                </Popconfirm>
              </div>
            </div>
          </div>
        </div>
      </Switch.Case>
    </Switch>
  );
});

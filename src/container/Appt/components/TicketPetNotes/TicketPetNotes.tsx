import { useDispatch, useSelector } from 'amos';
import classNames from 'classnames';
import React, { memo, useEffect, useMemo } from 'react';
import { useSetState } from 'react-use';
import { PetInfoRender } from '../../../../components/PetInfo/PetInfoRender';
import { useClientPets } from '../../../../components/PetInfo/hooks/useClientPets';
import { Switch } from '../../../../components/SwitchCase';
import { getAllClientInfo } from '../../../../store/customer/customer.actions';
import { petMapBox } from '../../../../store/pet/pet.boxes';
import { isNormal } from '../../../../store/utils/identifier';
import { NotesCreateList } from './NotesCreateList';

export interface TicketPetNotesProps {
  className?: string;
  clientId: number;
  defaultPetId?: number;
  filterPets?: number[];
}

export const TicketPetNotes = memo<TicketPetNotesProps>(function TicketPetNotes(props) {
  const { className, clientId, defaultPetId, filterPets } = props;
  const clientAlivePets = useClientPets(clientId, { filterAlive: false });
  const dispatch = useDispatch();
  const [petMap] = useSelector(petMapBox);
  const clientAlivePetsSorted = useMemo(() => {
    const hasFilterPets = Array.isArray(filterPets);
    const list = clientAlivePets.filter((id) => (hasFilterPets ? filterPets.includes(id) : true));
    if (hasFilterPets) {
      list.sort((a, b) => filterPets.indexOf(a) - filterPets.indexOf(b));
    }
    return list;
  }, [clientAlivePets, filterPets]);

  useEffect(() => {
    clientId && dispatch(getAllClientInfo(clientId));
  }, [clientId]);

  const [state, setState] = useSetState({
    activePetId: isNormal(defaultPetId) ? defaultPetId : clientAlivePetsSorted[0],
    notes: '',
  });

  const label = useMemo(() => {
    const { activePetId } = state;
    if (isNormal(activePetId)) {
      const { petName } = petMap.mustGetItem(activePetId);
      return `${petName}'s notes`;
    }
    return undefined;
  }, [state, petMap]);

  const petSize = clientAlivePetsSorted.length;

  if (!petSize) {
    return null;
  }

  return (
    <div
      className={classNames(
        className,
        'moe-w-[446px] moe-h-full moe-overflow-auto moe-flex moe-flex-col moe-gap-y-[20px]',
      )}
    >
      <Switch>
        <Switch.Case if={petSize > 1}>
          <div
            className={classNames(
              'moe-self-start moe-flex moe-items-center moe-bg-[#F2F3F6] moe-gap-x-[4px] moe-px-[4px] moe-py-[4px] moe-rounded-[56px] moe-select-none moe-whitespace-nowrap',
              className,
            )}
          >
            {clientAlivePetsSorted.map((petId) => {
              const isActive = petId === state.activePetId;
              return (
                <PetInfoRender key={petId} petId={petId}>
                  {({ pet }) => (
                    <div
                      key={petId}
                      className={classNames(
                        'moe-px-[11px] moe-py-[2px] moe-text-[14px] moe-leading-[20px] moe-text-[#333] moe-font-bold moe-cursor-pointer moe-rounded-[56px]',
                        isActive ? 'moe-bg-[#fff]' : 'moe-bg-transparent',
                      )}
                      onClick={() => setState({ activePetId: petId })}
                    >
                      <div className="moe-truncate moe-max-[200px]">{pet.petName}</div>
                    </div>
                  )}
                </PetInfoRender>
              );
            })}
          </div>
        </Switch.Case>
      </Switch>
      <NotesCreateList petId={state.activePetId} label={label} />
    </div>
  );
});

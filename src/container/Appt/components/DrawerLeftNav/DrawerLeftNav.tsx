import { cn } from '@moego/ui';
import React from 'react';
import { DrawerLeftNavItem, type DrawerLeftNavItemProps } from './DrawerLeftNavItem';

export interface DrawerLeftNavProps<T> {
  className?: string;
  options: Array<DrawerLeftNavItemProps<T>>;
  value: T;
  onChange?: (value: T) => void;
}

export const DrawerLeftNav = <T extends string | number>(props: DrawerLeftNavProps<T>) => {
  const { className, value, onChange, options } = props;
  return (
    <div className={cn(className, 'moe-w-[86px]   moe-flex-shrink-0')} style={{ borderRight: '1px solid #E6E6E6' }}>
      <div className="moe-flex moe-flex-col moe-items-stretch moe-gap-y-[20px] moe-pt-[32px]">
        {options.map((tab) => {
          const { label, icon, isDisabled } = tab;
          const isActive = tab.value === value;

          return (
            <DrawerLeftNavItem
              key={tab.value}
              value={tab.value}
              icon={icon}
              label={label}
              isActive={isActive}
              isDisabled={isDisabled}
              onClick={() => {
                onChange?.(tab.value);
              }}
            />
          );
        })}
      </div>
    </div>
  );
};

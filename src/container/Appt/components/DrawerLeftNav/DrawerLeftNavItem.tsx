import { cn } from '@moego/ui';
import React from 'react';
import { SvgIcon } from '../../../../components/Icon/Icon';

export interface DrawerLeftNavItemProps<T = string | number> {
  value: T;
  icon: React.ReactElement | string;
  label: string;
  isActive?: boolean;
  isDisabled?: boolean;
  onClick?: () => void;
}

export const DrawerLeftNavItem = (props: DrawerLeftNavItemProps) => {
  const { icon, isActive, isDisabled, label, onClick } = props;

  return (
    <div
      className={cn(
        'moe-flex moe-flex-col moe-items-center moe-gap-y-[4px] moe-text-center',
        isDisabled ? 'moe-cursor-not-allowed' : 'moe-cursor-pointer',
      )}
      onClick={() => !isDisabled && onClick?.()}
    >
      <div
        className={cn(
          'moe-w-[40px] moe-h-[40px] moe-flex moe-justify-center moe-items-center moe-rounded-full',
          isActive ? 'moe-bg-brand-bold' : '',
        )}
      >
        {typeof icon === 'string' ? (
          <SvgIcon src={icon} size={24} color={isDisabled ? '#B4B4B4' : isActive ? '#FFF' : '#333'} />
        ) : (
          React.cloneElement(icon, {
            className: cn(
              icon.props.className,
              isDisabled ? 'moe-text-[#B4B4B4]' : isActive ? 'moe-text-[#FFF]' : 'moe-text-[#333]',
            ),
          })
        )}
      </div>
      <div
        className={cn(
          'moe-text-xs moe-font-medium moe-select-none',
          isDisabled ? 'moe-text-[#B4B4B4]' : isActive ? 'moe-text-brand' : 'moe-text-[#333]',
        )}
      >
        {label.split('\u{160}').map((item, index) => {
          return <div key={index}>{item}</div>;
        })}
      </div>
    </div>
  );
};

import { Button, Form, Input, Upload, type UploadProps, UploadStatus, useForm, useWatch } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { memo, useEffect } from 'react';
import { Condition } from '../../../../../components/Condition';
import { generateUID } from '../../../../../components/Upload/MoeGoUIUpload';
import { type PetBelongsRecord } from '../../../../../store/pet/petBelongs.boxes';
import { isNormal } from '../../../../../store/utils/identifier';
import { type SerialCallback, useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { DrawerFooter } from '../../../../Calendar/latest/AwesomeCalendar.style';
import { uploadFiles } from '../../../../settings/Settings/ServicesSetting/utils/uploadFiles';
import { MAX_PET_BELONGINGS_LENGTH } from './utils';

export interface BelongingsFormProps {
  belongings: Partial<PetBelongsRecord>;
  petId: number;
  onSave: SerialCallback<(values: Partial<PetBelongsRecord>) => Promise<void>>;
  onCancel: () => void;
}

export const BelongingsForm = memo<BelongingsFormProps>(function TicketPetNotes(props) {
  const { petId, belongings, onSave, onCancel } = props;
  const belongingsForm = useForm<Partial<PetBelongsRecord>>({ mode: 'onSubmit' });
  const dispatch = useDispatch();
  const isEdit = isNormal(belongings?.petBelongingId);
  const [currentNameField] = useWatch({ control: belongingsForm.control, name: ['name'] });

  useEffect(() => {
    belongingsForm.reset({
      name: belongings?.name || '',
      area: belongings?.area || '',
      imgList: belongings?.photoUrl
        ? [
            {
              url: belongings?.photoUrl,
              uid: generateUID(0),
              status: UploadStatus.success,
            },
          ]
        : [],
    });
  }, [belongings, petId]);

  const handleUploadFiles: UploadProps['customRequest'] = (options) => {
    uploadFiles(dispatch, options);
  };

  const handleSaveClick = useSerialCallback(async () => {
    const submit = belongingsForm.handleSubmit(async (values) => {
      await onSave(values);
    });

    await submit();
  });

  return (
    <div className="moe-h-full moe-flex moe-flex-col moe-justify-between moe-flex-1">
      <Form form={belongingsForm} footer={null}>
        <Form.Item name="name" label="Belonging name" rules={{ required: true }}>
          <Input.TextArea
            isRequired
            maxLength={MAX_PET_BELONGINGS_LENGTH}
            showCount
            placeholder="e.g. Treats, toys, blankets"
          />
        </Form.Item>
        <Form.Item name="area" label="Area">
          <Input className="moe-w-full" maxLength={50} placeholder="e.g. Locker 1" />
        </Form.Item>
        <Form.Item name="imgList" label="Photo">
          <Upload
            customRequest={handleUploadFiles}
            enableDrop
            isMultiple
            maxCount={1}
            uploadDescription="Click or drag"
            variant="image-card"
          />
        </Form.Item>
      </Form>
      <DrawerFooter className="border !moe-px-[0] !moe-pb-[0] !moe-border-none">
        <Condition if={isEdit}>
          <Button variant="secondary" className="moe-flex-1" onPress={onCancel}>
            Cancel
          </Button>
        </Condition>
        <Button
          variant="primary"
          className="moe-flex-1"
          isDisabled={
            !(belongingsForm.formState.isDirty && Object.keys(belongingsForm.formState.errors).length === 0) ||
            !currentNameField
          }
          isLoading={handleSaveClick.isBusy()}
          onPress={handleSaveClick}
        >
          Save
        </Button>
      </DrawerFooter>
    </div>
  );
});

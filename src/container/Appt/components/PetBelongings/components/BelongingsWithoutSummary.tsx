import { Input, Upload, type UploadProps, UploadStatus } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { memo, useEffect } from 'react';
import { generateUID } from '../../../../../components/Upload/MoeGoUIUpload';
import { type PetBelongsRecord } from '../../../../../store/pet/petBelongs.boxes';
import { usePropsState } from '../../../../../utils/hooks/usePropsState';
import { uploadFiles } from '../../../../settings/Settings/ServicesSetting/utils/uploadFiles';
import { MAX_PET_BELONGINGS_LENGTH } from './utils';

export interface BelongingsDetailProps {
  petId: number;
  belongings: Partial<PetBelongsRecord>;
  onEdit: (value: Partial<PetBelongsRecord>) => Promise<void>;
}

export const BelongingsWithoutSummary = memo<BelongingsDetailProps>((props) => {
  const { belongings, petId, onEdit } = props;
  const dispatch = useDispatch();

  const [state, setState] = usePropsState(belongings || {}, [petId]);

  const handleUploadFiles: UploadProps['customRequest'] = (options) => {
    uploadFiles(dispatch, options);
  };

  const handleUpdate = (params: Partial<PetBelongsRecord>) => {
    setState({
      ...state,
      ...params,
    });
    onEdit({
      ...params,
      petId,
    });
  };

  // pet id 变化的时候，重新设置 belongings
  useEffect(() => {
    onEdit({
      ...belongings,
      petId,
    });
  }, [petId, belongings]);

  return (
    <>
      <Input.TextArea
        isRequired
        label="Belonging name"
        maxLength={MAX_PET_BELONGINGS_LENGTH}
        showCount
        // @text-lint ignore
        placeholder="e.g. Treats, toys, blankets"
        onChange={(val) => handleUpdate({ name: val.trimStart() })}
        value={state.name || ''}
      />
      <Input
        label="Area"
        className="moe-w-full moe-mt-6"
        maxLength={50}
        // @text-lint ignore
        placeholder="e.g. Locker 1"
        value={state.area || ''}
        onChange={(val) => handleUpdate({ area: val })}
      />
      <Upload
        label="Photo"
        className="moe-mt-6"
        onChange={([val] = []) => handleUpdate({ photoUrl: val?.url })}
        customRequest={handleUploadFiles}
        enableDrop
        isMultiple
        maxCount={1}
        uploadDescription="Click or drag"
        variant="image-card"
        value={
          state.photoUrl
            ? [
                {
                  url: state.photoUrl,
                  uid: generateUID(0),
                  status: UploadStatus.success,
                },
              ]
            : []
        }
      />
    </>
  );
});

import { MinorEditOutlined, MinorTrashOutlined } from '@moego/icons-react';
import { AlertDialog, IconButton, Spin, Text } from '@moego/ui';
import React, { memo, useEffect } from 'react';
import { Condition } from '../../../../../components/Condition';
import { Switch } from '../../../../../components/SwitchCase';
import { toastApi } from '../../../../../components/Toast/Toast';
import { type PetBelongsRecord } from '../../../../../store/pet/petBelongs.boxes';
import { isNormal } from '../../../../../store/utils/identifier';
import { useBool } from '../../../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { BelongingsForm } from './BelongingsForm';

export interface BelongingsWithSummaryProps {
  petId: number;
  belongings: Partial<PetBelongsRecord>;
  appointmentId?: number;
  onRemove?: (value: { petBelongingId: number; appointmentId: number; petId: number }) => Promise<void>;
  onEdit: (value: Partial<PetBelongsRecord>) => Promise<void>;
  onAdd?: (values: Partial<PetBelongsRecord> & { appointmentId: number; petId: number }) => Promise<void>;
}

export const BelongingsWithSummary = memo<BelongingsWithSummaryProps>((props) => {
  const { belongings, petId, appointmentId, onRemove, onEdit, onAdd } = props;
  const { name, area, photoUrl, petBelongingId } = belongings || {};
  const isEmpty = !isNormal(petBelongingId);
  const isEditingState = useBool();
  const doubleCheckModalVisible = useBool();

  const handleRemove = useSerialCallback(async () => {
    if (petBelongingId && appointmentId && petId) {
      await onRemove?.({ petBelongingId, appointmentId, petId });
      toastApi.success('Delete successfully');
    }
  });

  const handleSaveForm = useSerialCallback(async (values) => {
    const params = {
      ...values,
      photoUrl: values.imgList?.[0]?.url,
    };

    if (isEmpty) {
      await onAdd?.({ ...params, appointmentId, petId });
    } else {
      await onEdit({ ...params, petBelongingId });
    }
    isEditingState.close();
    toastApi.success('Update successfully');
  });

  useEffect(() => {
    isEditingState.close();
    doubleCheckModalVisible.close();
  }, [belongings, petId, appointmentId]);

  const belongingsName = name?.split('\n').map((item, index) => (
    <span key={index}>
      {item}
      <br />
    </span>
  ));

  return (
    <Spin
      classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }}
      isLoading={handleSaveForm.isBusy()}
    >
      <Switch>
        <Switch.Case if={!isEmpty && !isEditingState.value}>
          <div className="moe-flex moe-items-center moe-justify-between moe-p-4 moe-rounded-[20px] moe-border-[#CDCDCD] moe-border-[solid] moe-border-[1px]">
            <div className=" moe-flex moe-items-center moe-mr-[54px]">
              <Condition if={photoUrl}>
                <img
                  className="moe-w-[48px] moe-h-[48px] moe-rounded-8px-50 moe-object-cover moe-mr-4"
                  src={photoUrl}
                />
              </Condition>
              <div>
                <Text variant="regular">{belongingsName}</Text>
                <Condition if={area}>
                  <Text variant="small" className="moe-text-tertiary">
                    {area}
                  </Text>
                </Condition>
              </div>
            </div>
            <div className="moe-flex moe-gap-x-[16px]">
              <IconButton
                icon={<MinorTrashOutlined />}
                tooltip="Delete"
                onPress={doubleCheckModalVisible.open}
              ></IconButton>
              <IconButton icon={<MinorEditOutlined />} tooltip="Edit" onPress={isEditingState.open}></IconButton>
            </div>
          </div>
        </Switch.Case>
        <Switch.Case else>
          <BelongingsForm
            petId={petId}
            belongings={belongings}
            onSave={handleSaveForm}
            onCancel={isEditingState.close}
          />
        </Switch.Case>
      </Switch>
      <AlertDialog
        variant="danger"
        size="s"
        title="Delete pet belonging(s)"
        isOpen={doubleCheckModalVisible.value}
        onClose={doubleCheckModalVisible.close}
        onConfirm={handleRemove}
        confirmText="Delete"
      >
        <Text variant="regular">Are you sure to delete this pet’s belonging(s)?</Text>
      </AlertDialog>
    </Spin>
  );
});

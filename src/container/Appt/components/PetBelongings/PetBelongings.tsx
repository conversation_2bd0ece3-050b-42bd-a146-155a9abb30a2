import { SegmentControl, Spin } from '@moego/ui';
import React, { memo, useState } from 'react';
import { Condition } from '../../../../components/Condition';
import { Switch } from '../../../../components/SwitchCase';
import { type PetRecord } from '../../../../store/pet/pet.boxes';
import { type PetBelongsRecord } from '../../../../store/pet/petBelongs.boxes';
import { BelongingsWithSummary } from './components//BelongingsWithSummary';
import { BelongingsWithoutSummary } from './components/BelongingsWithoutSummary';

export interface PetBelongingsProps {
  className?: string;
  defaultPetId: number;
  appointmentId?: number;
  isBusy?: boolean;
  belongsMap: Record<number, Partial<PetBelongsRecord>[]>;
  pets: PetRecord[];
  onRemove?: (value: { petBelongingId: number; appointmentId: number; petId: number }) => Promise<void>;
  onEdit: (value: Partial<PetBelongsRecord>) => Promise<void>;
  onAdd?: (values: Partial<PetBelongsRecord> & { appointmentId: number; petId: number }) => Promise<void>;
  withoutSummary?: boolean;
}

export const PetBelongings = memo<PetBelongingsProps>((props) => {
  const { defaultPetId, appointmentId, pets, isBusy, belongsMap, onRemove, onEdit, onAdd, withoutSummary } = props;
  const [activePetId, setActivePetId] = useState(defaultPetId?.toString());
  const [belongings] = belongsMap[Number(activePetId)] || [];

  return (
    <Spin classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }} isLoading={isBusy}>
      <div className="moe-h-full moe-flex moe-flex-col moe-pb-0">
        <Condition if={pets?.length > 1}>
          <div className="moe-mb-8px-300">
            <SegmentControl value={activePetId} onChange={setActivePetId} className="moe-mt-8px-100">
              {pets.map((pet) => (
                <SegmentControl.Item value={pet.petId.toString()} label={pet.petName} key={pet.petId.toString()} />
              ))}
            </SegmentControl>
          </div>
        </Condition>
        <Switch>
          <Switch.Case if={!withoutSummary}>
            <BelongingsWithSummary
              petId={Number(activePetId)}
              belongings={belongings}
              appointmentId={appointmentId}
              onRemove={onRemove}
              onEdit={onEdit}
              onAdd={onAdd}
            />
          </Switch.Case>
          <Switch.Case else>
            <BelongingsWithoutSummary onEdit={onEdit} belongings={belongings} petId={Number(activePetId)} />
          </Switch.Case>
        </Switch>
      </div>
    </Spin>
  );
});

import { cn, Scroll, Tabs } from '@moego/ui';
import React, { memo, useState } from 'react';
import { FlexGrowFixedHeightBox } from '../../../components/FlexGrowFixedHeightBox';
import { CommentNoteType } from '../../../store/calendarLatest/calendar.utils';
import { useBusinessIsWorkingLocation } from '../../../utils/BusinessUtil';
import { ClientGroomingNotesAndHistory } from '../modules/QuickAddAppt/components/ClientGroomingNotesAndHistory';
import { type SubmitQuickAddParams } from '../modules/QuickAddAppt/hooks/useSubmitQuickAdd';
import { DrawerHead } from './DrawerHead/DrawerHead';
import { TicketClientNotes } from './TicketClientNotes/TicketClientNotes';
import { TicketPetNotes } from './TicketPetNotes/TicketPetNotes';

export interface CommentsProps extends SubmitQuickAddParams {
  clientId: number;
  petId?: number;
  activeTab?: CommentNoteType;
  comments?: string;
  onCommentsChange?: (value: string) => void;
}

export const Comments = memo<CommentsProps>((props) => {
  const { clientId, petId, comments = '', activeTab: defaultActiveTab, onCommentsChange } = props;
  const [activeTab, setState] = useState<CommentNoteType | undefined>(defaultActiveTab);

  const isWorkingLocation = useBusinessIsWorkingLocation();

  const tabListInfo = [
    {
      key: CommentNoteType.TicketComments,
      label: 'Ticket comments',
      tabClassName: 'moe-w-[130px]',
      element: (
        <ClientGroomingNotesAndHistory
          ticketComments={comments}
          onTicketCommentsChange={onCommentsChange}
          clientId={clientId}
        />
      ),
    },
    {
      key: CommentNoteType.PetNotes,
      label: 'Pet notes',
      tabClassName: 'moe-w-[72px]',
      element: <TicketPetNotes clientId={clientId} defaultPetId={petId} />,
    },
    {
      key: CommentNoteType.ClientNotes,
      label: 'Client notes',
      tabClassName: 'moe-w-[92px]',
      element: <TicketClientNotes clientId={clientId} disabled={!isWorkingLocation} />,
    },
  ];

  return (
    <div className={cn('moe-flex-1 moe-min-w-0 moe-min-h-0 moe-h-full moe-flex moe-flex-col moe-overflow-auto')}>
      <DrawerHead title="Comments & Notes" />
      <div className="moe-flex-1 moe-pt-[24px]">
        <Tabs
          onChange={(activeKey) => setState(activeKey as CommentNoteType)}
          selectedKey={activeTab}
          classNames={{
            panel: 'moe-h-full moe-flex-1 moe-mx-[-32px]',
            base: 'moe-h-full moe-flex moe-flex-col moe-px-[32px]',
          }}
        >
          {tabListInfo.map((tab) => (
            <Tabs.Item key={tab.key} label={tab.label} classNames={{ content: tab.tabClassName }}>
              <FlexGrowFixedHeightBox className="moe-h-full">
                <Scroll
                  className="moe-w-full moe-h-full moe-px-l"
                  classNames={{
                    scrollbar: 'moe-w-[4px] moe-p-none',
                    viewport: 'moe-pb-8px-600 moe-h-full',
                  }}
                >
                  {tab.element}
                </Scroll>
              </FlexGrowFixedHeightBox>
            </Tabs.Item>
          ))}
        </Tabs>
      </div>
    </div>
  );
});

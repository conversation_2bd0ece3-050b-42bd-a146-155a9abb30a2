import {
  ServiceItemType,
  ServiceOverrideType,
  ServiceType,
} from '@moego/api-web/moego/models/offering/v1/service_enum';
import { <PERSON><PERSON><PERSON> } from '@moego/finance-utils';
import { Tooltip, cn } from '@moego/ui';
import { useSelector } from 'amos';
import { Divider } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { isNumber } from 'lodash';
import React, { memo, useMemo, type PropsWithChildren } from 'react';
import { AddonQuantity } from '../../../../components/AddonQuantiity/AddonQuantity';
import { Condition } from '../../../../components/Condition';
import { PickService } from '../../../../components/ServiceApplicablePicker/components/PickService/PickService';
import { type ServicePriceDurationInfo } from '../../../../components/ServiceApplicablePicker/hooks/useServiceInfo';
import { type ServiceEntry } from '../../../../components/ServiceApplicablePicker/types/serviceEntry';
import { type ServiceOperationEntry } from '../../../../components/ServiceApplicablePicker/types/serviceOperation';
import { getDefaultService } from '../../../../components/ServiceApplicablePicker/utils/getDefaultService';
import { isMultipleStaffService } from '../../../../components/ServiceApplicablePicker/utils/isMultipleStaffService';
import { MultipleStaffId } from '../../../../components/ServiceStaffPicker/utils/multipleStaffId';
import { selectIsEnableSlotCalender } from '../../../../store/calendarLatest/calendar.selectors';
import { type ApptInfoPetServiceInfo } from '../../../../store/calendarLatest/calendar.types';
import { ScopeType } from '../../../../store/grooming/grooming.boxes';
import { serviceMapBox } from '../../../../store/service/service.boxes';
import { ID_ANONYMOUS, isNormal } from '../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { ReportActionName } from '../../../../utils/reportType';
import { reportData } from '../../../../utils/tracker';
import { splitPriceEqually } from '../../../CreateTicket/hooks/ticketSplitPriceEqual';
import { useSyncOperationPrice } from '../../../CreateTicket/hooks/useSyncOperationPrice';
import { useBDTimeLimit } from '../../hooks/useBDTimeLimit';
import { useDaycareServiceTime } from '../../hooks/useDaycareServiceTime';
import { matchApptServiceScene } from '../../store/appt.options';
import { ApptServiceScene, type ApptPetEvaluationInfo } from '../../store/appt.types';
import { EditOperationWorkMode } from '../EditPetServiceList/EditOperationWorkMode';
import { calcServiceDuration, initServiceOperation } from '../EditPetServiceList/EditPetServiceList.utils';
import { EditServiceOperation } from '../EditPetServiceList/EditServiceOperation';
import { SavedOptionList, SavedOptions } from '../EditPetServiceList/SavedOptions';
import { ServicePrice } from '../EditPetServiceList/ServicePrice';
import { ServiceTime } from '../EditPetServiceList/ServiceTime';
import { ServiceVariationByStaffOnBoarding } from '../EditPetServiceList/ServiceVariationByStaffOnBoarding';
import { useCheckServicePrice } from '../EditPetServiceList/hooks/useCheckEditValidate';
import { useResetOpName } from '../EditPetServiceList/hooks/useInitOpName';
import { useMemorizeLastOperation } from '../EditPetServiceList/hooks/useMemorizeLastOperation';
import { EvaluationCombined } from '../EvaluationCombined/EvaluationCombined';
import { useGetServiceItemStatus, useUpdateDateTypeAndAssociatedService } from './EditServiceItem.hook';
import { type EditServiceItemProps } from './EditServiceItem.type';
import { SelectLodging } from './components/SelectLodging';
import { SelectSplitLodging } from './components/SelectSplitLodgings';
import { SelectStaff } from './components/SelectStaff';

export const EditServiceItem = memo<PropsWithChildren<EditServiceItemProps>>((props) => {
  const {
    className,
    value,
    petId,
    clientId,
    disabledServices,
    serviceIds,
    appointmentId,
    disabledSavedPriceAndDuration = false,
    children,
    prefix,
    suffix,
    onChange,
  } = props;

  const { startDate = '', startTime = ID_ANONYMOUS, serviceItemType, serviceType, serviceId } = value || {};
  const [serviceMap, isEnableSlotCalender] = useSelector(serviceMapBox, selectIsEnableSlotCalender);
  const isMultiStaff = isMultipleStaffService(value);
  const checkServicePrice = useCheckServicePrice();
  const memorizeLastOperation = useMemorizeLastOperation();
  const resetOpName = useResetOpName();
  const priceCheck = useMemo(() => checkServicePrice(value), [value, checkServicePrice]);
  const { syncOperationPrice } = useSyncOperationPrice();
  const {
    isShowOnBoardingTips,
    isShowDuration,
    isShowQuantity,
    isShowLodging,
    isShowSplitLodging,
    isShowStaff,
    disabledTabs,
  } = useGetServiceItemStatus(props);

  const { getDaycareServiceTime } = useDaycareServiceTime();
  const { getDaycareEndTime } = useBDTimeLimit();
  const updateDateTypeAndAssociatedService = useUpdateDateTypeAndAssociatedService(
    String(appointmentId),
    String(petId),
  );
  const isCreatedOrder = isNormal(value?.orderLineItemId);

  const onChangeService = useLatestCallback(async (serviceId: number, extraServiceInfo: ServicePriceDurationInfo) => {
    const data = serviceMap.mustGetItem(serviceId);
    const {
      name: nextServiceName,
      type: serviceType,
      duration: serviceDuration,
      bundleServiceIds,
      requireDedicatedStaff,
    } = data;
    const isDaycare = serviceItemType === ServiceItemType.DAYCARE;

    let endTime = value?.endTime;
    let serviceTime = extraServiceInfo.serviceTime;

    if (isDaycare) {
      // 修改 daycare 的 service 时，同步需要修改 endTime
      endTime = getDaycareEndTime({
        ...value,
        serviceId: String(serviceId),
        serviceTime: extraServiceInfo.serviceTime,
      });
      // 修改 daycare 要计算实际的 duration 用于展示
      const daycareServiceTime = getDaycareServiceTime({
        startDate: value?.startDate,
        endDate: value?.endDate,
        startTime,
        endTime,
        serviceId: String(serviceId),
      });
      serviceTime = daycareServiceTime.serviceTime;
    } else if (value?.startDate && value.startTime && value.serviceTime) {
      const end = dayjs(value.startDate).setMinutes(value.startTime).add(extraServiceInfo.serviceTime, 'minute');
      endTime = end.getMinutes();
    }

    const lodging = matchApptServiceScene(ApptServiceScene.ServiceSplitLodgings, { serviceItemType })
      ? {
          splitLodgings: (value?.splitLodgings ?? []).map((item) => ({
            ...item,
            price: item.isApplicable ? MoeMoney.fromAmount(extraServiceInfo.servicePrice) : item.price,
          })),
        }
      : undefined;

    const service = getDefaultService({
      ...value,
      ...extraServiceInfo,
      ...lodging,
      serviceId,
      serviceName: nextServiceName,
      lodgingId: undefined,
      lodgingName: undefined,
      serviceType,
      bundleServiceIds,
      serviceTime,
      requireDedicatedStaff,
      ...(endTime ? { endTime } : {}),
    });
    const originServiceName = serviceMap.mustGetItem(value?.serviceId ?? ID_ANONYMOUS).name;
    // reset operation price
    // reset operation duration
    const operationList = splitPriceEqually<ServiceOperationEntry>(service.servicePrice, service.operationList!)
      .map((op) => ({ ...op, duration: service.serviceTime }))
      .map((op, index) => {
        return resetOpName(op, index, originServiceName, nextServiceName);
      });
    const { dateType, associatedId } = updateDateTypeAndAssociatedService({
      targetServiceId: serviceId,
      sourceService: value,
    });
    const nextService: ServiceEntry = {
      ...service,
      operationList,
      serviceTime: serviceType === ServiceType.ADDON ? serviceDuration : service.serviceTime,
      dateType,
      associatedId,
    };
    reportData(ReportActionName.apptEditServiceDuration, {
      serviceName: nextService.serviceName,
      serviceItemType: nextService.serviceItemType,
      serviceType: nextService.serviceType,
      isExistAppt: isNormal(appointmentId),
    });
    onChange?.(nextService);
  });

  const onChangePrice = useLatestCallback((price?: number) => {
    if (value?.servicePrice === price) {
      return;
    }
    const hasOperation = isMultipleStaffService(value);
    const nextOperationList = hasOperation
      ? splitPriceEqually<ServiceOperationEntry>(price ?? 0, value!.operationList!)
      : [];
    const nextSplitLodgings = value?.splitLodgings ?? [];

    const nextService = getDefaultService({
      ...value,
      isPriceModified: true,
      servicePrice: price,
      operationList: syncOperationPrice(nextOperationList, price),
      splitLodgings: nextSplitLodgings.map((item) => ({
        ...item,
        price: MoeMoney.fromAmount(price ?? 0),
      })),
    });
    const validateSavedOption = !!SavedOptionList.find((i) => i.value === nextService.scopeTypePrice);
    // !validateSavedOption : 未勾选SavedOption
    // !value?.isPriceModified : "本次"Edit未修改过价格
    if (!validateSavedOption || !value?.isPriceModified) {
      nextService.scopeTypePrice = ScopeType.NotSaved;
      nextService.priceOverrideType = ServiceOverrideType.UNSPECIFIED;
    }
    reportData(ReportActionName.apptEditServicePrice, {
      serviceName: nextService.serviceName,
      serviceItemType: nextService.serviceItemType,
      serviceType: nextService.serviceType,
      isExistAppt: isNormal(appointmentId),
    });
    onChange?.(nextService);
  });

  const onChangeOperationList = useLatestCallback((obj: Partial<ApptInfoPetServiceInfo>) => {
    const nextService = getDefaultService({ ...value, ...obj });
    const { staffId, operationList } = nextService;
    const isMultiStaff = isMultipleStaffService(nextService);
    const nextServiceTime = calcServiceDuration(nextService);
    // 如果是多人，且没有main staff，则自动选择第一个staff
    if (isMultiStaff) {
      const staffNoInOperation = !operationList!.find((op) => op.staffId === staffId);
      if (staffNoInOperation) {
        const firstHasStaffOp = operationList.find((op) => isNormal(op.staffId));
        nextService.staffId = firstHasStaffOp ? firstHasStaffOp.staffId : MultipleStaffId;
      }
    }
    const finalService: ServiceEntry = {
      ...nextService,
      serviceTime: nextServiceTime,
      operationList: isMultiStaff ? syncOperationPrice(operationList!, nextService.servicePrice) : [],
    };
    onChange?.(finalService);
    memorizeLastOperation(finalService);
  });

  const onChangeMainStaff = useLatestCallback((staffId: number) => {
    const nextService = getDefaultService({ ...value, staffId });
    onChange?.(nextService);
  });

  const onAddOperation = useLatestCallback(() => {
    const service = serviceMap.mustGetItem(serviceId ?? ID_ANONYMOUS);
    const op = initServiceOperation({ operationName: service.name });
    const currentOpList = value?.operationList ?? [];
    const nextOperationList = [...currentOpList, op];
    onChangeOperationList({ operationList: nextOperationList });
  });

  const onChangeServiceTime = useLatestCallback((serviceTime?: number) => {
    if (value?.serviceTime === serviceTime) {
      return;
    }
    const nextService = getDefaultService({
      ...value,
      serviceTime,
      isDurationModified: true,
    });
    const validateSavedOption = !!SavedOptionList.find((i) => i.value === nextService.scopeTypeTime);
    if (!validateSavedOption || !value?.isDurationModified) {
      nextService.scopeTypeTime = ScopeType.NotSaved;
      nextService.durationOverrideType = ServiceOverrideType.UNSPECIFIED;
    }
    onChange?.(nextService);
  });

  const onChangeServiceFields = useLatestCallback((obj: Partial<ApptInfoPetServiceInfo>) => {
    const nextService = getDefaultService({ ...value, ...obj });
    onChange?.(nextService);
  });

  const onChangeEvaluation = useLatestCallback((evaluations: ApptPetEvaluationInfo[]) => {
    const nextService = getDefaultService({ ...value });
    onChange?.({ ...nextService, evaluations });
  });

  return (
    <div className={classNames(className, 'moe-flex moe-flex-col moe-gap-s')}>
      {children}
      <Condition if={isNumber(appointmentId) && serviceType === ServiceType.SERVICE}>
        <EvaluationCombined
          prevEvaluations={value?.evaluations}
          onChange={onChangeEvaluation}
          petId={petId.toString()}
          associatedServiceIds={isNormal(value?.serviceId) ? [value.serviceId.toString()] : undefined}
          appointmentId={appointmentId!.toString()}
          startDate={startDate}
          startTime={startTime}
          isBoarding={serviceItemType === ServiceItemType.BOARDING}
        />
      </Condition>
      <Tooltip content={isCreatedOrder ? 'This item has been paid, you can not edit it anymore.' : undefined}>
        <div>
          <PickService
            petId={petId}
            clientId={clientId}
            info={value}
            isDisabled={isCreatedOrder}
            serviceId={isNormal(value?.serviceId) ? value.serviceId : undefined}
            disabledServices={disabledServices}
            serviceItemType={serviceItemType || ServiceItemType.UNSPECIFIED}
            serviceType={serviceType || ServiceType.UNSPECIFIED}
            disabledTabs={disabledTabs}
            onChange={onChangeService}
            serviceIds={serviceIds}
          />
        </div>
      </Tooltip>

      <Condition if={isNormal(serviceId)}>
        {prefix?.()}
        <div className={cn('moe-flex moe-items-start moe-gap-x-[8px]')}>
          <Condition if={isShowStaff}>
            <SelectStaff {...props} showMultiStaffOption={!isEnableSlotCalender} />
          </Condition>
          <div className="moe-flex-1 moe-min-w-0">
            <ServicePrice
              isDisabled={isCreatedOrder}
              serviceId={value?.serviceId}
              value={value?.servicePrice}
              onChange={onChangePrice}
            />
            <Condition if={!priceCheck.validate}>
              <div className="moe-max-w-[130px] moe-ml-[-50px] moe-mt-[4px] moe-text-[14px] moe-text-[#FAAD14] moe-leading-[18px] moe-whitespace-nowrap">
                {priceCheck.errorMsg}
              </div>
            </Condition>
          </div>
          <Condition if={isShowDuration}>
            <div className="moe-flex-1 moe-min-w-0">
              <ServiceTime
                disabled={isMultiStaff || isCreatedOrder}
                value={value?.serviceTime}
                onChange={onChangeServiceTime}
              />
            </div>
          </Condition>
          <Condition if={isShowLodging}>
            <SelectLodging {...props} />
          </Condition>
          <Condition if={isShowQuantity}>
            <div className="moe-flex-1 moe-min-w-0">
              <AddonQuantity
                isRequired
                isDisabled={isCreatedOrder}
                onChange={(val) => {
                  onChangeServiceFields({ quantityPerDay: val });
                }}
                quantityPerDay={value?.quantityPerDay}
              />
            </div>
          </Condition>
        </div>
        <Condition if={isShowSplitLodging}>
          <SelectSplitLodging {...props} />
        </Condition>

        <Condition if={value?.isPriceModified && !disabledSavedPriceAndDuration}>
          {() => (
            <SavedOptions
              type="price"
              appointmentDate={startDate}
              checked={value?.isSavePrice}
              onSwitch={(checked) => {
                onChangeServiceFields({
                  isSavePrice: checked,
                  scopeTypePrice: checked ? ScopeType.ThisAndFollowing : ScopeType.NotSaved,
                  priceOverrideType: checked ? ServiceOverrideType.CLIENT : ServiceOverrideType.UNSPECIFIED,
                });
              }}
              value={value?.scopeTypePrice}
              onChange={(value) => {
                onChangeServiceFields({
                  isSavePrice: true,
                  scopeTypePrice: value,
                  priceOverrideType: ServiceOverrideType.CLIENT,
                });
              }}
            />
          )}
        </Condition>
        <Condition if={value?.isDurationModified && !disabledSavedPriceAndDuration}>
          {() => (
            <SavedOptions
              type="duration"
              appointmentDate={startDate}
              checked={value?.isSaveDuration}
              onSwitch={(checked) => {
                onChangeServiceFields({
                  isSaveDuration: checked,
                  scopeTypeTime: checked ? ScopeType.ThisAndFollowing : ScopeType.NotSaved,
                  durationOverrideType: checked ? ServiceOverrideType.CLIENT : ServiceOverrideType.UNSPECIFIED,
                });
              }}
              value={value?.scopeTypeTime}
              onChange={(value) => {
                onChangeServiceFields({
                  isSaveDuration: true,
                  scopeTypeTime: value,
                  durationOverrideType: ServiceOverrideType.CLIENT,
                });
              }}
            />
          )}
        </Condition>
        <Condition if={isShowOnBoardingTips}>
          <ServiceVariationByStaffOnBoarding serviceId={serviceId} />
        </Condition>
        <Condition if={isMultiStaff}>
          {() => (
            <div className="moe-bg-[#F7F8FA] moe-rounded-[8px]">
              <EditOperationWorkMode
                className="moe-p-[16px]"
                value={value!.workMode}
                onChange={(workMode) => onChangeOperationList({ workMode, isDurationModified: true })}
              />
              <Divider className="!moe-m-0 !moe-border-[#E6E6E6]" />
              <EditServiceOperation
                appointmentDate={startDate}
                appointmentId={appointmentId}
                serviceStartTime={startTime}
                workMode={value!.workMode}
                mainStaffId={value?.staffId}
                value={value!.operationList}
                onChange={(operationList, service) => onChangeOperationList({ ...service, operationList })}
                onChangeMainStaff={onChangeMainStaff}
                servicePrice={value?.servicePrice}
                serviceId={value?.serviceId}
                onAddStaff={onAddOperation}
              />
            </div>
          )}
        </Condition>
        {suffix?.()}
      </Condition>
    </div>
  );
});

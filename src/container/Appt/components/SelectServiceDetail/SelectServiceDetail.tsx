import { BusinessPetMetadataName } from '@moego/api-web/moego/models/business_customer/v1/business_pet_metadata_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { ID_ANONYMOUS } from '@moego/finance-utils';
import { useSerialCallback } from '@moego/tools';
import { Button, Form, Spin, cn, useForm } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo } from 'react';
import { useMount } from 'react-use';
import { Condition } from '../../../../components/Condition';
import { DrawerHeader } from '../../../../components/Drawer/DrawerHeader';
import { ScrollerProvider } from '../../../../layout/components/ScrollerProvider';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { type QuickAddSelectServiceDetailPayload } from '../../../../store/calendarLatest/calendar.types';
import { getLodgingTypeList } from '../../../../store/lodging/actions/public/lodgingType.actions';
import { getLodgingUnitList } from '../../../../store/lodging/actions/public/lodgingUnit.actions';
import { petMapBox } from '../../../../store/pet/pet.boxes';
import {
  getPetFeedingScheduleList,
  getPetMedicationScheduleList,
} from '../../../../store/pet/petFeedingMedication.actions';
import { getPetMetaDataList } from '../../../../store/pet/petMetaData.action';
import { getBizScheduleOpeningHour } from '../../../../store/staffSchedule/staffSchedule.actions';
import { DrawerFooter } from '../../../Calendar/latest/AwesomeCalendar.style';
import { useGetAvailableStaff } from '../../../CreateTicket/hooks/useGetAvailableStaff';
import { useDisableSaveService } from '../../hooks/useDisableSaveService';
import { selectMainServiceInAppt } from '../../store/appt.selectors';
import { CreateApptId } from '../../store/appt.types';
import { type ServiceDetailFormFields } from './SelectServiceDetail.type';
import { AdditionalServiceList } from './components/AdditionalService/AdditionalServiceList';
import { Addon } from './components/Addon/Addon';
import { GroomingTip } from './components/GroomingTips/GroomingTip';
import { MultiplePetsDateTimePicker } from './components/MultiplePets/MultiplePetsDateTimePicker';
import { MultipleServiceItem } from './components/MultiplePets/MultipleServicesContent';
import { Service } from './components/Service/Service';
import { ServiceDetailDataProvider } from './contexts/ServiceDetailData.context';
import { useBoardingServiceScheduleSync } from './hooks/useBoardingServiceScheduleSync';
import { useServiceDetailData } from './hooks/useServiceDetailData';
import { useServiceDetailFormDataSync } from './hooks/useServiceDetailFormDataSync';
import { useServiceDetailSubmit } from './hooks/useServiceDetailSubmit';

// TODO(gq,winches,p2) 这里的问题是，组件内部只处理 petIdsServiceList 参数，完全忽略 petIds 和 services 参数
// 需要重构一下
export const SelectServiceDetail = memo((props: { payload: QuickAddSelectServiceDetailPayload }) => {
  const {
    petIds,
    services,
    isMultiplePets,
    serviceItemType,
    appointmentId = CreateApptId,
    onBack,
    onConfirm,
    petIdsServiceList: originPetIdsServiceList,
  } = props.payload;
  const form = useForm<ServiceDetailFormFields>();
  const dispatch = useDispatch();
  const [business, mainService, petMap] = useSelector(
    selectCurrentBusiness,
    selectMainServiceInAppt(appointmentId),
    petMapBox,
  );

  const getData = useSerialCallback(async () => {
    const getPetSchedules = (id: number) => [
      dispatch(getPetMedicationScheduleList(id)),
      dispatch(getPetFeedingScheduleList(id)),
    ];

    await Promise.all([
      dispatch(
        getPetMetaDataList([
          BusinessPetMetadataName.FEEDING_SCHEDULE,
          BusinessPetMetadataName.FEEDING_UNIT,
          BusinessPetMetadataName.FEEDING_TYPE,
          BusinessPetMetadataName.FEEDING_SOURCE,
          BusinessPetMetadataName.FEEDING_INSTRUCTION,
          BusinessPetMetadataName.MEDICATION_SCHEDULE,
          BusinessPetMetadataName.MEDICATION_UNIT,
        ]),
      ),
      dispatch(getBizScheduleOpeningHour()),
      dispatch(getLodgingUnitList({ businessId: String(business.id) })),
      dispatch(getLodgingTypeList()),
      ...(petIds ? petIds.flatMap(getPetSchedules) : []),
    ]);
  });

  useMount(getData);

  useGetAvailableStaff();

  const {
    fullServiceAddOnList,
    normalServiceList,
    normalAddOnList,
    normalPetIdsServiceAddOnList,
    serviceDetailDataContextValue,
  } = useServiceDetailData({
    form,
    appointmentId,
    petIds,
    services,
    serviceItemType,
    originPetIdsServiceList,
  });

  const disabledSave = useDisableSaveService({ appointmentId, services: fullServiceAddOnList });

  useServiceDetailFormDataSync({
    appointmentId,
    serviceItemType,
    petIds,
    form,
    petServiceAddOnList: fullServiceAddOnList,
  });

  useBoardingServiceScheduleSync({
    appointmentId,
    petServiceAddOnList: fullServiceAddOnList,
  });

  const { handleSubmit } = useServiceDetailSubmit({
    appointmentId,
    serviceItemType,
    petIds,
    form,
    services: fullServiceAddOnList,
    mainService,
    onConfirm,
  });

  const isLoading = getData.isBusy();

  return (
    <div className="moe-w-full moe-flex moe-flex-col moe-h-full">
      <DrawerHeader title="Service details" onClick={onBack} />
      <ServiceDetailDataProvider value={serviceDetailDataContextValue}>
        <ScrollerProvider className={cn('moe-flex-1')}>
          <Spin
            classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }}
            isLoading={isLoading}
          >
            <Form form={form} footer={null}>
              <div
                className={cn('moe-flex moe-flex-col moe-px-l moe-gap-m', {
                  'moe-pt-[14px]': isMultiplePets,
                })}
              >
                <Condition
                  if={
                    mainService.serviceItemType !== ServiceItemType.GROOMING &&
                    serviceItemType === ServiceItemType.GROOMING
                  }
                >
                  <GroomingTip />
                </Condition>
                {petIds && petIds.length > 1 ? (
                  <>
                    <MultiplePetsDateTimePicker
                      appointmentId={appointmentId}
                      petIds={petIds!.map(String)}
                      items={normalServiceList}
                      form={form}
                    />

                    <div className="moe-border-t-[1px] moe-border-divider moe-border-dashed" />

                    {petIds.map((petId, index) => (
                      <MultipleServiceItem
                        key={petId}
                        petId={petId}
                        index={index}
                        petIds={petIds.map(String)}
                        petIdsServiceList={normalPetIdsServiceAddOnList}
                        appointmentId={appointmentId}
                        form={form}
                        petMap={petMap}
                      />
                    ))}
                  </>
                ) : (
                  <>
                    {normalServiceList.map((s) => (
                      <div
                        className="moe-border-b-[1px] moe-border-divider moe-border-dashed moe-pb-m empty:moe-hidden last:moe-border-none"
                        key={s.id}
                      >
                        <Service
                          petId={petIds?.[0] ?? ID_ANONYMOUS}
                          appointmentId={appointmentId}
                          item={s}
                          form={form}
                          renderAdditionalService={(p) => <AdditionalServiceList {...p} />}
                          canRemove
                          onRemove={() => serviceDetailDataContextValue.onRemoveServiceDetail(s.id)}
                        />
                      </div>
                    ))}

                    {normalAddOnList.map((s) => {
                      return (
                        <div
                          className="moe-border-b-[1px] moe-border-divider moe-border-dashed moe-pb-m empty:moe-hidden last:moe-border-none"
                          key={s.id}
                        >
                          <Addon
                            petId={petIds?.[0] ?? ID_ANONYMOUS}
                            appointmentId={appointmentId}
                            item={s}
                            canRemove
                            onRemove={() => serviceDetailDataContextValue.onRemoveServiceDetail(s.id)}
                          />
                        </div>
                      );
                    })}
                  </>
                )}
              </div>
            </Form>
          </Spin>
        </ScrollerProvider>
      </ServiceDetailDataProvider>
      <DrawerFooter className="border">
        <Button className="moe-flex-1" variant="secondary" onPress={onBack}>
          Back
        </Button>
        <Button
          className="moe-flex-1"
          isLoading={handleSubmit.isBusy()}
          onPress={handleSubmit}
          isDisabled={disabledSave}
        >
          Save
        </Button>
      </DrawerFooter>
    </div>
  );
});

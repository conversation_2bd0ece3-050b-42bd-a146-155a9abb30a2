import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { DatePicker, Form } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo, useEffect, useMemo } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../../utils/DateTimeUtil';
import { useBDTimeLimit } from '../../../../../hooks/useBDTimeLimit';
import { useDaycareServiceTime } from '../../../../../hooks/useDaycareServiceTime';
import { useFillTimeSchedule } from '../../../../../hooks/useFillTimeSchedule';
import { selectApptPetService, selectPetInAppt } from '../../../../../store/appt.selectors';
import { type ApptPetServiceItem } from '../../../../../store/appt.types';
import { EditEvaluationCombined } from '../../../../Evaluation/EditEvaluationCombined';
import { BDScheduleTips } from '../../DaycareScheduleTips/DaycareScheduleTips';
import { useDaycareHook } from './ServiceDaycare.hook';
import { type ServiceDaycareBaseProps } from './ServiceDaycare.type';
import { ServiceDaycareBase } from './ServiceDaycareBase';
import { ServiceDaycareEndTime } from './ServiceDaycareEndTime';
import { ServiceDaycareStartTime } from './ServiceDaycareStartTime';
import { ServiceLodging } from './ServiceLodging';

interface ServiceDaycareNormalProps extends ServiceDaycareBaseProps {
  showLodging?: boolean;
  showDatePicker?: boolean;
  items?: ApptPetServiceItem[];
  showEvaluationTips?: boolean;
}

export const ServiceDaycareNormal = memo((props: ServiceDaycareNormalProps) => {
  const {
    formKey,
    appointmentId,
    petId,
    item,
    showLodging = true,
    showDatePicker = true,
    items,
    showEvaluationTips = true,
  } = props;
  const { id } = item;

  const { mustGetWorkingRange } = useFillTimeSchedule();
  const { getDaycareEndTime } = useBDTimeLimit();
  const [{ serviceId }] = useSelector(selectApptPetService(appointmentId, id));
  const [pet] = useSelector(selectPetInAppt(petId, appointmentId));

  const { getDaycareServiceTime } = useDaycareServiceTime();
  const { onChange, business, mainService, currentService } = useDaycareHook(appointmentId, id, items);
  const { startDate, startTime } = currentService;

  const isDisabledDate = useMemo(() => {
    if (!pet) return false;
    const services = pet.services.filter((service) => service.serviceType === ServiceType.SERVICE);
    // 是主 service 的情况下，service 竟然大于 1，那肯定是走的 add other care type 的老 6 逻辑
    return mainService.serviceItemType === ServiceItemType.DAYCARE && services.length > 1;
  }, [mainService.serviceItemType, pet]);

  const dateRange = useMemo(() => {
    const date = startDate ? dayjs(startDate) : undefined;
    return {
      startDate: date,
      endDate: date,
    };
  }, [startDate]);

  useEffect(() => {
    if (showDatePicker) {
      const { startDate, endDate, startTime, endTime } = currentService;
      const { serviceTime } = getDaycareServiceTime({
        startDate,
        endDate,
        startTime,
        endTime,
        serviceId,
      });
      onChange({ serviceTime });
    }
  }, [
    currentService.startDate,
    currentService.endDate,
    currentService.startTime,
    currentService.endTime,
    showDatePicker,
  ]);

  return (
    <ServiceDaycareBase {...props}>
      <Condition if={showDatePicker}>
        <div className="moe-flex moe-flex-col moe-gap-s">
          <Form.Item name={`${formKey}.startDate`} label="Date" rules={{ required: true }}>
            <DatePicker
              isDisabled={isDisabledDate && !!startDate}
              isRequired
              format={business.dateFormat}
              className="moe-flex-1"
              placeholder="Select date"
              onChange={(v) => {
                const date = v?.format(DATE_FORMAT_EXCHANGE);
                const startTime = v ? mustGetWorkingRange(v).startTime : undefined;
                const endTime = getDaycareEndTime({
                  serviceId,
                  serviceItemType: ServiceItemType.DAYCARE,
                  startDate: date,
                  startTime,
                });
                onChange({
                  startDate: date,
                  endDate: date,
                  startTime,
                  endTime,
                });
              }}
            />
          </Form.Item>
          <div className="moe-flex moe-gap-s">
            <ServiceDaycareStartTime {...props} onChange={onChange} />
            <ServiceDaycareEndTime startTime={startTime} {...props} onChange={onChange} />
          </div>
          <BDScheduleTips date={startDate} />
        </div>
      </Condition>
      <Condition if={showEvaluationTips}>
        <EditEvaluationCombined
          petId={petId.toString()}
          associatedServiceIds={[serviceId]}
          appointmentId={appointmentId}
        />
      </Condition>
      <Condition if={showLodging}>
        <ServiceLodging {...props} range={dateRange} isDisabled={!startDate} onChange={onChange} />
      </Condition>
    </ServiceDaycareBase>
  );
});

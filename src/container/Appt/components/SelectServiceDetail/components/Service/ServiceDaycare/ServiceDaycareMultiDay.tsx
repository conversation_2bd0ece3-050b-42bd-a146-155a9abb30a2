import { ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo, useMemo } from 'react';
import { DateType } from '../../../../../../../components/DateType/DateType';
import { toNumber } from '../../../../../../../store/utils/identifier';
import { selectApptPetService } from '../../../../../store/appt.selectors';
import { EditEvaluationCombined } from '../../../../Evaluation/EditEvaluationCombined';
import { useDaycareHook } from './ServiceDaycare.hook';
import { type ServiceDaycareBaseProps } from './ServiceDaycare.type';
import { ServiceDaycareBase } from './ServiceDaycareBase';
import { ServiceDaycareEndTime } from './ServiceDaycareEndTime';
import { ServiceDaycareStartTime } from './ServiceDaycareStartTime';
import { ServiceLodging } from './ServiceLodging';

interface ServiceDaycareMultiDayProps extends ServiceDaycareBaseProps {}

export const ServiceDaycareMultiDay = memo((props: ServiceDaycareMultiDayProps) => {
  const { item, appointmentId, petId } = props;
  const { id } = item;

  const { onChange, currentService, mainService } = useDaycareHook(appointmentId, id);
  const { dateType, specificDates, startTime, quantityPerDay } = currentService;
  const [{ serviceId, ownId }] = useSelector(selectApptPetService(appointmentId, id));

  const dateRange = useMemo(() => {
    return {
      startDate: dayjs(mainService.startDate),
      endDate: dayjs(mainService.endDate),
    };
  }, [mainService]);

  return (
    <ServiceDaycareBase {...props}>
      <EditEvaluationCombined
        petId={petId.toString()}
        associatedServiceIds={[serviceId]}
        appointmentId={appointmentId}
      />
      <DateType
        isRequired
        minDate={mainService.startDate}
        maxDate={mainService.endDate}
        specificDates={mainService.specificDates}
        value={{
          dateType,
          specificDates: specificDates,
          quantityPerDay,
        }}
        onChange={onChange}
        serviceType={ServiceType.SERVICE}
        serviceId={toNumber(serviceId)}
        ownId={ownId}
      />
      <div className="moe-flex moe-items-center moe-gap-[16px] moe-justify-between">
        <ServiceDaycareStartTime {...props} onChange={onChange} />
        <ServiceDaycareEndTime startTime={startTime} {...props} onChange={onChange} />
      </div>
      <ServiceLodging {...props} range={dateRange} onChange={onChange} />
    </ServiceDaycareBase>
  );
});

import { Text } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import React, { memo } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { Switch } from '../../../../../../../components/SwitchCase';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../../utils/DateTimeUtil';
import { useFillTimeSchedule } from '../../../../../hooks/useFillTimeSchedule';
import { setServiceForPet } from '../../../../../store/appt.actions';
import { selectApptPetService } from '../../../../../store/appt.selectors';
import { type ApptServiceInfoRecord, CreateApptId } from '../../../../../store/appt.types';
import { EditEvaluationCombined } from '../../../../Evaluation/EditEvaluationCombined';
import { getResetEnd } from '../../../SelectServiceDetail.utils';
import { useServiceDetailDisabledDate } from '../../../hooks/useServiceDetailDisabledDate';
import { BoardingDateTimePicker } from '../../BoardingDateTimePicker';
import { type ServiceBoardingProps } from './ServiceBoarding.types';
import { ServiceLodging } from './ServiceLodging';
import { useSyncFromMainService } from './hooks/useSyncFromMainService';

export const ServiceBoarding = memo((props: ServiceBoardingProps) => {
  const { petId, item, formKey: key, appointmentId = CreateApptId, isMultiple, form } = props;
  const { id, serviceItemType } = item;
  const dispatch = useDispatch();
  const [business, { endDate, startDate, startTime = 0, endTime, serviceId }] = useSelector(
    selectCurrentBusiness,
    selectApptPetService(appointmentId, id),
  );
  const { mustGetWorkingRange } = useFillTimeSchedule();
  const { disabledStartDate, disabledEndDate } = useServiceDetailDisabledDate(appointmentId, serviceItemType);
  const { allowShowSyncMainService } = useSyncFromMainService({ id, petId, appointmentId });
  const isDisabled = Boolean(!endDate || !startDate);

  const handleChange = (params: Partial<ApptServiceInfoRecord>) => {
    dispatch(setServiceForPet(appointmentId, id, params));
  };

  return (
    <>
      <Condition if={!isMultiple}>
        <Switch>
          <Switch.Case if={allowShowSyncMainService}>
            <div className="moe-flex moe-flex-col moe-gap-xxs">
              <Text variant="caption" className="moe-text-tertiary">
                Start & end time
              </Text>
              <Text variant="regular">
                {business.formatDate(startDate)} {business.formatFixedTime(startTime * T_MINUTE)}
                <span> - </span>
                {business.formatDate(endDate)} {business.formatFixedTime((endTime || 0) * T_MINUTE)}
              </Text>
            </div>
          </Switch.Case>
          <Switch.Case else>
            <BoardingDateTimePicker
              appointmentId={appointmentId}
              id={id}
              startDateProps={{
                name: `${key}.startDate`,
                onChange: (v) => {
                  if (!v) {
                    return handleChange({
                      startDate: undefined,
                      startTime: undefined,
                      endDate: undefined,
                      endTime: undefined,
                    });
                  }
                  const { startTime: bizStartTime } = mustGetWorkingRange(v);
                  handleChange({
                    startDate: v.format(DATE_FORMAT_EXCHANGE),
                    startTime: bizStartTime,
                    ...getResetEnd(v, dayjs(endDate), bizStartTime, endTime),
                  });
                },
                disabledDate: disabledStartDate,
              }}
              startTimeProps={{
                name: `${key}.startTime`,
                onChange: (v) => {
                  handleChange({
                    startTime: v?.getMinutes(),
                    ...getResetEnd(dayjs(startDate), dayjs(endDate), v?.getMinutes(), endTime),
                  });
                },
              }}
              endDateProps={{
                name: `${key}.endDate`,
                onChange: (v) => {
                  if (!v) {
                    return handleChange({
                      endDate: undefined,
                      endTime: undefined,
                    });
                  }
                  const { startTime: bizStartTime } = mustGetWorkingRange(v);
                  handleChange(getResetEnd(dayjs(startDate), v, startTime, bizStartTime));
                },
                extraDisabledDate: disabledEndDate,
              }}
              endTimeProps={{
                name: `${key}.endTime`,
                onChange: (v) => {
                  handleChange(getResetEnd(dayjs(startDate), dayjs(endDate), startTime, v?.getMinutes()));
                },
              }}
            />
          </Switch.Case>
        </Switch>
      </Condition>
      <EditEvaluationCombined
        petId={petId.toString()}
        associatedServiceIds={[serviceId]}
        appointmentId={appointmentId}
      />
      <ServiceLodging
        id={id}
        petId={petId}
        serviceId={serviceId}
        appointmentId={appointmentId}
        isDisabled={isDisabled}
        form={form}
        formKey={key}
        range={{
          startDate: startDate,
          startTime: startTime,
          endDate: endDate,
          endTime: endTime,
        }}
      />
    </>
  );
});

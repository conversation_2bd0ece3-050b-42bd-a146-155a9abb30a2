import { Markup, Tooltip, cn, Text } from '@moego/ui';
import React, { memo, useMemo } from 'react';
import { type BusinessRecord } from '../../../../../../../store/business/business.boxes';
import dayjs from 'dayjs';
import { type TimeSlot } from '@moego/api-web/moego/models/smart_scheduler/v1/time_slot_models';
import { TimeSlotType } from '@moego/api-web/moego/models/smart_scheduler/v1/time_slot_enums';

export interface TimeSlotItemProps {
  slot: TimeSlot;
  onClick?: (isFullyBooked: boolean, time: number) => void;
  business: BusinessRecord;
  value: number;
}

const NOT_ELIGIBLE = 'Not eligible';

export const TimeSlotItem = memo<TimeSlotItemProps>((props) => {
  const { slot, onClick, business, value } = props;
  const isFullyBooked = slot.timeSlotType === TimeSlotType.FULLY_BOOKED;
  const isShowWarning = [
    TimeSlotType.IN_BLOCK,
    TimeSlotType.LIMITATION_NOT_MET,
    TimeSlotType.SERVICE_LIMITATION_NOT_MET,
    TimeSlotType.PET_SIZE_LIMITATION_NOT_MET,
    TimeSlotType.PET_BREED_LIMITATION_NOT_MET,
    TimeSlotType.FULLY_BOOKED,
  ].includes(slot.timeSlotType);
  const time = slot.startTime;
  const isSelected = value === time;

  const { desc, tooltip } = useMemo(() => {
    switch (slot.timeSlotType) {
      case TimeSlotType.SERVICE_LIMITATION_NOT_MET:
        return { desc: NOT_ELIGIBLE, tooltip: 'This slot is not eligible due to service limitation' };
      case TimeSlotType.PET_SIZE_LIMITATION_NOT_MET:
        return { desc: NOT_ELIGIBLE, tooltip: 'This slot is not eligible due to pet size limitation' };
      case TimeSlotType.PET_BREED_LIMITATION_NOT_MET:
        return { desc: NOT_ELIGIBLE, tooltip: 'This slot is not eligible due to pet breed limitation' };
      case TimeSlotType.LIMITATION_NOT_MET:
        return {
          desc: NOT_ELIGIBLE,
          tooltip: 'This slot is not eligible due to pet size or breed or service limitation',
        };
      case TimeSlotType.IN_BLOCK:
        return { desc: 'Blocked', tooltip: 'This slot has been blocked' };
      default:
        return {
          desc: `${slot.bookedPetCount} / ${slot.petCapacity} Booked`,
          tooltip: slot.bookedInfos.length ? (
            <div>
              <Text variant="small" className="moe-text-white">
                Occupied by
              </Text>
              {slot.bookedInfos.map((item, index) => (
                <Markup variant="small" className="moe-text-white" key={`${item.petId}-${index}`}>
                  {item.petName} ({item.customerLastName})
                </Markup>
              ))}
            </div>
          ) : null,
        };
    }
  }, [slot]);

  const isWarningSelected = isShowWarning && isSelected;
  const isSuccessSelected = !isShowWarning && isSelected;
  const isWarning = isShowWarning && !isSelected;
  const isSuccess = !isShowWarning && !isSelected;

  return (
    <div
      className={cn('moe-rounded-s moe-p-xs moe-w-[122px] moe-cursor-pointer moe-border', {
        'moe-bg-warning-bold moe-border-warning': isWarningSelected,
        'moe-bg-success-bold moe-border-success': isSuccessSelected,
        'moe-bg-warning-subtle moe-border-warning': isWarning,
        'moe-bg-success-subtle moe-border-success': isSuccess,
      })}
      onClick={() => {
        onClick?.(isFullyBooked, time);
      }}
    >
      <Tooltip content={tooltip} side="top">
        <div className="moe-flex moe-flex-col moe-gap-xxs moe-text-white moe-items-center moe-justify-center">
          <Markup
            variant="regular-short"
            className={cn('moe-text-nowrap', {
              'moe-text-white': isSelected,
              'moe-text-primary': !isSelected,
            })}
          >
            {business.formatTime(dayjs().setMinutes(time))}
          </Markup>
          <Markup
            variant="caption"
            className={cn('moe-text-nowrap', {
              'moe-text-white': isSelected,
              'moe-text-warning': isWarning,
              'moe-text-success': isSuccess,
            })}
          >
            {desc}
          </Markup>
        </div>
      </Tooltip>
    </div>
  );
});

TimeSlotItem.displayName = 'TimeSlotItem';

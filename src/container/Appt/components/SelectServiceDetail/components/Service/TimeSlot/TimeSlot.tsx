import React, { memo, useMemo } from 'react';
import { Switch } from '../../../../../../../components/SwitchCase';
import { Link, Spin, Text, cn } from '@moego/ui';
import { PATH_ONLINE_BOOKING_NEW_SETTINGS } from '../../../../../../../router/paths';
import { TimeSlotPicker } from './TimeSlotPicker';
import { useGroomingAvailability } from '../../../hooks/useGroomingAvailability';
import { useEnableMultiPetBySlotFeature } from '../../../../../../Calendar/latest/components/SlotCalendar/hooks/useSlotCalendarFeature';
import { isNormal } from '../../../../../../../store/utils/identifier';
import { getListAvailableTimeSlots, transformPetParams } from '../../../../../store/appt.actions';
import { type BusinessRecord } from '../../../../../../../store/business/business.boxes';
import { useDispatch, useSelector } from 'amos';
import { useAsync } from 'react-use';
import { useClosedDate } from '../../../../../../settings/Settings/BusinessSetting/hooks/useClosedDate';
import { getClosedDateList } from '../../../../../../../store/business/closedDate.actions';
import { getHolidays } from '../../../../../../../store/business/holiday.actions';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { useQuery } from '../../../../../../../store/utils/useQuery';
import { getStaffsWorkingHour } from '../../../../../../../store/calendarLatest/actions/private/calendar.actions';
import { selectOnlineBookingLatestPreference } from '../../../../../../../store/onlineBooking/onlineBookingPreference.selectors';

interface TimeSlotValue {
  id: string;
  startTime: number;
  staffId?: string;
  startDate: string;
  petId: string | number;
  appointmentId: string;
}

export interface TimeSlotProps {
  isEnableSlotCalender: boolean;
  business: BusinessRecord;
  onSelect: (time: number) => void;
  className?: string;
  timeSlotClassName?: string;
  textClassName?: string;
  value: TimeSlotValue;
}

const TEXT_CLASSNAME = 'moe-py-[8px] moe-mt-[4px]';

export const TimeSlot = memo<TimeSlotProps>((props) => {
  const {
    isEnableSlotCalender,
    business,
    value: propsValue,
    onSelect,
    className,
    textClassName,
    timeSlotClassName,
  } = props;
  const { id, startTime, staffId, startDate, petId, appointmentId } = propsValue;

  const { isOBBySlot, isShiftSync, isLoading: isLoadingAvailability } = useGroomingAvailability();
  const [latestPreference] = useSelector(selectOnlineBookingLatestPreference);
  const isEnableOb = latestPreference.bookingEnable;
  const enableCalendarSlot = useEnableMultiPetBySlotFeature();
  const dispatch = useDispatch();
  const businessId = business.id;
  const closedDateList = useClosedDate(businessId, false);
  const { value: staffWorkingHour, loading: loadingStaffWorkingHour } = useQuery(
    getStaffsWorkingHour({ dispatch: false, startDate, endDate: startDate }),
  );

  const isWorkingDate = useMemo(() => {
    const workingHourData = staffWorkingHour?.find((item) => item.date === startDate);
    return workingHourData?.staffs.some((item) => String(item.staffId) === staffId);
  }, [staffWorkingHour, staffId, startDate]);

  const { value, loading: loadingTimeSlot } = useAsync(async () => {
    if (isEnableSlotCalender && isNormal(staffId) && startDate && isNormal(id) && isEnableOb) {
      const res = await dispatch(
        getListAvailableTimeSlots({
          businessId: businessId.toString(),
          dates: [startDate],
          petParams: transformPetParams([id], staffId, petId.toString(), appointmentId),
          ...(isNormal(appointmentId) ? { filterAppointmentId: appointmentId } : {}),
        }),
      );
      return res;
    }
    return undefined;
  }, [isEnableSlotCalender, staffId, startDate, id, petId, isEnableOb]);

  const { loading: closedDateListLoading } = useAsync(async () => {
    if (!closedDateList.length) {
      await Promise.all([dispatch(getClosedDateList(businessId)), dispatch(getHolidays(businessId))]);
    }
  }, [businessId, closedDateList.length]);

  const loading = isLoadingAvailability || loadingTimeSlot || closedDateListLoading || loadingStaffWorkingHour;

  const handleRouteToOB = useLatestCallback(() => {
    // OB 没开就先跳转到 OB，开了再跳转到 availability
    window.open(
      PATH_ONLINE_BOOKING_NEW_SETTINGS.build({ panel: !isEnableOb ? 'overview' : 'availability' }),
      '_blank',
      'noopener noreferrer',
    );
  });

  return (
    <Switch shortCircuit>
      <Switch.Case if={loading}>
        <Text
          variant="small"
          className={cn(TEXT_CLASSNAME, 'moe-flex moe-items-center moe-gap-x-xs moe-text-tertiary', textClassName)}
        >
          <Spin /> Getting available slot times for you...
        </Text>
      </Switch.Case>

      <Switch.Case
        if={closedDateList.some((item) => {
          if (!item.raw?.startDate || !item.raw?.endDate) return false;
          const currentDate = new Date(startDate);
          const closedStartDate = new Date(item.raw.startDate);
          const closedEndDate = new Date(item.raw.endDate);
          return currentDate >= closedStartDate && currentDate <= closedEndDate;
        })}
      >
        <Text variant="regular-short" className={cn(TEXT_CLASSNAME, 'moe-text-yellow-500', textClassName)}>
          Business closed date
        </Text>
      </Switch.Case>

      <Switch.Case if={!isWorkingDate}>
        <Text variant="regular-short" className={cn(TEXT_CLASSNAME, 'moe-text-yellow-500', textClassName)}>
          This staff is not scheduled to work at this date
        </Text>
      </Switch.Case>

      <Switch.Case if={(!isOBBySlot || !isEnableOb) && enableCalendarSlot}>
        <Text variant="small" className={cn(TEXT_CLASSNAME, 'moe-text-secondary', textClassName)}>
          To view slot times for the selected date, make sure{' '}
          <Link onClick={handleRouteToOB} variant="small">
            Online Booking
          </Link>{' '}
          is enabled and the availability type is set to By slot.
        </Text>
      </Switch.Case>

      <Switch.Case if={value?.timeSlots}>
        <div className={className}>
          {isShiftSync === false && (
            <Text variant="small" className={cn(TEXT_CLASSNAME, 'moe-text-secondary moe-mb-[4px]', textClassName)}>
              With OB Sync turned off, the slot times shown here follow the settings from{' '}
              <Link onClick={handleRouteToOB} variant="small">
                Online Booking
              </Link>
              .
            </Text>
          )}
          <TimeSlotPicker
            className={cn({ 'moe-mt-s': isShiftSync }, timeSlotClassName)}
            business={business}
            slots={value?.timeSlots || []}
            onSelect={onSelect}
            value={startTime}
          />
        </div>
      </Switch.Case>
    </Switch>
  );
});

TimeSlot.displayName = 'TimeSlot';

import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { cn, Markup } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { selectCompanyCareTypeNameMap } from '../../../../../../../store/careType/careType.selectors';
import { useGetServiceListByPet } from '../../../../../hooks/useGetServiceListByPet';
import { ApptCareTypeEnum } from '../../../../../store/appt.options';
import { selectPetsInAppt } from '../../../../../store/selectors/appt';
import { TimeSlotWrapperNormalContext } from './hooks/useTimeSlotWrapperNormalContext';

type TimeSlotWrapperRenderChildrenProps = {
  hasGroomingService: boolean;
  showServiceCategoryName: boolean;
};

export interface TimeSlotWrapperNormalProps {
  className?: string;
  children: React.ReactNode | ((props: TimeSlotWrapperRenderChildrenProps) => React.ReactNode);
  appointmentId: string;
  isEnableSlotCalender: boolean;
  serviceItemType: ServiceItemType;
}

/**
 * 非 Grooming only 情况使用，展示当前 serviceType 的名字和 icon
 */
export const TimeSlotWrapperNormal = memo<TimeSlotWrapperNormalProps>((props) => {
  const { children, appointmentId, isEnableSlotCalender, serviceItemType, className } = props;

  const [pets, companyCareTypeNameMap] = useSelector(selectPetsInAppt(appointmentId), selectCompanyCareTypeNameMap);
  const getServiceListByPet = useGetServiceListByPet();

  const petsServiceList = useMemo(() => {
    return pets.map((pet) => ({
      petId: pet.petId,
      services: getServiceListByPet(pet, appointmentId),
    }));
  }, [pets, getServiceListByPet, appointmentId]);

  const hasGroomingService = useMemo(() => {
    return petsServiceList.some((s) =>
      s.services.some((service) => service.serviceItemType === ServiceItemType.GROOMING),
    );
  }, [petsServiceList]);

  const showServiceCategoryName = isEnableSlotCalender && hasGroomingService;

  const providerValue = useMemo(() => {
    return {
      petsServiceList,
      isEnableSlotCalender,
      hasGroomingService,
    };
  }, [petsServiceList, isEnableSlotCalender, hasGroomingService]);

  return (
    <TimeSlotWrapperNormalContext.Provider value={providerValue}>
      <Condition if={showServiceCategoryName}>
        <Markup
          variant="caption"
          className={cn('moe-text-secondary moe-flex moe-gap-xxs moe-items-center moe-mb-s', className)}
        >
          {ApptCareTypeEnum.mapLabels[serviceItemType].icon}
          {companyCareTypeNameMap.get(serviceItemType)}
        </Markup>
      </Condition>
      {typeof children === 'function' ? children({ hasGroomingService, showServiceCategoryName }) : children}
    </TimeSlotWrapperNormalContext.Provider>
  );
});

TimeSlotWrapperNormal.displayName = 'TimeSlotWrapperNormal';

import { useSerialCallback } from '@moego/tools';
import { Form, cn, useForm } from '@moego/ui';
import { useDispatch, useSelector, useStore } from 'amos';
import React, { memo, useEffect } from 'react';
import { useSetState } from 'react-use';
import { Button } from '../../../../components/Button/Button';
import { Carousel } from '../../../../components/Carousel/Carousel';
import { DrawerHeader } from '../../../../components/Drawer/DrawerHeader';
import { selectIsEnableSlotCalender } from '../../../../store/calendarLatest/calendar.selectors';
import { type QuickAddTimeSchedulePayload } from '../../../../store/calendarLatest/calendar.types';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { DrawerFooter } from '../../../Calendar/latest/AwesomeCalendar.style';
import { useCheckConflictAlert } from '../../hooks/useCheckConflictAlert';
import { useSpanDays } from '../../hooks/useSpanDays';
import { useCreateApptRouterContext } from '../../modules/QuickAddAppt/QuickAddApptDrawer.router';
import { savePetsServiceAddonDates } from '../../store/appt.actions';
import { selectMainServiceInAppt, selectPetsInAppt, selectPetsServiceAddonDateInfo } from '../../store/appt.selectors';
import { CreateApptId, type ServiceAddOnDateInfo, type ServiceDateInfo } from '../../store/appt.types';
import { PetAvatar } from '../PetAvatar/PetAvatar';
import { useTimeSlotEditPetService } from '../SelectServiceDetail/components/Service/TimeSlot/hooks/useTimeSlot.hooks';
import { PetSchedule } from './PetSchedule/PetSchedule';

interface State {
  activePetId: string;
  initDates: ServiceAddOnDateInfo[];
  draftDates: ServiceAddOnDateInfo[];
}

export interface BoardingStartTimeProps {
  className?: string;
  appointmentId?: string;
  payload: QuickAddTimeSchedulePayload;
}

export const BoardingStartTime = memo<BoardingStartTimeProps>(function BoardingStartTime(props) {
  const { className, appointmentId = CreateApptId } = props;
  const dispatch = useDispatch();
  const store = useStore();
  const [pets, initDates] = useSelector(selectPetsInAppt(appointmentId), selectPetsServiceAddonDateInfo(appointmentId));
  const [state, setState] = useSetState<State>({
    activePetId: pets[0]?.petId,
    initDates,
    draftDates: initDates,
  });
  const checkConflictAlert = useCheckConflictAlert();
  const { handleClearTimeSlotPetService } = useTimeSlotEditPetService(appointmentId);
  const drawerRouter = useCreateApptRouterContext();

  const { activePetId, draftDates } = state;
  const isFirstPet = activePetId === pets[0]?.petId;
  const spanDays = useSpanDays(appointmentId, draftDates);

  const onCancel = useLatestCallback(() => {
    if (store.select(selectIsEnableSlotCalender())) {
      handleClearTimeSlotPetService();
    }
    dispatch(savePetsServiceAddonDates(state.initDates));
    drawerRouter.back();
  });

  const form = useForm({});
  useEffect(() => {
    form.clearErrors();
  }, [draftDates]);

  const handleSave = useSerialCallback(async () => {
    const mainService = store.select(selectMainServiceInAppt(appointmentId));
    const { startDate: startDateStr, endDate: endDateStr } = draftDates.find(
      (i) => i.serviceId === mainService.serviceId,
    ) as ServiceDateInfo;

    await checkConflictAlert({
      appointmentId,
      startDateStr,
      endDateStr,
      petIds: pets.map((i) => i.petId),
    });

    await new Promise((resolve, reject) => {
      form.handleSubmit(resolve, (error) => {
        Object.keys(error).forEach((key) => {
          const target = draftDates.find((i) => i.ownId === key);
          if (target) {
            setState({ activePetId: target.petId });
          }
        });
        reject(error);
      })();
    });

    dispatch(savePetsServiceAddonDates(draftDates));
    drawerRouter.back();
  });

  return (
    <div className={cn(className, 'moe-flex moe-flex-col moe-flex-1 moe-min-w-0')}>
      <DrawerHeader title="Edit schedule" onClick={onCancel} />

      {pets.length > 1 && (
        <Carousel className="moe-mt-[20px] moe-mx-[32px]">
          {pets.map((item) => {
            return (
              <PetAvatar
                isActive={item.petId === activePetId}
                className="moe-flex-1 moe-min-w-[240px]"
                key={item.petId}
                petId={item.petId}
                onClick={() => {
                  setState({ activePetId: item.petId });
                }}
              />
            );
          })}
        </Carousel>
      )}

      <div className="moe-flex-1 moe-min-h-0 moe-py-[20px] moe-px-[32px] moe-overflow-auto">
        <Form form={form} footer={null}>
          {pets.map((pet) => (
            <div
              key={pet.petId}
              className={cn({
                'moe-hidden': pet.petId !== activePetId,
              })}
            >
              <PetSchedule
                spanDayTip={spanDays}
                hiddenScheduleStartEndDate={!isFirstPet}
                appointmentId={appointmentId}
                petId={pet.petId}
                services={pet.services || []}
                initDates={initDates}
                draftDates={draftDates}
                onChangeDraftDates={(draftDates) => {
                  setState({
                    draftDates,
                  });
                }}
              />
            </div>
          ))}
        </Form>
      </div>

      <DrawerFooter className="border">
        <Button
          btnType="white-border"
          buttonRadius="circle"
          className="moe-flex-1 !moe-text-[16px] !moe-font-bold !moe-leading-[20px] !moe-py-[9px]"
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button
          htmlType="submit"
          btnType="primary"
          buttonRadius="circle"
          className="moe-flex-1 !moe-text-[16px] !moe-font-bold !moe-leading-[20px] !moe-py-[9px]"
          onClick={handleSave}
        >
          Save
        </Button>
      </DrawerFooter>
    </div>
  );
});

import { MinorInfoFilled } from '@moego/icons-react';
import { <PERSON><PERSON>, Heading, Text } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { isNumber } from 'lodash';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import React from 'react';
import { Condition } from '../../../../components/Condition';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { businessEvaluationMapBox } from '../../../../store/evaluation/evaluation.boxes';
import { lodgingUnitMapBox } from '../../../../store/lodging/lodgingUnit.boxes';
import { petMapBox } from '../../../../store/pet/pet.boxes';
import { staffMapBox } from '../../../../store/staff/staff.boxes';
import { toNumber } from '../../../../store/utils/identifier';
import { selectApptPetInfo, selectMainServiceInAppt } from '../../store/appt.selectors';

export interface EvaluationTipProps {
  petId: string;
  appointmentId: string;
  onEdit: () => void;
}

export const EvaluationTip: React.FC<EvaluationTipProps> = (props) => {
  const { petId, appointmentId, onEdit } = props;
  const [mainService, petInfo, staffMap, businessEvaluationMap, lodgingUnitMap, pet, business] = useSelector(
    selectMainServiceInAppt(appointmentId),
    selectApptPetInfo(appointmentId),
    staffMapBox,
    businessEvaluationMapBox,
    lodgingUnitMapBox,
    petMapBox.mustGetItem(toNumber(petId)),
    selectCurrentBusiness,
  );

  const currentPetEvaluation = petInfo?.pets.find((p) => p.petId === petId)?.evaluations?.[0];
  const staff = staffMap.mustGetItem(toNumber(currentPetEvaluation?.staffId || 0));
  const service = businessEvaluationMap.mustGetItem(currentPetEvaluation?.evaluationId || '');
  const lodging = lodgingUnitMap.mustGetItem(currentPetEvaluation?.lodgingId || '');
  const apptStartDate = dayjs(mainService.startDate);

  if (!currentPetEvaluation?.evaluationId || !isNumber(currentPetEvaluation?.startTime)) {
    return null;
  }

  return (
    <div className="moe-p-spacing-s moe-rounded-s moe-bg-information-subtle moe-flex moe-gap-xs moe-items-center moe-justify-between">
      <div className="moe-items-center moe-flex">
        <MinorInfoFilled className="moe-text-icon-information moe-mr-8px-100" />
        <div>
          <Text variant="small">
            {service.name} for {pet.petName}:{' '}
            <strong>
              {business.formatDate(apptStartDate)} {business.formatFixedTime(
                currentPetEvaluation.startTime * T_MINUTE,
              )}{' '}
            </strong>
          </Text>
          <Text variant="small">
            <Condition if={staff.firstName}>
              Staff: <strong>{staff.firstName}</strong>
              {lodging.name ? '; ' : ''}
            </Condition>
            <Condition if={lodging.name}>
              Lodging: <strong>{lodging.name}</strong>
            </Condition>
          </Text>
        </div>
      </div>

      <Button
        className="moe-grow-0 moe-shrink-0 moe-basis-auto moe-px-xs"
        size="s"
        variant="secondary"
        onPress={onEdit}
      >
        <Heading size="6">Edit</Heading>
      </Button>
    </div>
  );
};

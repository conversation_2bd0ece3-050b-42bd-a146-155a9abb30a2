import { ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useSelector, useStore } from 'amos';
import React, { useMemo, useRef } from 'react';
import { businessEvaluationMapBox } from '../../../../store/evaluation/evaluation.boxes';
import { serviceMapBox } from '../../../../store/service/service.boxes';
import { toNumber } from '../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { selectPetInAppt } from '../../store/appt.selectors';
import { type ApptPetEvaluationInfo } from '../../store/appt.types';
import { EvaluationActionType, EvaluationSlot, type EvaluationSlotRef } from './EvaluationSlot';

export interface EvaluationCombinedProps {
  petId: string;
  associatedServiceIds?: string[];
  appointmentId: string;
  prevEvaluations?: ApptPetEvaluationInfo[];
  startDate: string;
  startTime: number;
  isBoarding: boolean;
  onChange: (evaluations: ApptPetEvaluationInfo[]) => void;
}

export const EvaluationCombined: React.FC<EvaluationCombinedProps> = (props) => {
  const { petId, appointmentId, associatedServiceIds, onChange, prevEvaluations, startDate, isBoarding, startTime } =
    props;
  const [serviceMap] = useSelector(serviceMapBox);
  const isService = associatedServiceIds?.every(
    (id) => serviceMap.mustGetItem(toNumber(id)).type === ServiceType.SERVICE,
  );
  const store = useStore();
  const evaluationSlotRef = useRef<EvaluationSlotRef>(null);

  const alreadyExistEvaluation = useMemo(() => {
    // 当此组件被挂载时，判断一下是否已经存在 evaluations
    const petInfo = store.select(selectPetInAppt(petId, appointmentId));
    return petInfo?.evaluations[0];
  }, [appointmentId, petId]);

  const updateEvaluationInfo = useLatestCallback(
    (actionType: EvaluationActionType, updateInfo?: Partial<ApptPetEvaluationInfo>) => {
      const evaluationId = evaluationSlotRef.current?.currentEvaluationId;
      let evaluations = prevEvaluations || [];
      const evaluationFields = evaluationSlotRef.current?.getEvaluationFields?.();

      switch (actionType) {
        case EvaluationActionType.ADD:
          if (evaluationId) {
            const evaluationDetail = store.select(businessEvaluationMapBox.mustGetItem(evaluationId));
            evaluations = [
              ...evaluations,
              {
                ...evaluationFields,
                startTime: evaluationFields?.startTime?.getMinutes() || 0,
                petId,
                evaluationId,
                price: evaluationDetail.price,
                duration: evaluationDetail.duration,
                id: '',
              },
            ];
          }
          break;
        case EvaluationActionType.REMOVE:
          evaluations = [];
          break;
        case EvaluationActionType.EDIT:
          evaluations = evaluations.map((evaluation) => {
            if (evaluation.petId === petId) {
              return { ...evaluation, ...updateInfo } as ApptPetEvaluationInfo;
            }
            return evaluation;
          });
          break;
      }
      onChange(evaluations);
    },
  );

  // 该组件只用于 service，不考虑 addon
  if (!isService) {
    return null;
  }

  return (
    <EvaluationSlot
      petId={petId}
      associatedServiceIds={associatedServiceIds}
      hideSlot={!!alreadyExistEvaluation}
      startDate={startDate || ''}
      startTime={startTime}
      isBoarding={isBoarding}
      updateEvaluationInfo={updateEvaluationInfo}
      ref={evaluationSlotRef}
    />
  );
};

import { EvaluationStatus } from '@moego/api-web/moego/models/customer/v1/customer_pet_enums';
import { MinorCheckFilled, MinorWarningFilled } from '@moego/icons-react';
import { But<PERSON>, Heading, Text } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { isEqual, isNumber } from 'lodash';
import React, { forwardRef, memo, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Condition } from '../../../../components/Condition';
import { Switch } from '../../../../components/SwitchCase';
import { listEvaluationStatusByPetService } from '../../../../store/evaluation/evaluation.actions';
import { businessEvaluationMapBox } from '../../../../store/evaluation/evaluation.boxes';
import { isNormal } from '../../../../store/utils/identifier';
import { useBool } from '../../../../utils/hooks/useBool';
import { useCancelableCallback } from '../../../../utils/hooks/useCancelableCallback';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { ReportActionName } from '../../../../utils/reportType';
import { reportData } from '../../../../utils/tracker';
import { type ApptPetEvaluationInfo } from '../../store/appt.types';
import {
  AddEvaluationForm,
  type AddEvaluationFormRef,
  type EvaluationApptFields,
} from '../EvaluationCombined/AddEvaluationForm';

export interface EvaluationSlotProps {
  petId: string;
  associatedServiceIds?: string[];
  hideSlot?: boolean;
  startDate: string;
  startTime?: number;
  isBoarding: boolean;
  updateEvaluationInfo: (actionType: EvaluationActionType, updateInfo?: Partial<ApptPetEvaluationInfo>) => void;
  /**
   * 传入以后不会再请求 evaluation status
   * 而是直接使用 defaultEvaluationId
   *
   * @type {string}
   * @memberof EvaluationSlotProps
   */
  defaultEvaluationId?: string;
}

export interface EvaluationSlotRef {
  currentEvaluationId?: string;
  getEvaluationFields?: () => EvaluationApptFields;
}

export enum EvaluationActionType {
  ADD = 'add',
  EDIT = 'edit',
  REMOVE = 'remove',
}

const TipsTitle = {
  [EvaluationStatus.PASS]: '',
  [EvaluationStatus.FAIL]: 'Evaluation failed.',
  [EvaluationStatus.UNSPECIFIED]: 'Missing evaluation.',
};

export const EvaluationSlot = memo(
  forwardRef<EvaluationSlotRef, EvaluationSlotProps>((props, ref) => {
    const {
      petId,
      associatedServiceIds,
      hideSlot,
      startDate,
      isBoarding,
      updateEvaluationInfo,
      startTime,
      defaultEvaluationId,
    } = props;
    const dispatch = useDispatch();
    const [evaluationId, setEvaluationId] = useState<string | undefined>(defaultEvaluationId);
    const [evaluationDetail] = useSelector(businessEvaluationMapBox.mustGetItem(evaluationId));
    const [evaluationStatus, setEvaluationStatus] = useState<EvaluationStatus | undefined>(EvaluationStatus.PASS);
    const displayForm = useBool();
    const addEvaluationFormRef = useRef<AddEvaluationFormRef>(null);
    const [associatedIds, setAssociatedIds] = useState<string[] | undefined>();

    const fetchEvaluationStatus = useCancelableCallback(async (signal) => {
      if (!hideSlot && associatedServiceIds?.length && isNormal(petId)) {
        const res = await dispatch(
          listEvaluationStatusByPetService(
            {
              petServiceIds: associatedServiceIds.map((id) => {
                return {
                  petId: petId,
                  serviceId: id,
                };
              }),
            },
            signal,
          ),
        );
        const evaluationInfo = res?.evaluationStatusResults.find(
          (item) => item.finalEvaluationStatus !== EvaluationStatus.PASS,
        );

        setEvaluationStatus(evaluationInfo?.finalEvaluationStatus);
        setEvaluationId(evaluationInfo?.evaluationId);
      }
    });

    useEffect(() => {
      if (isNormal(defaultEvaluationId)) {
        setEvaluationId(defaultEvaluationId);
        setEvaluationStatus(EvaluationStatus.UNSPECIFIED);
      }
    }, [defaultEvaluationId]);

    useEffect(() => {
      if (!isEqual(associatedIds, associatedServiceIds) && !isNormal(defaultEvaluationId)) {
        setAssociatedIds(associatedServiceIds);
        fetchEvaluationStatus();
      }
    }, [fetchEvaluationStatus, associatedServiceIds, associatedIds, defaultEvaluationId]);

    const handleRemoveEvaluation = () => {
      reportData(ReportActionName.combinedEvaluationRemove);
      updateEvaluationInfo(EvaluationActionType.REMOVE);
      displayForm.close();
    };

    const handleAddEvaluation = useLatestCallback(() => {
      reportData(ReportActionName.combinedEvaluationAdd);
      if (evaluationId) {
        displayForm.open();
        updateEvaluationInfo(EvaluationActionType.ADD);
      }
    });

    const handleUpdateStaff = useLatestCallback((newStaffId: string) => {
      updateEvaluationInfo(EvaluationActionType.EDIT, { staffId: newStaffId });
    });

    useImperativeHandle(ref, () => ({
      currentEvaluationId: evaluationId,
      getEvaluationFields: addEvaluationFormRef.current?.form?.getValues,
    }));

    if (evaluationStatus === EvaluationStatus.PASS || !isNumber(evaluationStatus) || hideSlot) {
      return null;
    }

    return (
      <Switch>
        <Switch.Case if={displayForm.value}>
          <div className="moe-p-spacing-s moe-rounded-s moe-bg-information-subtle moe-flex-col moe-flex moe-gap-s">
            <div className="moe-flex moe-items-center moe-justify-between">
              <div className="moe-items-center moe-flex">
                <MinorCheckFilled className="moe-text-icon-success moe-mr-8px-100" />
                <Text variant="small">Evaluation added {isBoarding && 'to the 1st day'}.</Text>
              </div>
              <Button size="s" variant="secondary" onPress={handleRemoveEvaluation}>
                <Heading size="6">Remove evaluation</Heading>
              </Button>
            </div>
            <Condition if={evaluationId && displayForm.value}>
              <AddEvaluationForm
                onUpdateStaff={handleUpdateStaff}
                ref={addEvaluationFormRef}
                serviceId={evaluationId!}
                startDate={startDate}
                startTime={startTime || 0}
                serviceTime={evaluationDetail.duration || 0}
                petId={petId}
                onChange={(val) => {
                  updateEvaluationInfo(EvaluationActionType.EDIT, val);
                }}
              />
            </Condition>
          </div>
        </Switch.Case>
        <Switch.Case else>
          <div className="moe-p-spacing-s moe-rounded-s moe-bg-warning-subtle moe-flex moe-gap-xs moe-items-center moe-justify-between">
            <div className="moe-items-center moe-flex">
              <MinorWarningFilled className="moe-text-icon-warning moe-mr-8px-100" />
              <Text variant="small">{TipsTitle[evaluationStatus]}</Text>
            </div>

            <Button
              className="moe-grow-0 moe-shrink-0 moe-basis-auto moe-px-xs"
              size="s"
              variant="secondary"
              onPress={handleAddEvaluation}
            >
              <Heading size="6">Add evaluation to this stay</Heading>
            </Button>
          </div>
        </Switch.Case>
      </Switch>
    );
  }),
);

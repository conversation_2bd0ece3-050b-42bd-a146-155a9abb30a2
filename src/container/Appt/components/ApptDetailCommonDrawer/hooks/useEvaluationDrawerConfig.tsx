import { useDispatch, useSelector } from 'amos';
import {
  setEvaluationApptDetailDrawer,
  setEvaluationApptDetailDrawerEdit,
} from '../../../../../store/evaluation/evaluation.actions';
import {
  EvaluationApptDetailStep,
  evaluationApptDetailDrawerBox,
  evaluationApptDetailDrawerEditBox,
} from '../../../../../store/evaluation/evaluation.boxes';
import { useCloseApptDetailDrawer } from '../../../../../utils/hooks/useCloseApptDetailDrawer';
import { type DrawerConfig } from '../types';

export const useEvaluationDrawerConfig = (): DrawerConfig => {
  const dispatch = useDispatch();
  const { closeApptDetailDrawer } = useCloseApptDetailDrawer();
  const [{ stepInfo }] = useSelector(evaluationApptDetailDrawerEditBox);

  const handleCloseDrawer = () => {
    closeApptDetailDrawer();
    dispatch([
      setEvaluationApptDetailDrawerEdit(evaluationApptDetailDrawerEditBox.initialState),
      setEvaluationApptDetailDrawer(evaluationApptDetailDrawerBox.initialState),
    ]);
  };

  return {
    drawerConfig: { onClose: handleCloseDrawer },
    confirmModalConfig: {
      showConfirm: stepInfo.type === EvaluationApptDetailStep.RescheduleDateAndTime,
    },
  };
};

import { datadogRum } from '@datadog/browser-rum';
import { useDispatch, useSelector } from 'amos';
import React, { useEffect, useMemo } from 'react';
import ReactDOM from 'react-dom';
import { PATH_GROOMING_CALENDAR, PATH_MAP_VIEW } from '../../../../router/paths';
import { setApptDetailDrawer } from '../../../../store/calendarLatest/actions/public/calendar.actions';
import { apptDetailDrawerBox } from '../../../../store/calendarLatest/calendar.boxes';
import { BusinessIdProvider } from '../../../../utils/BusinessUtil';
import { useDrawer } from '../../../../utils/Drawer';
import { useToggleIntercom } from '../../../../utils/hooks/useToggleIntercom';
import { useUnsavedConfirmGlobalV2 } from '../../../../utils/hooks/useUnsavedConfirmGlobalV2';
import { DataDogActionName } from '../../../../utils/logger';
import { CalendarDrawer } from '../../../Calendar/latest/ApptCalendar/components/CalendarDrawer';
import { useDraftConfirmConfig } from '../../modules/QuickAddAppt/hooks/useQuickAddLeaveConfirm';
import { apptInfoMapBox } from '../../store/appt.boxes';
import { useSpecificTypeDrawer } from './hooks/useSpecificTypeDrawer';

export const ApptDetailCommonDrawer = () => {
  const [apptInfoMap, drawerDetail] = useSelector(apptInfoMapBox, apptDetailDrawerBox);
  const { ticketId, visible } = drawerDetail;
  const currentApptBusinessId = apptInfoMap.mustGetItem(ticketId.toString())?.appointment?.businessId;
  const { drawerConfig, DrawerComp, confirmModalConfig } = useSpecificTypeDrawer();
  const apptDetailDrawer = useDrawer('apptDetail');
  const dispatch = useDispatch();
  const { getDoubleConfirmConfig } = useDraftConfirmConfig();

  const handleCloseDrawer = () => {
    datadogRum.startDurationVital(DataDogActionName.CLOSE_APPT_DRAWER);
    ReactDOM.unstable_batchedUpdates(() => {
      const closeDrawer = () => {
        dispatch(setApptDetailDrawer({ visible: false }));
      };

      if (drawerConfig?.onClose) {
        drawerConfig?.onClose(closeDrawer);
        return;
      }

      closeDrawer();
    });
  };

  useEffect(() => {
    // from 原有的appDetailDrawer逻辑：和waitList drawer互相唤起的时候，会判断一下，对方是否是打开状态，再做逻辑
    visible ? apptDetailDrawer.openDrawer() : apptDetailDrawer.closeDrawer();
  }, [visible]);

  const confirmConfig = useMemo(() => getDoubleConfirmConfig(), [getDoubleConfirmConfig]);
  useUnsavedConfirmGlobalV2({
    modalProps: {
      ...confirmConfig,
      confirmText: confirmConfig.okText,
      onConfirm: () => {
        return Promise.reject();
      },
      onCancel: () => {
        handleCloseDrawer();
        return Promise.resolve();
      },
    },
    noNeedConfirmPath: [PATH_GROOMING_CALENDAR.path, PATH_MAP_VIEW.path],
    alwaysRun: true,
    ...confirmModalConfig,
  });

  useToggleIntercom(visible);

  return (
    <BusinessIdProvider value={currentApptBusinessId}>
      <CalendarDrawer {...drawerConfig} size="l" isOpen={visible} onClose={handleCloseDrawer}>
        <DrawerComp />
      </CalendarDrawer>
    </BusinessIdProvider>
  );
};

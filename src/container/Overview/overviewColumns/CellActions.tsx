import { MajorMoreOutlined } from '@moego/icons-react';
import { Button, Dropdown, Tooltip, cn } from '@moego/ui';
import { DropdownMenu } from '@moego/ui/dist/esm/components/Dropdown/DropdownMenu';
import { DropdownTriggerButton } from '@moego/ui/dist/esm/components/Dropdown/DropdownTrigger';
import { type CellContext } from '@moego/ui/dist/esm/components/Table/Table.types';
import { useDispatch } from 'amos';
import React, { memo, useEffect, useRef, type ReactNode } from 'react';
import { useCardVisible, useCardVisibleObserver } from '../../../components/CardVisible';
import { toastApi } from '../../../components/Toast/Toast';
import { setApptDetailDrawer } from '../../../store/calendarLatest/actions/public/calendar.actions';
import {
  deleteOverviewItemByApptId,
  setOverviewSilentRefresh,
} from '../../../store/overview/actions/public/overview.actions';
import { type OverviewRecordModel } from '../../../store/overview/overview.boxes';
import { useAsyncCallback } from '../../../utils/hooks/useAsyncCallback';
import { prefetchTicket } from '../../Appt/modules/ApptDetailDrawer/hooks/useTicketActions';
import { NextActionLabel } from '../../Appt/modules/ApptDetailDrawer/hooks/useTicketStatusCTA';
import { setApptStoreInfo } from '../../Appt/store/actions/private/appt';
import { useSpecialCheckInProcess } from '../hooks/useSpecialCheckInProcess';
import { useCTAAction } from './useCTAAction';
import { useMoreActions, type OverviewActionItem } from './useMoreActions';
import { usePaymentActions } from './usePaymentActions';
import { OnlineBookingReporter } from '../../../utils/reportData/reporter/onlineBookingReporter';

type CellActionsProps = {
  ctx: CellContext<OverviewRecordModel, unknown>;
  onChange: () => void;
  goNext?: () => void;
  onMainActionClick?: () => void;
  onMoreActionClick?: () => void;
  className?: string;
};

function wrapWithTooltip(action: OverviewActionItem, ele: ReactNode) {
  if (action.showTooltip) {
    return (
      <Tooltip
        content={action.tooltipContent}
        side="top"
        align="center"
        delay={0}
        closeDelay={0}
        isDisabled={!action.showTooltip}
      >
        {ele}
      </Tooltip>
    );
  }
  return ele;
}

/**
 * 整合appt主要按钮,其中第一个按钮从dropdown里挪出去.展示为主按钮
 */
export const CellActionsInner: React.FC<CellActionsProps> = memo((props) => {
  const { ctx, onChange, goNext, onMainActionClick, onMoreActionClick } = props;
  const { id: ticketId } = ctx.row.original.appointment;
  const dispatch = useDispatch();

  const ctaAction = useCTAAction({
    ctx,
    onChange,
    goNext,
  });
  let optList = useMoreActions({
    ctx,
    onChange,
  });

  const paymentActionList = usePaymentActions({ ctx, onChange });

  optList = [...paymentActionList, ...optList];
  // cta操作位:最高优先级
  if (ctaAction) {
    optList.unshift(ctaAction);
  }

  // 取第一个为主action
  const mainAction = optList.shift() as OverviewActionItem;

  // 由于appt设计之初仅考虑了单个数据展示的情况.所以overview的任何操作都需要模拟appt drawer的行为
  // 这里就是预请求上下文数据,不然很多操作拿不到值
  const preAllAction = async () => {
    const { serviceItemTypes, invoice } = ctx.row.original;
    await prefetchTicket({
      appointmentId: ticketId,
      serviceItemTypes,
      invoiceId: invoice.invoiceId,
    });

    dispatch(setApptDetailDrawer({ ticketId: Number(ticketId), scene: 'overview' }));
  };

  const { handleSpecialCheckInProcess } = useSpecialCheckInProcess(+ticketId);

  const mainActionOnPress = useAsyncCallback(async () => {
    const { appointment, serviceItemTypes } = ctx.row.original;
    if (NextActionLabel.Confirmed === mainAction.label) {
      OnlineBookingReporter.reportApptClickCheckIn(+ticketId);
      dispatch(setApptStoreInfo(ticketId, { appointment, serviceItemTypes }));
      // 如果有特殊 checkin process，则这里处理完就返回
      // 不放在 useCTAAction 里处理是因为放进去之后，这里会跟 setOverviewSilentRefresh 有冲突，所以只能在这里处理
      if (handleSpecialCheckInProcess()) {
        return;
      }
      dispatch(setOverviewSilentRefresh({ [ticketId]: true }));
    }
    if (NextActionLabel.Ready === mainAction.label) {
      OnlineBookingReporter.reportApptClickCheckOut(+ticketId);
    }

    onMainActionClick?.();

    // Overview 里点 check in 不需要预请求 appt 数据，因为 status 状态变更不需要预请求数据，变更后会重新拉取 appt 数据
    NextActionLabel.Confirmed !== mainAction.label && (await preAllAction());

    try {
      await mainAction.onClick?.();
    } catch {
      dispatch(setOverviewSilentRefresh({ [ticketId]: false }));
      return;
    }

    if (NextActionLabel.Confirmed === mainAction.label) {
      dispatch(deleteOverviewItemByApptId(ticketId));
      toastApi.success('Appointment successfully checked in.');
    }
  });

  const moreActionsPress = useAsyncCallback(async (idx: number | string) => {
    onMoreActionClick?.();
    await preAllAction();
    await optList[idx as number]?.onClick?.();
  });

  return (
    <>
      {wrapWithTooltip(
        mainAction,
        <Button
          className="moe-max-w-[146px] moe-justify-start"
          variant="tertiary"
          align="start"
          onPress={mainActionOnPress}
          isLoading={mainActionOnPress.loading}
          isDisabled={mainAction.disabled}
        >
          {mainAction.label}
        </Button>,
      )}
      <Dropdown>
        <DropdownTriggerButton
          isLoading={moreActionsPress.loading}
          variant="tertiary-legacy"
          icon={<MajorMoreOutlined />}
          className="moe-p-none moe-min-w-[40px] hover:!moe-bg-neutral-sunken-1 active:!moe-bg-neutral-sunken-2"
          classNames={{ icon: 'moe-mr-none' }}
        />
        <DropdownMenu onAction={moreActionsPress}>
          {optList.map((opt, idx) => (
            <Dropdown.MenuItem
              title={wrapWithTooltip(opt, opt.label)}
              key={idx}
              isDisabled={opt.disabled}
              textValue={`${idx}`}
            />
          ))}
        </DropdownMenu>
      </Dropdown>
    </>
  );
});
CellActionsInner.displayName = 'CellActionsInner';

export const CellActions: React.FC<CellActionsProps> = memo((props) => {
  const { ctx, className } = props;
  const { id: ticketId } = ctx.row.original.appointment;
  const visible = useCardVisible(ticketId);
  const observerRef = useCardVisibleObserver();
  const divRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const divCurrent = divRef.current;
    const observerCurrent = observerRef.current;
    if (divCurrent && observerCurrent) {
      observerCurrent.observe(divCurrent);
    }
    return () => {
      if (divCurrent && observerCurrent) {
        observerCurrent.unobserve(divCurrent);
      }
    };
  }, [observerRef]);

  return (
    <div className={cn('moe-flex moe-gap-s moe-justify-end', className)} data-card-id={ticketId} ref={divRef}>
      {visible && <CellActionsInner {...props} />}
    </div>
  );
});

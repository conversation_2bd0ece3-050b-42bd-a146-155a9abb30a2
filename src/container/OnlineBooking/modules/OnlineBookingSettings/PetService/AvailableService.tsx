import { But<PERSON>, Heading } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo } from 'react';
import { useHistory } from 'react-router';
import { WithPermission } from '../../../../../components/GuardRoute/WithPermission';
import { PATH_SERVICE_SETTING } from '../../../../../router/paths';
import { getPetSizeList } from '../../../../../store/onlineBooking/actions/private/petSize.actions';
import { useBizIdReadyEffect } from '../../../../../utils/hooks/useBizIdReadyEffect';
import { ServicesNav } from '../../../../settings/Settings/ServicesSetting/types';
import { SettingsTitle } from '../components/SettingsTitle';
import { OnlineBookingNav } from '../types';
import { ServicePreference } from './components/ServicePreference';
import { BDBookingService } from './components/BDBookingService/BDBookingService';
import { selectBDFeatureEnable } from '../../../../../store/company/company.selectors';
import { GroomingOnlyService } from './components/GroomingOnlyService/GroomingOnlyService';

export interface AvailableServiceProps {
  className?: string;
}

export const AvailableService = memo<AvailableServiceProps>(() => {
  const history = useHistory();
  const [isEnableBD] = useSelector(selectBDFeatureEnable);
  const dispatch = useDispatch();

  useBizIdReadyEffect(() => {
    dispatch(getPetSizeList());
  }, []);

  const handleJumpToServiceSetting = () => {
    history.push(PATH_SERVICE_SETTING.build({ panel: ServicesNav.Services }));
  };

  return (
    <div className="moe-font-manrope moe-pb-[20px]">
      <SettingsTitle
        title={OnlineBookingNav.Services}
        headerRight={
          <WithPermission permissions={['viewSetting', 'accessServiceSettings']}>
            <Button onPress={handleJumpToServiceSetting}>Set up services</Button>
          </WithPermission>
        }
      />
      <ServicePreference />
      <div className="moe-mt-[48px]">
        <Heading size="3" className="moe-mb-m">
          Available services
        </Heading>

        {isEnableBD ? <BDBookingService /> : <GroomingOnlyService />}
      </div>
    </div>
  );
});

import { cn, <PERSON><PERSON>, <PERSON>, Spin, Text } from '@moego/ui';
import React, { memo } from 'react';
import { PATH_SETTING_STAFF } from '../../../../../../../../../router/paths';
import { SMTestIds } from '../../../../../../../../../config/testIds/shiftManagement';
import { DateOverrideType } from '../../../../../../../../../store/staffSchedule/staffSchedule.types';
import { OverridesBySlot } from '../../../../../../../../settings/Settings/StaffSetting/ShiftManagement/components/StaffSchedule/DateOverride/OverridesBySlot';
import { isNormal } from '../../../../../../../../../store/utils/identifier';
import { useDispatch } from 'amos';
import { getStaffSlotOverride } from '../../../../../../../../../store/staffSchedule/staffSchedule.actions';
import { useAsync } from 'react-use';

export interface OBDateOverrideProps {
  isWorkingHour: boolean;
  businessId: number;
  staffId: number;
  className?: string;
}

/**
 * 暂时隐藏，产品还没决定好要不要上
 */
export const OBDateOverride = memo<OBDateOverrideProps>((props) => {
  const { isWorkingHour, businessId, staffId, className } = props;
  const dispatch = useDispatch();

  const getOverrideDataAsync = async (staffId: number) => {
    if (!isNormal(staffId) || isWorkingHour) return;

    await dispatch(getStaffSlotOverride([staffId]));
  };

  const { loading } = useAsync(async () => {
    await getOverrideDataAsync(staffId);
  }, [staffId, isWorkingHour]);

  if (isWorkingHour) {
    return null;
  }

  return (
    <Spin isLoading={loading}>
      <div className={cn('moe-flex moe-gap-[12px] moe-flex-col moe-pl-[20px]', className)}>
        <div className="moe-flex moe-gap-xxs moe-flex-col">
          <Heading size="5" className="moe-text-primary">
            Ongoing date override
          </Heading>

          <Text variant="small" className="moe-text-tertiary">
            The date override settings for Online Booking follow settings in{' '}
            <Link
              onClick={() => {
                window.open(PATH_SETTING_STAFF.build({ panel: 'workingHours' }), '_blank', 'noopener,noreferrer');
              }}
              className="moe-text-tertiary"
            >
              Shift Management
            </Link>
            .
          </Text>
        </div>

        <div className="moe-pb-m moe-pt-[6px]" data-testid={SMTestIds.SettingDateOverrideList}>
          <OverridesBySlot editable={false} businessId={businessId} type={DateOverrideType.Ongoing} staffId={staffId} />
        </div>
      </div>
    </Spin>
  );
});

OBDateOverride.displayName = 'OBDateOverride';

import { But<PERSON>, Heading, cn } from '@moego/ui'; // Import the cn function
import upperFirst from 'lodash/upperFirst';
import React, { memo } from 'react';
import { Condition } from '../../../../../../components/Condition';
import {
  type OnlineBookingQuestionCategory,
  OnlineBookingQuestionCategoryMap,
} from '../../../../../../store/onlineBooking/models/OBQuestion.base';
import { isServiceQuestionCategory } from '../../../../../../store/onlineBooking/models/OBQuestion.utils';
import { useGetQuestionListIsEmpty } from '../hooks/useGetQuestionListIsEmpty';

export interface QuestionTitleProps {
  onSelectQuestions?: () => void;
  type: OnlineBookingQuestionCategory;
  className?: string; // Add className prop
}

export const QuestionTitle = memo(function QuestionTitle(props: QuestionTitleProps) {
  const { type, onSelectQuestions, className } = props;
  const typeText = OnlineBookingQuestionCategoryMap[type];
  const isQuestionListEmpty = useGetQuestionListIsEmpty(type);
  const isServiceQuestion = isServiceQuestionCategory(type);
  return (
    <div className={cn('moe-flex moe-justify-between moe-items-center moe-h-[28px] moe-mb-[24px]', className)}>
      <Heading size="3">{upperFirst(typeText)} questions</Heading>
      <Condition if={!isQuestionListEmpty}>
        <Button variant="tertiary" onPress={onSelectQuestions}>
          {isServiceQuestion ? 'Select questions' : 'Add question'}
        </Button>
      </Condition>
    </div>
  );
});

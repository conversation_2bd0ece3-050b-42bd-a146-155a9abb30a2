import { Spin } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useState } from 'react';
import { toast<PERSON><PERSON> } from '../../../../../../components/Toast/Toast';
import { updateQuestionList } from '../../../../../../store/onlineBooking/actions/private/questions.actions';
import { OnlineBookingQuestionCategory } from '../../../../../../store/onlineBooking/models/OBQuestion.base';
import { selectDraftQuestionDirty } from '../../../../../../store/onlineBooking/settings/questions.selectors';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { Update } from '../../components/Update';
import { QuestionDrawer } from '../components/QuestionDrawer';
import { QuestionTable } from '../components/QuestionTable';
import { QuestionTitle } from '../components/QuestionTitle';
import { TabDescription } from '../components/TabDescription';
import { usePreloadingQuestions } from '../hooks/usePreloadingQuestions';
import { TabDescriptionType } from '../types';

const boardingType = OnlineBookingQuestionCategory.ForBoarding;
const daycareType = OnlineBookingQuestionCategory.ForDaycare;

const typeList = [boardingType, daycareType];

export const ServiceQuestions = memo(function ServiceQuestions() {
  const isBoardingLoading = usePreloadingQuestions(boardingType);
  const isDaycareLoading = usePreloadingQuestions(daycareType);
  const dispatch = useDispatch();
  const showQuestionsDrawer = useBool();
  const [isBoardingDirty, isDaycareDirty] = useSelector(
    selectDraftQuestionDirty(boardingType),
    selectDraftQuestionDirty(daycareType),
  );
  const [currentDrawerType, setCurrentDrawerType] = useState(boardingType);
  const handleSubmit = useSerialCallback(async () => {
    if (isBoardingDirty) {
      await dispatch(updateQuestionList(boardingType));
    }
    if (isDaycareDirty) {
      await dispatch(updateQuestionList(daycareType));
    }
    toastApi.success('Questions saved');
  });
  const isDirty = isBoardingDirty || isDaycareDirty;
  const handleOpenDrawer = useLatestCallback((type: OnlineBookingQuestionCategory) => {
    setCurrentDrawerType(type);
    showQuestionsDrawer.open();
  });
  return (
    <div>
      <TabDescription description={TabDescriptionType.Service} />
      <Spin
        classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }}
        isLoading={isBoardingLoading || isDaycareLoading || handleSubmit.isBusy()}
      >
        <div className="moe-flex moe-flex-col moe-gap-y-[40px]">
          {typeList.map((item) => {
            return (
              <div key={item}>
                <QuestionTitle type={item} onSelectQuestions={() => handleOpenDrawer(item)} className="moe-w-[724px]" />
                <QuestionTable type={item} onSelectQuestions={() => handleOpenDrawer(item)} />
              </div>
            );
          })}
        </div>
        <QuestionDrawer
          type={currentDrawerType}
          isOpen={showQuestionsDrawer.value}
          onClose={showQuestionsDrawer.close}
          canAddCustomQuestion={false}
        />
      </Spin>
      <Update
        updateTxt="Save"
        active={isDirty}
        onSubmit={handleSubmit}
        buttonVisible={!showQuestionsDrawer.value && isDirty}
      />
    </div>
  );
});

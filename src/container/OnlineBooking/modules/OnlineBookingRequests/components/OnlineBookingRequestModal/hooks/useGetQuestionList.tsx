import { useSelector } from 'amos';
import { useMemo } from 'react';
import { onlineBookingQuestionMapBox } from '../../../../../../../store/onlineBooking/settings/questions.boxes';
import { useQuestionNavList } from '../../../../OnlineBookingSettings/Questions/hooks/useQuestionNavList';

export const useGetQuestionList = (type: number, omitKeyList: string[]) => {
  const [questionMap] = useSelector(onlineBookingQuestionMapBox);
  const { systemList, customList, preservedList } = useQuestionNavList(type);

  return useMemo(
    () =>
      [...preservedList, ...systemList, ...customList]
        .map((id) => questionMap.mustGetItem(id))
        .filter(({ key }) => !omitKeyList.includes(key)),
    [questionMap, systemList, customList, preservedList, omitKeyList],
  );
};

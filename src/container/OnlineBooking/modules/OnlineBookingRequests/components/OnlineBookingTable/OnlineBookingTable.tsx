import { Input, type SortingState, Table } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { getOBAutoMessageList } from '../../../../../../store/autoMessage/autoMessage.actions';
import { getBookingRequestList } from '../../../../../../store/onlineBooking/actions/private/onlineBooking.actions';
import { OnlineBookingRequestListType } from '../../../../../../store/onlineBooking/models/OnlineBookingRequest';
import { onlineBookingRequestMapBox } from '../../../../../../store/onlineBooking/onlineBooking.boxes';
import { selectBusinessOnlineBookingRequests } from '../../../../../../store/onlineBooking/onlineBooking.selectors';
import { getCompanyStaffList } from '../../../../../../store/staff/staff.actions';
import { useQuery } from '../../../../../../store/utils/useQuery';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { useOnlineBookingRequest } from '../../hooks/useOnlineBookingRequest';
import { useOnlineBookingRequestColumns } from './hooks/useOnlineBookingRequestColumns';
import { getAllCompanyBasicServiceInfoList } from '../../../../../../store/service/actions/public/service.actions';
import { useDebounce } from 'react-use';

export const OnlineBookingTable = memo(() => {
  const columns = useOnlineBookingRequestColumns();
  const dispatch = useDispatch();
  const [requestList, onlineBookingRequestMap] = useSelector(
    selectBusinessOnlineBookingRequests(OnlineBookingRequestListType.Requests),
    onlineBookingRequestMapBox,
  );
  const onlineBookingRequest = useOnlineBookingRequest();
  const [sorting, setSorting] = useState<SortingState>([{ desc: true, id: 'createdAt' }]);
  const [keyword, setKeyword] = useState('');
  const [layoutEle] = document.getElementsByClassName('online-booking-layout') || [];
  useQuery(getCompanyStaffList());
  useQuery(getAllCompanyBasicServiceInfoList({ withAddon: false }));
  useQuery(getOBAutoMessageList());

  useEffect(() => {
    dispatch(getBookingRequestList({ type: OnlineBookingRequestListType.Requests, keyword }));
  }, []);

  const runCount = useRef(0);
  useDebounce(
    () => {
      if (runCount.current++) {
        dispatch(getBookingRequestList({ type: OnlineBookingRequestListType.Requests, keyword }));
      }
    },
    300,
    [keyword],
  );

  const tablePagination = useMemo(
    () => ({
      pageIndex: requestList.pageNum,
      pageSize: requestList.pageSize,
      totalSize: requestList.total,
    }),
    [requestList],
  );

  const handleSortingChange = useSerialCallback(async (val: SortingState) => {
    const [sort] = val || [];
    await dispatch(
      getBookingRequestList({
        type: OnlineBookingRequestListType.Requests,
        orderBy: sort?.id,
        orderType: sort?.desc ? 'desc' : 'asc',
      }),
    );
    setSorting(val);
  });

  const dataSource = requestList.getList().map((id) => onlineBookingRequestMap.mustGetItem(id));

  return (
    <div>
      <div className="moe-my-s">
        <Input.Search
          className="moe-min-w-[460px] !moe-w-[500px] moe-flex-1"
          placeholder="Search by pet name, customer name, or phone number"
          value={keyword}
          onChange={(keyword) => {
            setKeyword(keyword);
          }}
        />
      </div>
      <Table
        stickyHeader
        columns={columns}
        data={dataSource}
        sorting={sorting}
        getRowId={(row) => row.id.toString()}
        isLoading={requestList.isLoading()}
        stickyContainer={(layoutEle as HTMLElement) ?? undefined}
        classNames={{
          base: 'moe-mt-[20px]',
          bodyRow: 'moe-cursor-pointer',
          headWrapper: 'moe-top-[-24px]',
          headCell: 'last:moe-pl-[32px]',
        }}
        onRowClick={(row) => {
          onlineBookingRequest(row.original.groomingId);
        }}
        pagination={tablePagination}
        onSortingChange={handleSortingChange}
        onPaginationChange={({ pageIndex, pageSize }) => {
          const [sort] = sorting || [];
          dispatch(
            getBookingRequestList({
              type: OnlineBookingRequestListType.Requests,
              pageSize: pageSize,
              pageNum: pageIndex,
              orderBy: sort?.id,
              orderType: sort?.desc ? 'desc' : 'asc',
            }),
          );
        }}
      />
    </div>
  );
});

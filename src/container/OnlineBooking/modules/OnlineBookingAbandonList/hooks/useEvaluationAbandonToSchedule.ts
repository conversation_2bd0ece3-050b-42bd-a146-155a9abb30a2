import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { toastApi } from '../../../../../components/Toast/Toast';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { EvaluationQuickAddSource } from '../../../../../store/evaluation/evaluation.boxes';
import { useInitBusinessApplicableEvaluation } from '../../../../../store/evaluation/evaluation.hooks';
import { type OnlineBookingAbandonRecordType } from '../../../../../store/onlineBooking/abandonBooking.boxes';
import { isNormal } from '../../../../../store/utils/identifier';
import { useCreateEvaluationDrawer } from '../../../../Appt/hooks/useCreateEvaluationDrawer';
import { EvaluationNotFoundError } from '../../../../Appt/modules/QuickAddEvaluationApptDrawer/utils/errors';
import { useEvaluationServiceDetailsEditModal } from '../../OnlineBookingRequests/hooks/useEvaluationServiceDetailsEditModal';
import { useGetCreateTicketRouteState } from './useGetCreateTicketRouteState';

export const useEvaluationAbandonToSchedule = () => {
  const [business] = useSelector(selectCurrentBusiness);
  const getCreateTicketRouteState = useGetCreateTicketRouteState();
  const openCreateEvaluation = useCreateEvaluationDrawer();

  const { isMultipleEvaluationMode, currentEvaluation, evaluationList } = useInitBusinessApplicableEvaluation(true);
  const evaluationServiceDetailsEditModal = useEvaluationServiceDetailsEditModal();

  return async (abandonRecord: OnlineBookingAbandonRecordType, clientId?: number, petIdList?: number[]) => {
    const { date, time, presetInfo } = await getCreateTicketRouteState(abandonRecord);
    const petId = petIdList?.[0] || presetInfo?.petAndServices?.[0]?.pet;

    if (!currentEvaluation) {
      toastApi.error('Evaluation is inactive based on your settings.');
      throw new EvaluationNotFoundError();
    }

    let finalEvaluation = currentEvaluation.toJSON();
    if (isMultipleEvaluationMode) {
      const res = await evaluationServiceDetailsEditModal({
        petId: petId.toString(),
        isEvaluationPickerEnable: true,
        isServiceTimeEnable: false,
        isStaffSelectEnable: false,
      });
      const newEvaluationId = res?.evaluationId;
      if (!newEvaluationId) {
        return;
      }
      const newEvaluation = evaluationList.find((evaluation) => evaluation.id === newEvaluationId);
      if (newEvaluation) {
        finalEvaluation = newEvaluation.toJSON();
      }
    }

    // todo(ikun): 需要支持 muti pet evaluation
    const { id: serviceId = '', price: servicePrice = 0, duration: serviceTime = 0 } = finalEvaluation;

    const petIds = petIdList ?? abandonRecord.pets.map((pet) => pet.petId).filter(isNormal);

    openCreateEvaluation({
      businessId: business.id,
      clientId: clientId || presetInfo?.client?.value,
      source: EvaluationQuickAddSource.OBAbandon,
      preset: {
        appointmentStart: dayjs(date).setMinutes(dayjs(time).getMinutes()),
        petServiceList:
          petIds?.map((petId) => ({
            petId: petId.toString(),
            serviceId,
            servicePrice,
            serviceTime,
          })) || [],
      },
    });
  };
};

import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useStore } from 'amos';
import dayjs from 'dayjs';
import { useImperativeServicePriceDurationInfo } from '../../../../../components/ServiceApplicablePicker/hooks/useServiceInfo';
import { getDefaultService } from '../../../../../components/ServiceApplicablePicker/utils/getDefaultService';
import { ComMoegoServerGroomingWebVoObAbandonClientRecordVOCareType } from '../../../../../openApi/grooming-schema';
import { type OnlineBookingAbandonRecordType } from '../../../../../store/onlineBooking/abandonBooking.boxes';
import { getPetFeedingScheduleList } from '../../../../../store/pet/petFeedingMedication.actions';
import { serviceMapBox } from '../../../../../store/service/service.boxes';
import { isNormal } from '../../../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../../../../utils/DateTimeUtil';
import { quickServiceToServices } from '../../../../Appt/components/SelectServiceDetail/SelectServiceDetail.utils';
import { useCreateApptDrawer } from '../../../../Appt/hooks/useCreateApptDrawer';
import { CreateApptRouteName } from '../../../../Appt/modules/QuickAddAppt/QuickAddApptDrawer.router';
import { usePresetNextDetail } from '../../../../Appt/modules/QuickAddAppt/StepSelectPet.hook';

const { GROOMING, EVALUATION, BOARDING, DAYCARE, DOG_WALKING, GROUP_CLASS } =
  ComMoegoServerGroomingWebVoObAbandonClientRecordVOCareType;
const ServiceItemTypeMap = {
  [GROOMING]: ServiceItemType.GROOMING,
  [EVALUATION]: ServiceItemType.EVALUATION,
  [BOARDING]: ServiceItemType.BOARDING,
  [DAYCARE]: ServiceItemType.DAYCARE,
  [DOG_WALKING]: ServiceItemType.DOG_WALKING,
  [GROUP_CLASS]: ServiceItemType.GROUP_CLASS,
};

export const useBoardingDaycareAbandonToSchedule = () => {
  const openCreateDrawer = useCreateApptDrawer();
  const store = useStore();
  const getServicePriceDurationInfo = useImperativeServicePriceDurationInfo();
  const presetNextDetail = usePresetNextDetail();
  const dispatch = useDispatch();

  return async (abandonRecord: OnlineBookingAbandonRecordType, clientId?: number, petIdList?: number[]) => {
    const {
      customer,
      pets,
      careType,
      petDetails,
      appointmentDate,
      appointmentStartTime,
      appointmentEndDate,
      appointmentEndTime,
      specificDates,
    } = abandonRecord;
    // 现在先考虑获取首个 pet 即可
    const pet = pets?.[0];
    const petDetail = petDetails?.[0];
    const serviceId = pet?.serviceId;
    const petId = petIdList?.[0] || pet?.petId;
    const customerId = clientId || customer?.customerId;
    const customerIsInValid = !isNormal(customerId);
    const petIdIsInvalid = !isNormal(petId);
    const serviceIdIsInvalid = !isNormal(serviceId);
    const isDaycare = careType === DAYCARE;
    const serviceItemType = ServiceItemTypeMap[careType];

    const defaultStartDate = dayjs();
    const defaultStartTime = defaultStartDate?.getMinutes();
    const startDate = appointmentDate || defaultStartDate?.format(DATE_FORMAT_EXCHANGE);
    const startTime = appointmentStartTime;
    const endDate = appointmentEndDate || startDate;
    const endTime = appointmentEndTime;
    const expectedTime: Record<string, string | number | undefined> = { startDate, startTime, endTime, endDate };

    // from Winches: Daycare 不预填startTime和endTime
    if (isDaycare) {
      expectedTime.startDate = specificDates?.[0] || startDate;
      expectedTime.endDate = expectedTime.startDate;
    } else {
      expectedTime.startTime = startTime || defaultStartTime;
      expectedTime.endTime = endTime || expectedTime.startTime + 60;
    }

    if (customerIsInValid) {
      return;
    }

    // step1 界面: 用户只选择了 pet
    if (petIdIsInvalid) {
      openCreateDrawer({
        params: {
          clientId: customerId,
        },
      });
      return;
    }

    // step2界面: 用户只选择了 pet、date
    if (serviceIdIsInvalid) {
      openCreateDrawer({
        params: {
          clientId: customerId,
        },
        preset: {
          appointmentStart: dayjs(expectedTime.startDate).setMinutes(Number(expectedTime.startTime)),
          appointmentEnd: dayjs(expectedTime.endDate).setMinutes(Number(expectedTime.endTime)),
        },
        onReady({ router }) {
          router?.go(CreateApptRouteName.SelectPetService, { serviceItemType, petIds: [petId] });
        },
      });
      return;
    }

    // step3界面: 用户只选择了 pet、date、service，或者是缺少 lodging 的 boarding
    openCreateDrawer({
      params: {
        clientId: customerId,
      },
      async onReady({ router, close }) {
        const service = store.select(serviceMapBox.mustGetItem(serviceId!));
        const { name: serviceName, requireDedicatedStaff } = service;
        const newVal = getDefaultService({
          ...getServicePriceDurationInfo({ petId, serviceId, staffId: petDetail.staffId }),
          serviceId: serviceId,
          serviceName,
          serviceType: ServiceType.SERVICE,
          serviceItemType,
          requireDedicatedStaff,
          ...expectedTime,
        });

        const serviceList = [newVal];
        const petIdsServiceList = [{ petId, serviceList }];
        const skipDetail = await presetNextDetail({
          petIdsServiceList,
          serviceList,
          serviceItemType: ServiceItemTypeMap[careType],
        });

        if (skipDetail) {
          return;
        }

        // 用户选择了 pet、date、service、time。并且 service 是 Daycare
        if (isDaycare && startDate && startTime) {
          await dispatch(getPetFeedingScheduleList(petId));
          return;
        }

        router?.go(CreateApptRouteName.SelectServiceDetail, {
          services: quickServiceToServices(serviceList, serviceItemType),
          serviceItemType,
          petIds: [petId],
          petIdsServiceList: petIdsServiceList?.map((s) => ({
            ...s,
            serviceList: quickServiceToServices(s.serviceList, serviceItemType),
          })),
          onBack() {
            close();
          },
          onConfirm() {
            router?.go(CreateApptRouteName.Home);
          },
        });
      },
    });
  };
};

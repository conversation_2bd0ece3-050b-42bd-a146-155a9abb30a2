import { DATE_FORMAT_EXCHANGE } from '@moego/reporting';
import { useSelector } from 'amos';
import dayjs, { type Dayjs } from 'dayjs';
import { calendarQuickAddApptVisible } from '../../../store/calendarLatest/calendar.boxes';
import { usePetServiceListByAppt } from '../../Appt/hooks/useGetPetServiceListByAppt';
import { useStaffListByAppt } from '../../Appt/hooks/useGetStaffListByAppt';
import { useQuickAddConfig } from '../../Appt/modules/QuickAddAppt/hooks/useQuickAddConfig';
import { selectApptStartAndEndTime, selectPetsInAppt } from '../../Appt/store/appt.selectors';
import { CreateApptId } from '../../Appt/store/appt.types';
import { useEventChangeNewAppt } from '../../Calendar/latest/ApptCalendar/hooks/useEventChange/useEventChangeNewAppt';
import { useClientCoord } from './useClientCoord';

export const useQuickAddDrawerState = () => {
  const petServiceList = usePetServiceListByAppt(CreateApptId);
  const staffIdList = useStaffListByAppt(CreateApptId);
  const [visible, { startDateTime, endDateTime }, pets] = useSelector(
    calendarQuickAddApptVisible,
    selectApptStartAndEndTime(),
    selectPetsInAppt(),
  );
  const [{ clientId }] = useQuickAddConfig();
  const changeNewApptEvent = useEventChangeNewAppt();
  const coordinate = useClientCoord(clientId);

  const data = {
    visible,
    date: startDateTime?.format(DATE_FORMAT_EXCHANGE),
    startTime: startDateTime?.getMinutes(),
    endTime: endDateTime?.getMinutes(),
    clientId,
    pets,
    petServiceList,
    staffIdList,
    coordinate: visible ? coordinate : undefined,
  };

  // 参考：src/container/Calendar/latest/ApptCalendar/hooks/useDateClick.ts
  const set = ({ staffId, date }: { staffId?: number; date: Dayjs }) => {
    changeNewApptEvent({
      staffId,
      appointmentDate: dayjs(date),
    });
  };

  return {
    data,
    set,
  };
};

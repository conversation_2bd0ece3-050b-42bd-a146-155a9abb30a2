import { DATE_FORMAT_EXCHANGE } from '@moego/reporting';
import { useDispatch } from 'amos';
import { type Dayjs } from 'dayjs';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { CreateApptRouteName, createApptRouter } from '../../Appt/modules/QuickAddAppt/QuickAddApptDrawer.router';
import { setGroomingOnlyNewSchedule, setNewApptPreset } from '../../Appt/store/appt.actions';
import { useQuickAddDrawerState } from './useQuickAddDrawerState';
import { useRepeatSeriesState } from './useRepeatSeriesState';

export function useDateSelectorProps() {
  const dispatch = useDispatch();
  const {
    data: { visible: repeatDrawerVisible },
  } = useRepeatSeriesState();
  const {
    data: { startTime, visible: quickAddApptDrawerVisible },
  } = useQuickAddDrawerState();

  const onChange = useLatestCallback(async (nextDate: Dayjs | null) => {
    if (!nextDate) {
      return;
    }

    if (quickAddApptDrawerVisible && !repeatDrawerVisible) {
      // 时间选择器返回时间为当天 0 点，startTime 为 0，故需手动添加 startTime
      const finalDate = startTime ? nextDate.add(startTime, 'minute') : nextDate;

      if (createApptRouter.current?.is(CreateApptRouteName.SelectPetService)) {
        // pet and service 选择阶段，see <src/container/Calendar/latest/ApptCalendar/components/EventPlaceholder/EventPlaceholder.tsx#L67>
        const startDate = finalDate.format(DATE_FORMAT_EXCHANGE);
        dispatch(
          setNewApptPreset({
            startDate: startDate,
            startTime: finalDate.getMinutes(),
          }),
        );
      }

      dispatch(
        setGroomingOnlyNewSchedule({
          appointmentDate: finalDate,
        }),
      );
    }
  });

  return {
    onChange,
  };
}

import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect, useRef } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { Tooltip } from '../../../../../../components/Popup/Tooltip';
import { WithPricingEnableUpgrade } from '../../../../../../components/Pricing/WithPricingComponents';
import { usePricingEnableUpgrade } from '../../../../../../components/Pricing/pricing.hooks';
import { isMultipleStaffService } from '../../../../../../components/ServiceApplicablePicker/utils/isMultipleStaffService';
import { MoeTag } from '../../../../../../components/Tag/MoeTag';
import { WithFeature } from '../../../../../../components/WithFeature/WithFeature';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../../../../store/business/role.selectors';
import {
  addGroomingServiceCharges,
  applyGroomingPackage,
  getIsAvailablePackage,
  removeGroomingPackage,
  removeGroomingServiceCharges,
} from '../../../../../../store/grooming/grooming.actions';
import { type OrderDetails, OrderItemType } from '../../../../../../store/grooming/grooming.boxes';
import { selectGroomingTicketInvoice } from '../../../../../../store/grooming/grooming.selectors';
import { useEnableFeature } from '../../../../../../store/metadata/featureEnable.hooks';
import { META_DATA_KEY_LIST } from '../../../../../../store/metadata/metadata.config';
import { getServiceChargeList } from '../../../../../../store/service/actions/public/serviceCharge.actions';
import { selectServiceChargeList } from '../../../../../../store/service/service.selectors';
import { staffMapBox } from '../../../../../../store/staff/staff.boxes';
import { ID_ANONYMOUS, isNormal } from '../../../../../../store/utils/identifier';
import { c_primary } from '../../../../../../style/_variables';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { ReportActionName } from '../../../../../../utils/reportType';
import { reportData } from '../../../../../../utils/tracker';
import { buildLodgingInfoFromOrderPetDetail } from '../../../../../Appt/utils/lodgingDisplayHelpers';
import { ServiceChargeUpSellTooltipOverlay } from '../../../../../settings/Services/components/ServiceChargeUpSellTooltip';
import { MembershipBenefits } from '../../MembershipRedeem/MembershipBenefits';
import { type InvoiceSectionInfoProps } from '../InvoiceInfo';
import { FlexBetween, OrderSection, RegularText12, SectionContent, SectionTitle } from '../InvoiceInfo.style';
import { useGetServiceChargeView } from '../hooks/useGetServiceChargeView';
import { Add, Edit } from './AddAndEdit';
import { AddServiceChargeModal } from './AddServiceChargeModal';
import { InvoiceRowItem } from './InvoiceRowItem';
import { OrderLineItem } from './OrderLineItem';
import { SetupServiceChargeModal } from './SetupServiceChargeModal';
import { uniqBy } from 'lodash';

const useFetch = (invoiceId: number, appointmentId: number) => {
  const dispatch = useDispatch();
  const canUsePackage = useBool();
  const fetch = async () => {
    if (isNormal(invoiceId)) {
      await Promise.all([
        fetchIsAvailablePackage(),
        dispatch(getServiceChargeList({ isActive: true, appointmentId: String(appointmentId) })),
      ]);
    }
  };

  const fetchIsAvailablePackage = async () => {
    if (isNormal(invoiceId)) {
      const res = await dispatch(getIsAvailablePackage(invoiceId));
      canUsePackage.as(res);
    }
  };

  useEffect(() => {
    fetch();
  }, [invoiceId]);

  return { canUsePackage, fetchIsAvailablePackage };
};

export const ServicesAddons = memo<InvoiceSectionInfoProps>(
  ({ invoiceId, isDeposit, editable, handleActionWithLoading }) => {
    const [business, invoice, staffMap, activeServiceChargeList, rolePermission] = useSelector(
      selectCurrentBusiness,
      selectGroomingTicketInvoice(invoiceId),
      staffMapBox,
      selectServiceChargeList(true),
      selectCurrentPermissions(),
    );
    const { isViewed, view } = useGetServiceChargeView();
    const haveViewSettingPermission = rolePermission.has('viewSetting');
    const { canUsePackage, fetchIsAvailablePackage } = useFetch(invoice.id, invoice.groomingId);
    const { enable: enableServiceCharge } = useEnableFeature('service_charge_enable');
    const serviceList = invoice.filterItemsByType(OrderItemType.Service);
    const evaluationServiceList = invoice.filterItemsByType(OrderItemType.EvaluationService);
    const noShowList = invoice.filterItemsByType(OrderItemType.NoShow);
    const serviceChargeList = invoice.filterItemsByType(OrderItemType.ServiceCharge);
    const selectedServiceChargeIdList = serviceChargeList.map((i) => i.objectId.toString());
    const isSelectAllServiceCharge =
      activeServiceChargeList.size > 0 &&
      activeServiceChargeList.every((serviceCharge) => selectedServiceChargeIdList.includes(serviceCharge.id));
    const allServiceList = [...serviceList, ...evaluationServiceList];
    const serviceListForRender = allServiceList.length ? allServiceList : noShowList;
    const editDisabled = invoice.isCompleted || !editable;
    const canAddAddons = !editDisabled && !invoice.isNoShow && !isDeposit;
    const serviceDiscount = invoice.discountMap?.service?.discountAmount;
    const serviceSubtotal = invoice.subTotalAmountMap?.service;
    const canEditServiceCharges = !invoice.isNoShow && !invoice.isCompleted && !editDisabled;
    const showAddServiceChargeModal = useBool();
    const showSetupServiceChargeModal = useBool();
    const { access } = usePricingEnableUpgrade('serviceCharge');
    const isAutoApplyPackageRef = useRef(false);

    // 不能编辑，不展示。
    // 如果有权限，并且选择了全部的 active service charge，不展示。
    const notShowAddServiceChargeButton = !canEditServiceCharges || (access && isSelectAllServiceCharge);

    const handleDeletePackage = async (applyId: number, serviceId: number) => {
      await handleActionWithLoading(removeGroomingPackage(invoice.id, applyId, serviceId));
      canUsePackage.open();
    };

    const handleApplyPackage = async () => {
      await handleActionWithLoading(applyGroomingPackage(invoice.id));
      fetchIsAvailablePackage();
    };

    const renderAddons = () => {
      return (
        <>
          {!serviceSubtotal || !serviceDiscount ? null : (
            <Edit title="Discount" value={`-${business.formatAmount(serviceDiscount)}`} />
          )}

          {canUsePackage.value && canAddAddons && (
            <WithFeature metaKey={META_DATA_KEY_LIST.PackageEnabled}>
              <Add title="Use package" onClick={handleApplyPackage} />
            </WithFeature>
          )}
        </>
      );
    };

    const renderServices = () => {
      // 由前端简单修复因为导数据可能会造成的重复数据问题，后续 legacy invoice 也不会增强功能了，所以这里简单处理一下。
      const getUniquePetDetails = (petDetails: OrderDetails['items'][number]['petDetails']) => {
        return uniqBy(
          (petDetails || []).filter(Boolean),
          ({ petId, operationList, lodgingInfos }) =>
            `${petId}-${JSON.stringify(operationList)}-${JSON.stringify(lodgingInfos)}`,
        );
      };

      return (
        <>
          <SectionTitle>Services & addons</SectionTitle>
          <SectionContent>
            {serviceListForRender.map((service) => {
              const { petDetails, petEvaluationDetails } = service;

              const details = petEvaluationDetails?.length ? petEvaluationDetails : petDetails;
              return (
                <OrderLineItem
                  key={service.id}
                  lineItem={service}
                  hideQuantity={invoice.isNoShow}
                  extra={
                    <>
                      <MembershipBenefits items={service.membershipDetailList} />
                      <Condition if={service.discountInfo?.discountAmount}>
                        <FlexBetween>
                          <RegularText12 colorType="tertiary">{`Discount: ${
                            service.discountInfo?.discountCode ?? ''
                          }`}</RegularText12>
                          <RegularText12 colorType="tertiary">{`-${business.formatAmount(
                            service.discountInfo?.discountAmount,
                          )}`}</RegularText12>
                        </FlexBetween>
                      </Condition>
                      {getUniquePetDetails(details as OrderDetails['items'][number]['petDetails'])?.map(
                        (petDetail, index) => {
                          const { petName, petBreed, operationList, staffFirstName } = petDetail;
                          const hasOperation = isMultipleStaffService(petDetail);
                          const names = hasOperation
                            ? (operationList || [])
                                .map((i) => i.staffId)
                                .map((i) => staffMap.mustGetItem(i ?? ID_ANONYMOUS).firstName)
                            : [];
                          const { hasLodgings, getLodgingsInfoText } = buildLodgingInfoFromOrderPetDetail(petDetail);
                          return (
                            <div key={index}>
                              <Condition if={petName && petBreed}>
                                <RegularText12 colorType="secondary">
                                  {petName} ({petBreed})
                                </RegularText12>
                              </Condition>
                              <Condition if={hasLodgings}>
                                <RegularText12 colorType="secondary" style={{ marginLeft: 8 }}>
                                  {getLodgingsInfoText()}
                                </RegularText12>
                              </Condition>
                              <Condition if={names?.length || staffFirstName}>
                                <RegularText12 colorType="secondary" style={{ marginLeft: 8 }}>
                                  finished by {hasOperation ? names?.join(', ') : staffFirstName}
                                </RegularText12>
                              </Condition>
                            </div>
                          );
                        },
                      )}
                    </>
                  }
                />
              );
            })}
          </SectionContent>
        </>
      );
    };

    const handleDeleteServiceCharge = async (id: number) => {
      reportData(ReportActionName.MobileFeeDelete);
      await handleActionWithLoading(removeGroomingServiceCharges(invoice.id, [id.toString()]));
    };

    const handleAddServiceChargeList = async (idList: number[]) => {
      await handleActionWithLoading(
        addGroomingServiceCharges(
          invoice.id,
          idList.map((id) => id.toString()),
        ),
      );
    };

    const serviceChargeTooltipVisible = useBool();
    const renderAddServiceChargeButton = () => {
      const needShowUpSellTooltip = !access;
      return (
        <Tooltip
          disabled={!needShowUpSellTooltip}
          placement="top"
          width={280}
          visible={serviceChargeTooltipVisible.value}
          onVisibleChange={serviceChargeTooltipVisible.as}
          overlay={<ServiceChargeUpSellTooltipOverlay onUpgrade={serviceChargeTooltipVisible.close} />}
        >
          <div className="!moe-inline-block">
            <WithPricingEnableUpgrade permission="serviceCharge">
              {(capture) => {
                return (
                  <div className={`moe-flex moe-items-center ${serviceChargeList.length > 0 ? 'moe-mt-[8px]' : ''}`}>
                    <Add
                      title="Add fees"
                      onClick={() => {
                        view();
                        reportData(ReportActionName.MobileFeeAck);
                        serviceChargeTooltipVisible.close();
                        if (capture) {
                          capture();
                          return;
                        }

                        if (activeServiceChargeList.size === 0) {
                          showSetupServiceChargeModal.open();
                          return;
                        }

                        showAddServiceChargeModal.open();
                      }}
                      iconColor={needShowUpSellTooltip ? '#ccc' : c_primary}
                      titleClassName={needShowUpSellTooltip ? 'moe-text-[#ccc]' : ''}
                    />
                    <Condition if={!isViewed}>
                      <MoeTag text="New" className="!moe-ml-[4px]" />
                    </Condition>
                  </div>
                );
              }}
            </WithPricingEnableUpgrade>
          </div>
        </Tooltip>
      );
    };

    const renderServiceCharges = () => {
      return (
        <Condition if={enableServiceCharge}>
          <div className="moe-mb-[12px]">
            <div className="moe-flex moe-flex-col moe-gap-[4px]">
              <Condition if={serviceChargeList.length > 0}>
                <SectionTitle>Fees</SectionTitle>
              </Condition>
              <Condition if={serviceChargeList.length > 0}>
                <div className="moe-mt-[12px]">
                  {serviceChargeList.map(({ id, unitPrice, quantity, name, objectId }) => {
                    return (
                      <OrderLineItem
                        key={id}
                        lineItem={{ id, name, quantity, unitPrice }}
                        customPrice={business.formatAmount(unitPrice)}
                        onDelete={() => handleDeleteServiceCharge(objectId)}
                        hideQuantity={false}
                        deletable={canEditServiceCharges}
                        deleteIconClassName="moe-mt-[2px]"
                        className="[&:not(:first-child)]:moe-mt-[6px]"
                      />
                    );
                  })}
                </div>
              </Condition>
            </div>
            <Condition if={!notShowAddServiceChargeButton}>
              {renderAddServiceChargeButton()}
              <AddServiceChargeModal
                visible={showAddServiceChargeModal.value}
                onCancel={showAddServiceChargeModal.close}
                onSubmit={handleAddServiceChargeList}
                disabledIdList={selectedServiceChargeIdList}
                appointmentId={invoice.groomingId}
              />
              <SetupServiceChargeModal
                visible={showSetupServiceChargeModal.value}
                onCancel={showSetupServiceChargeModal.close}
                showSetupButton={haveViewSettingPermission}
              />
            </Condition>
          </div>
        </Condition>
      );
    };

    useEffect(() => {
      if (canUsePackage.value && canAddAddons && !isAutoApplyPackageRef.current) {
        isAutoApplyPackageRef.current = true;
        handleApplyPackage();
      }
    }, [canUsePackage.value, canAddAddons]);

    if (!serviceListForRender.length) return null;
    return (
      <OrderSection>
        {renderServices()}
        {invoice.appliedPackageServices && (
          <SectionContent>
            {invoice.appliedPackageServices.map(
              ({ packageServiceId, packageName, serviceName, quantity, servicePrice, serviceId, id }) => (
                <OrderLineItem
                  key={`${id}-${serviceId}`}
                  lineItem={{ id: packageServiceId, name: packageName, quantity, unitPrice: servicePrice }}
                  customPrice={`-${business.formatAmount(servicePrice)}`}
                  extra={<RegularText12 colorType="secondary">{serviceName}</RegularText12>}
                  deletable={!editDisabled}
                  onDelete={() => handleDeletePackage(id, serviceId)}
                />
              ),
            )}
          </SectionContent>
        )}
        {renderServiceCharges()}
        <InvoiceRowItem
          label="Service subtotal"
          labelClassName="!moe-text-[#333]"
          style={{ paddingTop: '12px', borderTop: '1px solid #E6E6E6' }}
        >
          {business.formatAmount(Number(serviceSubtotal))}
        </InvoiceRowItem>
        {renderAddons()}
      </OrderSection>
    );
  },
);

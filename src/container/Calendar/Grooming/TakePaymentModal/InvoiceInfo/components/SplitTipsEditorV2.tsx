import { type CustomizedTipType } from '@moego/api-web/moego/models/order/v1/split_tips_enum';
import { AmountUtil } from '@moego/finance-utils';
import { Button, FocusScope, Heading } from '@moego/ui';
import { useDebounceFn } from 'ahooks';
import { useDispatch, useSelector } from 'amos';
import { Radio, Select } from 'antd';
import { type RadioChangeEvent } from 'antd/lib/radio';
import { clone, cloneDeep, slice, sumBy } from 'lodash';
import React, { type FC, type ReactNode, memo, useRef } from 'react';
import { useSetState } from 'react-use';
import { toastApi } from '../../../../../../components/Toast/Toast';
import { enumOptions } from '../../../../../../components/common/select';
import { RE_INPUT_AMOUNT, RE_INPUT_NUMBER } from '../../../../../../components/form/NumberInput';
import { ApptTestIds } from '../../../../../../config/testIds/apptDrawer';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { previewSplitTips, setSplitTips } from '../../../../../../store/grooming/grooming.actions';
import {
  type GroomingSplitTipsRecordModel,
  type StaffTipAmountMap,
} from '../../../../../../store/grooming/grooming.boxes';
import {
  selectGroomingSplitTips,
  selectGroomingTicketInvoice,
} from '../../../../../../store/grooming/grooming.selectors';
import { META_DATA_KEY_LIST } from '../../../../../../store/metadata/metadata.config';
import { useMetaData } from '../../../../../../store/metadata/metadata.hooks';
import { CustomizedType, SplitTipsMethod } from '../../../../../../store/payment/payment.boxes';
import { staffMapBox } from '../../../../../../store/staff/staff.boxes';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { createBigNum, toFixedNum } from '../../../../../../utils/utils';
import { StyledNumberInput } from './SplitTipsEditor.style';

export interface SplitTipsEditorProps {
  invoiceId: number;
  onClose: () => void;
}

export interface RowItemProps {
  className?: string;
  label?: ReactNode;
}

export interface StaffsTipsProps {
  itemClassName?: string;
  splitTipsMethod?: number;
  showBusinessTip?: boolean;
  splitTipsDetail: GroomingSplitTipsRecordModel;
}

interface UIState {
  tipsForStaffAmount: string;
  method: number;
  type: number;
  customStaffTipsList: {
    staffId: number;
    amount?: string;
    percentage?: string;
  }[];
  businessTipAmount: number;
}

const RowItem: FC<RowItemProps> = memo(({ className = '', label = null, children }) => {
  return (
    <div className={`!moe-flex !moe-items-center ${className}`}>
      <div className="!moe-w-[90px] !moe-flex-shrink-0 !moe-truncate !moe-text-[#666] !moe-mr-[16px]">{label}</div>
      {children}
    </div>
  );
});

export const StaffsTips = memo<StaffsTipsProps>(
  ({ itemClassName = '', splitTipsMethod, splitTipsDetail, showBusinessTip = false }) => {
    const [business, staffMap] = useSelector(selectCurrentBusiness, staffMapBox);

    const currentSplitMethod = splitTipsMethod ?? splitTipsDetail.splitMethod;

    const getAmount = (amountMap: StaffTipAmountMap) => {
      switch (currentSplitMethod) {
        case SplitTipsMethod.Equally:
          return amountMap.byEqually?.amount;
        case SplitTipsMethod.Custom:
          return amountMap.customized?.amount;
        default:
          return amountMap.byService?.amount;
      }
    };

    return (
      <div className="moe-gap-y-[4px]">
        {(splitTipsDetail.staffTipAmountList || []).map(({ staffId, amountMap }) => (
          <div key={staffId} className={`moe-text-[12px] moe-text-[#999] ${itemClassName}`}>{`${business.formatAmount(
            getAmount(amountMap) ?? 0,
          )}  for ${staffMap.mustGetItem(staffId).firstName}`}</div>
        ))}
        {splitTipsDetail.businessTipAmount && showBusinessTip ? (
          <div className={`moe-text-[12px] moe-text-[#999] ${itemClassName}`}>{`${business.formatAmount(
            splitTipsDetail.businessTipAmount ?? 0,
          )} remaining tips`}</div>
        ) : null}
      </div>
    );
  },
);

const transformDetailToUIState = (params: GroomingSplitTipsRecordModel) => {
  return {
    method: params.splitMethod,
    type: params.customizedType || CustomizedType.Percent,
    businessTipAmount: params.businessTipAmount,
    customStaffTipsList: (params.staffTipAmountList || []).map(({ staffId, amountMap }) => {
      const { customized, byEqually } = amountMap || {};
      const initPercentageTips = customized ?? byEqually;
      return {
        staffId,
        amount: initPercentageTips?.amount?.toString(),
        percentage: initPercentageTips?.percentage?.toString(),
      };
    }),
  };
};

export const SplitTipsEditorV2 = memo<SplitTipsEditorProps>(({ invoiceId, onClose }) => {
  const [business, invoice, staffMap, groomingSplitTips] = useSelector(
    selectCurrentBusiness,
    selectGroomingTicketInvoice(invoiceId),
    staffMapBox,
    selectGroomingSplitTips(invoiceId),
  );

  // detailState 可以理解为后端下发的 split tips 数据，但是非我们的 UI 数据，我们需要 transform 之后才能使用。
  const [detailState, setDetailState] = useSetState(cloneDeep(groomingSplitTips));
  const [UIState, setUIState] = useSetState<UIState>({
    tipsForStaffAmount: AmountUtil.minus(invoice.tipsAmount, detailState.businessTipAmount).toString(),
    ...transformDetailToUIState(detailState),
  });

  const isSingleStaff = detailState.staffTipAmountList.length === 1;
  const abortController = useRef(new AbortController());
  const dispatch = useDispatch();
  const isDirty = useBool();
  const isTipsSplitAmountDirty = useBool();
  const [editedTipForStaffAmountMap, _setEditedTipForStaffAmountMap, isLoading] = useMetaData<Record<number, boolean>>(
    META_DATA_KEY_LIST.IsEditedTipForStaffAmount,
  );
  // 当前没 change 过 + 没 change and save 过，就显示提示语。
  const isShowTipsForStaffAmountTips =
    !isTipsSplitAmountDirty.value && !editedTipForStaffAmountMap?.[invoiceId] && !isLoading;
  const tipsSplitAmount = +UIState.tipsForStaffAmount;
  const isByPercent = UIState.type === CustomizedType.Percent;
  const percentAmountKey: 'percentage' | 'amount' = isByPercent ? 'percentage' : 'amount';

  const calculateReqParams = (UIState: UIState) => {
    const customizedParams =
      UIState.method === SplitTipsMethod.Custom
        ? {
            customizedType: UIState.type as CustomizedTipType,
            customizedConfig: (UIState.customStaffTipsList || []).map((item) => {
              return {
                staffId: item.staffId.toString(),
                amount: +(item.amount ?? 0),
                percentage: +(item.percentage ?? 0),
              };
            }),
          }
        : {
            customizedType: undefined,
            customizedConfig: [],
          };

    return {
      orderId: invoiceId.toString(),
      splitMethod: UIState.method,
      // 这里的 return 是返回给后端进行计算的，所以需要是一个确认的 biz 分配值，所以是已收金额 - 已分金额
      businessTipAmount: AmountUtil.minus(invoice.tipsAmount, tipsSplitAmount),
      ...customizedParams,
    };
  };

  const checkShouldPreview = (nextUIState: UIState) => {
    if (!RE_INPUT_AMOUNT.test(nextUIState.tipsForStaffAmount.toString())) return false;
    return validateStaffTipsInput(nextUIState);
  };

  const preview = useSerialCallback(async (nextUIState: UIState) => {
    abortController.current = new AbortController();
    const res = await dispatch(previewSplitTips(calculateReqParams(nextUIState), abortController.current.signal));
    setDetailState(res);
    setUIState(transformDetailToUIState(res));
  });
  const debouncePreview = useDebounceFn(preview, { wait: 1000 });

  const checkAndPreview = (nextUIState: UIState) => {
    const shouldPreview = checkShouldPreview(nextUIState);
    // 当会进入 checkAndPreview 时，就是数据源改变了，如果数据源改变了，就放弃上一次的请求
    abortController.current.abort();
    debouncePreview.cancel();
    if (!shouldPreview) return;
    debouncePreview.run(nextUIState);
  };

  const handleChangeMethod = (value: number) => {
    isDirty.open();
    const nextUIState = {
      ...UIState,
      method: value,
    };
    setUIState(nextUIState);
    checkAndPreview(nextUIState);
  };

  const handleChangeCustomizedType = (e: RadioChangeEvent) => {
    isDirty.open();
    const nextCustomStaffTipsList = calculateCustomStaffTipsList(
      +UIState.tipsForStaffAmount,
      UIState.customStaffTipsList,
      e.target.value,
    );
    const nextUIState = {
      ...UIState,
      type: e.target.value,
      customStaffTipsList: nextCustomStaffTipsList,
    };
    setUIState(nextUIState);
    checkAndPreview(nextUIState);
  };

  // 校正输入值
  const reviseStaffTips = (customStaffTipsList: UIState['customStaffTipsList'], staffId: number, value: string) => {
    const clonedList = cloneDeep(customStaffTipsList);
    const staffTips = clonedList.find((staffTips) => staffTips.staffId === staffId);
    if (staffTips) {
      const finalValue = isByPercent ? Math.min(+value, 100) : Math.min(+value, tipsSplitAmount);
      staffTips[percentAmountKey] = finalValue.toString() ?? void 0;
    }

    // 如果新输入的值使全部值加起来大于 tipsSplitAmount 或者 100%，则需要 index 从大到小，依次减去多余的部分。
    for (let i = clonedList.length - 1; i >= 0; i--) {
      const staffTips = clonedList[i];
      const sum = sumBy(slice(clonedList), (staffTips) => +(staffTips[percentAmountKey] ?? 0));
      if (staffTips.staffId === staffId) continue;
      if (!isByPercent && sum > tipsSplitAmount) {
        const diff = sum - tipsSplitAmount;
        const curValue = +(staffTips[percentAmountKey] ?? 0);
        const nextValue = toFixedNum(Math.max(0, curValue - diff), 2).toString();
        staffTips[percentAmountKey] = nextValue;
      }
      if (isByPercent && sum > 100) {
        const diff = sum - 100;
        const curValue = +(staffTips[percentAmountKey] ?? 0);
        const nextValue = toFixedNum(Math.max(0, curValue - diff), 2).toString();
        staffTips[percentAmountKey] = nextValue;
      }
    }

    return clonedList;
  };

  const handleChangeStaffInput = (staffId: number, value: string) => {
    isDirty.open();
    let clonedList = cloneDeep(UIState.customStaffTipsList);

    const isInputtingWithDecimalDot = value.endsWith('.') || value === '';

    // 判断 value 是否以 “.” 结尾的格式，如果是的话认为是中间态，仅做值修改且保持可输入。
    if (isInputtingWithDecimalDot) {
      clonedList.forEach((staffTips) => {
        if (staffTips.staffId === staffId) {
          staffTips[percentAmountKey] = value;
        }
      });
    } else {
      clonedList = reviseStaffTips(clonedList, staffId, value);
    }

    const nextCustomStaffTipsList = calculateCustomStaffTipsList(+UIState.tipsForStaffAmount, clonedList);

    setUIState({
      customStaffTipsList: nextCustomStaffTipsList,
    });
    const nextUIState = {
      ...UIState,
      customStaffTipsList: nextCustomStaffTipsList,
    };

    // 如果是输入中间态，不进行 preview，否则返回值的重写会将中间态打破。
    if (!isInputtingWithDecimalDot) {
      checkAndPreview(nextUIState);
    }
  };

  const handleChangeTipsForStaff = (value: string) => {
    isDirty.open();
    isTipsSplitAmountDirty.open();

    const nextCustomStaffTipsList = calculateCustomStaffTipsList(+value, UIState.customStaffTipsList);

    const nextUIState = {
      ...UIState,
      tipsForStaffAmount: value,
      customStaffTipsList: nextCustomStaffTipsList,
    };
    setUIState(nextUIState);
    checkAndPreview(nextUIState);
  };

  const calculateCustomStaffTipsList = (
    tipsSplitAmount: number,
    nextCustomStaffTipsList: UIState['customStaffTipsList'],
    type: UIState['type'] = UIState.type,
  ) => {
    const clonedList = clone(nextCustomStaffTipsList);
    const isByPercent = type === CustomizedType.Percent;
    const percentAmountKey: 'percentage' | 'amount' = isByPercent ? 'percentage' : 'amount';
    clonedList.forEach((staffTips, index, array) => {
      const prevSum = sumBy(slice(array, 0, index), (staffTips) => +(staffTips[percentAmountKey] ?? 0));
      const curSum = sumBy(slice(array, 0, index + 1), (staffTips) => +(staffTips[percentAmountKey] ?? 0));
      if (!isByPercent) {
        // 校正机制：当输入的 tips for staff amount 少于原本分配给 staff 的值时，需要校正为剩余值。
        if (curSum > tipsSplitAmount) {
          staffTips[percentAmountKey] = toFixedNum(tipsSplitAmount - prevSum, 2).toString();
          return;
        }
        // 补值机制：当输入的 tips for staff amount 多于原本分配给 staff 的值时，需要补值到最后一个 staff。
        if (index === array.length - 1) {
          staffTips[percentAmountKey] = toFixedNum(tipsSplitAmount - prevSum, 2).toString();
        }
      }

      if (isByPercent) {
        if (curSum > 100) {
          staffTips[percentAmountKey] = toFixedNum(100 - prevSum, 2).toString();
          return;
        }
        if (index === array.length - 1) {
          staffTips[percentAmountKey] = toFixedNum(100 - prevSum, 2).toString();
        }
      }
    });

    return clonedList;
  };

  const validateStaffTipsInput = useLatestCallback((nextUIState: UIState) => {
    if (nextUIState.method !== SplitTipsMethod.Custom) {
      return true;
    }

    let isGreaterZero = true;
    const { percentageSum, amountSum } = nextUIState.customStaffTipsList.reduce(
      (sum, { percentage = '', amount = '' }) => {
        if (+percentage < 0 || +amount < 0) {
          isGreaterZero = false;
        }
        return { percentageSum: sum.percentageSum + +percentage, amountSum: sum.amountSum.plus(createBigNum(amount)) };
      },
      { percentageSum: 0, amountSum: createBigNum(0) },
    );
    return (
      isGreaterZero &&
      (isByPercent ? percentageSum === 100 : amountSum.eq(createBigNum(nextUIState.tipsForStaffAmount)))
    );
  });

  const handleConfirm = useSerialCallback(async () => {
    await dispatch(setSplitTips(calculateReqParams(UIState)));
    // const originalTipsForStaffAmount = AmountUtil.minus(invoice.tipsAmount, groomingSplitTips.businessTipAmount);
    // if (+UIState.tipsForStaffAmount !== originalTipsForStaffAmount) {
    //   setEditedTipForStaffAmountMap({ ...editedTipForStaffAmountMap, [invoiceId]: true });
    // }
    toastApi.success('Split tip edited successfully!');
    onClose();
  });

  const renderSplitMethodSelect = () => (
    <RowItem label="Split method" className={isShowTipsForStaffAmountTips ? 'moe-mt-[36px]' : 'moe-mt-[16px]'}>
      {isSingleStaff ? (
        renderStaffsTipsContent()
      ) : (
        <Select
          className="!moe-w-full"
          value={UIState.method}
          defaultValue={SplitTipsMethod.ByService}
          onChange={handleChangeMethod}
          data-testid={ApptTestIds.ApptSplitTipsMethodSelect}
        >
          {enumOptions(SplitTipsMethod)}
        </Select>
      )}
    </RowItem>
  );

  const renderStaffsTipsContent = () => (
    <StaffsTips
      itemClassName="!moe-text-[#333] moe-text-[14px]"
      splitTipsMethod={UIState.method}
      splitTipsDetail={detailState}
    />
  );

  const renderStaffsTips = () => {
    if (isSingleStaff) {
      return null;
    }
    return <RowItem className="moe-mt-[4px]">{renderStaffsTipsContent()}</RowItem>;
  };

  const renderCustomizeTips = () => {
    const prefixOrSuffix = isByPercent
      ? {
          suffix: (
            <span className="!moe-flex !moe-items-center !moe-text-[#999] !moe-ml-[4px]">
              <i className="!moe-inline-block !moe-w-[1px] !moe-h-[16px] !moe-bg-[#dee1e6] !moe-mr-[8px]" />%
            </span>
          ),
        }
      : {
          prefix: (
            <span className="!moe-flex !moe-items-center !moe-text-[#999] !moe-mr-[4px]">
              {business.currencySymbol}
              <i className="!moe-inline-block !moe-w-[1px] !moe-h-[16px] !moe-bg-[#dee1e6] !moe-ml-[8px]" />
            </span>
          ),
        };
    // change display order, by percentage first
    const customizedTypeOptions = CustomizedType.values
      .map((value) => {
        return {
          label: CustomizedType.mapLabels[value],
          value,
        };
      })
      .sort((a, b) => b.value - a.value);

    return (
      <div>
        <RowItem label="Type" className="!moe-mt-[20px]">
          <Radio.Group
            defaultValue={CustomizedType.Percent}
            value={UIState.type}
            onChange={handleChangeCustomizedType}
            options={customizedTypeOptions}
          />
        </RowItem>
        {(UIState.customStaffTipsList || []).map((item, index, array) => {
          return (
            <RowItem key={item.staffId} label={staffMap.mustGetItem(item.staffId).firstName} className="!moe-mt-[20px]">
              <StyledNumberInput
                {...prefixOrSuffix}
                value={item[percentAmountKey]}
                inputFormat={isByPercent ? RE_INPUT_NUMBER : RE_INPUT_AMOUNT}
                max={isByPercent ? 100 : tipsSplitAmount}
                disabled={index === array.length - 1}
                onChange={(value) => handleChangeStaffInput(item.staffId, value)}
                data-testid={`appt-split-staff-input-${staffMap.mustGetItem(item.staffId).firstName}`}
              />
            </RowItem>
          );
        })}
      </div>
    );
  };

  const renderButton = () => {
    return (
      <RowItem className="moe-mt-s">
        <div className="moe-flex-1 moe-justify-end moe-flex">
          <Button variant="secondary" size="s" onPress={onClose}>
            Cancel
          </Button>
          <Button
            className="moe-ml-xs moe-relative"
            size="s"
            isDisabled={!isDirty.value || !validateStaffTipsInput(UIState) || preview.isBusy()}
            isLoading={handleConfirm.isBusy()}
            onPress={handleConfirm}
          >
            Save
          </Button>
        </div>
      </RowItem>
    );
  };

  return (
    <FocusScope contain autoFocus>
      <div className="moe-bg-white moe-rounded-lg moe-border-[1px] moe-border-[#E6E6E6] moe-border-solid moe-px-[32px] moe-py-[20px]">
        <Heading size={6}>Edit tip split</Heading>
        <RowItem label="Tips collected" className="moe-mt-s">
          <span className="moe-text-[#333]">{business.formatAmount(invoice.tipsAmount)}</span>
        </RowItem>

        <div className="moe-p-s moe-bg-neutral-sunken-0 moe-mt-s">
          <RowItem label="Tips for staff">
            <div className="moe-flex-1 moe-relative">
              <StyledNumberInput
                prefix="$"
                value={UIState.tipsForStaffAmount}
                inputFormat={RE_INPUT_AMOUNT}
                max={invoice.tipsAmount}
                onChange={(value) => handleChangeTipsForStaff(value)}
                data-testid={ApptTestIds.ApptSplitTipsForStaffAmountInput}
              />
              {isShowTipsForStaffAmountTips ? (
                <p className="moe-absolute moe-text-[12px] moe-text-[#999] moe-mt-xxs">
                  Based on provided service price
                </p>
              ) : null}
            </div>
          </RowItem>

          {renderSplitMethodSelect()}
          {UIState.method === SplitTipsMethod.Custom ? renderCustomizeTips() : renderStaffsTips()}
        </div>
        <p className="moe-text-[14px] moe-text-[#666] moe-mt-xs moe-leading-[18px]">
          Remaining tips: {preview.isBusy() ? 'Loading...' : business.formatAmount(detailState.businessTipAmount)}
        </p>
        {renderButton()}
      </div>
    </FocusScope>
  );
});

import { useDispatch, useSelector } from 'amos';
import { Button, Modal, Select } from 'antd';
import { type ModalFuncProps } from 'antd/lib/modal';
import React, { useEffect, useState } from 'react';
import { useMount } from 'react-use';
import { getServiceChargeList } from '../../../../../../store/service/actions/public/serviceCharge.actions';
import { selectServiceChargeList } from '../../../../../../store/service/service.selectors';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { ReportActionName } from '../../../../../../utils/reportType';
import { reportData } from '../../../../../../utils/tracker';

export const AddServiceChargeModal = ({
  visible,
  onSubmit,
  onCancel,
  disabledIdList,
  appointmentId,
}: { onSubmit: (idList: number[]) => {}; disabledIdList: string[]; appointmentId: number } & ModalFuncProps) => {
  const dispatch = useDispatch();
  const [serviceChargeList] = useSelector(selectServiceChargeList(true));
  const [selectedIdList, setSelectedIdList] = useState<number[]>([]);
  const disableSubmit = selectedIdList.length === 0;

  useMount(() => {
    dispatch(getServiceChargeList({ isActive: true, appointmentId: String(appointmentId) }));
  });

  useEffect(() => {
    if (!visible) {
      setSelectedIdList([]);
    }
  }, [visible]);

  const handleChange = (idList: number[]) => {
    setSelectedIdList(idList);
  };

  const handleSubmit = useSerialCallback(async () => {
    reportData(ReportActionName.MobileFeeManualEdit);
    await onSubmit(selectedIdList);
    onCancel?.();
  });

  const dropdownVisible = useBool();

  return (
    <Modal
      visible={visible}
      title="Add fees"
      onCancel={onCancel}
      footer={
        <>
          <Button onClick={onCancel} shape="round" className="!moe-text-[#333] !moe-shadow-none">
            Cancel
          </Button>
          <Button
            onClick={disableSubmit ? undefined : handleSubmit}
            shape="round"
            type="primary"
            className={`!moe-ml-[16px] !moe-shadow-none ${
              disableSubmit ? '!moe-opacity-60 !moe-cursor-not-allowed' : ''
            }`}
            loading={handleSubmit.isBusy()}
          >
            Submit
          </Button>
        </>
      }
    >
      <Select
        mode="multiple"
        allowClear
        optionFilterProp="label"
        placeholder="Please select fees"
        onChange={handleChange}
        value={selectedIdList}
        autoFocus
        showArrow
        options={(serviceChargeList || [])
          .map((item) => ({ label: item.name, value: item.id, disabled: disabledIdList.includes(item.id) }))
          .toJSON()}
        className="moe-rounded-[24px] moe-w-full"
        onInputKeyDown={(e) => {
          if (e.key === 'Escape') {
            // The Select is contained by the Modal.
            // So we need to stop the event propagation when press ESC to prevent from close the Modal.
            e.stopPropagation();
            dropdownVisible.close();
          }
        }}
        onDropdownVisibleChange={dropdownVisible.as}
        open={dropdownVisible.value}
      />
    </Modal>
  );
};

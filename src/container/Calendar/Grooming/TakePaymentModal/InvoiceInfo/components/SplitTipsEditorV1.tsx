import { type CustomizedTipType } from '@moego/api-web/moego/models/order/v1/split_tips_enum';
import { useDispatch, useSelector } from 'amos';
import { Radio, Select } from 'antd';
import { sumBy } from 'lodash';
import React, { type FC, type ReactNode, memo, useCallback } from 'react';
import { useSetState } from 'react-use';
import { Condition } from '../../../../../../components/Condition';
import { toastApi } from '../../../../../../components/Toast/Toast';
import { enumOptions } from '../../../../../../components/common/select';
import { RE_INPUT_AMOUNT, RE_INPUT_NUMBER } from '../../../../../../components/form/NumberInput';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { setSplitTips } from '../../../../../../store/grooming/grooming.actions';
import {
  type GroomingTicketInvoiceRecord,
  type StaffTipAmountMap,
} from '../../../../../../store/grooming/grooming.boxes';
import {
  selectGroomingSplitTips,
  selectGroomingTicketInvoice,
} from '../../../../../../store/grooming/grooming.selectors';
import { CustomizedType, SplitTipsMethod } from '../../../../../../store/payment/payment.boxes';
import { staffMapBox } from '../../../../../../store/staff/staff.boxes';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { createBigNum, toFixedNum } from '../../../../../../utils/utils';
import { EditorButton, StyledNumberInput } from './SplitTipsEditor.style';

export interface SplitTipsEditorProps {
  invoiceId: number;
  onClose: () => void;
}

export interface RowItemProps {
  className?: string;
  label?: ReactNode;
}

export interface StaffsTipsProps {
  itemClassName?: string;
  splitTipsMethod?: number;
  invoiceId: number;
}

interface State {
  method: number;
  type: number;
  customStaffTipsList: {
    staffId: number;
    amount?: string;
    percentage?: string;
  }[];
}

export const RowItem: FC<RowItemProps> = memo(({ className = '', label = null, children }) => {
  return (
    <div className={`!moe-flex !moe-items-center ${className}`}>
      <div className="!moe-w-[82px] !moe-flex-shrink-0 !moe-truncate !moe-text-[#666] !moe-mr-[16px]">{label}</div>
      {children}
    </div>
  );
});

export const StaffsTips = memo<StaffsTipsProps>(
  ({ itemClassName = '!moe-text-[#999]', splitTipsMethod, invoiceId }) => {
    const [business, staffMap, groomingSplitTips] = useSelector(
      selectCurrentBusiness,
      staffMapBox,
      selectGroomingSplitTips(invoiceId),
    );

    const currentSplitMethod = splitTipsMethod ?? groomingSplitTips.splitMethod;

    const getAmount = (amountMap: StaffTipAmountMap) => {
      switch (currentSplitMethod) {
        case SplitTipsMethod.Equally:
          return amountMap.byEqually?.amount;
        case SplitTipsMethod.Custom:
          return amountMap.customized?.amount;
        default:
          return amountMap.byService?.amount;
      }
    };

    return (
      <div>
        {groomingSplitTips.staffTipAmountList?.map(({ staffId, amountMap }) => (
          <div key={staffId} className={`${itemClassName} !moe-text-[12px] !moe-mt-[4px]`}>{`${business.formatAmount(
            getAmount(amountMap) ?? 0,
          )}  for ${staffMap.mustGetItem(staffId).firstName}`}</div>
        ))}
      </div>
    );
  },
);

export const SplitTipsEditorV1 = memo<SplitTipsEditorProps>(({ invoiceId, onClose }) => {
  const [business, invoice, staffMap, groomingSplitTips] = useSelector(
    selectCurrentBusiness,
    selectGroomingTicketInvoice(invoiceId),
    staffMapBox,
    selectGroomingSplitTips(invoiceId),
  );
  const dispatch = useDispatch();
  const isDirty = useBool();
  const [state, setState] = useSetState<State>({
    method: groomingSplitTips.splitMethod,
    type: groomingSplitTips.customizedType,
    customStaffTipsList: (groomingSplitTips.staffTipAmountList || []).map(({ staffId, amountMap }) => {
      const { customized, byEqually } = amountMap || {};
      const initPercentageTips = customized ?? byEqually;
      return {
        staffId,
        amount: customized?.amount?.toString(),
        percentage: initPercentageTips?.percentage?.toString(),
      };
    }),
  });
  const tipsSplitAmount = (invoice as GroomingTicketInvoiceRecord).tipsSplitAmount;
  const isByPercent = state.type === CustomizedType.Percent;
  const percentAmountKey: 'percentage' | 'amount' = isByPercent ? 'percentage' : 'amount';

  const handleInputChange = (staffId: number, value: string) => {
    const tempList = state.customStaffTipsList;

    let autoComplete = true;
    tempList.forEach((staff, index, array) => {
      if (staff.staffId === staffId) {
        staff[percentAmountKey] = value ?? void 0;
      }

      if (index !== array.length - 1) {
        // it will not change, once autoComplete is false,
        if (typeof staff[percentAmountKey] === 'undefined' && autoComplete) {
          autoComplete = false;
        }
      } else {
        const sumExceptLast = sumBy(tempList.slice(0, -1), (item) => +(item[percentAmountKey] ?? 0));
        const autoCompleteValue = toFixedNum((isByPercent ? 100 : tipsSplitAmount) - sumExceptLast, 2).toString();
        array[array.length - 1][percentAmountKey] = autoComplete ? autoCompleteValue : void 0;
      }
    });

    isDirty.open();
    setState({
      customStaffTipsList: tempList,
    });
  };

  const validateStaffTipsInput = useCallback(() => {
    if (state.method !== SplitTipsMethod.Custom) {
      return true;
    }

    let isGreaterZero = true;
    const { percentageSum, amountSum } = state.customStaffTipsList.reduce(
      (sum, { percentage = '', amount = '' }) => {
        if (+percentage < 0 || +amount < 0) {
          isGreaterZero = false;
        }
        return { percentageSum: sum.percentageSum + +percentage, amountSum: sum.amountSum.plus(createBigNum(amount)) };
      },
      { percentageSum: 0, amountSum: createBigNum(0) },
    );

    return isGreaterZero && (isByPercent ? percentageSum === 100 : amountSum.eq(createBigNum(tipsSplitAmount)));
  }, [state.customStaffTipsList, tipsSplitAmount, state.type, state.method]);

  const handleConfirm = useSerialCallback(async () => {
    const customizedParams =
      state.method === SplitTipsMethod.Custom
        ? {
            customizedType: state.type as CustomizedTipType,
            customizedConfig: state.customStaffTipsList.map((item) => {
              return {
                staffId: item.staffId.toString(),
                amount: +(item.amount ?? 0),
                percentage: +(item.percentage ?? 0),
              };
            }),
          }
        : {
            customizedType: undefined,
            customizedConfig: [],
          };
    await dispatch(
      setSplitTips({
        orderId: invoiceId.toString(),
        splitMethod: state.method,
        ...customizedParams,
        businessTipAmount: groomingSplitTips.businessTipAmount,
      }),
    );
    toastApi.success('Edited tip split successfully.');
    onClose();
  });

  const renderSplitMethodSelect = () => (
    <RowItem label="Split method" className="!moe-mt-[20px]">
      <Select
        className="!moe-w-full"
        value={state.method}
        defaultValue={SplitTipsMethod.ByService}
        onChange={(value) => {
          isDirty.open();
          setState({
            method: value,
          });
        }}
      >
        {enumOptions(SplitTipsMethod)}
      </Select>
    </RowItem>
  );

  const renderStaffsTips = () => (
    <RowItem className="!moe-mt-[4px]">
      <StaffsTips splitTipsMethod={state.method} invoiceId={invoiceId} />
    </RowItem>
  );

  const renderCustomizeTips = () => {
    const prefixOrSuffix = isByPercent
      ? {
          suffix: (
            <span className="!moe-flex !moe-items-center !moe-text-[#999] !moe-ml-[4px]">
              <i className="!moe-inline-block !moe-w-[1px] !moe-h-[16px] !moe-bg-[#dee1e6] !moe-mr-[8px]" />%
            </span>
          ),
        }
      : {
          prefix: (
            <span className="!moe-flex !moe-items-center !moe-text-[#999] !moe-mr-[4px]">
              {business.currencySymbol}
              <i className="!moe-inline-block !moe-w-[1px] !moe-h-[16px] !moe-bg-[#dee1e6] !moe-ml-[8px]" />
            </span>
          ),
        };
    // change display order, by percentage first
    const customizedTypeOptions = CustomizedType.values
      .map((value) => {
        return {
          label: CustomizedType.mapLabels[value],
          value,
        };
      })
      .sort((a, b) => b.value - a.value);

    return (
      <div>
        <RowItem label="Type" className="!moe-mt-[20px]">
          <Radio.Group
            defaultValue={CustomizedType.Percent}
            value={state.type}
            onChange={(e) => {
              isDirty.open();
              setState({
                type: e.target.value,
              });
            }}
            options={customizedTypeOptions}
          />
        </RowItem>
        {state.customStaffTipsList.map((item, index, array) => {
          return (
            <RowItem key={item.staffId} label={staffMap.mustGetItem(item.staffId).firstName} className="!moe-mt-[20px]">
              <StyledNumberInput
                {...prefixOrSuffix}
                value={item[percentAmountKey]}
                inputFormat={isByPercent ? RE_INPUT_NUMBER : RE_INPUT_AMOUNT}
                max={isByPercent ? 100 : tipsSplitAmount}
                disabled={index === array.length - 1}
                onChange={(value) => handleInputChange(item.staffId, value)}
              />
            </RowItem>
          );
        })}
      </div>
    );
  };

  const renderButton = () => {
    return (
      <RowItem className="!moe-mt-[24px]">
        <div>
          <EditorButton
            productButtonType="primary"
            disabled={!isDirty.value || !validateStaffTipsInput()}
            loading={handleConfirm.isBusy()}
            onClick={handleConfirm}
          >
            Confirm
          </EditorButton>
          <EditorButton productButtonType="cancel" className="!moe-ml-[8px]" onClick={onClose}>
            Cancel
          </EditorButton>
        </div>
      </RowItem>
    );
  };

  return (
    <div className="moe-bg-white moe-rounded-lg moe-border-[1px] moe-border-[#E6E6E6] moe-border-solid moe-px-[32px] moe-py-[20px]">
      <span className="moe-text-[#333] moe-font-bold moe-text-[14px]">Edit tip split</span>
      <Condition if={tipsSplitAmount !== invoice.tipsAmount}>
        <RowItem label="Tips" className="!moe-mt-[20px]">
          <span className="moe-text-[#333]">{business.formatAmount(tipsSplitAmount)}</span>
        </RowItem>
      </Condition>

      {renderSplitMethodSelect()}
      {state.method === SplitTipsMethod.Custom ? renderCustomizeTips() : renderStaffsTips()}
      {renderButton()}
    </div>
  );
});

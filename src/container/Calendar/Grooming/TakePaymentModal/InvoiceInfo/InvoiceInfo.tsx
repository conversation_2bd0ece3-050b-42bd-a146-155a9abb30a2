/*
 * @since 2021-07-01 14:20:53
 * <AUTHOR> <<EMAIL>>
 */

import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { type Action, useDispatch, useSelector } from 'amos';
import React, { memo, useContext, useEffect, useMemo, useRef } from 'react';
import { Condition } from '../../../../../components/Condition';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { getTaxList } from '../../../../../store/business/tax.actions';
import { taxMapBox } from '../../../../../store/business/tax.boxes';
import { getGroomingTicketInvoice, setGroomingTicketTips } from '../../../../../store/grooming/grooming.actions';
import {
  GroomingTicketInvoiceType,
  OrderItemType,
  type OrderTypeValue,
  type PublishableId,
} from '../../../../../store/grooming/grooming.boxes';
import { selectGroomingTicketInvoice } from '../../../../../store/grooming/grooming.selectors';
import { autoApplyInvoiceMembership } from '../../../../../store/membership/membershipApply.actions';
import { type IsDeposit, invoiceRefundMapBox } from '../../../../../store/payment/payment.boxes';
import { selectPaymentSettingInfo } from '../../../../../store/payment/payment.selectors';
import { selectCurrentStaff } from '../../../../../store/staff/staff.selectors';
import { isNormal } from '../../../../../store/utils/identifier';
import { uniq } from '../../../../../store/utils/utils';
import { GrowthBookFeatureList } from '../../../../../utils/growthBook/growthBook.config';
import { useBool } from '../../../../../utils/hooks/useBool';
import { type LatestCallback, useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { ReportActionName } from '../../../../../utils/reportType';
import { reportData } from '../../../../../utils/tracker';
import { useGetCredit } from '../../../../Client/ClientInfo/CreditCards/hooks/useGetCredit';
import { SetTipsModal } from '../../../../Payment/components/SetTipsModal';
import { InvoiceRefund } from '../InvoiceRefund';
import { MembershipRedeemEntry } from '../MembershipRedeem/MembershipRedeemEntry';
import { useInvoiceMembershipHelpers } from '../MembershipRedeem/hooks/useInvoiceMembershipHelpers';
import { TakePaymentContext } from '../TakePaymentContext';
import { ModalHeader } from '../components/ModalHeader';
import { InvoiceInfoView, MediumText14, OrderFeeSectionView, TextPrimary } from './InvoiceInfo.style';
import { AddAndEdit } from './components/AddAndEdit';
import { BillTo } from './components/BillTo';
import { CheckInTime } from './components/CheckInTime';
import { Credit } from './components/Credit/Credit';
import { Discounts } from './components/Discounts';
import { InvoiceRowItem } from './components/InvoiceRowItem';
import { MembershipProducts } from './components/MembershipProducts';
import { Packages } from './components/Packages';
import { Products } from './components/Products';
import { ServicesAddons } from './components/ServicesAddons';
import { SplitTips } from './components/SplitTips';
import { Summary } from './components/Summary';
import { ViewInQuickBooks } from './components/ViewInQuickBooks';
import { useStoreCredit } from './hooks/useStoreCredit';

export interface InvoiceInfoProps {
  className?: string;
  invoiceId: PublishableId;
  editable?: boolean;
  isDeposit?: IsDeposit;
  showHeader?: boolean;
  membershipDrawerVisible?: boolean;
  productDrawerVisible?: boolean;
  showCreatedOrderPackage?: boolean;
  addLoading?: (count: number) => void;
  onClickAddProduct?: () => void;
  onClickAddDiscount?: () => void;
  onClickAddMembership?: () => void;
  readonly?: boolean;
  disabledProduct?: boolean;
  disabledMembership?: boolean;
}

export type InvoiceSectionInfoProps = Pick<InvoiceInfoProps, 'invoiceId' | 'editable' | 'isDeposit'> & {
  handleActionWithLoading: LatestCallback<(action: Action) => Promise<void>>;
};

export type DiscountModalType = Extract<OrderTypeValue, 'all' | 'service' | 'product'> | undefined;

export const InvoiceInfo = memo<InvoiceInfoProps>(
  ({
    className,
    invoiceId,
    editable = true,
    isDeposit,
    showHeader = true,
    productDrawerVisible,
    showCreatedOrderPackage,
    addLoading,
    onClickAddProduct,
    onClickAddDiscount,
    onClickAddMembership,
    readonly = false,
    disabledProduct = false,
    disabledMembership = false,
  }) => {
    const dispatch = useDispatch();
    const [business, invoice, staff, paymentSetting, taxMap] = useSelector(
      selectCurrentBusiness,
      selectGroomingTicketInvoice(invoiceId),
      selectCurrentStaff(),
      selectPaymentSettingInfo(),
      taxMapBox,
    );
    const { isMembershipEnabled } = useInvoiceMembershipHelpers();
    const { customizedFeeName = 'Fees' } = paymentSetting;
    const { isProcessingFeeShow, processingFeeAmountVal } = useContext(TakePaymentContext);
    const scrollContainerRef = useRef<HTMLDivElement>(null);
    const { scrollHeight = 0, offsetHeight = 0 } = scrollContainerRef.current || {};
    const [invoiceRefund] = useSelector(invoiceRefundMapBox.mustGetItem(invoiceId));
    const realTotalAmount = isProcessingFeeShow ? invoice.totalAmount + processingFeeAmountVal : invoice.totalAmount;
    const isCreditEnabled = useFeatureIsOn(GrowthBookFeatureList.Credit);
    const setTipsVisible = useBool();
    const { handleEditCreditAmount, appliedCreditData, removeAppleCredit, applyCredit } = useStoreCredit(invoiceId);
    const { amount: appliedCreditAmount, id: creditId } = appliedCreditData;
    const showAppliedCredit = appliedCreditAmount && isCreditEnabled;
    const discountAmountForInvoice = (invoice.discountMap?.all?.discountAmount || 0) - appliedCreditAmount;

    // 初始化获取当前 customer 的 credit
    useGetCredit(invoice.customerId);

    const serviceList = invoice.filterItemsByType(OrderItemType.Service);
    const staffs = serviceList
      .map((service) =>
        (service.petDetails || [])
          .map((item) => {
            const { operationList } = item as any;
            const hasOperation = Array.isArray(operationList) && operationList.length > 0;
            return hasOperation ? (operationList || []).map((i) => i.staffId) : item.staffId;
          })
          .flat(),
      )
      .flat();
    const staffCount = uniq(staffs).length;
    const canSplitTips = invoice.isCompleted && invoice.tipsSplitAmount && staffCount > 1 && editable;

    useEffect(() => {
      if (isNormal(business.id)) {
        dispatch(getTaxList());
      }
    }, [business.id]);

    useEffect(() => {
      if (isMembershipEnabled && !invoice.isCompleted && isNormal(invoice.id) && !appliedCreditAmount) {
        // 自动 apply membership
        dispatch(autoApplyInvoiceMembership(invoiceId));
      }
    }, [invoiceId, isMembershipEnabled, invoice.id, appliedCreditAmount]);

    const handleActionWithLoading = useLatestCallback(async (action: Action) => {
      try {
        addLoading?.(1);
        await dispatch(action);
      } finally {
        addLoading?.(-1);
      }
    });

    const handleRefundOk = async () => {
      await dispatch(getGroomingTicketInvoice(invoice.id));
      handleRefundClose();
    };

    const handleRefundClose = () => {
      dispatch(invoiceRefundMapBox.deleteItem(invoiceId));
    };

    const handleSetTips = (_: number, value: number) => {
      reportData(ReportActionName.MoeGoPayInvoiceAddTip);
      return dispatch(setGroomingTicketTips(invoice.id, value, invoice.updateTime));
    };

    const handleDeleteTips = () => handleActionWithLoading(setGroomingTicketTips(invoice.id, 0, invoice.updateTime));

    const classifiedTax = useMemo(() => {
      const taxInfoMap = new Map<number, (typeof invoice.items)[0]['taxInfo']>();
      invoice.items.forEach((item) => {
        if (!item.taxInfo) {
          return;
        }
        const { taxId, taxAmount } = item.taxInfo;
        const taxInfo = taxInfoMap.get(taxId);
        if (taxInfo) {
          taxInfo.taxAmount += taxAmount;
          taxInfoMap.set(taxId, taxInfo);
        } else {
          taxInfoMap.set(taxId, item.taxInfo);
        }
      });

      return Array.from(taxInfoMap.values());
    }, [invoice.items]);

    const OtherFees = useLatestCallback(() => {
      const editDisabled = invoice.isCompleted || !editable;
      const disableAddTips =
        !invoice.tipsAmount && (paymentSetting.skipTipping || editDisabled || invoice.isNoShow || isDeposit);

      return (
        <div className="moe-grid moe-gap-y-[8px] moe-mb-[12px] moe-mt-[12px]">
          <InvoiceRowItem label="Subtotal">
            <TextPrimary>{business.formatAmount(invoice.subTotalAmountMap?.all_no_tip ?? 0)}</TextPrimary>
          </InvoiceRowItem>
          <Condition if={discountAmountForInvoice && discountAmountForInvoice !== appliedCreditAmount}>
            <InvoiceRowItem label="Discount" valueClassName="!moe-text-brand">
              -{business.formatAmount(discountAmountForInvoice)}
            </InvoiceRowItem>
          </Condition>
          <Condition if={showAppliedCredit}>
            <AddAndEdit
              addText=""
              editText="Store credit"
              titleClassName="moe-text-[#666]"
              isEdit
              value={<MediumText14 colorType="green">-{business.formatAmount(appliedCreditAmount)}</MediumText14>}
              onEdit={editDisabled ? undefined : handleEditCreditAmount}
              onRemove={editDisabled ? undefined : () => removeAppleCredit(Number(creditId))}
              style={{ marginTop: 0, lineHeight: '18px' }}
            />
          </Condition>
          {classifiedTax.map(({ taxId, taxRate, taxAmount }) => (
            <InvoiceRowItem key={taxId} label={`${taxMap.mustGetItem(taxId).taxName || 'Tax'} (${taxRate}%)`}>
              <TextPrimary>{business.formatAmount(taxAmount)}</TextPrimary>
            </InvoiceRowItem>
          ))}
          <Condition if={!disableAddTips}>
            <AddAndEdit
              addText="Add tips"
              editText="Tips"
              className="!moe-mt-0"
              titleClassName="moe-text-[#666]"
              isEdit={!!invoice.tipsAmount}
              value={business.formatAmount(invoice.tipsAmount)}
              onEdit={editDisabled ? void 0 : setTipsVisible.open}
              onRemove={editDisabled ? void 0 : handleDeleteTips}
            />
          </Condition>
          <Condition if={canSplitTips}>
            <SplitTips invoiceId={invoice.id} />
          </Condition>
          {!!invoice.convenienceFee && (
            <InvoiceRowItem label={`${customizedFeeName} paid`}>
              <TextPrimary>{business.formatAmount(invoice.convenienceFee)}</TextPrimary>
            </InvoiceRowItem>
          )}
          {!!isProcessingFeeShow && !!processingFeeAmountVal && (
            <InvoiceRowItem label={`${customizedFeeName}${invoice.convenienceFee ? ' (current payment)' : ''}`}>
              <TextPrimary>{business.formatAmount(processingFeeAmountVal)}</TextPrimary>
            </InvoiceRowItem>
          )}
        </div>
      );
    });

    const Total = useLatestCallback(() => {
      return (
        <InvoiceRowItem
          label="Total"
          style={!invoice.isCompleted ? { paddingTop: '12px', borderTop: '1px solid #e9ecef' } : {}}
          labelClassName="!moe-text-[#333] !moe-font-bold"
        >
          {business.formatAmount(realTotalAmount)}
        </InvoiceRowItem>
      );
    });

    const renderPartialPayBlock = () => {
      if (invoice.isCompleted) {
        return null;
      }

      const hadRefund = Array.isArray(invoice.paymentSummary?.refunds) && invoice.paymentSummary.refunds.length;

      return (
        <>
          <InvoiceRowItem
            label="Amount paid"
            className="moe-pt-[12px] moe-mt-[12px] moe-border-t-[1px] moe-border-t-[#e9ecef] moe-border-solid"
          >
            {business.formatAmount(invoice.paidAmount)}
          </InvoiceRowItem>
          <Condition if={hadRefund}>
            <InvoiceRowItem label="Refund" className="moe-mt-[8px]">
              -{business.formatAmount(invoice.refundedAmount)}
            </InvoiceRowItem>
          </Condition>
        </>
      );
    };

    return (
      <InvoiceInfoView className={className}>
        {showHeader && (
          <ModalHeader
            title={
              <span>
                Invoice&nbsp;
                <Condition if={readonly}>(read only)&nbsp;</Condition>
                <a style={{ color: '#0091ff' }}>#{invoice.id}</a>
              </span>
            }
          />
        )}

        <div className="item-detail-container" ref={scrollContainerRef}>
          {invoice.isCompleted ? (
            <div className="moe-flex moe-justify-between moe-items-center">
              <BillTo customerFirstName={invoice.customerFirstName} customerLastName={invoice.customerLastName} />
              <CheckInTime invoiceId={invoiceId} className="moe-items-end" />
            </div>
          ) : (
            <div className="moe-mb-xs">
              <CheckInTime invoiceId={invoiceId} />
            </div>
          )}
          {invoice.type !== GroomingTicketInvoiceType.Membership && (
            <ServicesAddons
              invoiceId={invoiceId}
              editable={editable}
              isDeposit={isDeposit}
              handleActionWithLoading={handleActionWithLoading}
            />
          )}
          {!disabledProduct && (
            <Products
              invoiceId={invoiceId}
              editable={editable}
              isDeposit={isDeposit}
              productDrawerVisible={productDrawerVisible}
              onClickAddProduct={onClickAddProduct}
              handleActionWithLoading={handleActionWithLoading}
            />
          )}
          <MembershipProducts
            invoiceId={invoiceId}
            editable={editable}
            handleActionWithLoading={handleActionWithLoading}
          />
          {/* MARK(yueyue): Retail sales invoice only */}
          <Condition if={showCreatedOrderPackage}>
            <Packages invoiceId={invoiceId} />
          </Condition>
          <Discounts
            invoiceId={invoiceId}
            editable={editable}
            isDeposit={isDeposit}
            onClickAddDiscount={onClickAddDiscount}
            handleActionWithLoading={handleActionWithLoading}
          />
          <Condition if={!disabledMembership}>
            <MembershipRedeemEntry
              invoiceId={invoice.id}
              customerId={String(invoice.customerId)}
              onClick={onClickAddMembership}
              dispatchActionWithLoading={handleActionWithLoading}
            />
          </Condition>

          <Condition if={!showAppliedCredit}>
            <Credit
              onClickAddCredit={async () => {
                await applyCredit();
              }}
              customerId={invoice.customerId}
              isCreditEnabled={isCreditEnabled && !invoice.isCompleted}
            />
          </Condition>
          <OrderFeeSectionView>
            <OtherFees />
            <Total />
            {invoice.isCompleted && <Summary invoice={invoice} />}
            {renderPartialPayBlock()}
          </OrderFeeSectionView>
          {staff.isOwner() && invoice.isSync && (
            <ViewInQuickBooks link={invoice.qbInvoiceLink} syncTime={invoice.syncTime} />
          )}
        </div>
        {/* 根据是否出现滚动条设padding，保证上下对齐 */}
        {!invoice.isCompleted && (
          <Summary invoice={invoice} footerStyle={{ paddingRight: scrollHeight > offsetHeight ? '83px' : '68px' }} />
        )}
        {!!invoiceRefund?.refundDetails && (
          <InvoiceRefund
            invoiceRefundParams={invoiceRefund}
            onClose={handleRefundClose}
            onOk={handleRefundOk}
            invoiceId={invoiceId}
          />
        )}
        <SetTipsModal
          onClose={setTipsVisible.close}
          initialTipAmount={invoice.tipsAmount}
          amount={invoice.tipBasedAmount}
          maxTipsAmount={business.maxTipsAmount}
          visible={setTipsVisible.value}
          business={business}
          onSubmit={handleSetTips}
        />
      </InvoiceInfoView>
    );
  },
);

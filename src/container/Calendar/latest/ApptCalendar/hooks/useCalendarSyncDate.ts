import { useDispatch, useSelector, useStore } from 'amos';
import dayjs from 'dayjs';
import { useContext, useEffect } from 'react';
import { currentBusinessIdBox } from '../../../../../store/business/business.boxes';
import {
  calendarAddBlockTimeConfigBox,
  calendarOnlineBookingModal,
  calendarQuickAddApptVisible,
  calendarSelectedDate,
} from '../../../../../store/calendarLatest/calendar.boxes';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { FullCalendarCtx } from '../../AwesomeCalendar.utils';

/**
 * 用途
 * 1. 用于同步calendar的日期，store中的日期修改，根据full calendar api跳转到对应的日期
 * 2. 用于清除OB的modal弹窗
 * 3. 清除placeholder的状态
 */
export function useCalendarSyncDate() {
  const ref = useContext(FullCalendarCtx);
  const dispatch = useDispatch();
  const [selectedDate] = useSelector(calendarSelectedDate, currentBusinessIdBox);
  const store = useStore();

  /** 指定calendar跳转到某个日期 */
  const fullCalendarGo2Date = useLatestCallback((date: dayjs.ConfigType) => {
    const calendarApi = ref.current?.getApi();
    if (!calendarApi) return;

    const nextDate = dayjs(date);
    const currentDate = calendarApi.getDate();
    if (dayjs(currentDate).isSame(nextDate, 'date')) {
      return;
    }
    calendarApi.gotoDate(nextDate.toDate());
  });

  // 同步store数据中的日期
  useEffect(() => {
    fullCalendarGo2Date(selectedDate);
  }, [selectedDate]);

  // 清除OB的modal弹窗
  useEffect(() => {
    return () => {
      const quickAddVisible = store.select(calendarQuickAddApptVisible);
      // quickAdd 切换视图时保留modal弹窗
      if (quickAddVisible) {
        return;
      }

      dispatch([
        calendarOnlineBookingModal.setState(calendarOnlineBookingModal.initialState),
        calendarAddBlockTimeConfigBox.setState(() => calendarAddBlockTimeConfigBox.initialState),
      ]);
    };
  }, []);
}

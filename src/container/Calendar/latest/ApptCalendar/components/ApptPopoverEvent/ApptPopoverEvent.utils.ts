import { ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';

export interface ServiceAddonEvaluationBriefModel {
  serviceId: string;
  serviceName: string;
  lodgingUnitName?: string;
  serviceType?: ServiceType;
  isSlotFreeService?: boolean;
}

export const getServiceNameAndLodgingUnitName = (value: ServiceAddonEvaluationBriefModel) => {
  const { serviceId, serviceName, lodgingUnitName = '', serviceType } = value;
  return {
    serviceId,
    serviceName,
    lodgingUnitName,
    isSlotFreeService: serviceType === ServiceType.SERVICE && value.isSlotFreeService,
  };
};

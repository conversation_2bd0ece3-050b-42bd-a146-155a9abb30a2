import { type BusinessPetCodeModel } from '@moego/api-web/moego/models/business_customer/v1/business_pet_code_models';
import { MinorPawsOutlined } from '@moego/icons-react';
import { Tag, Text } from '@moego/ui';
import React, { memo } from 'react';
import { PetCodeTags } from '../../../../../../components/PetInfo/PetCodeTags';
import { useEventPopoverState } from '../../hooks/useEventPopoverState';
import { ApptPopFeedingMedication } from './ApptPopFeedingMedication';
import { ApptPopItemView } from './ApptPopoverEvent.style';
import { getServiceNameAndLodgingUnitName } from './ApptPopoverEvent.utils';

interface PetItemProps {
  petId: number;
  petName: string;
  breed: string;
  petCodes: BusinessPetCodeModel[];
}

const PetItem = memo(function PetItem({ petId, petName, breed, petCodes }: PetItemProps) {
  return (
    <div aria-label="pet and its services">
      <div className="moe-flex moe-items-center moe-gap-x-[4px] moe-text-primary">
        <Text aria-label="pet name" variant="small">
          {petName}
        </Text>
        <Text aria-label="pet breed" variant="small">{`(${breed})`}</Text>
        <PetCodeTags max={5} petId={petId} codes={petCodes.map((c) => ({ ...c, id: +c.id }))} />
      </div>
    </div>
  );
});

export function ApptPopPetServices() {
  const { petServices } = useEventPopoverState();

  return (
    <ApptPopItemView className="moe-items-start">
      <MinorPawsOutlined />
      <div className="moe-flex moe-flex-col moe-flex-1 moe-gap-y-[20px]">
        {petServices.map(({ petId, services, addOns, breed, petCodes, petName, evaluations }) => {
          const serviceDisplayInfoList = [...services, ...addOns, ...(evaluations || [])].map(
            getServiceNameAndLodgingUnitName,
          );
          return (
            <div key={petId} className="moe-flex moe-flex-col moe-gap-y-[4px]">
              <PetItem key={petId} petId={+petId} petCodes={petCodes} petName={petName} breed={breed} />
              {serviceDisplayInfoList.map(({ serviceId, serviceName, lodgingUnitName, isSlotFreeService }) => {
                const serviceLabel = lodgingUnitName ? `${serviceName} (${lodgingUnitName})` : serviceName;
                return (
                  <div key={serviceId} className="moe-flex moe-items-center moe-gap-x-[4px]">
                    <Text variant="small" className="moe-text-secondary">
                      {serviceLabel}
                    </Text>
                    {isSlotFreeService && (
                      <Tag
                        variant="filled"
                        className="moe-text-primary"
                        classNames={{ label: 'moe-w-full', base: 'moe-max-w-[120px]' }}
                        label="Slot free service"
                      />
                    )}
                  </div>
                );
              })}
              <ApptPopFeedingMedication petId={petId} />
            </div>
          );
        })}
      </div>
    </ApptPopItemView>
  );
}

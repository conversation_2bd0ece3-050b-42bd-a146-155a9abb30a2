import { useLatestCallback } from '@moego/finance-utils';
import { MinorChevronDownOutlined, MinorNewTabOutlined } from '@moego/icons-react';
import { Condition, Dropdown, Heading, Text, Tooltip, cn } from '@moego/ui';
import { useSelector } from 'amos';
import { isNumber } from 'lodash';
import React, { memo, useMemo, useRef } from 'react';
import { CommonTestIds } from '../../../../../../config/testIds/common';
import { PATH_SETTING_STAFF } from '../../../../../../router/paths';
import { selectCurrentPermissions } from '../../../../../../store/business/role.selectors';
import {
  selectCalendarSlotSettingInfo,
  selectIsEnableSlotCalender,
  selectStaffCalendarSlotInfo,
} from '../../../../../../store/calendarLatest/calendar.selectors';
import { smartSchedulingDataStore } from '../../../../../../store/smartScheduling/smartSchedulingStore.boxes';
import { isNoAssignedStaffId } from '../../../../../../store/staff/no-assigned-staff-helper';
import { StaffWorkingHourViewType } from '../../../../../../store/staffSchedule/staffSchedule.types';
import { truly } from '../../../../../../store/utils/utils';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { ReportActionName } from '../../../../../../utils/reportType';
import { reportData } from '../../../../../../utils/tracker';
import { renderCountableNounPlurals } from '../../../../../../utils/utils';
import { StaffInfoRender } from '../../../components/StaffInfoRender';
import { useBatchModal } from '../BatchOperate/hooks/useBatchModal';
import { StaffSlotTooltip } from './StaffSlotTooltip';
import { store } from '../../../../../../provider';

enum StaffMenuKeys {
  Cancel = 'Cancel',
  BookAgain = 'BookAgain',
  Reschedule = 'Reschedule',
  ShiftManagement = 'ShiftManagement',
}

export interface StaffHeaderProps extends React.HTMLAttributes<HTMLSpanElement> {
  date: string;
  staffId: number;
  className?: string;
  /** 有多少次预约 */
  apptsNum?: number;
  /** 有多少只宠物 */
  petsNum?: number;
  /** 是否显示 slot 位置 */
  showSlotLocation?: boolean;
}

export const StaffHeader = memo<StaffHeaderProps>(function StaffHeader(props) {
  const { staffId, date, className, apptsNum, petsNum, onMouseEnter, onMouseLeave, showSlotLocation, ...restProps } =
    props;
  const [{ isSmartScheduling }, permissions, staffCalendarSlotInfo, isEnableSlotCalender] = useSelector(
    smartSchedulingDataStore,
    selectCurrentPermissions,
    selectStaffCalendarSlotInfo(String(staffId), date),
    selectIsEnableSlotCalender(),
  );
  const descRef = useRef<HTMLParagraphElement>(null);
  const calendarSlotSettingInfo = store.select(selectCalendarSlotSettingInfo(String(staffId), date));
  const { openRescheduleModal, openBookAgainModal, openCancelModal } = useBatchModal();
  const { value: dropDownIconVisible, open: showDropDownIcon, close: hideDropDownIcon } = useBool();
  const isFullyBooked =
    !!staffCalendarSlotInfo.slotInfoList.length &&
    isEnableSlotCalender &&
    staffCalendarSlotInfo.usedPetCapacity >= calendarSlotSettingInfo.petCapacity;
  const notInSlotPetNum = useMemo(() => {
    return (
      (petsNum || 0) - staffCalendarSlotInfo.slotInfoList.reduce((acc, slotInfo) => acc + slotInfo.usedPetCapacity, 0)
    );
  }, [petsNum, staffCalendarSlotInfo.slotInfoList]);

  const hasAppts = isNumber(apptsNum) && apptsNum > 0;
  const hasPets = isNumber(petsNum) && petsNum > 0;
  const description = (() => {
    let apptsDesc = hasAppts ? renderCountableNounPlurals(apptsNum, 'appt') : '0 appt';
    let petsDesc = hasPets ? renderCountableNounPlurals(petsNum, 'pet') : '0 pet';

    if (staffCalendarSlotInfo.slotInfoList.length && isEnableSlotCalender) {
      petsDesc = calendarSlotSettingInfo.petCapacity
        ? `${calendarSlotSettingInfo.usedPetCapacity || 0}/${renderCountableNounPlurals(calendarSlotSettingInfo.petCapacity, 'pet')}`
        : hasPets
          ? renderCountableNounPlurals(petsNum, 'pet')
          : '0 pet';
    }

    if (isEnableSlotCalender && calendarSlotSettingInfo.appointmentCount) {
      apptsDesc = renderCountableNounPlurals(calendarSlotSettingInfo.appointmentCount, 'appt');
    }

    const description = [apptsDesc, petsDesc].join(',\u{20}');
    return description;
  })();

  const {
    hasBatchReschedulePermission,
    hasBatchBookAgainPermission,
    hasBatchCancelOrderPermission,
    hasShiftManagementPermission,
  } = useMemo(
    () => ({
      hasBatchReschedulePermission: permissions.has('canAdvancedEditTicket'),
      hasBatchBookAgainPermission: permissions.has('createAppointment'),
      hasBatchCancelOrderPermission: permissions.has('cancelOrDeleteTicket'),
      hasShiftManagementPermission: permissions.has('accessStaffShift'),
    }),
    [permissions],
  );

  const menuDisableKeys = useMemo(
    () =>
      [
        !hasBatchReschedulePermission && StaffMenuKeys.Reschedule,
        !hasBatchBookAgainPermission && StaffMenuKeys.BookAgain,
        !hasBatchCancelOrderPermission && StaffMenuKeys.Cancel,
        (isNoAssignedStaffId(staffId) || !hasShiftManagementPermission) && StaffMenuKeys.ShiftManagement,
      ].filter(truly),
    [hasBatchReschedulePermission, hasBatchBookAgainPermission, hasBatchCancelOrderPermission],
  );

  const BatchActionMenuSection = useMemo(
    () => [
      {
        label: 'Reschedule',
        key: StaffMenuKeys.Reschedule,
        onAction: () => openRescheduleModal({ staffId, date }),
      },
      {
        label: 'Book again',
        key: StaffMenuKeys.BookAgain,
        onAction: () => openBookAgainModal({ staffId, date }),
      },
      {
        label: 'Cancel',
        key: StaffMenuKeys.Cancel,
        onAction: () => openCancelModal({ staffId, date }),
      },
    ],
    [staffId, date],
  );

  const isShowSlotCapacityTooltip =
    (notInSlotPetNum || staffCalendarSlotInfo.slotInfoList.length) && showSlotLocation && isEnableSlotCalender;

  const handleGoToShiftManagement = useLatestCallback(() => {
    reportData(ReportActionName.CalendarStaffNameClick);
    window
      .open(
        PATH_SETTING_STAFF.queried(
          { workingHourListView: StaffWorkingHourViewType.ListView.toString(), workingHourStaffId: String(staffId) },
          { panel: 'workingHours' },
        ),
      )
      ?.focus();
  });

  const descEllipsisHeader = useMemo(() => {
    if (!descRef.current) {
      return null;
    }
    const isEllipsis = descRef.current.scrollWidth > descRef.current.clientWidth;
    return isEllipsis ? (
      <Text variant="caption" className="moe-text-tertiary">
        {description}
      </Text>
    ) : null;
  }, [description]);

  return (
    <StaffInfoRender staffId={staffId}>
      {({ staff }) => (
        <Dropdown align="end" side="bottom" className="moe-w-[200px]" isDisabled={Boolean(isSmartScheduling)}>
          <Dropdown.Trigger>
            <div
              {...restProps}
              data-testid={CommonTestIds.CalendarStaffName}
              className={cn(
                'moe-block moe-px-[8px] moe-py-[8px] moe-relative',
                showSlotLocation && 'moe-py-[16px]',
                className,
              )}
              onMouseEnter={(event) => {
                onMouseEnter?.(event);
                if (!isSmartScheduling) {
                  showDropDownIcon();
                }
              }}
              onMouseLeave={(event) => {
                onMouseLeave?.(event);
                hideDropDownIcon();
              }}
            >
              <Heading size="6" className="moe-text-primary moe-truncate">
                {staff.fullName()}
              </Heading>
              <Tooltip
                side="bottom"
                backgroundTheme="light"
                content={
                  isShowSlotCapacityTooltip ? (
                    <StaffSlotTooltip
                      isFullyBooked={isFullyBooked}
                      notInSlotPetNum={notInSlotPetNum}
                      slotInfoList={staffCalendarSlotInfo.slotInfoList}
                      header={descEllipsisHeader}
                    />
                  ) : null
                }
              >
                {/* div 不能去，否则 e2e 不展示 tooltip 测试的时候，Text 没有透传全部的 dom 属性导致的 */}
                <div>
                  <Text
                    variant="caption"
                    className={cn('moe-text-tertiary moe-mt-[2px] moe-truncate', {
                      'moe-text-warning': isFullyBooked,
                      'hover:moe-underline': isShowSlotCapacityTooltip,
                    })}
                    ref={descRef}
                  >
                    {description}
                  </Text>
                </div>
              </Tooltip>
              <Condition if={dropDownIconVisible}>
                <div
                  className={cn(
                    'moe-py-8px-150 moe-px-8px-50',
                    'moe-absolute moe-top-[4px] moe-bottom-[4px] moe-right-0',
                  )}
                >
                  <MinorChevronDownOutlined
                    className="moe-rounded-[10px]"
                    data-testid={CommonTestIds.CalendarStaffNameDropDownIcon}
                  />
                </div>
              </Condition>
            </div>
          </Dropdown.Trigger>
          <Dropdown.Menu disabledKeys={menuDisableKeys} selectedKeys={[]}>
            <Dropdown.MenuSection title="Bulk action" showSeparator>
              {BatchActionMenuSection.map((item) => (
                <Dropdown.MenuItem key={item.key} onAction={item.onAction}>
                  {item.label}
                </Dropdown.MenuItem>
              ))}
            </Dropdown.MenuSection>
            <Dropdown.MenuSection title="Staff setting">
              <Dropdown.MenuItem key={StaffMenuKeys.ShiftManagement} onAction={handleGoToShiftManagement}>
                <div className="moe-flex moe-gap-xs moe-items-center">
                  <Text variant="small" color="moe-text-primary">
                    Shift management
                  </Text>
                  <MinorNewTabOutlined className="moe-text-icon-tertiary moe-w-[16px] moe-h-[16px]" />
                </div>
              </Dropdown.MenuItem>
            </Dropdown.MenuSection>
          </Dropdown.Menu>
        </Dropdown>
      )}
    </StaffInfoRender>
  );
});

import { Drawer, type DrawerProps } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useEffect } from 'react';
import { ApptTestIds } from '../../../../../config/testIds/apptDrawer';
import { currentBusinessIdBox } from '../../../../../store/business/business.boxes';
import { useDrawer } from '../../../../../utils/Drawer';
import { useReloadWorkingStaff } from '../../hooks/useLoadWorkingStaff';

export interface CalendarDrawerProps extends Omit<DrawerProps, 'ref'> {}

export const CalendarDrawer = memo<CalendarDrawerProps>(function CalendarDrawer(props) {
  const { zIndex } = useDrawer('apptDetail');
  const [businessId] = useSelector(currentBusinessIdBox);
  const reloadWorkingStaff = useReloadWorkingStaff();

  useEffect(() => {
    reloadWorkingStaff(false);
  }, [businessId]);

  return (
    <Drawer
      closeButtonProps={{
        'data-testid': ApptTestIds.ApptCloseBtn,
      }}
      zIndex={zIndex}
      size="l"
      isDismissable={false}
      title={null}
      header={null}
      footer={null}
      {...props}
      classNames={{
        // base: '!moe-z-[100]',
        body: 'moe-flex-row moe-p-0 moe-rounded-8px-300 moe-border',
        container: 'moe-max-h-none',
        header: 'moe-hidden',
        ...props.classNames,
      }}
    />
  );
});

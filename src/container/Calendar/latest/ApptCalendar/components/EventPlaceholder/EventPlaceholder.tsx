import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Tooltip } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import { DEFAULT_BLOCK_PRESET_COLORS } from '../../../../../../components/ColorPicker/ColorCodePickerFlat';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { quickAddPlaceholderBox } from '../../../../../../store/calendarLatest/calendar.boxes';
import {
  type CalendarCardComponent,
  CalendarCardComponentType,
} from '../../../../../../store/calendarLatest/card.types';
import { EvaluationQuickAddSource } from '../../../../../../store/evaluation/evaluation.boxes';
import { isNoAssignedStaffId } from '../../../../../../store/staff/no-assigned-staff-helper';
import { useCloseAllDrawer } from '../../../../../../utils/hooks/useCloseAllDrawer';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { apptReporter } from '../../../../../../utils/reportData/reporter/apptReporter';
import { useCreateApptDrawer } from '../../../../../Appt/hooks/useCreateApptDrawer';
import { useCreateEvaluationDrawer } from '../../../../../Appt/hooks/useCreateEvaluationDrawer';
import { BlockDefaultDuration } from '../../../AwesomeCalendar.utils';
import { withRewriteProps } from '../../ApptCalendar.utils';
import { useBlockTimeConfig } from '../BlockTimeDrawer/hooks/useBlockTimeConfig';
import { PlaceholderCardApptBlock } from '../NewCardPlaceholder/PlaceholderCardApptBlock';

export const EventPlaceholder = withRewriteProps(
  () => ({}),
  function CalendarCardPlaceholder(props) {
    const [placeholderInfo, business] = useSelector(quickAddPlaceholderBox, selectCurrentBusiness());
    const { closeAllDrawer } = useCloseAllDrawer();
    const [, setBlockTimeDrawerConfig] = useBlockTimeConfig();
    const { staffId: placeholderStaffId, apptDateTime, apptEndDateTime } = placeholderInfo;
    const isNoAssignStaff = isNoAssignedStaffId(placeholderStaffId);
    const openCreateAppt = useCreateApptDrawer();
    const openCreateEvaluation = useCreateEvaluationDrawer();

    const onAddBlock = useLatestCallback(() => {
      closeAllDrawer();
      setBlockTimeDrawerConfig({
        visibleAddBlockDrawer: true,
        preset: {
          staffId: placeholderStaffId,
          blockStartDate: apptDateTime,
          duration: BlockDefaultDuration,
        },
        previewColorCode: DEFAULT_BLOCK_PRESET_COLORS[0],
      });
    });

    const onAddAppt = useLatestCallback(() => {
      apptReporter.setCreateApptDrawerOpenTime();
      apptReporter.setCreateApptFrom('calendar');

      if (isNoAssignStaff) {
        openCreateEvaluation({
          businessId: business.id,
          source: EvaluationQuickAddSource.Calendar,
          preset: { appointmentStart: apptDateTime, appointmentEnd: apptEndDateTime },
        });
        return;
      }
      openCreateAppt({
        preset: {
          staffId: placeholderStaffId,
          appointmentStart: apptDateTime,
          serviceItemType: ServiceItemType.GROOMING,
        },
      });
    });

    return (
      <Tooltip content={business.formatDateTime(apptDateTime)}>
        <div {...props} className="moe-pl-[1px] moe-h-full">
          <PlaceholderCardApptBlock
            className="moe-overflow-hidden"
            onAddAppt={onAddAppt}
            onAddBlock={onAddBlock}
            showAddBlock={!isNoAssignStaff}
          />
        </div>
      </Tooltip>
    );
  },
) as CalendarCardComponent;

EventPlaceholder.cardType = CalendarCardComponentType.Placeholder;

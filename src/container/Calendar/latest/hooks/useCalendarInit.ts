import { useDispatch, useSelector, useStore } from 'amos';
import dayjs, { isDayjs } from 'dayjs';
import { sleep } from 'monofile-utilities/lib/sleep';
import { useContext, useEffect, useMemo, useRef } from 'react';
import { useHistory } from 'react-router';
import { useAsync, useMount, usePrevious } from 'react-use';
import { PATH_GROOMING_CALENDAR } from '../../../../router/paths';
import { AutoMessageType } from '../../../../store/autoMessage/autoMessage.boxes';
import { currentBusinessIdBox } from '../../../../store/business/business.boxes';
import {
  getCalendarConfig,
  getCalendarSlotInfo,
  setStaffsForCalendar,
} from '../../../../store/calendarLatest/actions/private/calendar.actions';
import {
  CalendarViewNameArr,
  type StaffCalendarInfoBox,
  calendarCurrentViewBox,
  calendarNotificationBox,
  calendarQuickAddApptFields,
  calendarSelectedDate,
} from '../../../../store/calendarLatest/calendar.boxes';
import { FullCalendarViewType, QuickAddSource, ViewType } from '../../../../store/calendarLatest/calendar.types';
import { CalendarType, calendarTypeStore } from '../../../../store/calendarLatest/serviceSummary.boxes';
import { getGroomingServiceAvailability } from '../../../../store/onlineBooking/actions/private/onlineBookingSettings.actions';
import { getPetCodeList } from '../../../../store/pet/petCode.actions';
import { getAllBusinessBasicServiceInfoList } from '../../../../store/service/actions/public/service.actions';
import { getBusinessServiceArea } from '../../../../store/serviceArea/serviceArea.actions';
import { getSmartSchedulingSetting } from '../../../../store/smartScheduling/actions/public/smartScheduling.actions';
import { smartSchedulingDataStore } from '../../../../store/smartScheduling/smartSchedulingStore.boxes';
import { getWorkingRangeList } from '../../../../store/staff/workingRange.actions';
import { isNormal } from '../../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../../../utils/DateTimeUtil';
import { useRouteState } from '../../../../utils/RoutePath';
import { globalEvent } from '../../../../utils/events/events';
import { useAsyncEffect } from '../../../../utils/hooks/useAsyncEffect';
import { jsonParse } from '../../../../utils/utils';
import { useSignNowMessageListener } from '../../../Agreement/hooks/useSignNowMessageListener';
import { useGetCalendarEvents } from '../ApptCalendar/hooks/useFullCalendarEvents';
import { FullCalendarCtx, LK_VISIBLE_STAFFS_V2 } from '../AwesomeCalendar.utils';
import { useEnableMultiPetBySlotFeature } from '../components/SlotCalendar/hooks/useSlotCalendarFeature';
import { useReloadStaffServiceArea } from './useLoadStaffServiceArea';
import { useReloadWorkingStaff } from './useLoadWorkingStaff';
import { getRangeDates, useSelectedRangeDatesWithSelector } from './useSelectedRangeDates';

export function useCalendarInit() {
  const store = useStore();
  const calendarRef = useContext(FullCalendarCtx);
  const history = useHistory();
  const initRender = useRef(true);
  const dispatch = useDispatch();
  const { rangeDates: dateRanges } = useSelectedRangeDatesWithSelector();
  const { reloadAppts } = useGetCalendarEvents();
  const reloadWorkingStaff = useReloadWorkingStaff();
  const reloadStaffServiceArea = useReloadStaffServiceArea();
  const [
    currentView,
    selectedDate,
    bizId,
    { source, staffId: bookAgainStaffId },
    { isSmartScheduling, staffIdList: SSTargetStaffIdList },
    calendarType,
  ] = useSelector(
    calendarCurrentViewBox,
    calendarSelectedDate,
    currentBusinessIdBox,
    calendarQuickAddApptFields,
    smartSchedulingDataStore,
    calendarTypeStore,
  );
  const isFromBookAgain = source === QuickAddSource.BookAgain;
  const preDateRanges = usePrevious(dateRanges);
  const preSelectedDate = usePrevious(selectedDate);
  const preBizId = usePrevious(bizId);
  const locationState = useRouteState(PATH_GROOMING_CALENDAR) ?? {};
  const {
    /** 其他路由跳转过来的时间 */
    selectedDate: initSelectedDate,
    backCalendarView,
  } = locationState;
  const isEnableCalendarSlot = useEnableMultiPetBySlotFeature();
  const { startDate, endDate } = useMemo(() => getRangeDates(selectedDate, currentView), [selectedDate, currentView]);

  // 基本数据信息
  useAsyncEffect(async () => {
    if (isNormal(bizId)) {
      await dispatch(getCalendarConfig());
      const now = dayjs().format(DATE_FORMAT_EXCHANGE);
      dispatch([
        getWorkingRangeList({ startDate: now, endDate: now }),
        getAllBusinessBasicServiceInfoList(),
        getPetCodeList(),
        getBusinessServiceArea(bizId),
      ]);
    }
  }, [bizId]);

  useMount(() => {
    const { ticketId, customerId, apptDate, apptStartTime, isOB, isChangedStartTime } = locationState || {};
    if (!isOB && isNormal(ticketId) && isNormal(customerId)) {
      // 仅修改 apptStartTime 需要 popup
      if (apptDate && apptStartTime && isChangedStartTime) {
        dispatch(
          calendarNotificationBox.setState({
            ticketId,
            customerId,
            mode: AutoMessageType.AppointmentRescheduled,
          }),
        );
      } else if (!apptStartTime) {
        // valid apptStartTime means from edit appt page
        dispatch(
          calendarNotificationBox.setState({
            ticketId,
            customerId,
            mode: AutoMessageType.AppointmentBooked,
          }),
        );
      }
    }
    // 清空之前保存的history state
    history.replace(location.pathname + location.search + location.hash);
  });

  useEffect(() => {
    if (isNormal(bizId)) {
      dispatch([
        getSmartSchedulingSetting(),
        getCalendarSlotInfo({
          startDate,
          endDate,
          businessId: bizId.toString(),
        }),
      ]);
    }
  }, [bizId]);

  useEffect(() => {
    if (isNormal(bizId) && isEnableCalendarSlot) {
      dispatch(getGroomingServiceAvailability({ businessId: bizId.toString() }));
    }
  }, [bizId, isEnableCalendarSlot]);

  // 初始化时间
  useEffect(() => {
    if (initSelectedDate) {
      const nextSelectedDate = dayjs(initSelectedDate, DATE_FORMAT_EXCHANGE).startOf('date');
      dispatch(calendarSelectedDate.setState(nextSelectedDate));
    }
  }, [initSelectedDate]);

  useEffect(() => {
    if (!!backCalendarView && CalendarViewNameArr.includes(backCalendarView)) {
      dispatch(calendarCurrentViewBox.setState(backCalendarView as ViewType));
    }
  }, [backCalendarView]);

  // 同步currentView到calendar中
  useAsyncEffect(async () => {
    const isListView = currentView === ViewType.LIST;
    const calendar = calendarRef.current;
    if (!calendar || isListView) return;
    const currentViewName = calendar.getApi().view.type;
    const config = FullCalendarViewType.mapLabels[FullCalendarViewType[currentView]];
    const { fullCalendarViewName } = config;
    if (currentViewName === fullCalendarViewName) {
      return;
    }
    // changeView会有点卡顿，先sleep一下，更新下UI
    await sleep(0);
    calendar.getApi().changeView(fullCalendarViewName);
  }, [currentView]);

  // 获取所有appt卡片的数据，并scroll到对应的视图
  useAsync(async () => {
    const currentView = store.select(calendarCurrentViewBox);

    const isMonthly = currentView === ViewType.MONTH;
    const monthlyUnChanged = isMonthly && isDayjs(preSelectedDate) && preSelectedDate.isSame(selectedDate, 'month');
    // 同一个月份，不需要重新获取数据
    if (monthlyUnChanged) {
      return;
    }

    // 不需要重新获取数据
    if (isDayjs(preSelectedDate)) {
      const { startDate, endDate } = getRangeDates(preSelectedDate, currentView);
      if (!isMonthly && selectedDate.isSameOrAfter(startDate, 'date') && selectedDate.isSameOrBefore(endDate, 'date')) {
        return;
      }
    }
    if (calendarType === CalendarType.Service) {
      return;
    }

    await reloadAppts();
    // it's ok to ignore the preSelectedDate in deps
  }, [selectedDate]);

  // 获取show on calendar的staff数据
  useAsync(async () => {
    const isMonthly = store.select(calendarCurrentViewBox) === ViewType.MONTH;
    const monthlyUnChanged =
      isMonthly &&
      // it's ok to ignore the preDateRanges in deps
      preDateRanges &&
      preDateRanges.startDate === dateRanges.startDate &&
      preDateRanges.endDate === dateRanges.endDate &&
      preBizId === bizId;

    // 同一个月份，不需要重新获取数据
    if (monthlyUnChanged) {
      return;
    }
    const staffs = await reloadWorkingStaff(!initRender.current);
    await reloadStaffServiceArea();
    // 第一次默认选中的状态
    if (initRender.current) {
      initRender.current = false;
      // 初始化阶段第一次执行，所以不需要加入deps
      const bookAgainStaffValid =
        isFromBookAgain &&
        isNormal(bookAgainStaffId) &&
        staffs.some((i) => i.id === bookAgainStaffId && !!i.showOnCalendar);

      const hasSSTargetStaff = isSmartScheduling && SSTargetStaffIdList && SSTargetStaffIdList?.length > 0;
      const str = localStorage.getItem(LK_VISIBLE_STAFFS_V2 + bizId);
      const obj = jsonParse<Pick<StaffCalendarInfoBox, 'onlyWorkingStaff' | 'selectedStaffs'> | null>(str, null);
      // 缓存中存在，用缓存的
      if (obj) {
        const { onlyWorkingStaff, selectedStaffs } = obj;
        // 需要过滤一下，有些 staff 可能已经发生变化(比如被删除、can not access 之类的)
        const filteredSelectedStaffs = onlyWorkingStaff
          ? staffs.filter((i) => i.isWorkingStaff).map((i) => i.id)
          : selectedStaffs.filter((i) => staffs.some((j) => j.id === i));
        dispatch(
          setStaffsForCalendar((pre) => {
            // SS逻辑不变，SS和bookAgain不会同时发生
            const nextSelectedStaffs = hasSSTargetStaff ? SSTargetStaffIdList : filteredSelectedStaffs;
            let nextOnlyWorkingStaff = hasSSTargetStaff ? false : onlyWorkingStaff;
            // book again需要加入可能没选中的staff
            if (bookAgainStaffValid) {
              const isSelected = nextSelectedStaffs.includes(bookAgainStaffId);
              const preOnlyWorkingStaff = onlyWorkingStaff && !isSelected;
              // bookAgain时，未选中的，要选中
              if (!isSelected) {
                nextSelectedStaffs.push(bookAgainStaffId);
              }
              // 之前是working staff，但是当前将要quick add的staff没有被选中，加入selectedStaffs后，需要手动清除onlyWorkingStaff
              nextOnlyWorkingStaff = preOnlyWorkingStaff ? false : nextOnlyWorkingStaff;
            }
            const result = {
              ...pre,
              selectedStaffs: nextSelectedStaffs,
              onlyWorkingStaff: nextOnlyWorkingStaff,
            };
            // 如果最终没有任何一个staff被选中，则兜底全选
            if (result.selectedStaffs.length === 0) {
              // isAllStaff=true后，不需要主动result.selectedStaffs = [...]，setStaffsForCalendar内部已经处理了
              result.isAllStaff = true;
              // all和working staff互斥，这里确保一下reset了onlyWorkingStaff
              result.onlyWorkingStaff = false;
            }
            return result;
          }),
        );
        return;
      }
      // 缓存中不存在，默认全部选中
      const selectedStaffs = hasSSTargetStaff ? SSTargetStaffIdList : staffs.map((i) => i.id);
      dispatch(setStaffsForCalendar((pre) => ({ ...pre, selectedStaffs, onlyWorkingStaff: false })));
    }
    // it's ok to ignore the preDateRanges in deps
    // preDateRanges加入依赖的话，如果其他状态导致这个hooks重新渲染，也会导致重复拉数据。因为如果上一次dateRanges变化了，preDateRanges肯定也会变化。仅需要判断dateRanges是否变化即可
  }, [dateRanges.startDate, dateRanges.endDate, bizId, isSmartScheduling, SSTargetStaffIdList]);

  useSignNowMessageListener(async () => {
    await reloadAppts();
  });

  useEffect(() => {
    const dispose = globalEvent.refresh.on(() => {
      reloadAppts();
      reloadWorkingStaff();
    });
    return dispose;
  }, []);
}

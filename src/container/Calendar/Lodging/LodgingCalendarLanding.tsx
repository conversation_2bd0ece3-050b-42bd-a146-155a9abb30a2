import { Spin } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect, useRef, useState } from 'react';
import { useMount } from 'react-use';
import { CardVisibleProvider } from '../../../components/CardVisible';
import { LayoutContainer } from '../../../layout/LayoutContainer';
import { getLodgingCalendarList } from '../../../store/calendarLatest/actions/private/lodgingCalendar.actions';
import { selectLodgingCalendarCurrentBusinessId } from '../../../store/calendarLatest/lodgingCalendar.selectors';
import { getCompanyPreferenceSetting } from '../../../store/company/company.actions';
import { selectBDFeatureEnable } from '../../../store/company/company.selectors';
import { getPetSizeList } from '../../../store/onlineBooking/actions/private/petSize.actions';
import { getPetVaccineList } from '../../../store/pet/petVaccine.actions';
import { globalEvent } from '../../../utils/events/events';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { AwesomeCalendarProvider } from '../latest/AwesomeCalendarProvider';
import { LodgingCalendar } from './LodgingCalendar/LodgingCalendar';
import { LodgingCalendarController } from './LodgingCalendarController/LodgingCalendarController';

export const LodgingCalendarLanding = memo(() => {
  const [lodgingCalendarBusinessId] = useSelector(selectLodgingCalendarCurrentBusinessId);
  const dispatch = useDispatch();
  const scrollerProviderRef = useRef<HTMLDivElement>(null);

  const getLodgingCalendarData = useSerialCallback(async () => {
    await dispatch(getLodgingCalendarList());
  });

  useEffect(() => {
    const dispose = globalEvent.refresh.on(getLodgingCalendarData);
    return dispose;
  }, [getLodgingCalendarData]);

  useMount(() => {
    dispatch([getCompanyPreferenceSetting(), getPetSizeList(), getPetVaccineList()]);
  });

  useEffect(() => {
    getLodgingCalendarData();
  }, [lodgingCalendarBusinessId]);

  const [boardingDaycareFeatureEnable] = useSelector(selectBDFeatureEnable);

  const isLoading = getLodgingCalendarData.isBusy();
  const [_loading, setLoading] = useState(false);
  const loading = _loading || isLoading;

  if (!boardingDaycareFeatureEnable) return null;

  return (
    <AwesomeCalendarProvider>
      <CardVisibleProvider>
        <LayoutContainer className="moe-px-l moe-pt-0 moe-pb-l moe-bg-white" scrollerProviderRef={scrollerProviderRef}>
          <LodgingCalendarController onLoading={setLoading} />
          <Spin
            classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-flex moe-w-full moe-h-full' }}
            isLoading={loading}
          >
            <LodgingCalendar scrollerProviderRef={scrollerProviderRef} isLoading={isLoading} />
          </Spin>
        </LayoutContainer>
      </CardVisibleProvider>
    </AwesomeCalendarProvider>
  );
});

LodgingCalendarLanding.displayName = 'LodgingCalendarLanding';

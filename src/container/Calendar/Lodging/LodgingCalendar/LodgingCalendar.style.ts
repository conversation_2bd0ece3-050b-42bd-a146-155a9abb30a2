import styled from 'styled-components';

export const LodgingCalendarView = styled.div<{ isAllResourceGroupNotExpanded: boolean }>`
  --fc-border-color: #e6e6e6;
  --fc-hover-bg-color: rgba(0, 0, 0, 0.024);
  flex: 1;
  /* 去掉拉伸时出现的背景色 */
  .fc-timeline-bg {
    display: none;
  }
  /* resource cell */
  &.resource-empty .fc-scrollgrid-section-liquid {
    display: none;
  }
  .fc .fc-datagrid-cell-cushion {
    padding: 0;
  }

  // hidden resource scroll bar
  .fc-scrollgrid-section-liquid td:first-of-type .fc-scroller-liquid-absolute {
    padding-right: 20px;
    margin-right: -20px;
  }
  .fc-datagrid-cell-frame {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .fc .fc-datagrid-body {
    border-bottom-style: solid;
  }
  .fc .fc-scrollgrid-section-liquid > td {
    border-bottom: none;
  }

  /* disable divider for resource resize */
  .fc-resource-timeline-divider {
    pointer-events: none;
    width: 0px;
    border: none;
  }
  /* 最右侧border */
  .fc-scrollgrid-section-body > td:last-child ,
  /* 内容区横线border */
  .fc-scrollgrid-sync-table .fc-timeline-lane {
    /* note: don't use border:none, it will affect the table row height */
    border-color: transparent;
  }
  .fc-timeline-lane-frame {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    &:hover {
      background-color: var(--fc-hover-bg-color);
      box-shadow: 0 0 0 1px var(--fc-border-color);
    }
  }
  /* 外圈border */
  .fc-theme-standard .fc-scrollgrid,
  /* Room */
  .fc-theme-standard th,
  /* 日期 */
  .fc-scrollgrid-section-header td {
    border-left: none;
    border-right: none;
  }
  /* 内容区 补齐宽度 */
  .fc-scroller-harness-liquid {
    margin-right: -1px;
  }

  .fc .fc-timeline-slot-frame {
    display: flex;
    justify-content: center;
  }

  /* fc event style */
  .fc-h-event {
    border: none;
    background-color: transparent;
  }

  /* event cursor */
  .fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start,
  .fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end {
    cursor: ew-resize;
  }
  /* event edge left */
  .moe-fc-event-add-lodging:not(.fc-event-selected) .fc-event-resizer-start {
    left: 0;
    top: -2px;
    width: 3px;
    height: 48px;
    border-radius: 4px;
    background-color: #3a3a3a;
  }
  /* event edge right */
  .moe-fc-event-add-lodging:not(.fc-event-selected) .fc-event-resizer-end {
    right: 0;
    top: -2px;
    width: 3px;
    height: 48px;
    border-radius: 4px;
    background-color: #3a3a3a;
  }

  .moe-disable-event-resize .fc-event-resizer {
    display: none;
  }

  /* event that cross view, hidden the triangle indicator */
  .fc-direction-ltr .fc-timeline-event:not(.fc-event-start):before,
  .fc-direction-ltr .fc-timeline-event:not(.fc-event-end):after {
    display: none;
  }

  /* slot-lane close date bg */
  .fc .fc-timeline-slot-lane > div:has(> div[slot-close='true']) {
    height: 100%;
  }

  /** group label */
  .fc-resource-group .fc-datagrid-expander .fc-icon::before,
  .fc-resource-group .fc-datagrid-expander {
    display: none;
    pointer-events: none;
    select: none;
  }
  /** group label divider */
  .moe-resource-label-divider::before {
    content: '';
    position: absolute;
    right: 0;
    width: 1px;
    height: 100%;
    background-color: white;
  }
  .moe-resource-label-divider-hover::before {
    background-color: var(--moe-color-bg-neutral-sunken-light);
  }
  /** all group not collapsed clear bottom border */
  .fc-resource-timeline .fc-scrollgrid-section > td {
    ${(props) => {
      return props.isAllResourceGroupNotExpanded ? 'border-right: none;' : '';
    }}
  }
  .fc-scrollgrid .fc-scrollgrid-section .fc-timeline-body .fc-timeline-slot-lane {
    ${(props) => {
      return props.isAllResourceGroupNotExpanded ? 'display: none;' : '';
    }}
  }

  /** 用于 group title 一体 */
  .fc-scrollgrid-section .fc-scroller-harness .fc-scroller-liquid-absolute,
  .fc-scroller-harness .fc-scroller:first-child,
  .fc-scrollgrid-section .fc-scroller-harness {
    overflow: visible !important;
  }
  /* 重新设置 thead 的样式 */
  .fc-resource-timeline > table.fc-scrollgrid:has(> thead) {
    border-top: none;
  }
  .fc-resource-timeline > table.fc-scrollgrid > thead > tr > td:first-child,
  .fc-resource-timeline > table.fc-scrollgrid > thead > tr > td .fc-timeline-header > table {
    box-shadow: 0 -1px 0 0 var(--fc-border-color);
  }
  /** resource label 高度撑满 */
  .fc-datagrid-body .fc-datagrid-cell > .fc-datagrid-cell-frame {
    position: relative;
  }
  .fc-timeline-body .fc-scrollgrid-sync-table tr:last-child .fc-timeline-lane-frame {
    box-shadow: 0 1px 0 0 var(--fc-border-color);
  }

  /* slot label 背景 */
  .fc-timeline-header th.fc-timeline-slot .fc-timeline-slot-frame {
    position: relative;
    height: 62px !important;
  }
  .fc-timeline-header th.fc-timeline-slot a {
    height: 62px !important;
  }

  /* 表头 sticky */

  .fc-scrollgrid.fc-scrollgrid-liquid > thead {
    position: sticky;
    top: calc(var(--lodging-controller-height, 80px) + 25px); // 保持原始的 24px 偏移 + 1px 的 border + 基于 LodgingCalendarController 的高度
    z-index: 2; // 要比 1 高，且比 LodgingCalendarController 的 z-index: 5 低
    background-color: #fff;
  }
`;
export const LodgingAppointmentCardView = styled.div`
  &.isDragging {
    cursor: move;
    box-shadow: 0px 0px 0px 1px #e6e6e6 inset;
    filter: drop-shadow(0px 4px 22px rgba(0, 0, 0, 0.08)) drop-shadow(0px 0px 8px rgba(0, 0, 0, 0.04));
  }
`;

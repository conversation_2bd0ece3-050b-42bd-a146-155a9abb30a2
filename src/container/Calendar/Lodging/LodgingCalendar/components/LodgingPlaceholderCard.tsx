import { type EventApi } from '@fullcalendar/react';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Heading, Tooltip, cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo, useLayoutEffect, useMemo, useState } from 'react';
import { Switch } from '../../../../../components/SwitchCase';
import { selectCreateAppointmentPermission } from '../../../../../store/business/role.selectors';
import { removeLodgingPlaceholderCard } from '../../../../../store/calendarLatest/actions/private/lodgingCalendar.actions';
import { QuickAddSource } from '../../../../../store/calendarLatest/calendar.types';
import {
  LodgingCalendarPlaceholderCardTypeMap,
  lodgingPlaceholderCardMapBox,
} from '../../../../../store/calendarLatest/lodgingCalendar.boxes';
import { ReportActionName } from '../../../../../utils/reportType';
import { reportData } from '../../../../../utils/tracker';
import { useCreateApptDrawer } from '../../../../Appt/hooks/useCreateApptDrawer';
import { useFillTimeSchedule } from '../../../../Appt/hooks/useFillTimeSchedule';
import { getDiffDateTextByUnit } from '../../../../Appt/utils/getDiffDateTextByUnit';
import { useTextOverflow } from '../../../latest/ApptCalendar/hooks/useTextOverflow';
import { LodgingAppointmentCardView } from '../LodgingCalendar.style';

export interface LodgingPlaceholderCardProps {
  event: EventApi;
  isResizing: boolean;
  isDragging: boolean;
}

export const LodgingAddPlaceholderCard = memo<LodgingPlaceholderCardProps>(({ event, isResizing, isDragging }) => {
  const { id } = event;
  const [hasAddAuth, lodgingPlaceholderCard] = useSelector(
    selectCreateAppointmentPermission,
    lodgingPlaceholderCardMapBox.mustGetItem(id),
  );
  const { lodgingUnitId, startDate, endDate } = lodgingPlaceholderCard;
  const tooltipContentId = useMemo(() => `tooltip-content-${id}`, [id]);
  const { mustGetWorkingRange } = useFillTimeSchedule();
  const dispatch = useDispatch();
  const openCreateDrawer = useCreateApptDrawer();

  const diffDays = useMemo(() => dayjs(event.end).diff(event.start, 'day'), [event.end, event.start]);

  const tooltipText = useMemo(() => {
    if (!hasAddAuth) {
      return `Please request "Can create appointment" permission from the business owner`;
    }

    if (diffDays === 0) {
      return null;
    }

    return getDiffDateTextByUnit(diffDays);
  }, [diffDays, hasAddAuth]);

  const handleAddAppointment = () => {
    if (!hasAddAuth) {
      return;
    }

    reportData(ReportActionName.lodgingViewOpenQuickAddAppt);
    dispatch(removeLodgingPlaceholderCard());
    openCreateDrawer({
      params: {
        source: QuickAddSource.LodgingView,
      },
      preset: {
        appointmentStart: startDate,
        // 如果end time也取working hour会有些edge case，比如赶上close date怎么取之类的，所以干脆就是取跟start time同一个时刻了
        appointmentEnd: endDate.setMinutes(mustGetWorkingRange(startDate).startTime),
        lodgingId: lodgingUnitId,
        serviceItemType: ServiceItemType.BOARDING,
      },
    });
  };

  useLayoutEffect(() => {
    if (isResizing || isDragging) {
      // HACK: 当节点处于 resizing 或 dragging 状态时，删除旧 tooltip，否则会出现多个 tooltip 的情况
      const tooltip = document.getElementById(tooltipContentId)?.closest('div[role="tooltip"]') as HTMLDivElement;
      if (tooltip) {
        tooltip.style.display = 'none';
      }
    }
  }, [isResizing, isDragging]);

  return (
    <Tooltip side="top" isOpen={!isResizing && !isDragging} content={<div id={tooltipContentId}>{tooltipText}</div>}>
      <div
        className={cn(
          'moe-h-[40px] moe-flex moe-items-center moe-justify-center moe-rounded-[2px] moe-bg-neutral-sunken-1',
          {
            'moe-cursor-not-allowed': !hasAddAuth,
          },
        )}
        onClick={handleAddAppointment}
      >
        <Heading size="6" className="moe-text-primary moe-border-b moe-border-solid">
          Add
        </Heading>
      </div>
    </Tooltip>
  );
});

interface LodgingNewAppointmentPlaceholderCardProps {
  event: EventApi;
}
export const LodgingNewAppointmentPlaceholderCard = memo<LodgingNewAppointmentPlaceholderCardProps>(({ event }) => {
  const { extendedProps } = event;
  const { splitLodging } = extendedProps;
  const [text, setText] = useState(
    splitLodging ? `New appt (${splitLodging.index + 1}/${splitLodging.total})` : 'New Appointment',
  );
  const ref = useTextOverflow<HTMLDivElement>(() => {
    setText('New');
  });

  return (
    <LodgingAppointmentCardView className="moe-h-[40px] moe-flex moe-items-center moe-justify-center moe-rounded-[2px] moe-bg-[#DFF3FF]">
      <Heading ref={ref} size="6" className="moe-text-primary moe-truncate">
        {text}
      </Heading>
    </LodgingAppointmentCardView>
  );
});

export const LodgingPlaceholderCard = memo<LodgingPlaceholderCardProps>(({ event, ...rest }) => {
  const { extendedProps } = event;
  const { type } = extendedProps;

  return (
    <Switch>
      <Switch.Case if={type === LodgingCalendarPlaceholderCardTypeMap.Add}>
        <LodgingAddPlaceholderCard event={event} {...rest} />
      </Switch.Case>
      <Switch.Case else>
        <LodgingNewAppointmentPlaceholderCard event={event} />
      </Switch.Case>
    </Switch>
  );
});

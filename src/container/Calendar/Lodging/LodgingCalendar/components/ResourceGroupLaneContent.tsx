import { type ColCellContentArg } from '@fullcalendar/resource-common';
import { MinorChevronDownOutlined } from '@moego/icons-react';
import { Heading, Tooltip, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useEffect, useMemo } from 'react';
import { Condition } from '../../../../../components/Condition';
import { lodgingViewExpandAllStore } from '../../../../../store/calendarLatest/lodgingCalendar.boxes';
import { selectLodgingCalendarConfig } from '../../../../../store/calendarLatest/lodgingCalendar.selectors';
import { getLodgingTypeNameWithoutSorting } from '../../../../../store/calendarLatest/lodgingCalendar.utils';
import { lodgingTypeMapBox } from '../../../../../store/lodging/lodgingType.boxes';
import { lodgingTypesNameMapBox } from '../../../../../store/lodging/lodgingUnit.boxes';
import { useBool } from '../../../../../utils/hooks/useBool';
import { type LatestCallback, useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { ReportActionName } from '../../../../../utils/reportType';
import { reportData } from '../../../../../utils/tracker';
import { useDefaultAreaTooltip } from '../../hooks/useDefaultAreaTooltip';
import { RESOURCE_TYPE_FILED } from '../LodgingCalendar.config';
import { LodgingDefaultAreaSummary } from './LodgingDefaultAreaSummary';
import { LodgingRoomDescription } from './LodgingRoomDescription';
import { LodgingSummary } from './LodgingSummary';

export const RELATED_GROUP_LANE_WRAPPER = 'related-group-lane-wrapper';
export const DEFAULT_GROUP_LABEL_WIDTH = 110;

export type GroupSourceDataMap = Record<
  string,
  {
    hoverValue?: boolean;
    laneElement?: HTMLElement;
    /** labelElement */
    element?: HTMLElement;
    expandedValue?: boolean;
  }
>;

export interface ResourceGroupLaneContentProps {
  className?: string;
  laneContentArgs: ColCellContentArg;
  groupSourceDataMap: GroupSourceDataMap;
  setGroupSourceDataMap: LatestCallback<(value: GroupSourceDataMap) => void>;
  handleClickExpand: () => void;
}

export const ResourceGroupLaneContent = memo<ResourceGroupLaneContentProps>((props) => {
  const { laneContentArgs, groupSourceDataMap, setGroupSourceDataMap, handleClickExpand } = props;
  const { groupValue, view } = laneContentArgs;
  const [lodgingTypesNameMap, lodgingCalendarConfig, isExpandAll, lodgingTypeMap] = useSelector(
    lodgingTypesNameMapBox,
    selectLodgingCalendarConfig,
    lodgingViewExpandAllStore,
    lodgingTypeMapBox,
  );
  const expanded = useBool(isExpandAll);

  const lodgingTypes = lodgingTypesNameMap.mustGetItem(groupValue);
  const lodgingType = lodgingTypeMap.mustGetItem(lodgingTypes.id);
  const patchId = `${RESOURCE_TYPE_FILED}:${groupValue}`;
  const groupSourceHoverData = groupSourceDataMap[groupValue] || {};
  const { title: defaultAreaTitle, content: defaultAreaContent } = useDefaultAreaTooltip();

  // 第一个分组不显示底部阴影在折叠的时候
  const isFirstGroup = view.calendar.getResources()[0]?.extendedProps?.lodgingTypeName === groupValue;

  const handleCollapse = useLatestCallback((value = !expanded.value) => {
    if (value === expanded.value) {
      return;
    }
    expanded.as(value);
    view.calendar.dispatch({
      type: 'SET_RESOURCE_ENTITY_EXPANDED',
      id: patchId,
      isExpanded: value,
    });
    reportData(ReportActionName.lodgingViewLodgingTypeCollapse);
  });

  const toolTipContent = useMemo(() => {
    const title = getLodgingTypeNameWithoutSorting(groupValue);

    return (
      <div className="moe-flex moe-flex-col moe-gap-s">
        <Heading size="5" className="moe-text-primary">
          {lodgingTypes.isDefaultArea ? defaultAreaTitle : title}
        </Heading>
        <Condition if={lodgingType.hasPhoto}>
          <img
            className="moe-w-[288px] moe-h-[144px] moe-mx-auto moe-rounded-s moe-object-cover"
            src={lodgingType.firstPhoto}
          />
        </Condition>
        {lodgingTypes.isDefaultArea ? defaultAreaContent : <LodgingRoomDescription col lodgingTypes={lodgingTypes} />}
      </div>
    );
  }, [lodgingTypes, lodgingType, defaultAreaTitle, defaultAreaContent, groupValue]);

  useEffect(() => {
    // 初始化时展开
    view.calendar.dispatch({
      type: 'SET_RESOURCE_ENTITY_EXPANDED',
      id: patchId,
      isExpanded: expanded.value,
    });
  }, []);

  useEffect(() => {
    handleCollapse(isExpandAll);
  }, [isExpandAll]);

  useEffect(() => {
    if (groupSourceHoverData.element) {
      groupSourceHoverData.element.onclick = () => {
        handleCollapse(!expanded.value);
      };
    }
    return () => {
      if (groupSourceHoverData.element) {
        groupSourceHoverData.element.onclick = null;
      }
    };
  }, [groupSourceHoverData.element]);

  useEffect(() => {
    // 更新折叠状态
    setGroupSourceDataMap({
      [groupValue]: {
        expandedValue: expanded.value,
      },
    });
  }, [expanded.value]);

  return (
    <div className="moe-w-full">
      <div
        data-slot={RELATED_GROUP_LANE_WRAPPER}
        className={cn(
          'moe-shadow-[0_1px_0_0_#e6e6e6,0_-1px_0_0_#e6e6e6] moe-pl-[16px] moe-flex moe-items-center moe-absolute hover:moe-bg-neutral-sunken-light moe-cursor-pointer moe-left-[-110px] moe-z-[1]',
          'width-fill',
          expanded.value ? 'moe-h-[35px]' : 'moe-h-[59px]',
          {
            'moe-bg-neutral-sunken-light': groupSourceHoverData.hoverValue,
            'first:moe-shadow-none':
              !expanded.value &&
              isFirstGroup &&
              (lodgingCalendarConfig.lodgingTypeList.length > 1 || lodgingCalendarConfig.lodgingTypeList.length === 0),
            'moe-h-[35.5px] moe-pb-[0.5px]':
              (expanded.value && isFirstGroup) || lodgingCalendarConfig.lodgingTypeList.length === 1,
            'moe-h-[59.5px] moe-pb-[0.5px]': !expanded.value && isFirstGroup,
          },
        )}
        onClick={() => {
          handleCollapse();
          handleClickExpand();
        }}
        onMouseEnter={() => {
          setGroupSourceDataMap({
            [groupValue]: {
              hoverValue: true,
            },
          });
        }}
        onMouseLeave={() => {
          setGroupSourceDataMap({
            [groupValue]: {
              hoverValue: false,
            },
          });
        }}
      >
        <Heading size="6" className={cn('moe-flex moe-items-center moe-gap-[8px] moe-text-secondary moe-flex-1')}>
          <MinorChevronDownOutlined
            className={cn('-moe-rotate-90', {
              'moe-transform moe-rotate-0': expanded.value,
            })}
          />
          <Tooltip backgroundTheme="light" side="top" align="start" content={toolTipContent}>
            <span className={!expanded.value ? 'moe-w-[66px] moe-line-clamp-2' : undefined}>
              {getLodgingTypeNameWithoutSorting(groupValue)}
            </span>
          </Tooltip>
          {!expanded.value ? (
            lodgingTypes.isDefaultArea ? (
              <LodgingDefaultAreaSummary view={view} lodgingTypes={lodgingTypes} />
            ) : (
              <LodgingSummary view={view} lodgingTypes={lodgingTypes} />
            )
          ) : null}
        </Heading>
      </div>
    </div>
  );
});

ResourceGroupLaneContent.displayName = 'ResourceGroupLaneContent';

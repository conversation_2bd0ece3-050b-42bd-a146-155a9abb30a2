import interactionPlugin from '@fullcalendar/interaction';
import { type CalendarOptions } from '@fullcalendar/react';
import resourceDayGridPlugin from '@fullcalendar/resource-daygrid';
import resourceTimelinePlugin from '@fullcalendar/resource-timeline';
import timeGridPlugin from '@fullcalendar/timegrid';
import { type NonUndefined } from 'utility-types';
import { type EnumValues, createEnum } from '../../../../store/utils/createEnum';

export type LodgingCalendarViewsType = EnumValues<typeof LodgingCalendarViewsMap>;

export const LodgingCalendarHeight = {
  Empty: '76px',
  Normal: '100%',
};

// 源码 compare 逻辑 "flexibleCompare" node_modules/.pnpm/@fullcalendar+common@5.9.0/node_modules/@fullcalendar/common/main.js
// function flexibleCompare(a, b) {
//   if (!a && !b) {
//       return 0;
//   }
//   if (b == null) {
//       return -1;
//   }
//   if (a == null) {
//       return 1;
//   }
//   if (typeof a === 'string' || typeof b === 'string') {
//       return String(a).localeCompare(String(b));
//   }
//   return a - b;
// }
export const RESOURCE_TYPE_FILED = 'lodgingTypeName';

export const LAYOUT_PADDING = 20;

/** 源码 nodes 插入逻辑 "ensureGroupNodes" node_modules/.pnpm/@fullcalendar+resource-common@5.9.0/node_modules/@fullcalendar/resource-common/main.js
 * resourceGroupField: "department",
 * resourceOrder: "department" // 升序：A、B、C...
 * // 或者
 * resourceOrder: "-department" // 降序：Z、Y、X...
 *
 * resourceGroupField: "department",
 * resourceOrder: "priority" // 不影响组排序，只影响组内资源排序
 */
export const LodgingCalendarViewsMap = createEnum({
  Week: [
    'week',
    {
      type: 'resourceTimelineDay',
      duration: { weeks: 1 },
      buttonText: 'Week',
      resourceGroupField: RESOURCE_TYPE_FILED,
    },
  ],
  Fortnight: [
    'fortnight',
    {
      type: 'resourceTimelineDay',
      duration: { weeks: 2 },
      buttonText: '2 Weeks',
      resourceGroupField: RESOURCE_TYPE_FILED,
    },
  ],
});

export const lodgingFullCalendarOptionViews = LodgingCalendarViewsMap.values.reduce<
  NonUndefined<CalendarOptions['views']>
>((acc, cur) => {
  acc[cur] = LodgingCalendarViewsMap.mapLabels[cur];
  return acc;
}, {});

export const LodgingCalendarDefaultProps: CalendarOptions = {
  schedulerLicenseKey: '**********-fcs-**********',
  plugins: [resourceDayGridPlugin, resourceTimelinePlugin, timeGridPlugin, interactionPlugin],

  slotLabelInterval: '24:00:00',
  slotLabelFormat: [{ day: 'numeric', weekday: 'short' }],
  slotDuration: '24:00:00',
  snapDuration: '1:00:00',

  headerToolbar: false,
  initialView: LodgingCalendarViewsMap.Week,
  views: lodgingFullCalendarOptionViews,
  resourceOrder: `orderIndex,${RESOURCE_TYPE_FILED}`,
  resourceAreaWidth: 110,
  resourceAreaHeaderContent: () => null,

  editable: true,
  eventResizableFromStart: true,
};

import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { LAYOUT_PADDING } from './LodgingCalendar.config';

export const useHandleScrollHeight = ({
  boxRef,
  setScrollHeight,
}: {
  boxRef: React.RefObject<HTMLDivElement>;
  setScrollHeight: (height: number) => void;
}) => {
  const getTableElements = useLatestCallback(() => {
    if (!boxRef.current) {
      return null;
    }

    /**
     * outer table
     */
    const outerTable = boxRef.current.querySelector('table');

    /**
     * table header
     */
    const tableHeaderContainer = boxRef.current?.querySelector('.fc-timeline-header');
    const tableHeader = tableHeaderContainer?.querySelector('table .fc-scrollgrid-sync-table');

    /**
     * table body
     */
    const tableBodyContainer = boxRef.current?.querySelector('.fc-timeline-body');
    const tableBody = tableBodyContainer?.querySelector('table .fc-scrollgrid-sync-table');
    return {
      outerTable,
      tableBody,
      tableHeader,
    };
  });

  const handleScrollHeight = useLatestCallback(() => {
    const { outerTable, tableBody, tableHeader } = getTableElements() || {};

    // 表格内容区高度 + 表格表头区高度，这个是最准的
    const tableHeight =
      (tableBody?.getBoundingClientRect?.()?.height ?? 0) + (tableHeader?.getBoundingClientRect?.()?.height ?? 0);

    // 当 lodging calendar 切换 filter 的时候，scrollHeight 会维持之前的高度，所以只能作为 fallback
    const fallbackTableHeight = outerTable?.scrollHeight ?? 0;

    // 加上 padding 就是最终的高度
    const finalHeight = (tableHeight ?? fallbackTableHeight ?? 0) + LAYOUT_PADDING;

    setScrollHeight(finalHeight);
  });

  return handleScrollHeight;
};

/*
 * @since 2020-09-02 09:40:32
 * <AUTHOR> <<EMAIL>>
 */

import { Text } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import React, { memo, useEffect, useMemo } from 'react';
import { NavLink, useHistory } from 'react-router-dom';
import IconIconDeleteSvg from '../../../../assets/icon/icon-delete.svg';
import { Avatar } from '../../../../components/Avatar/Avatar';
import { BrandedAppTag } from '../../../../components/BrandedApp/BrandedAppTag';
import { NewBookingButton } from '../../../../components/Button/Button.style';
import { WithPermission } from '../../../../components/GuardRoute/WithPermission';
import { ImgIcon } from '../../../../components/Icon/Icon';
import { MembershipIdentify } from '../../../../components/MembershipIdentify/MembershipIdentify';
import { useHasMembershipPermission } from '../../../../components/MessageSendBox/components/hooks/useHasMembershipPermission';
import { modalApi } from '../../../../components/Modal/Modal';
import { toastApi } from '../../../../components/Toast/Toast';
import { Upload } from '../../../../components/Upload/Upload';
import { UploadPhotoView } from '../../../../components/Upload/UploadPhotoView';
import {
  PATH_CUSTOMER_AGREEMENTS,
  PATH_CUSTOMER_BOOKINGS,
  PATH_CUSTOMER_CREDIT_CARDS,
  PATH_CUSTOMER_DETAIL,
  PATH_CUSTOMER_LIST,
  PATH_CUSTOMER_MEMBERSHIP_SUBSCRIPTION,
  PATH_CUSTOMER_OVERVIEW,
  PATH_CUSTOMER_PACKAGE,
  PATH_CUSTOMER_PAYMENT_HISTORY,
  PATH_CUSTOMER_PETS,
  PATH_CUSTOMER_REVIEWS,
  PATH_GROOMING_CALENDAR,
  useCustomerId,
} from '../../../../router/paths';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { QuickAddSource, ViewType } from '../../../../store/calendarLatest/calendar.types';
import { selectBDFeatureEnable } from '../../../../store/company/company.selectors';
import { removeCustomer, updateCustomer } from '../../../../store/customer/customer.actions';
import { type CustomerRecord, customerMapBox } from '../../../../store/customer/customer.boxes';
import { getCustomerActiveSubscriptionList } from '../../../../store/membership/membership.actions';
import { useEnableFeature } from '../../../../store/metadata/featureEnable.hooks';
import { META_DATA_KEY_LIST } from '../../../../store/metadata/metadata.config';
import { getStripeCustomerPaymentMethodList } from '../../../../store/stripe/actions/public/stripe.actions';
import { type RecordProps } from '../../../../store/utils/RecordMap';
import { isNormal } from '../../../../store/utils/identifier';
import { truly } from '../../../../store/utils/utils';
import { ByteUnit } from '../../../../utils/common';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { MenuList } from '../../../../utils/menu';
import { GroomingReportActionName } from '../../../../utils/reportData/grooming/grooming';
import { apptReporter } from '../../../../utils/reportData/reporter/apptReporter';
import { reportData } from '../../../../utils/tracker';
import { useCreateApptDrawer, useGetStartTime } from '../../../Appt/hooks/useCreateApptDrawer';
import { stripeAvailableCountryOption } from '../../../CardProcessing/stripe/StripeSetup';
import { CofStatus } from '../../../Payment/components/CofStatus';
import { useInvoiceReinvent } from '../../../PaymentFlow/hooks/useInvoiceReinvent';
import { ClientInfoPanelView, ClientTagView, MenuListView } from './ClientInfoPanel.style';

const customerPageList = [
  { title: 'Overview', path: PATH_CUSTOMER_OVERVIEW },
  { title: 'Client details', path: PATH_CUSTOMER_DETAIL },
  { title: 'Pets', path: PATH_CUSTOMER_PETS },
  { title: 'Bookings', newInvoiceGrayFlowTitle: 'Appointments', path: PATH_CUSTOMER_BOOKINGS },
  { title: 'Agreements', path: PATH_CUSTOMER_AGREEMENTS },
  { title: 'Reviews', path: PATH_CUSTOMER_REVIEWS },
  { title: 'Payment', path: PATH_CUSTOMER_CREDIT_CARDS },
  { title: 'Payment history', path: PATH_CUSTOMER_PAYMENT_HISTORY },
  { title: 'Packages', path: PATH_CUSTOMER_PACKAGE },
  { title: 'Memberships', path: PATH_CUSTOMER_MEMBERSHIP_SUBSCRIPTION },
].filter(truly);

export const customerPages = new MenuList(customerPageList);

export interface ClientInfoPanelProps {}

export const ClientInfoPanel = memo<ClientInfoPanelProps>(() => {
  const customerId = useCustomerId();
  const [business, customer, permission, isBD] = useSelector(
    selectCurrentBusiness,
    customerMapBox.mustGetItem(customerId),
    selectCurrentPermissions,
    selectBDFeatureEnable,
  );
  const dispatch = useDispatch();
  const history = useHistory();
  const { isEnableToNewFlow } = useInvoiceReinvent();
  const { enable: packageEnabled } = useEnableFeature(META_DATA_KEY_LIST.PackageEnabled);
  const hasMembershipPermission = useHasMembershipPermission('accessClientMemberships');
  const { followCalendar } = useGetStartTime();
  const openCreateDrawer = useCreateApptDrawer();

  const go2QuickAddNew = useLatestCallback(() => {
    reportData(GroomingReportActionName.ClientInfoNewBookClick);
    // 如果不是 BD 用户，才跳去 grooming calendar
    if (!isBD) {
      history.push(
        PATH_GROOMING_CALENDAR.stated({
          backCalendarView: ViewType.DAY,
        }),
      );
    }
    apptReporter.setCreateApptDrawerOpenTime();
    openCreateDrawer({
      params: {
        source: QuickAddSource.ClientProfile,
        clientId: customerId,
      },
      preset: {
        appointmentStart: followCalendar(),
      },
    });
  });

  useEffect(() => {
    isNormal(customerId) &&
      stripeAvailableCountryOption(business.country) &&
      dispatch(getStripeCustomerPaymentMethodList(customerId));
    isNormal(customerId) && dispatch(getCustomerActiveSubscriptionList(customerId + ''));
  }, [customerId]);

  const pages = useMemo(() => {
    const showCustomerPackage = packageEnabled && permission.has('accessClientPackageList');
    let filterPages = customerPageList;
    if (!showCustomerPackage) {
      filterPages = filterPages.filter((page) => page.path !== PATH_CUSTOMER_PACKAGE);
    }
    if (!hasMembershipPermission) {
      filterPages = filterPages.filter((page) => page.path !== PATH_CUSTOMER_MEMBERSHIP_SUBSCRIPTION);
    }
    return new MenuList(filterPages);
  }, [packageEnabled, hasMembershipPermission]);

  const handleDelete = () => {
    modalApi.confirm({
      content:
        'Delete this client will delete related pets and all future appointments. This action cannot be reversed. Are you sure to continue?',
      okText: 'Yes',
      cancelText: 'No',
      okType: 'danger',
      onOk: () => {
        dispatch(removeCustomer(customerId)).then(() => {
          toastApi.success('Client has been deleted successfully!');
          history.push(PATH_CUSTOMER_LIST.build());
        });
      },
    });
  };
  const handleChange = useSerialCallback(
    async <K extends keyof RecordProps<CustomerRecord>>(field: K, value: RecordProps<CustomerRecord>[K]) => {
      await dispatch(updateCustomer({ customerId, [field]: value }));
    },
  );

  return (
    <ClientInfoPanelView>
      <div className="card base-info">
        <Upload
          maxSize={5 * ByteUnit.MB}
          accept="image/*"
          multiple={false}
          onChange={(value) => handleChange('avatarPath', value)}
          value={customer.avatarPath}
        >
          {(state) => (
            <UploadPhotoView
              {...state}
              size={64}
              defaultElem={<Avatar src={customer.avatarPath} size="64px" info={customer} />}
            />
          )}
        </Upload>
        <div className="tag-list">
          {customer.isProspectCustomer ? (
            <ClientTagView className="prospect" />
          ) : (
            customer.isNewCustomer && <ClientTagView className="new" />
          )}
          {customer.inactive > 0 && <ClientTagView className="inactive" />}
          {customer.inactive < 1 && <ClientTagView className="active" />}
          {customer.isBlockOnlineBooking > 0 && <ClientTagView className="blocked ob" />}
          {customer.isBlockMessage > 0 && <ClientTagView className="blocked message" />}
        </div>
        <div className="moe-flex moe-flex-col moe-gap-[4px] moe-items-center moe-mb-[16px]">
          <div className="client-name moe-flex" style={{ color: customer.clientColor }}>
            <Text
              variant="regular"
              className={'moe-max-w-[124px] moe-font-[700] moe-tracking-[-0.32px]'}
              ellipsis={{
                tooltip: {
                  align: 'center',
                  side: 'top',
                },
              }}
            >
              {customer.fullName()}
            </Text>

            <MembershipIdentify className="moe-ml-[8px]" customerId={String(customerId)} />
            <CofStatus customerId={customerId} source="client-info" iconClassName="!moe-ml-[8px] !moe-flex-none" />
            {customer.hasPetParentAppAccount && <BrandedAppTag iconClassName="!moe-ml-[8px]" />}
          </div>
          <div className="create-time">Started on {business.formatDate(customer.createTime * T_SECOND)}</div>
        </div>
        <WithPermission permissions={['createAppointment']}>
          <NewBookingButton onClick={go2QuickAddNew}>+ New booking</NewBookingButton>
        </WithPermission>
        <WithPermission permissions={['deleteClient', 'viewClientList']}>
          <div className="btn-delete" onClick={handleDelete}>
            <ImgIcon src={IconIconDeleteSvg} width={16} />
          </div>
        </WithPermission>
      </div>
      <MenuListView className="card">
        <div className="items">
          {pages.map((item) => (
            <NavLink to={item.path.build({ customerId })} key={item.path.path}>
              {isEnableToNewFlow && item.newInvoiceGrayFlowTitle ? item.newInvoiceGrayFlowTitle : item.title}
            </NavLink>
          ))}
        </div>
      </MenuListView>
    </ClientInfoPanelView>
  );
});

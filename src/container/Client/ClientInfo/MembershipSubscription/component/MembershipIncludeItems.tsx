import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { type Money } from '@moego/api-web/google/type/money';
import {
  type GetRedeemHistoryResult,
  type ListAllPerkCycleResult,
} from '@moego/api-web/moego/api/membership/v1/membership_api';
import { useSerialCallback } from '@moego/tools';
import { Button, type ColumnDef, Heading, LegacySelect as Select, Table, Tooltip } from '@moego/ui';
import { useMemoizedFn } from 'ahooks';
import { useDispatch, useSelector } from 'amos';
import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { usePermissionCheck } from '../../../../../components/GuardRoute/WithPermission';
import { useCustomerId } from '../../../../../router/paths';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { getPerkUsageDetail, listAllPerkCycle } from '../../../../../store/membership/membership.actions';
import { GrowthBookFeatureList } from '../../../../../utils/growthBook/growthBook.config';
import { useGetCredit } from '../../CreditCards/hooks/useGetCredit';
import { type SubscriptionAndMembershipProps } from '../types';
import {
  BenefitTransferCreditModal,
  type BenefitTransferCreditModalProps,
  type BenefitTransferCreditModalRefs,
} from './BenefitTransferCreditModal';
import { RedeemHistoryModal } from './MembershipRedeemHistory';
import { memoForwardRef } from '../../../../../utils/react';

interface SubscriptionAndMembershipIncludeProps extends Omit<SubscriptionAndMembershipProps, 'membership'> {
  membershipId: string;
}

interface MembershipIncludeItemTableData {
  serviceName: string;
  packageId: string;
  quantity: number;
  Remaining: number;
  unlimited: boolean;
  expirationDate: string | undefined;
  serviceList: {
    serviceId: string;
    serviceName: string;
    servicePrice: Money;
  }[];
}

export interface MembershipIncludeItemsRef {
  fetchParkCycleData: () => void;
}

export const MembershipIncludeItems = memoForwardRef(
  (props: SubscriptionAndMembershipIncludeProps, ref: React.Ref<MembershipIncludeItemsRef>) => {
    const { membershipId, subscription } = props;
    const [selectedService, setSelectedService] = useState<BenefitTransferCreditModalProps['serviceList']>([]);
    const [maxQuantity, setMaxQuantity] = useState<number>(0);
    const transferCreditsRef = useRef<BenefitTransferCreditModalRefs>(null);
    const [business] = useSelector(selectCurrentBusiness);
    const [perkCycles, setPerkCycles] = useState<ListAllPerkCycleResult['perkCycleItem'] | null>(null);
    const [packageServices, setPackageServices] = useState<GetRedeemHistoryResult['includedBenefits'] | null>(null);
    const dispatch = useDispatch();
    const customerId = useCustomerId();
    const [currentPerkCycle, setCurrentPerkCycle] = useState<string>();
    const [currentPackageId, setCurrentPackageId] = useState('');
    const isCreditEnabled = useFeatureIsOn(GrowthBookFeatureList.Credit);
    const hasAccessStoreCredit = usePermissionCheck({ permissions: ['accessStoreCredit'] });

    const fetchParkCycleData = useMemoizedFn(async () => {
      if (membershipId) {
        const data = await dispatch(
          listAllPerkCycle({
            customerId: customerId + '',
            membershipId,
          }),
        );
        setPerkCycles(data.perkCycleItem ?? []);
        if (data.perkCycleItem?.length > 0 && data.perkCycleItem[0].validityStartTime) {
          setCurrentPerkCycle(data.perkCycleItem[0].validityStartTime);
          fetchPerkUsageDetail(data.perkCycleItem[0].validityStartTime);
        }
      }
    });

    useImperativeHandle(ref, () => ({
      fetchParkCycleData: () => {
        fetchParkCycleData();
      },
    }));

    const fetchPerkUsageDetail = useSerialCallback(async (validityStartTime: string) => {
      if (membershipId) {
        const data = await dispatch(
          getPerkUsageDetail({
            customerId: customerId + '',
            membershipId,
            filter: {
              validityStartTime: validityStartTime,
            },
          }),
        );
        setPackageServices(data.includedBenefits || []);
      }
    });

    const onConfirm = useMemoizedFn(() => {
      fetchPerkUsageDetail(currentPerkCycle ?? '');
    });

    useEffect(() => {
      if (!membershipId) {
        return;
      }
      fetchParkCycleData();
    }, [membershipId]);

    const data = useMemo(() => {
      return (
        packageServices?.map((item) => ({
          serviceName: item.itemDetails?.map((item) => item.itemName).join(', '),
          packageId: item.id,
          quantity: item.totalQuantity,
          Remaining: item.remainingQuantity,
          unlimited: !item.isLimited,
          expirationDate: item.redeemTime,
          serviceList:
            item.itemDetails?.map?.((item) => ({
              serviceId: item.itemId,
              serviceName: item.itemName,
              servicePrice: item.price,
            })) || [],
        })) || []
      );
    }, [packageServices]);

    useGetCredit(customerId);

    const transferCredits = (
      packageId: string,
      maxQuantity: number,
      serviceList: {
        serviceId: string;
        serviceName: string;
        servicePrice: Money;
      }[],
    ) => {
      setCurrentPackageId(packageId);
      setSelectedService(serviceList);
      setMaxQuantity(maxQuantity);
      transferCreditsRef.current?.open();
    };
    const columns = useMemo(() => {
      const base: ColumnDef<MembershipIncludeItemTableData>[] = [
        {
          header: 'Service Name',
          accessorKey: 'serviceName',
          size: 200,
          cell: (props) => <div className="!moe-break-all ">{props.getValue()}</div>,
        },
        {
          header: 'Quantity',
          accessorKey: 'quantity',
          size: 200,
          cell: (props) => (
            <div className="!moe-break-all ">{props.row.original.unlimited ? 'Unlimited' : props.getValue()}</div>
          ),
        },
        {
          header: 'Remaining',
          accessorKey: 'Remaining',
          size: 200,
          cell: (props) => (
            <div className="!moe-break-all ">{props.row.original.unlimited ? 'Unlimited' : props.getValue()}</div>
          ),
        },
      ];

      if (isCreditEnabled) {
        base.push({
          header: 'Action',
          accessorKey: 'action',
          size: 138,
          cell: (props) => (
            <Tooltip
              isDisabled={hasAccessStoreCredit}
              content="You don't have permission to adjust store credit. Please contact your account manager for access."
              side="top"
            >
              <Button
                variant="tertiary-legacy"
                className="moe-pl-0 enabled:hover:moe-bg-[#********]"
                isDisabled={props.row.original.unlimited || props.row.original.Remaining <= 0 || !hasAccessStoreCredit}
                onPress={() => {
                  transferCredits(
                    props.row.original.packageId,
                    props.row.original.Remaining,
                    props.row.original.serviceList,
                  );
                }}
              >
                Transfer to store credit
              </Button>
            </Tooltip>
          ),
        });
      }
      return base;
    }, [isCreditEnabled]);

    if (!perkCycles || perkCycles.length === 0) {
      return null;
    }

    return (
      <div className="moe-mt-[8px] moe-pl-[16px]">
        <div className="moe-flex moe-justify-between moe-mb-m moe-items-center">
          <div className="moe-flex moe-items-center">
            <Heading size="5">Subscription history</Heading>
            <Select
              className="moe-ml-4"
              options={
                perkCycles?.map((item) => ({
                  label: `${item.validityStartTime ? 'Purchased on ' + business.formatDate(item.validityStartTime) : ''}`,
                  value: item.validityStartTime || '',
                })) ?? []
              }
              isSearchable={false}
              value={currentPerkCycle}
              onChange={(value) => {
                setCurrentPerkCycle(value);
                value && fetchPerkUsageDetail(value);
              }}
            />
          </div>
          {!!membershipId && <RedeemHistoryModal subscription={subscription} membershipId={membershipId} />}
        </div>
        <Table
          className="moe-pr-[16px]"
          data={data}
          getRowId={(row) => currentPerkCycle + row.packageId}
          isLoading={fetchPerkUsageDetail.isBusy()}
          columns={columns}
        />
        <BenefitTransferCreditModal
          ref={transferCreditsRef}
          validityStartTime={currentPerkCycle}
          serviceList={selectedService}
          onConfirm={onConfirm}
          maxQuantity={maxQuantity}
          customerId={customerId}
          currentPackageId={currentPackageId}
        />
      </div>
    );
  },
);

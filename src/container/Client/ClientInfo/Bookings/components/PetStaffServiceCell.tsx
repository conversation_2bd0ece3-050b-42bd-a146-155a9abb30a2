import React, { memo } from 'react';
import { Condition } from '../../../../../components/Condition';

interface PetStaffServiceCellProps {
  staffName?: string;
  serviceName: string;
  petName: string;
}

export const PetStaffServiceCell = memo((props: PetStaffServiceCellProps) => {
  const { staffName, serviceName, petName } = props;

  return (
    <div className="pet-item">
      <p>
        <span className="name" style={{ marginRight: '12px' }}>
          {petName}
        </span>
        <span className="service" style={{ color: '#979797' }}>
          {serviceName}
        </span>
        <Condition if={staffName}>
          <span className="staff-name" style={{ color: '#979797' }}>
            &nbsp;(by {staffName})
          </span>
        </Condition>
      </p>
    </div>
  );
});

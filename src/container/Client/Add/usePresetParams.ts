import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { getDefaultService } from '../../../components/ServiceApplicablePicker/utils/getDefaultService';
import { type CustomerAddState } from '../../../router/paths';
import { QuickAddSource } from '../../../store/calendarLatest/calendar.types';
import { serviceMapBox } from '../../../store/service/service.boxes';
import { isNormal } from '../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';
import { useCalcGroomingOnlySchedule } from '../../Appt/hooks/useCalcGroomingOnlySchedule';
import { useCreateApptDrawer } from '../../Appt/hooks/useCreateApptDrawer';
import { useResolveStaff } from '../../Appt/hooks/useResolveStaff';
import { CreateApptId } from '../../Appt/store/appt.types';

interface ResolveParamsProps {
  petIdList: number[];
  customerAddRouteState?: CustomerAddState;
}

export const usePresetParams = () => {
  const [serviceMap] = useSelector(serviceMapBox);
  const calcGroomingOnlySchedule = useCalcGroomingOnlySchedule();
  const { resolveStaff, getAvailableStaff } = useResolveStaff();
  const openCreateDrawer = useCreateApptDrawer();

  // 如果有值，就用传入的值，否则用当前时间，date time目前的接口返回是一样的
  const resolveSchedule = (customerAddRouteState?: CustomerAddState) => {
    const now = dayjs();
    const { date, time } = customerAddRouteState?.createTicketInfo || {};
    const isValid = (d?: number) => d && dayjs(d).isSameOrAfter(now, 'date');

    return {
      date: isValid(date) ? dayjs(date).format(DATE_FORMAT_EXCHANGE) : now.format(DATE_FORMAT_EXCHANGE),
      time: isValid(time) ? dayjs(time).getMinutes() : now.getMinutes(),
    };
  };

  // 转换为quick add 需要的结构，这里关键是时间和staff
  const resolvePetAndService = (props: ResolveParamsProps & { staffId: number }) => {
    const { petIdList, customerAddRouteState, staffId } = props;
    const { date, time } = resolveSchedule(customerAddRouteState);
    const petAndServices = customerAddRouteState?.createTicketInfo?.presetInfo?.petAndServices;

    if (petAndServices?.length && petIdList?.length) {
      return petIdList
        ?.map((petId, index) => {
          const { serviceItem } = petAndServices[index] || {};
          return {
            petId,
            serviceList: serviceItem?.map((service) => {
              const { serviceId } = service;
              // 这里我们应该故意不用快照，直接用最新的数据
              const { type, name, duration, price } = serviceMap.mustGetItem(serviceId);

              return getDefaultService({
                serviceType: type,
                serviceId,
                staffId,
                serviceName: name,
                startDate: date ? dayjs(date).format(DATE_FORMAT_EXCHANGE) : undefined,
                startTime: time,
                serviceTime: duration,
                servicePrice: price,
              });
            }),
          };
        })
        .filter((item) => isNormal(item.petId) && item.serviceList?.length);
    }
    return undefined;
  };

  // 如果是as，quick add 参数不需要petServices，通过额外的setPresetInQuickAdd方法设置值
  // 如果不是as，quick add 参数需要petServices
  const resolveParams = async (props: ResolveParamsProps) => {
    const { petIdList, customerAddRouteState } = props;
    const staffId = resolveStaff(customerAddRouteState?.createTicketInfo?.staffId, await getAvailableStaff());
    const petServices = resolvePetAndService({ petIdList, customerAddRouteState, staffId });
    const { date, time } = resolveSchedule(customerAddRouteState);

    return {
      time,
      date,
      appointmentDate: dayjs(date).setMinutes(time),
      staffId,
      petServices,
    };
  };

  const setPresetInQuickAdd = async (props: ResolveParamsProps & { clientId?: number }) => {
    const { clientId } = props;
    const { petServices, appointmentDate, staffId } = await resolveParams(props);

    openCreateDrawer({
      params: {
        clientId,
        source: QuickAddSource.ClientProfile,
      },
      preset: {
        appointmentStart: appointmentDate,
        staffId,
      },
    });

    if (petServices && isNormal(staffId)) {
      for (const item of petServices) {
        await calcGroomingOnlySchedule({
          petIdsServiceList: [{ petId: item.petId, serviceList: item.serviceList }],
          allPetsStartAtSameTime: false,
          customerId: String(clientId),
          appointmentId: CreateApptId,
        });
      }
    }
  };

  return { resolveParams, setPresetInQuickAdd };
};

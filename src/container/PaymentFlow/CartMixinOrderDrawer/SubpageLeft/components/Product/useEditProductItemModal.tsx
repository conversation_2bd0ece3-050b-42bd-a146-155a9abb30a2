import { useSerialCallback } from '@moego/finance-utils';
import { useDispatch } from 'amos';
import { type A0 } from 'monofile-utilities/lib/types';
import React from 'react';
import { useFloatableHost } from '../../../../../../utils/hooks/useFloatableHost';
import {
  deleteProduct,
  updateProduct,
} from '../../../SubpageRight/AddMoreItemSubpage/modules/AddProducts/store/orderProducts.actions';
import { EditProductItemModal, type EditProductItemProps } from './EditProductItemModal';

export function useEditProductItemModal() {
  const { mountModal } = useFloatableHost();
  const dispatch = useDispatch();

  const onUpdated = useSerialCallback(async (payload: A0<EditProductItemProps['onUpdated']>) => {
    dispatch(updateProduct(payload));
  });

  const onRemove = useSerialCallback(async (id: number) => {
    dispatch(deleteProduct(id));
  });

  return async (props: EditProductItemProps) => {
    const { value } = props;
    const { promise, closeFloatable: closeModal } = mountModal(
      <EditProductItemModal
        {...props}
        onClose={() => {
          closeModal?.();
        }}
        onUpdated={async (payload) => {
          await onUpdated(payload);
          closeModal?.();
        }}
        onRemove={async () => {
          await onRemove(value.id);
          closeModal?.();
        }}
      />,
    );
    return promise;
  };
}

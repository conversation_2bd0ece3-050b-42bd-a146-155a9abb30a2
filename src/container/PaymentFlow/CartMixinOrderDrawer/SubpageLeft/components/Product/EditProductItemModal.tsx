import { type A0, isNormal, useSerialCallback } from '@moego/finance-utils';
import { OrderItemType, RE_INPUT_AMOUNT } from '@moego/finance-web-kit';
import { Condition, Form, Input, Modal, LegacySelect as Select, Text, useForm, useWatch } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect, useMemo } from 'react';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { getTaxList } from '../../../../../../store/business/tax.actions';
import { taxMapBox } from '../../../../../../store/business/tax.boxes';
import { selectBusinessTaxes } from '../../../../../../store/business/tax.selectors';
import { staffMapBox } from '../../../../../../store/staff/staff.boxes';
import { selectBusinessStaffs } from '../../../../../../store/staff/staff.selectors';
import { PaymentActionName } from '../../../../../../utils/reportData/payment';
import { useInvoiceReinventReport } from '../../../../hooks/useInvoiceReinvent.report';
import { ProductItemCard } from '../../../SubpageRight/AddMoreItemSubpage/modules/AddProducts/components/ProductItemCard';
import {
  productItemInfoMapBox,
  type TProductSelected,
} from '../../../SubpageRight/AddMoreItemSubpage/modules/AddProducts/store/orderProducts.boxes';
import { LayoutDivider } from '../../../components/Layout/LayoutDivider';

export interface EditProductItemProps {
  value: TProductSelected;
  onClose?: () => void;
  onUpdated?: (input: Omit<TProductSelected, 'externalUuid' | 'name'>) => Promise<void>;
  onRemove?: () => Promise<void>;
}

export const EditProductItemModal = memo((props: EditProductItemProps) => {
  const { value, onClose, onUpdated, onRemove } = props;
  const dispatch = useDispatch();
  const [business, businessTaxes, taxMap, staffList, staffMap, product] = useSelector(
    selectCurrentBusiness,
    selectBusinessTaxes(),
    taxMapBox,
    selectBusinessStaffs,
    staffMapBox,
    productItemInfoMapBox.mustGetItem(value.id),
  );
  const reportPaymentData = useInvoiceReinventReport();

  const form = useForm<Omit<TProductSelected, 'id'>>({ mode: 'all', defaultValues: value });

  const taxOptions = useMemo(() => {
    return businessTaxes.toArray().map((id) => {
      const tax = taxMap.mustGetItem(id);
      return {
        label: `${tax.taxName} (${tax.taxRate}%)`,
        value: id,
      };
    });
  }, [businessTaxes, taxMap]);

  const staffOptions = useMemo(() => {
    return staffList.toArray().map((staffId) => {
      const staff = staffMap.mustGetItem(staffId);
      return {
        label: staff.fullName(),
        value: staffId + '',
      };
    });
  }, [staffMap, staffList]);

  useEffect(() => {
    if (isNormal(business.id)) {
      dispatch(getTaxList());
    }
  }, [business.id]);

  const handleConfirm = useSerialCallback(async () => {
    const isValid = await form.trigger();
    if (!isValid) {
      return;
    }

    const values = form.getValues();
    const editParams: A0<EditProductItemProps['onUpdated']> = {
      id: product.id,
      staffId: isNormal(values.staffId) ? values.staffId : undefined,
      price: values.price,
      quantity: values.quantity,
      taxId: values.taxId ? values.taxId : undefined,
    };
    await onUpdated?.(editParams);
    reportPaymentData(PaymentActionName.EditOrderItemConfirm, {
      orderItemType: OrderItemType.Product,
      isPriceChanged: editParams.price !== value.price,
      isStaffChanged: editParams.staffId !== value.staffId,
      isQuantityChange: editParams.quantity !== value.quantity,
    });
  });

  const handleRemove = useSerialCallback(async () => {
    onRemove?.();
  });

  const loading = handleConfirm.isBusy();
  const [quantity, staffId] = useWatch({
    control: form.control,
    name: ['quantity', 'staffId'],
  });
  const stockLimit = (product?.stock || Number.MAX_SAFE_INTEGER) < quantity;
  const isDirty = form.formState.isDirty;
  const isValid = form.formState.isValid;
  return (
    <Modal
      isOpen
      title={`Edit product`}
      tertiaryText={'Remove'}
      confirmText={'Update'}
      size="s"
      onClose={onClose}
      onConfirm={handleConfirm}
      onCancel={onClose}
      autoCloseOnConfirm={false}
      showTertiaryButton
      onTertiary={handleRemove}
      tertiaryButtonProps={{
        isLoading: handleRemove.isBusy(),
        isDisabled: handleRemove.isBusy(),
      }}
      confirmButtonProps={{
        isLoading: loading,
        isDisabled: !isDirty || !isValid || loading,
      }}
    >
      <ProductItemCard
        className="moe-p-none"
        selectable={false}
        classNames={{ name: '!moe-h4 moe-mb-xxs' }}
        productId={product.id}
        productInfo={product}
      />
      <LayoutDivider className="moe-mt-s moe-mb-m" />
      <div className={'moe-flex moe-justify-between moe-gap-s'}>
        <Form form={form} footer={null}>
          <div className={'moe-flex moe-justify-between moe-gap-s'}>
            <Form.Item
              name="price"
              rules={{
                required: true,
                validate: (value) => {
                  if (!RE_INPUT_AMOUNT.test(value)) {
                    return 'Invalid price';
                  }
                  return true;
                },
              }}
            >
              <Input.Number
                className={'moe-w-1/2'}
                prefix={<span className="!moe-text-[#333]">{business.printCurrency()}</span>}
                isRequired
                label="Price"
                placeholder=""
              />
            </Form.Item>
            <Form.Item name="quantity" rules={{ required: true }}>
              <div className={'moe-flex moe-flex-col moe-flex-grow moe-w-1/2'}>
                <Input.Number
                  minValue={1}
                  step={1}
                  isRequired
                  defaultValue={value.quantity}
                  label="Quantity"
                  placeholder=""
                  value={quantity}
                  onChange={(value) => {
                    form.setValue('quantity', value?.valueOf() || 0, { shouldDirty: true });
                  }}
                />
                <Condition if={stockLimit}>
                  <Text variant={'small'} className={'moe-text-warning moe-mt-xxs'}>
                    The quantity is more than available.
                  </Text>
                </Condition>
              </div>
            </Form.Item>
          </div>
          <Form.Item name="taxId">
            <Select label={'Tax'} className="moe-w-full" options={taxOptions}></Select>
          </Form.Item>
          <Form.Item name="staffId">
            <>
              <Select
                label={'Sold by'}
                placeholder="Select staff"
                className="moe-w-full"
                options={staffOptions}
                value={staffId}
                onChange={(value) => {
                  form.setValue('staffId', value, { shouldDirty: true });
                }}
                isClearable
              />
            </>
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
});

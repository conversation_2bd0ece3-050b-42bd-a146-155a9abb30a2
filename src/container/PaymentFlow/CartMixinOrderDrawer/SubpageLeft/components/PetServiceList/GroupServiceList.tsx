import { type PetService } from '@moego/api-web/moego/models/fulfillment/v1/fulfillment_defs';
import React, { memo } from 'react';
import { useGroupedServices } from '../../hooks/useGroupedServices';
import { ServiceList, type ServiceListProps } from './ServiceList';
import { type BaseItemProps } from './type';

export interface GroupServiceListProps extends BaseItemProps, Pick<ServiceListProps, 'orderItems'> {
  petService: PetService;
  appointmentId: string;
}

export const GroupServiceList = memo((props: GroupServiceListProps) => {
  const { petService, appointmentId, orderItems, ...rest } = props;
  const groupedServices = useGroupedServices(petService, appointmentId);
  return (
    <div className="moe-flex moe-flex-col moe-gap-xs">
      {groupedServices.map((list, index) => {
        return <ServiceList orderItems={orderItems} key={index} services={list} {...rest} />;
      })}
    </div>
  );
});

import { <PERSON><PERSON><PERSON> } from '@moego/finance-utils';
import { OrderChargeType } from '@moego/finance-web-kit';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { useAsyncCallback } from '../../../../utils/hooks/useAsyncCallback';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useCartAppt } from '../Cart/useCartAppt';
import { useInitCart } from '../Cart/useInitCart';
import { type Capabilities } from '../CartMixinOrderDrawer.types';
import { ColumnsBridgeEvent } from '../Columns/ColumnsBridge';
import { PromotionType } from '../Promotions/Promotion.types';
import { getAppliedStoreCredit, groupOrderAppliedPromotionsByType } from '../Promotions/Promotion.utils';
import { usePromotion } from '../Promotions/usePromotion';
import { LayoutDivider } from '../components/Layout/LayoutDivider';
import { LayoutSubTotal } from '../components/Layout/LayoutSubTotal';
import { LayoutSubpage } from '../components/Layout/LayoutSubpage';
import { LayoutSubpageScrollBody } from '../components/Layout/LayoutSubpageScrollBody';
import { LayoutRowItem } from '../components/LayoutRowItem/LayoutRowItem';
import { useDrawerScopeContext } from '../context/DrawerScopeContext';
import { type PreviewOrderResult } from '../hooks/Preview.types';
import { useBridgeEventData, useBridgeListener } from '../hooks/use-bridge-hooks';
import { usePreviewSalesOrder } from '../hooks/usePreviewSalesOrder';
import { LeftColumnFooter } from './components/LeftColumnFooter';
import { LeftColumnHeader } from './components/LeftColumnHeader';
import { ProcessingFee } from './components/ProcessingFee/ProcessingFee';
import { DiscountInfo } from './components/Promotion/DiscountInfo';
import { MembershipInfo } from './components/Promotion/MembershipInfo';
import { PackageInfo } from './components/Promotion/PackageInfo';
import { TipsInfo } from './components/SetTips/TipsInfo';
import { StoreCreditInfo } from './components/StoreCredit/StoreCreditInfo';
import { TaxInfo } from './components/TaxInfo/TaxInfo';
import { ERPScope } from './modules/ERPScope';

interface INormalChargeSubpageProps {
  capabilities: Capabilities;
}

export const NormalChargeSubpage = memo<INormalChargeSubpageProps>((props) => {
  const { capabilities } = props;
  const { columnsBridge, customer, sourceId, chargeType } = useDrawerScopeContext();
  const [business, permissions] = useSelector(selectCurrentBusiness, selectCurrentPermissions);
  const { initCart, clearCart } = useCartAppt(sourceId);
  const { fetchAllPromotion, updateCartPromotions, clearCartPromotions } = usePromotion(sourceId);
  const cvFee = useBridgeEventData(columnsBridge, ColumnsBridgeEvent.OnConvenienceFeeChange, 0);
  const afterPreview = useLatestCallback(async (data: PreviewOrderResult) => {
    updateCartPromotions(data.promotionsByTypeMap);
  });

  const { previewOrder, previewedResult, createOrder } = usePreviewSalesOrder({
    afterPreview,
  });

  // TODO(yueyue): initCart 调用多次，这里的事件需要重新整理一下
  useInitCart({
    sourceId,
    columnsBridge,
    initCart,
    beforeInitialPreview: fetchAllPromotion,
    preview: previewOrder,
    createOrder,
    cleanCart: () => {
      clearCart();
      clearCartPromotions();
    },
  });

  const previewedOrder = previewedResult.normalizedOrder;
  const handleUpdateTips = useAsyncCallback(async (tipsAmount: number) => {
    await previewOrder({ tipsAmount: MoeMoney.fromAmount(tipsAmount, business.currencyCode) });
  });

  useBridgeListener(columnsBridge, ColumnsBridgeEvent.OnTipsAmountChange, async (tipsAmount: number) => {
    await handleUpdateTips(tipsAmount);
  });

  // promotions 应用后回显
  const { orderPromotionsByTypeMap, appliedStoreCredit } = useMemo(() => {
    const promotionsByType = groupOrderAppliedPromotionsByType(previewedResult.order.orderDetail.orderPromotions);
    const storeCreditPromotions = promotionsByType.get(PromotionType.StoreCredit) ?? [];
    const appliedStoreCredit = getAppliedStoreCredit(storeCreditPromotions);

    return {
      orderPromotionsByTypeMap: promotionsByType,
      appliedStoreCredit,
    };
  }, [previewedResult.order.orderDetail.orderPromotions]);

  const handleStoreCreditUpdate = useLatestCallback(async (amount: number) => {
    await previewOrder({
      appliedPromotions: {
        type: PromotionType.StoreCredit,
        promotions: [
          {
            cartItemExternalUuids: [],
            storeCredit: MoeMoney.fromAmount(amount, business.currencyCode),
          },
        ],
      },
    });
  });

  return (
    <LayoutSubpage>
      <LeftColumnHeader title={chargeType === OrderChargeType.Checkout ? 'Check out' : `Take payment`} />
      <LayoutSubpageScrollBody>
        <ERPScope capabilities={capabilities} previewedResult={previewedResult} />
        <section className="moe-flex moe-flex-col moe-mt-xl moe-ml-xxs moe-mr-s">
          <LayoutRowItem
            label="Subtotal"
            value={business.formatMoney(previewedResult.originalResult?.order.preDiscountSubtotalByItemType.all)}
          />
          <MembershipInfo
            isEditable={true}
            appliedList={orderPromotionsByTypeMap.get(PromotionType.Membership) ?? []}
            appointmentId={sourceId}
          />
          <PackageInfo
            appliedList={orderPromotionsByTypeMap.get(PromotionType.Package) ?? []}
            appointmentId={sourceId}
            canSetPackage={capabilities.canSetItems}
          />
          <DiscountInfo
            appliedList={orderPromotionsByTypeMap.get(PromotionType.Discount) ?? []}
            oneTimeDiscountList={orderPromotionsByTypeMap.get(PromotionType.OneTimeDiscount) ?? []}
            appointmentId={sourceId}
            canSetDiscount={capabilities.canSetDiscount && permissions.has('canAddRemoveDiscountAtCheckOut')}
          />
          <StoreCreditInfo
            customerId={customer.customerId}
            appliedCreditAmount={appliedStoreCredit.valueOf()}
            isEditable={true}
            onUpdate={handleStoreCreditUpdate}
          />
          <TaxInfo isEditable taxList={previewedResult.order?.subtotalByTax ?? []} />
          <TipsInfo
            tipsAmount={previewedOrder.tipsAmount}
            tipsBasedAmount={previewedOrder.tipsBasedAmount}
            handleUpdateTips={handleUpdateTips}
            canSetTips={capabilities.canSetTips}
          />
          {previewedOrder.depositAmount.valueOf() > 0 && (
            <LayoutRowItem label="Deposit" value={`-${business.formatMoney(previewedOrder.depositAmount)}`} />
          )}
          <ProcessingFee
            className="moe-gap-s"
            editingFee={cvFee.valueOf()}
            paidFee={previewedOrder.convenienceFee.valueOf()}
          />
          <LayoutDivider className="moe-my-s" />
          <LayoutSubTotal
            title="Total"
            subtotal={business.formatMoney(
              MoeMoney.fromMoney(previewedOrder.totalAmount).plus(MoeMoney.fromAmount(cvFee)),
            )}
          />
        </section>
      </LayoutSubpageScrollBody>
      <LeftColumnFooter
        amountPaid={previewedOrder.paidAmount.valueOf()}
        amountToPay={previewedOrder.remainAmountV2.plus(MoeMoney.fromAmount(cvFee)).valueOf()}
      />
    </LayoutSubpage>
  );
});

import { enumToOptions } from '@moego/finance-utils';
import { AmountCalcType, type TypeofAmountCalcType } from '@moego/finance-web-kit';
import { Heading, Input, LegacySelect, Markup, SegmentControl, Text, cn } from '@moego/ui';
import { useControllableValue } from 'ahooks';
import { useSelector } from 'amos';
import { isUndefined } from 'lodash';
import React from 'react';
import { PATH_PAYMENT_SETTING } from '../../../../../router/paths';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';

interface DepositAmountProps {
  className?: string;
  onChange: (value: { type: TypeofAmountCalcType; value?: number }) => void;
  value: {
    type: TypeofAmountCalcType;
    value?: number;
  };
  rulesAmount?: number;
}

const DepositTypeOptions = enumToOptions(AmountCalcType);
export const DepositAmount = (props: DepositAmountProps) => {
  const { onChange: _onChange, value: _value, rulesAmount, className } = props;
  const [business] = useSelector(selectCurrentBusiness);

  const [value, setValue] = useControllableValue({
    value: _value,
    onChange: (value) => {
      _onChange(value);
    },
  });

  const isReadOnly = value.type === AmountCalcType.AMOUNT;
  const isAmount = value.type === AmountCalcType.AMOUNT;

  const handleChangeSegment = (segment: 'byRule' | 'byCustomized') => {
    setValue({
      type: segment === 'byRule' ? AmountCalcType.RULES : AmountCalcType.AMOUNT,
      value: segment === 'byRule' ? (rulesAmount ?? 0) : 0,
    });
  };

  const handleChangeValue = (_amount?: number | null) => {
    setValue({
      type: value.type,
      value: Number(_amount ?? 0),
    });
  };

  const handleChangeCustomizedType = (type: TypeofAmountCalcType) => {
    setValue({
      type,
      value: undefined,
    });
  };

  const handleGoToSettings = () => {
    window.open(PATH_PAYMENT_SETTING.build({ panel: 'deposit' }), '_blank');
  };

  const segmentValue = value.type === AmountCalcType.RULES ? 'byRule' : 'byCustomized';

  const renderForm = () => {
    if (value.type === AmountCalcType.RULES) {
      return (
        <div className="moe-mt-s">
          {!isUndefined(rulesAmount) ? (
            <div>
              <Input value={value.value?.toFixed(2) ?? '0.00'} isReadOnly prefix={business.currencySymbol} />
              <Text variant="small" className="moe-text-tertiary moe-mt-xxs moe-text-left">
                The amount is calculated based on rules set up in{' '}
                <Markup
                  as="span"
                  variant="small"
                  className="moe-underline moe-cursor-pointer"
                  onClick={handleGoToSettings}
                >
                  {`Settings > Payments > Deposit`}
                </Markup>
                .
              </Text>
            </div>
          ) : (
            <div className="moe-py-l moe-px-s moe-rounded-m moe-bg-neutral-sunken-0">
              <Heading size="5" className="moe-text-center">
                No available deposit rules
              </Heading>
              <Text variant="small" className="moe-text-secondary moe-mt-xxs moe-text-center">
                No available deposit rules apply to this appointment. You can set up rules in{' '}
                <Markup
                  as="span"
                  variant="small"
                  className="moe-underline moe-cursor-pointer"
                  onClick={handleGoToSettings}
                >{`Settings > Payments > Deposit`}</Markup>
                .
              </Text>
            </div>
          )}
        </div>
      );
    }

    return (
      <div className="moe-flex moe-gap-s moe-mt-s">
        <LegacySelect
          className="moe-flex-1"
          value={value.type}
          onChange={handleChangeCustomizedType}
          options={DepositTypeOptions.filter((item) => item.value !== AmountCalcType.RULES)}
        />
        <div className="moe-w-[216px]">
          {isAmount ? (
            <Input
              key={value.type}
              value={value.value ? Number(value.value).toFixed(2) : ''}
              prefix={business.currencySymbol}
              isReadOnly={isReadOnly}
            />
          ) : (
            <Input.Number
              minValue={0}
              maxValue={100}
              key={value.type}
              onChange={handleChangeValue}
              value={value.value}
              suffix={'%'}
              isReadOnly={isReadOnly}
              precision={0}
            />
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={cn('', className)}>
      <Heading size={'4'}>Deposit amount</Heading>
      <div className={'moe-mt-s moe-flex moe-flex-col'}>
        <SegmentControl
          isBlock
          onChange={(v) => handleChangeSegment(v as 'byRule' | 'byCustomized')}
          value={segmentValue}
        >
          <SegmentControl.Item value="byRule" label="By rule" />
          <SegmentControl.Item value="byCustomized" label="Customized" />
        </SegmentControl>
        {renderForm()}
      </div>
    </div>
  );
};

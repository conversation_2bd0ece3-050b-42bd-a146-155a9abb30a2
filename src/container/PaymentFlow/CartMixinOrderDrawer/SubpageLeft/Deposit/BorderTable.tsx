import React from 'react';
import { uniq } from 'lodash';
import { Checkbox, cn, Heading } from '@moego/ui';

export type BorderTableColumn<D> = {
  header: string;
  size?: number;
  className?: string;
  cell: (props: D) => React.ReactNode;
};

export const BorderTable = <D,>(props: {
  columns: BorderTableColumn<D>[];
  data: D[];
  getRowId: (row: D) => string;
  rowSelection?: {
    onSelect: (ids: string[]) => void;
    rowIds: string[];
  };
  className?: string;
}) => {
  const { columns, data, rowSelection, className } = props;

  const headers = columns.map((c) => c.header);

  const haveSelected = (rowSelection?.rowIds.length ?? 0) > 0;
  const isSelectAll = rowSelection?.rowIds.length === data.length;
  const handleSelectAll = (isSelectAll: boolean) => {
    if (isSelectAll) {
      rowSelection?.onSelect(data.map((d) => props.getRowId(d)));
    } else {
      rowSelection?.onSelect([]);
    }
  };

  return (
    <div
      className={cn('moe-flex moe-flex-col moe-border moe-border-solid moe-border-divider moe-rounded-m', className)}
    >
      <div className="moe-flex moe-items-center moe-h-[48px]">
        {rowSelection ? (
          <Checkbox
            isSelected={isSelectAll}
            onChange={handleSelectAll}
            isIndeterminate={haveSelected && !isSelectAll}
            className="moe-p-s"
          />
        ) : null}
        {headers.map((h, i) => (
          <div
            style={{
              width: columns[i].size ?? 'auto',
              flex: columns[i].size ? undefined : 1,
            }}
            className={cn('moe-p-s', columns[i].className)}
            key={h}
          >
            <Heading size="6" className="moe-text-secondary">
              {h}
            </Heading>
          </div>
        ))}
      </div>
      <div className="moe-flex moe-flex-col">
        {data.map((d) => (
          <div
            className="moe-flex moe-items-center moe-border-t moe-border-divider moe-border-solid"
            key={props.getRowId(d)}
          >
            {rowSelection ? (
              <Checkbox
                className="moe-p-s"
                isSelected={rowSelection.rowIds.includes(props.getRowId(d))}
                onChange={(isSelected) => {
                  console.log('isSelected', isSelected, rowSelection.rowIds, props.getRowId(d));
                  if (isSelected) {
                    rowSelection.onSelect(uniq([...rowSelection.rowIds, props.getRowId(d)]));
                  } else {
                    const result = rowSelection.rowIds.filter((id) => id !== props.getRowId(d));
                    rowSelection.onSelect(result);
                  }
                }}
              />
            ) : null}
            {columns.map((c, i) => (
              <div
                style={{
                  width: c.size ?? 'auto',
                  flex: c.size ? undefined : 1,
                }}
                key={i}
                className={cn('moe-p-s', c.className)}
              >
                {c.cell(d)}
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

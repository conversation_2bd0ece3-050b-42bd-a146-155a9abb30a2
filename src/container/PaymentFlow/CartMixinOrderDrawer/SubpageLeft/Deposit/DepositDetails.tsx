import { Input, Text, Heading, cn } from '@moego/ui';
import React from 'react';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { useSelector } from 'amos';
import { type PriceDetailModelPriceItemOperator } from '@moego/api-web/moego/models/order/v1/order_line_item_models';
import { BorderTable, type BorderTableColumn } from './BorderTable';

type PetId = string;

export interface DepositDetail {
  petName: string;
  id: string;
  serviceId: string;
  name: string;
  quantity: number;
  operator: PriceDetailModelPriceItemOperator;
  description: string;
  totalPrice: number;
  unitPrice: number;
  amount: number | null;
  amountTooltip?: React.ReactNode;
  isSelected: boolean;
  isReadonly: boolean;
  isInput: boolean;
}

export const DepositDetails = (props: {
  value: Record<PetId, DepositDetail[]>;
  onChangeAmount: (
    value: Record<
      PetId,
      {
        id: string;
        amount: number | null;
      }
    >,
  ) => void;
  onChangeSelectedIds: (value: Record<string, string[]>) => void;
  className?: string;
  isSelectAble: boolean;
}) => {
  const { onChangeAmount, onChangeSelectedIds, value, className, isSelectAble } = props;

  const petIds = Object.keys(value);

  return (
    <div className={cn(className)}>
      <Heading size="4">Deposit Details</Heading>
      <div className="moe-flex moe-flex-col moe-gap-s moe-mt-s">
        {petIds.map((petId) => (
          <PetDepositDetails
            key={petId}
            value={value[petId]}
            onChangeAmount={(id, amount) => onChangeAmount({ [petId]: { id, amount } })}
            onChangeSelected={(ids) => onChangeSelectedIds({ [petId]: ids })}
            isSelectAble={isSelectAble}
          />
        ))}
      </div>
    </div>
  );
};

const PetDepositDetails = (props: {
  className?: string;
  value: DepositDetail[];
  onChangeAmount: (id: string, amount: number | null) => void;
  onChangeSelected: (ids: string[]) => void;
  isSelectAble: boolean;
}) => {
  const { value, className, onChangeAmount, onChangeSelected, isSelectAble } = props;
  const { petName } = value?.[0] ?? '';
  const [business] = useSelector(selectCurrentBusiness);
  const selectedIds = value.filter((v) => v.isSelected).map((v) => v.id);

  const haveInput = value.some((v) => v.isInput);

  const columns: BorderTableColumn<DepositDetail>[] = [
    {
      header: 'Item',
      cell: (props) => <ItemCell name={props.name} description={props.description} />,
    },
    {
      header: 'Item price',
      size: 120,
      className: 'moe-text-right',
      cell: (props) => <Text variant="regular-short">{business.formatAmount(props.totalPrice)}</Text>,
    },
    {
      header: 'Deposit amount',
      size: 160,
      className: cn(haveInput ? '' : 'moe-text-right'),
      cell: (props) => {
        const isUnselected = !selectedIds.includes(props.id);
        return (
          <div className={cn('moe-flex moe-items-center moe-gap-xxs', haveInput ? '' : 'moe-justify-end')}>
            {props.isInput ? (
              props.isReadonly || isUnselected ? (
                <Input
                  // 为了支持两位小数展示
                  value={isUnselected ? '-' : props.amount?.toFixed(2)}
                  isReadOnly
                  prefix={business.currencySymbol}
                />
              ) : (
                <Input.Number
                  isReadOnly={props.isReadonly}
                  value={props.amount}
                  minValue={0}
                  maxValue={props.totalPrice}
                  onChange={(value) => onChangeAmount(props.id, value ?? null)}
                  prefix={business.currencySymbol}
                  precision={2}
                />
              )
            ) : (
              <>
                <Text variant="regular-short">{business.formatAmount(props.amount ?? 0)}</Text>
                {props.amountTooltip ? props.amountTooltip : null}
              </>
            )}
          </div>
        );
      },
    },
  ];

  if (value.length === 0) {
    return null;
  }

  return (
    <div className={className}>
      <Heading size="5">{petName}:</Heading>
      <BorderTable
        className="moe-mt-s"
        columns={columns}
        data={value}
        getRowId={(r) => r.id}
        rowSelection={
          isSelectAble
            ? {
                onSelect: onChangeSelected,
                rowIds: selectedIds,
              }
            : undefined
        }
      />
    </div>
  );
};

export const ItemCell = (props: {
  name: string;
  description?: string;
}) => {
  const { name, description } = props;
  return (
    <div className="moe-flex moe-flex-col">
      <Text variant="regular-short">{name}</Text>
      {description ? (
        <Text variant="small" className="moe-text-tertiary moe-mt-xxs">
          {description}
        </Text>
      ) : null}
    </div>
  );
};

import { ItemType } from '@moego/api-web/moego/models/order/v1/order_enums';
import { PriceDetailModelPriceItemOperator } from '@moego/api-web/moego/models/order/v1/order_line_item_models';
import { OrderModelOrderType, type OrderModelV1 } from '@moego/api-web/moego/models/order/v1/order_models';
import { type PreviewDepositOrderResponse_PriceItemByRule } from '@moego/bff-openapi/clients/client.order';
import { AmountUtil, MoeMoney, useSerialCallback } from '@moego/finance-utils';
import { AmountCalcType, RealmType, type TypeofAmountCalcType } from '@moego/finance-web-kit';
import { MinorInfoOutlined } from '@moego/icons-react';
import { Tooltip } from '@moego/ui';
import { useDebounceFn, useRequest, useSetState } from 'ahooks';
import { useDispatch, useSelector, useStore } from 'amos';
import { groupBy, isUndefined, sum } from 'lodash';
import React, { useEffect } from 'react';
import { BFFOrderClient } from '../../../../../middleware/bff';
import { FinanceKit } from '../../../../../service/finance-kit';
import { cartServiceRecordMapBox } from '../../../../../store/PaymentFlow/cart.boxes';
import { selectCartList } from '../../../../../store/PaymentFlow/cart.selectors';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { type DepositRuleVM, listDepositRules } from '../../../../../store/payment/actions/private/deposit.actions';
import { getAllCompanyFullServiceInfoList } from '../../../../../store/service/actions/public/service.actions';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { useGetDepositRuleClientGroupText } from '../../../../settings/Settings/PaymentSetting/components/DepositSetting/hooks/useGetDepositRuleClientGroupText';
import { useGetDepositRuleDateRangeText } from '../../../../settings/Settings/PaymentSetting/components/DepositSetting/hooks/useGetDepositRuleDateRangeText';
import { useGetDepositRuleDeposit } from '../../../../settings/Settings/PaymentSetting/components/DepositSetting/hooks/useGetDepositRuleDeposit';
import { useGetDepositRuleServiceTypeText } from '../../../../settings/Settings/PaymentSetting/components/DepositSetting/hooks/useGetDepositRuleServiceTypeText';
import { RightSideSubpage } from '../../../hooks/useSubpageRouter';
import { buildOrderV2 } from '../../CardMixinOrderDrawer.utils';
import { useCartAppt } from '../../Cart/useCartAppt';
import { useInitCart } from '../../Cart/useInitCart';
import { ColumnsBridgeEvent } from '../../Columns/ColumnsBridge';
import { useDrawerScopeContext } from '../../context/DrawerScopeContext';
import { type OptimizedPreviewOrderParams, type PreviewOrderResult } from '../../hooks/Preview.types';
import { useBridgeListener } from '../../hooks/use-bridge-hooks';
import { useBroadcastChargeAmount } from '../../hooks/useBroadcastChargeAmount';
import { getSubtotalMoney } from '../OrderFlow/OrderDetail.utils';
import { useGetGroupedServices } from '../hooks/useGroupedServices';
import { type DepositDetail } from './DepositDetails';
import { usePreviewDepositOrderAmountByRules } from './usePreviewDepositOrderAmountByRules';

export const useDepositData = ({
  previewOrder,
  inPreview,
}: {
  previewOrder: (params: Partial<OptimizedPreviewOrderParams>) => Promise<PreviewOrderResult>;
  inPreview: boolean;
}) => {
  const dispatch = useDispatch();
  const [business, cartServiceRecordMap] = useSelector(selectCurrentBusiness, cartServiceRecordMapBox);
  const getDepositRuleServiceTypeText = useGetDepositRuleServiceTypeText();
  const getDepositRuleClientGroupText = useGetDepositRuleClientGroupText();
  const getDepositRuleDateRangeText = useGetDepositRuleDateRangeText();
  const getDepositRuleDeposit = useGetDepositRuleDeposit();
  const store = useStore();
  const { columnsBridge, sourceType, customer, rightColumnPageRouter, sourceId } = useDrawerScopeContext();
  const goDefault = () => rightColumnPageRouter.go(RightSideSubpage.DEFAULT);

  const [data, setData] = useSetState<{
    chargeAmountWithConvenienceFee: number;
    preServiceSubtotal: number;
    serviceSubTotal: number;
    depositType: TypeofAmountCalcType;
    depositValue?: number;
    depositAmount: number;
    cvFee: number;
    rulesAmount?: number;
    depositDetails: Record<string, DepositDetail[]>;
    depositPriceItems: PreviewDepositOrderResponse_PriceItemByRule[];
    saleOrderPreviewResult: PreviewOrderResult | null;
  }>({
    chargeAmountWithConvenienceFee: 0,
    preServiceSubtotal: 0,
    serviceSubTotal: 0,
    depositType: AmountCalcType.AMOUNT as TypeofAmountCalcType,
    depositValue: undefined,
    depositAmount: 0,
    cvFee: 0,
    depositDetails: {},
    depositPriceItems: [],
    saleOrderPreviewResult: null,
  });

  const setDepositAmount = (amount: number) => {
    setData({
      depositAmount: amount,
      chargeAmountWithConvenienceFee: MoeMoney.fromAmount(amount).plus(MoeMoney.fromAmount(data.cvFee)).valueOf(),
    });
  };
  const previewDepositOrderAmountByRules = usePreviewDepositOrderAmountByRules();
  const getGroupedServices = useGetGroupedServices();
  const { run: fetchData, data: activeRules = [] } = useRequest(
    async () => {
      const [{ activeRules }] = await Promise.all([
        dispatch(listDepositRules()),
        dispatch(getAllCompanyFullServiceInfoList({ withAddon: false })),
      ]);

      return activeRules;
    },
    {
      manual: true,
    },
  );

  const { broadcastChargeAmount } = useBroadcastChargeAmount({
    columnsBridge,
  });

  const broadcastAmount = async (amount: number) => {
    // calculate cv fee base on amount
    broadcastChargeAmount(MoeMoney.fromAmount(amount));
  };

  const broadcastAmountDebounced = useDebounceFn(broadcastAmount, { wait: 1000 });

  const changeAmount = (amount: number) => {
    setDepositAmount(amount);

    columnsBridge.runParallel(
      ColumnsBridgeEvent.OnPreviewOrderReady,
      FinanceKit.buildModel(RealmType.OrderV2, {
        orderType: OrderModelOrderType.DEPOSIT,
        customerId: customer.customerId.toString(),
      }),
      null,
    );

    broadcastAmountDebounced.run(amount);
  };

  const { initCart } = useCartAppt(sourceId);
  useBridgeListener(columnsBridge, ColumnsBridgeEvent.OnConvenienceFeeChange, (cvFee) => {
    setData(({ depositAmount, ...rest }) => ({
      ...rest,
      chargeAmountWithConvenienceFee: MoeMoney.fromAmount(depositAmount, business.currencyCode)
        .plus(MoeMoney.fromAmount(cvFee))
        .valueOf(),
      cvFee,
    }));
  });

  const handleChangeDepositValue = useSerialCallback(
    async (_value: {
      type: TypeofAmountCalcType;
      value?: number;
      rules?: DepositRuleVM[];
    }) => {
      const { type, value, rules = activeRules } = _value;

      // 如果只是切换 percentage value，则直接改变原来的 depositDetails 的 amount 即可。
      const changePercentageValue =
        type === AmountCalcType.PERCENTAGE && data.depositType === AmountCalcType.PERCENTAGE;

      const nextData = {
        depositType: type,
        depositValue: value,
        depositDetails: changePercentageValue
          ? getDepositDetailsOnChangePercentageValue(value ?? 0)
          : getDepositDetails({
              previewedResult: data.saleOrderPreviewResult,
              priceItems: data.depositPriceItems,
              type,
              value,
              rules,
            }),
      };

      setData(nextData);
      const amount = getAmount(nextData.depositDetails);

      changeAmount(amount);
      goDefault();
    },
  );

  const getDepositDetails = (params: {
    previewedResult: PreviewOrderResult | null;
    priceItems: PreviewDepositOrderResponse_PriceItemByRule[];
    type: TypeofAmountCalcType;
    value?: number;
    rules?: DepositRuleVM[];
  }) => {
    const { previewedResult, priceItems, type, value, rules = activeRules } = params;
    if (!previewedResult) {
      return {};
    }
    const valueNumber = Number(value ?? 0);
    const { petServices } = store.select(selectCartList(sourceId));
    const isRules = type === AmountCalcType.RULES;
    const isPercentage = type === AmountCalcType.PERCENTAGE;

    const result = petServices
      .map((petService) => {
        const groupedServices = getGroupedServices(petService, sourceId);
        return groupedServices.flat().map((service) => {
          const info = cartServiceRecordMap.mustGetItem(service);
          const orderItems = previewedResult.order.orderDetail.orderItems;
          const orderItem = orderItems.find((v) => v.externalUuid === info.externalId);
          const totalPrice = MoeMoney.fromMoney(orderItem?.totalAmount ?? MoeMoney.empty()).valueOf();

          const priceItem = priceItems.find((v) => v.externalUuid === info.externalId);
          const amount = isRules
            ? MoeMoney.fromMoney(priceItem?.priceItem?.subTotal ?? MoeMoney.empty()).valueOf()
            : isPercentage
              ? AmountUtil.divide(AmountUtil.multiply(valueNumber, totalPrice), 100)
              : 0;

          const rule = rules?.find((v) => v.id === priceItem?.ruleId);
          let amountTooltip = null;

          if (isRules) {
            if (rule) {
              const name = rule.ruleName;
              const serviceTypeText = getDepositRuleServiceTypeText(rule.serviceType);
              const clientGroupText = getDepositRuleClientGroupText(rule.clientGroup);
              const dateRangeText = getDepositRuleDateRangeText(rule.dateRange);
              const depositText = getDepositRuleDeposit(rule.depositConfig);
              const items = [
                {
                  label: 'Rule name',
                  value: name,
                },

                {
                  label: 'Client group',
                  value: clientGroupText,
                },
                {
                  label: 'Service type',
                  value: serviceTypeText,
                },
                {
                  label: 'Date range',
                  value: dateRangeText,
                },
                {
                  label: 'Deposit',
                  value: depositText,
                },
              ];

              amountTooltip = (
                <Tooltip
                  content={
                    <div>
                      {items.map((item) => (
                        <div key={item.label} className="moe-flex moe-gap-xxs">
                          <p className="moe-text-nowrap">{item.label}:</p>
                          <p>{item.value}</p>
                        </div>
                      ))}
                    </div>
                  }
                >
                  <MinorInfoOutlined className="moe-text-icon-tertiary" />
                </Tooltip>
              );
            } else {
              amountTooltip = (
                <Tooltip content="No deposit rule was matched to this item.">
                  <MinorInfoOutlined className="moe-text-icon-tertiary" />
                </Tooltip>
              );
            }
          }

          return {
            id: info.detailId,
            serviceId: info.serviceId,
            petId: petService.pet.id,
            name: info.name,
            quantity: info.quantity,
            operator: priceItem?.priceItem?.operator ?? PriceDetailModelPriceItemOperator.ADD,
            description: `${business.formatMoney(info.unitPrice)} x${info.quantity}`,
            totalPrice: totalPrice,
            petName: petService.pet.petName,
            unitPrice: MoeMoney.fromMoney(info.unitPrice).valueOf(),
            amount,
            amountTooltip,
            isSelected: true,
            isReadonly: isPercentage,
            isInput: !isRules,
          };
        });
      })
      .flat()
      .filter(Boolean) as DepositDetail[];

    return groupBy(result, 'petId');
  };

  const getAmount = (input: ReturnType<typeof getDepositDetails>) => {
    return sum(
      Object.values(input)
        .flat()
        .filter((detail) => detail.isSelected)
        .map((detail) => detail.amount),
    );
  };

  const handleChangeDepositDetailsAmount = (value: Record<string, { id: string; amount: number | null }>) => {
    const nextDepositDetails = { ...data.depositDetails };
    const petId = Object.keys(value)[0];
    const curPetDetails = nextDepositDetails[petId];
    const nextPetDetails = curPetDetails.map((detail) => {
      const detailId = detail.id;
      if (detailId === value[petId].id) {
        return { ...detail, amount: value[petId].amount };
      }
      return detail;
    });
    nextDepositDetails[petId] = nextPetDetails;
    const amount = getAmount(nextDepositDetails);

    setData({ depositDetails: nextDepositDetails, depositValue: amount });

    changeAmount(amount);

    goDefault();
  };

  const handleChangeDepositDetailsSelected = (value: Record<string, string[]>) => {
    const nextDepositDetails = { ...data.depositDetails };
    const petId = Object.keys(value)[0];
    const curPetDetails = nextDepositDetails[petId];
    const nextPetDetails = curPetDetails.map((detail) => {
      const detailId = detail.id;
      return { ...detail, isSelected: value[petId].includes(detailId) };
    });
    nextDepositDetails[petId] = nextPetDetails;

    setData({ depositDetails: nextDepositDetails });

    const amount = getAmount(nextDepositDetails);
    changeAmount(amount);
    goDefault();
  };

  const getDepositDetailsOnChangePercentageValue = (percentage: number) => {
    const currentDepositDetails = data.depositDetails;
    const petIds = Object.keys(currentDepositDetails);
    const nextDepositDetails: Record<string, DepositDetail[]> = {};
    petIds.forEach((petId) => {
      const petDetails = currentDepositDetails[petId];
      nextDepositDetails[petId] = petDetails.map((detail) => {
        return { ...detail, amount: (detail.totalPrice * percentage) / 100 };
      }) as DepositDetail[];
    });
    return nextDepositDetails;
  };

  const preview = useLatestCallback(async (params: Partial<OptimizedPreviewOrderParams>) => {
    const [previewResult, { remainAmount, priceItems }] = await Promise.all([
      previewOrder(params),
      previewDepositOrderAmountByRules(sourceId),
    ]);

    const subtotal = getSubtotalMoney(previewResult);
    const serviceSubtotal = subtotal.discountedServiceSubtotal.valueOf();
    setData({
      serviceSubTotal: serviceSubtotal,
      preServiceSubtotal: subtotal.preServiceSubtotal.valueOf(),
      rulesAmount: remainAmount ? Math.min(remainAmount, serviceSubtotal) : undefined,
      depositPriceItems: priceItems,
      saleOrderPreviewResult: previewResult,
      depositDetails: getDepositDetails({
        previewedResult: previewResult,
        priceItems: priceItems,
        type: data.depositType,
        value: data.depositValue ?? 0,
      }),
    });
    return previewResult;
  });

  const createOrder = useLatestCallback(async () => {
    const { depositType, depositValue } = data;

    const allSelectedItemTotalPrice = Object.values(data.depositDetails)
      .flat()
      .filter((detail) => detail.isSelected)
      .reduce((acc, detail) => AmountUtil.plus(acc, detail.totalPrice), 0);

    const description =
      depositType === AmountCalcType.PERCENTAGE
        ? `By percentage, ${business.formatAmount(allSelectedItemTotalPrice)} * ${depositValue}%`
        : depositType === AmountCalcType.AMOUNT
          ? `By amount`
          : `By rule`;

    const result = await BFFOrderClient.createDepositOrder({
      sourceId,
      sourceType: sourceType as unknown as any,
      depositAmount: {
        ...MoeMoney.fromAmount(data.depositAmount),
      },
      businessId: business.id.toString(),
      description: description,
      depositPriceDetail: {
        priceItems: Object.values(data.depositDetails)
          .flat()
          .filter((detail) => detail.isSelected)
          .map((detail) => ({
            name: detail.name,
            operator: detail.operator as any,
            // 这里 quantity 默认给 1，所以 unitPrice 和 subtotal 就是 amount。
            quantity: 1,
            unitPrice: MoeMoney.fromAmount(detail.amount ?? 0),
            subTotal: MoeMoney.fromAmount(detail.amount ?? 0),
            objectType: ItemType.SERVICE,
            objectId: detail.serviceId,
          })),
      },
    });

    const order = result.order as unknown as OrderModelV1;
    return buildOrderV2(order);
  });

  useInitCart({
    sourceId,
    initCart,
    autoApplyWhenCartChanges: false,
    preview: async (params) => {
      return await preview(params);
    },
    createOrder,
    columnsBridge,
  });

  useEffect(() => {
    if (activeRules.length === 0) {
      fetchData();
      return;
    }
    if (!inPreview && !isUndefined(data.rulesAmount)) {
      handleChangeDepositValue({
        type: AmountCalcType.RULES,
        value: data.rulesAmount,
        rules: activeRules,
      });
    }
  }, [inPreview, data.rulesAmount, activeRules.length]);

  return {
    isLoading: inPreview,
    data,
    handleChangeDepositValue,
    handleChangeDepositDetailsAmount,
    handleChangeDepositDetailsSelected,
    isLoadingCVFee: handleChangeDepositValue.isBusy(),
  };
};

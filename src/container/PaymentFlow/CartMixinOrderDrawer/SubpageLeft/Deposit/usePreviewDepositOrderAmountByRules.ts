import { OrderSourceType } from '@moego/api-web/moego/models/order/v1/order_enums';
import { type z } from '@moego/bff-openapi';
import { type schemas } from '@moego/bff-openapi/clients/client.order';
import { <PERSON><PERSON><PERSON> } from '@moego/finance-utils';
import { useDispatch, useStore } from 'amos';
import { groupBy } from 'lodash';
import { BFFOrderClient } from '../../../../../middleware/bff';
import { getListPetServices } from '../../../../../store/PaymentFlow/cart.action';
import { selectSelectedCartDetailList } from '../../../../../store/PaymentFlow/cart.selectors';
import { getTaxList } from '../../../../../store/business/tax.actions';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';

export const usePreviewDepositOrderAmountByRules = () => {
  const store = useStore();
  const dispatch = useDispatch();

  const resolveItems = (appointmentId: string) => {
    const sourceItems = store.select(selectSelectedCartDetailList(appointmentId));
    return {
      sourceItems,
    };
  };

  const previewDepositOrderAmountByRules = useLatestCallback(async (sourceId: string) => {
    await Promise.all([dispatch(getListPetServices({ id: sourceId })), dispatch(getTaxList())]);
    const { sourceItems } = resolveItems(sourceId);
    const petServicesMap = groupBy(sourceItems, 'pet.id');
    const petIds = Object.keys(petServicesMap);

    const { priceItems, orderDetail } = await BFFOrderClient.previewDepositOrderByRules({
      sourceId: sourceId,
      sourceType: OrderSourceType.APPOINTMENT,
      petServices: petIds.map((petId) => ({
        petId,
        services: petServicesMap[petId].map((s) => ({
          serviceId: s.serviceId,
          unitPrice: s.unitPrice,
          quantity: s.quantity,
          totalPrice: s.totalPrice,
          staffId: s.staffId,
          associatedServiceId: s.associatedServiceId,
          serviceItemType: s.serviceItemType,
          serviceType: s.serviceType,
          externalUuid: s.externalId,
        })) as unknown as z.infer<typeof schemas.PreviewDepositOrderByRulesRequest>['petServices'][number]['services'],
      })),
    });

    const remainAmount = orderDetail?.order?.remainAmount ?? undefined;

    return {
      remainAmount: remainAmount ? MoeMoney.fromMoney(remainAmount).valueOf() : undefined,
      priceItems,
    };
  });

  return previewDepositOrderAmountByRules;
};

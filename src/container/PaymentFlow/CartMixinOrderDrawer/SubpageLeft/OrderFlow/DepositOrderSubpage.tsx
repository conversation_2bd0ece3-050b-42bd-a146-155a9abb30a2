import { <PERSON><PERSON><PERSON>, useSerialCallback } from '@moego/finance-utils';
import { MinorInfoFilled } from '@moego/icons-react';
import { <PERSON>ert<PERSON><PERSON><PERSON>, <PERSON>ton, Text, toast } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React from 'react';
import { ClientInfoSummaryCard } from '../../../../../components/ClientPicker/ClientInfoSummaryCard';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { cancelOrder } from '../../../../../store/PaymentFlow/order.actions';
import { selectCurrentStaff } from '../../../../../store/staff/staff.selectors';
import { PaymentDrawerCloseType } from '../../../interface';
import { LayoutDivider } from '../../components/Layout/LayoutDivider';
import { LayoutSubpage } from '../../components/Layout/LayoutSubpage';
import { LayoutSubpageScrollBody } from '../../components/Layout/LayoutSubpageScrollBody';
import { LayoutSubTotal } from '../../components/Layout/LayoutSubTotal';
import { LayoutRowItem } from '../../components/LayoutRowItem/LayoutRowItem';
import { useDrawerScopeContext } from '../../context/DrawerScopeContext';
import { LeftColumnFooter } from '../components/LeftColumnFooter';
import { LeftColumnHeader } from '../components/LeftColumnHeader';
import { ProcessingFee } from '../components/ProcessingFee/ProcessingFee';
import { type OrderSubpageProps } from './OrderFlowSubpage.types';
import { DepositDetails } from './DepositDetails';

export const DepositOrderSubpage = (props: OrderSubpageProps) => {
  const { order, orderAllDetailView, cvFee } = props;
  const { customer, closeDrawer, isStartedWithPreview } = useDrawerScopeContext();
  const [business, staff] = useSelector(selectCurrentBusiness, selectCurrentStaff);
  const dispatch = useDispatch();

  const handleCancelOrder = useSerialCallback(async () => {
    AlertDialog.open({
      title: 'Cancel payment request',
      content: 'Once cancelled, the online payment link will expire and no longer be accessible by the client.',
      confirmText: 'Cancel request',
      cancelText: 'Do it later',
      onConfirm: async () => {
        await dispatch(
          cancelOrder({
            businessId: business.id.toString(),
            staffId: staff.id.toString(),
            orderId: order.id,
          }),
        );
        toast({
          title: 'Payment request cancelled!',
          type: 'success',
        });
        closeDrawer(PaymentDrawerCloseType.CancelOrder);
      },
    });
  });

  const renderAlert = () => {
    // 刚创建的订单不展示，再次进入展示
    if (!isStartedWithPreview) {
      return (
        <div className="moe-rounded-s moe-bg-information-subtle moe-border-information moe-border moe-p-s moe-mt-xl">
          <div className="moe-flex moe-justify-between moe-gap-s moe-items-center">
            <Text variant="small" className="moe-flex-shrink moe-flex moe-items-start">
              <MinorInfoFilled className="moe-text-information moe-mr-xxs" />A payment request has already been sent to
              this client. To make changes, cancel the request and send a new one.
            </Text>
            <Button variant="secondary" className="moe-flex-shrink-0" size="s" onPress={handleCancelOrder}>
              Cancel payment request
            </Button>
          </div>
        </div>
      );
    }
    return null;
  };

  const priceItems = orderAllDetailView?.order.orderDetail.orderItems?.[0].subtotalDetail.priceItems;

  return (
    <LayoutSubpage>
      <LeftColumnHeader title={'Take deposit'} />
      <LayoutSubpageScrollBody className="moe-pb-l">
        <ClientInfoSummaryCard
          client={customer}
          displayClientTags={false}
          className="moe-border moe-border-divider"
          style={{ boxShadow: 'none' }}
        />
        {renderAlert()}
        {orderAllDetailView?.order.orderDetail.orderItems.map((item) => {
          return (
            <div
              key={item.id}
              className="moe-flex moe-flex-col moe-gap-xxs moe-mt-xl moe-border-l-[3px] moe-border-solid moe-border-divider moe-pl-xs moe-py-spacing-xxs"
            >
              <Text variant="regular-short" className="moe-flex moe-justify-between">
                <span>Deposit</span>
                <span>{business.formatMoney(item.totalAmount)}</span>
              </Text>
              <Text variant="small" className="moe-text-tertiary">
                {item.description}
              </Text>
            </div>
          );
        })}

        <LayoutDivider className="moe-my-s" />
        <LayoutRowItem label="Subtotal" value={business.formatMoney(order.subTotalAmount)} />
        <ProcessingFee className="moe-gap-x-s" editingFee={cvFee.valueOf()} paidFee={order.convenienceFee.valueOf()} />
        <LayoutDivider className="moe-mt-s moe-mb-xs" />
        <LayoutSubTotal
          title="Total"
          className="moe-my-s"
          subtotal={business.formatMoney(order.totalAmount.plus(MoeMoney.fromAmount(cvFee)))}
        />
        <DepositDetails priceItems={priceItems ?? []} className="moe-mt-xl" />
      </LayoutSubpageScrollBody>
      <LeftColumnFooter
        amountToPay={order.remainAmountV2.plus(MoeMoney.fromAmount(cvFee)).valueOf()}
        amountPaid={order.paidAmount.valueOf()}
      />
    </LayoutSubpage>
  );
};

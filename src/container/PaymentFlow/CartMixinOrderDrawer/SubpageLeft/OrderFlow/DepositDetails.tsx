import { type PriceDetailModelPriceItem } from '@moego/api-web/moego/models/order/v1/order_line_item_models';
import React from 'react';
import { BorderTable, type BorderTableColumn } from '../Deposit/BorderTable';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { useSelector } from 'amos';
import { ItemCell } from '../Deposit/DepositDetails';
import { Heading, Text } from '@moego/ui';

export const DepositDetails = (props: {
  priceItems: PriceDetailModelPriceItem[];
  className?: string;
}) => {
  const { priceItems, className } = props;
  const [business] = useSelector(selectCurrentBusiness);

  if (priceItems.length === 0) {
    return null;
  }

  const columns: BorderTableColumn<PriceDetailModelPriceItem>[] = [
    {
      header: 'Item',
      cell: (item) => <ItemCell name={item.name} />,
    },
    {
      header: 'Deposit amount',
      size: 160,
      className: 'moe-text-right',
      cell: (item) => (
        <Text variant="regular-short" className="moe-text-right">
          {business.formatMoney(item.subTotal)}
        </Text>
      ),
    },
  ];

  return (
    <div className={className}>
      <Heading size="4">Deposit details</Heading>
      <BorderTable className="moe-mt-s" columns={columns} data={priceItems} getRowId={(item) => item.name} />
    </div>
  );
};

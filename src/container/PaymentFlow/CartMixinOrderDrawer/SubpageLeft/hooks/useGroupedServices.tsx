import { type PetService } from '@moego/api-web/moego/models/fulfillment/v1/fulfillment_defs';
import { useMemo } from 'react';
import { ApptCareTypeEnum } from '../../../../Appt/store/appt.options';
import { CartServiceRecord } from '../../../../../store/PaymentFlow/cart.boxes';
import { group } from '../../../../../store/utils/utils';
import { isNormal } from '../../../../../store/utils/identifier';

export const useGetGroupedServices = () => {
  const getGroupedServices = (petService: PetService, appointmentId: string) => {
    const { services } = petService;
    return group(
      services,
      (s) => s.serviceItemType,
      (s) => s,
    )
      .sort((a, b) => {
        const aConfig = ApptCareTypeEnum.mapLabels[a[0]];
        const bConfig = ApptCareTypeEnum.mapLabels[b[0]];
        return aConfig?.order - bConfig.order;
      })
      .map((group) => {
        const [_, list] = group;
        return list
          .filter((v) => !isNormal(v.orderLineItemId)) /** 过滤掉已创建过 order 的 */
          .map((l) => CartServiceRecord.createKeyId(appointmentId, l.detailId));
      });
  };
  return getGroupedServices;
};

export const useGroupedServices = (petService: PetService, appointmentId: string) => {
  const groupedServices = useMemo(() => {
    const { services } = petService;
    return group(
      services,
      (s) => s.serviceItemType,
      (s) => s,
    )
      .sort((a, b) => {
        const aConfig = ApptCareTypeEnum.mapLabels[a[0]];
        const bConfig = ApptCareTypeEnum.mapLabels[b[0]];
        return aConfig?.order - bConfig.order;
      })
      .map((group) => {
        const [_, list] = group;
        return list
          .filter((v) => !isNormal(v.orderLineItemId)) /** 过滤掉已创建过 order 的 */
          .map((l) => CartServiceRecord.createKeyId(appointmentId, l.detailId));
      });
  }, [petService, appointmentId]);

  return groupedServices;
};

import { AmountCalcType } from '@moego/finance-web-kit';
import { Spin } from '@moego/ui';
import { isUndefined, noop } from 'lodash';
import React from 'react';
import { ClientInfoSummaryCard } from '../../../../components/ClientPicker/ClientInfoSummaryCard';
import { LayoutSubpage } from '../components/Layout/LayoutSubpage';
import { LayoutSubpageScrollBody } from '../components/Layout/LayoutSubpageScrollBody';
import { useDrawerScopeContext } from '../context/DrawerScopeContext';
import { usePreviewSalesOrder } from '../hooks/usePreviewSalesOrder';
import { LeftColumnFooter } from './components/LeftColumnFooter';
import { LeftColumnHeader } from './components/LeftColumnHeader';
import { DepositAmount } from './Deposit/DepositAmount';
import { DepositDetails } from './Deposit/DepositDetails';
import { useDepositData } from './Deposit/useDepositData';

export const DepositSubpage = () => {
  const { customer } = useDrawerScopeContext();
  const { previewOrder, inPreview } = usePreviewSalesOrder({
    afterPreview: noop,
  });
  const {
    data,
    handleChangeDepositValue,
    handleChangeDepositDetailsAmount,
    handleChangeDepositDetailsSelected,
    isLoading,
  } = useDepositData({
    previewOrder,
    inPreview,
  });

  const isRules = data.depositType === AmountCalcType.RULES;
  const hideDepositDetails = isRules && isUndefined(data.rulesAmount);

  if (isLoading) {
    return (
      <div className="moe-flex moe-h-full moe-items-center moe-justify-center">
        <Spin />
      </div>
    );
  }

  return (
    <LayoutSubpage>
      <LeftColumnHeader title={'Take deposit'} />
      <LayoutSubpageScrollBody className="moe-pb-l">
        <ClientInfoSummaryCard
          client={customer}
          displayClientTags={false}
          className="moe-border moe-border-divider"
          style={{ boxShadow: 'none' }}
        />
        <DepositAmount
          className={'moe-mt-l'}
          rulesAmount={data.rulesAmount}
          onChange={handleChangeDepositValue}
          value={{
            type: data.depositType,
            value: data.depositValue,
          }}
        />
        {hideDepositDetails ? null : (
          <DepositDetails
            className="moe-mt-l"
            value={data.depositDetails}
            onChangeAmount={handleChangeDepositDetailsAmount}
            onChangeSelectedIds={handleChangeDepositDetailsSelected}
            isSelectAble={data.depositType === AmountCalcType.PERCENTAGE}
          />
        )}
      </LayoutSubpageScrollBody>
      <LeftColumnFooter
        amountPaid={0}
        amountToPay={data.chargeAmountWithConvenienceFee}
        amountToPayTitle={'Deposit amount due'}
      />
    </LayoutSubpage>
  );
};

import { CouponSearchConditionSourceType } from '@moego/api-web/moego/models/promotion/v1/coupon';
import { type A0, type <PERSON><PERSON>oney, useSerialCallback } from '@moego/finance-utils';
import { Empty, Input, Spin } from '@moego/ui';
import { useSelector } from 'amos';
import { debounce } from 'lodash';
import React, { memo, useMemo } from 'react';
import { useAsync, useSetState } from 'react-use';
import { type OneTimeDiscountAppliedModel } from '../../../../../store/PaymentFlow/cart.boxes';
import { selectCartDiscount, selectSelectedOneTimeDiscount } from '../../../../../store/PaymentFlow/cart.selectors';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { useCartAppt } from '../../Cart/useCartAppt';
import { useCartChangesPreview } from '../../Cart/useCartChangesPreview';
import { GetCartItemUuidsType, PromotionType } from '../../Promotions/Promotion.types';
import { transOneTimeDiscountToPreview } from '../../Promotions/Promotion.utils';
import { usePromotion } from '../../Promotions/usePromotion';
import { LayoutRightPageFooter } from '../../components/Layout/LayoutRightPageFooter';
import { LayoutRightPageHeader } from '../../components/Layout/LayoutRightPageHeader';
import { LayoutSubpage } from '../../components/Layout/LayoutSubpage';
import { LayoutSubpageScrollBody } from '../../components/Layout/LayoutSubpageScrollBody';
import { ContinueToPayButton } from '../components/ContinueToPay';
import { DiscountCardItem } from './components/DiscountCardItem';
import { AddOneTimeDiscountButton } from './components/OneTimeDiscount/AddOneTimeDiscountButton';
import { type AddOneTimeDiscountModalProps } from './components/OneTimeDiscount/AddOneTimeDiscountModal';
import { OneTimeDiscountCardItem } from './components/OneTimeDiscountCardItem';

interface DiscountSubpageProps {
  baseAmount: MoeMoney;
  appointmentId: string;
}

export const DiscountSubpage = memo<DiscountSubpageProps>((props) => {
  const { baseAmount, appointmentId } = props;
  const [business] = useSelector(selectCurrentBusiness);
  const { getCartItems } = useCartAppt(appointmentId);
  const { fetchData, getTargetCartItemsUuid } = usePromotion(appointmentId);
  const [{ availableCoupons }, oneTimeDiscountList] = useSelector(
    selectCartDiscount(appointmentId),
    selectSelectedOneTimeDiscount(appointmentId),
  );
  const [{ keyword }, setState] = useSetState<{
    keyword: string;
  }>({
    keyword: '',
  });

  const preview = useCartChangesPreview();
  const handleAddOneTimeDiscount: AddOneTimeDiscountModalProps['onConfirm'] = useSerialCallback(
    async (data: A0<AddOneTimeDiscountModalProps['onConfirm']>) => {
      const newPromotion = {
        cartItemExternalUuids: data.cartItemUuids,
        oneTimeDiscount: transOneTimeDiscountToPreview(data.discountInfo, business.currencyCode),
      };

      await preview({
        appliedPromotions: {
          type: PromotionType.OneTimeDiscount,
          promotions: [...oneTimeDiscountList]
            .map((d) => ({
              cartItemExternalUuids: getTargetCartItemsUuid({
                type: GetCartItemUuidsType.SELECTED,
                cacheKeyId: d.keyId,
              }),
              oneTimeDiscount: transOneTimeDiscountToPreview(d, business.currencyCode),
            }))
            .concat(newPromotion),
        },
      });
    },
  );

  const handleRemoveOneTimeDiscount = useSerialCallback(async (v: OneTimeDiscountAppliedModel) => {
    const newItems = oneTimeDiscountList.filter((d) => d.keyId !== v.keyId);
    await preview({
      appliedPromotions: {
        type: PromotionType.OneTimeDiscount,
        promotions: newItems.map((d) => ({
          cartItemExternalUuids: getTargetCartItemsUuid({
            type: GetCartItemUuidsType.SELECTED,
            cacheKeyId: d.keyId,
          }),
          oneTimeDiscount: transOneTimeDiscountToPreview(d, business.currencyCode),
        })),
      },
    });
  });

  const handleOneTimeDiscountAppliedItemsChange = useSerialCallback(
    async (keyId: string, targetCartItemExternalUuids: string[]) => {
      await preview({
        appliedPromotions: {
          type: PromotionType.OneTimeDiscount,
          promotions: oneTimeDiscountList.map((d) => ({
            cartItemExternalUuids:
              d.keyId === keyId
                ? targetCartItemExternalUuids
                : getTargetCartItemsUuid({
                    type: GetCartItemUuidsType.SELECTED,
                    cacheKeyId: d.keyId,
                  }),
            oneTimeDiscount: transOneTimeDiscountToPreview(d, business.currencyCode),
          })),
        },
      });
    },
  );

  const { loading } = useAsync(async () => {
    await fetchData([CouponSearchConditionSourceType.DISCOUNT], keyword);
  }, [fetchData, keyword]);

  const isShowEmpty = !availableCoupons.length && !oneTimeDiscountList.length && !loading;

  const handleSearch = useMemo(() => debounce((v) => setState({ keyword: v }), 500), []);

  return (
    <LayoutSubpage>
      <LayoutRightPageHeader
        title={'Discount'}
        extra={
          <div className="moe-flex-1 moe-flex moe-justify-end">
            <AddOneTimeDiscountButton
              appointmentId={appointmentId}
              prevOneTimeDiscount={oneTimeDiscountList.at(-1)}
              baseAmount={baseAmount}
              onAdd={handleAddOneTimeDiscount}
            />
          </div>
        }
      />
      <LayoutSubpageScrollBody>
        <Input.Search placeholder="Search" onChange={handleSearch} className="moe-mb-m" />
        <Spin isLoading={loading} className="moe-min-h-[100px]">
          {isShowEmpty && <Empty className="moe-mt-8px-500" title="No discount found" description="" />}
          <div className="moe-flex moe-flex-col moe-gap-s">
            {oneTimeDiscountList.map((discount) => (
              <OneTimeDiscountCardItem
                key={discount.keyId}
                discount={discount}
                onRemove={handleRemoveOneTimeDiscount}
                getTargetCartItemsUuid={getTargetCartItemsUuid}
                getCartItems={getCartItems}
                onAppliedItemsChange={handleOneTimeDiscountAppliedItemsChange}
              />
            ))}

            {availableCoupons.map((coupon) => (
              <DiscountCardItem
                getTargetCartItemsUuid={getTargetCartItemsUuid}
                getCartItems={getCartItems}
                appointmentId={appointmentId}
                key={coupon.source.id}
                coupon={coupon}
              />
            ))}
          </div>
        </Spin>
      </LayoutSubpageScrollBody>
      <LayoutRightPageFooter>
        <ContinueToPayButton />
      </LayoutRightPageFooter>
    </LayoutSubpage>
  );
});

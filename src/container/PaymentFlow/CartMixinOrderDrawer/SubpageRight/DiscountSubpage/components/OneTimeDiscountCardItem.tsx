import { useLatestCallback } from '@moego/finance-utils';
import { AmountCalcType, getDiscountValueTag } from '@moego/finance-web-kit';
import { Button, Heading } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { type OneTimeDiscountAppliedModel } from '../../../../../../store/PaymentFlow/cart.boxes';
import { selectPromotionAppliedLineItems } from '../../../../../../store/PaymentFlow/cart.selectors';
import { type useCartAppt } from '../../../Cart/useCartAppt';
import { GetCartItemUuidsType, type GetTargetCartItemUuidList } from '../../../Promotions/Promotion.types';
import { LineItemPromotionSelector } from '../../components/LineItemPromotionSelector';
import { NormalCard } from './NormalCard';

interface OneTimeDiscountCardItemProps {
  discount: OneTimeDiscountAppliedModel;
  getTargetCartItemsUuid: GetTargetCartItemUuidList;
  getCartItems: ReturnType<typeof useCartAppt>['getCartItems'];
  onAppliedItemsChange: (keyId: string, targetCartItemExternalUuids: string[]) => void;
  onRemove: (discount: OneTimeDiscountAppliedModel) => void;
}

export const OneTimeDiscountCardItem = (props: OneTimeDiscountCardItemProps) => {
  const { discount, getTargetCartItemsUuid, getCartItems, onRemove, onAppliedItemsChange } = props;
  const [business, appliedLineItems] = useSelector(
    selectCurrentBusiness,
    selectPromotionAppliedLineItems(discount.keyId),
  );
  const name = getDiscountValueTag({
    discount: {
      discountType: discount.discountType === AmountCalcType.AMOUNT ? 'amount' : 'percentage',
      discountRate: discount.discountValue,
      discountAmount: discount.discountValue,
    },
    currencySymbol: business.currencySymbol,
  });

  const getAvailableCartItemUuids = useLatestCallback(() => {
    return getTargetCartItemsUuid({
      type: GetCartItemUuidsType.ALL,
    });
  });

  const handleSelectItem = async (targetCartItemExternalUuids: string[]) => {
    onAppliedItemsChange(discount.keyId, targetCartItemExternalUuids);
  };

  return (
    <NormalCard
      key={discount.keyId}
      isSelected
      name={name}
      description="one time discount"
      right={
        <Button variant="tertiary" onPress={() => onRemove(discount)}>
          <Heading size="6">{'Remove'}</Heading>
        </Button>
      }
    >
      <LineItemPromotionSelector
        className="moe-w-[300px] moe-ml-[28px] moe-mt-xxs"
        appliedLineItems={appliedLineItems}
        allItemOptionTitle="All items"
        getCartItems={getCartItems}
        getAvailableCartItemUuids={getAvailableCartItemUuids}
        onChange={handleSelectItem}
      />
    </NormalCard>
  );
};

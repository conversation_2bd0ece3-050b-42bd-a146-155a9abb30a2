import React from 'react';
import { useFloatableHost } from '../../../../../../../utils/hooks/useFloatableHost';
import { AddOneTimeDiscountModal, type AddOneTimeDiscountModalProps } from './AddOneTimeDiscountModal';

export const useAddOneTimeDiscountModal = () => {
  const { mountModal } = useFloatableHost();

  return async (options: AddOneTimeDiscountModalProps) => {
    const { promise, closeFloatable: closeModal } = mountModal(
      <AddOneTimeDiscountModal
        {...options}
        onClose={() => {
          closeModal?.();
        }}
        onConfirm={async (input) => {
          await options.onConfirm(input);
          closeModal?.();
        }}
      />,
    );
    return promise;
  };
};

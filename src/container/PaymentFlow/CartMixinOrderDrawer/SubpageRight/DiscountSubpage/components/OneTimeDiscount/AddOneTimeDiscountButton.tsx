import { type <PERSON><PERSON>oney, useSerialCallback } from '@moego/finance-utils';
import { type TypeofAmountCalcType } from '@moego/finance-web-kit';
import { MinorPlusOutlined } from '@moego/icons-react';
import { Button } from '@moego/ui';
import React, { memo } from 'react';
import { PaymentActionName } from '../../../../../../../utils/reportData/payment';
import { useInvoiceReinventReport } from '../../../../../hooks/useInvoiceReinvent.report';
import { useCartAppt } from '../../../../Cart/useCartAppt';
import { usePromotion } from '../../../../Promotions/usePromotion';
import { type AddOneTimeDiscountModalProps } from './AddOneTimeDiscountModal';
import { type TOneTimeDiscountValue } from './type';
import { useAddOneTimeDiscountModal } from './useAddOneTimeDiscountModal';

export interface AddOneTimeDiscountButtonProps {
  prevOneTimeDiscount?: TOneTimeDiscountValue;
  baseAmount: MoeMoney;
  onAdd: AddOneTimeDiscountModalProps['onConfirm'];
  appointmentId: string;
}

export const AddOneTimeDiscountButton = memo((props: AddOneTimeDiscountButtonProps) => {
  const { prevOneTimeDiscount, baseAmount, onAdd, appointmentId } = props;
  const reportPaymentData = useInvoiceReinventReport();
  const { getCartItems } = useCartAppt(appointmentId);
  const { getTargetCartItemsUuid } = usePromotion(appointmentId);

  const openAddOneTimeDiscountModal = useAddOneTimeDiscountModal();
  const handleAdd = useSerialCallback(async () => {
    reportPaymentData(PaymentActionName.AddOneTimeDiscount, {
      // orderId: order.id,
      // isExtraOrder: isExtraCharge,
    });
    await openAddOneTimeDiscountModal({
      showPreviewAmount: false,
      subTotalAmount: baseAmount.valueOf(),
      getCartItems,
      getTargetCartItemsUuid,
      discountInfo: {
        discountRate: prevOneTimeDiscount?.discountValue || 0,
        discountType: prevOneTimeDiscount?.discountType as TypeofAmountCalcType,
      },
      onConfirm: onAdd,
    });
  });

  return (
    <Button onPress={handleAdd} variant="tertiary" icon={<MinorPlusOutlined />} size="s">
      Add a one time discount
    </Button>
  );
});

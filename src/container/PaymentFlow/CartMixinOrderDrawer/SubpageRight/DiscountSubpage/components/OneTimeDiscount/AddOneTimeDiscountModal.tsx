import {
  AmountCalcType,
  calcAmountValue,
  getDiscountValue,
  normalizeDiscount,
  validatePositiveAmount,
  type TypeofAmountCalcType,
} from '@moego/finance-web-kit';
import { Form, Input, Modal, Select, useForm, useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../../../utils/hooks/useSerialCallback';
import { FormItemExtra } from '../../../../../../Calendar/Grooming/TakePaymentModal/components/CommonEditModal.style';
import { type useCartAppt } from '../../../../Cart/useCartAppt';
import { GetCartItemUuidsType, type GetTargetCartItemUuidList } from '../../../../Promotions/Promotion.types';
import { LineItemPromotionSelector } from '../../../components/LineItemPromotionSelector';
import { type TOneTimeDiscountValue } from './type';

export interface AddOneTimeDiscountModalProps {
  subTotalAmount: number;
  discountInfo?: { discountType: TypeofAmountCalcType; discountRate: number };
  onClose?: () => void;
  onConfirm: (data: {
    discountInfo: TOneTimeDiscountValue;
    cartItemUuids: string[];
  }) => void;
  showPreviewAmount?: boolean;
  getTargetCartItemsUuid: GetTargetCartItemUuidList;
  getCartItems: ReturnType<typeof useCartAppt>['getCartItems'];
}

export const AddOneTimeDiscountModal = memo<AddOneTimeDiscountModalProps>((props) => {
  const {
    discountInfo,
    subTotalAmount,
    showPreviewAmount = false,
    onClose,
    onConfirm,
    getCartItems,
    getTargetCartItemsUuid,
  } = props;
  const [business] = useSelector(selectCurrentBusiness);
  const initialValues = useMemo(() => {
    const normalized = normalizeDiscount(discountInfo);
    const discountValue = getDiscountValue(normalized);
    return {
      discountType: normalized.discountType,
      discountValue: discountValue ? discountValue + '' : '',
      cartItemUuids: [],
    };
  }, [discountInfo]);

  const form = useForm({
    mode: 'all',
    defaultValues: { ...initialValues },
  });

  const getAvailableCartItemUuids = useLatestCallback(() => {
    return getTargetCartItemsUuid({
      type: GetCartItemUuidsType.ALL,
    });
  });

  const handleConfirm = useSerialCallback(async () => {
    const isValid = await form.trigger();
    if (!isValid) return;

    const values = form.getValues();
    await onConfirm({
      discountInfo: {
        discountValue: +values.discountValue.trim(),
        discountType: values.discountType,
      },
      cartItemUuids: values.cartItemUuids.length ? values.cartItemUuids : getAvailableCartItemUuids(),
    });
  });

  const [discountType, discountValue, cartItemUuids] = useWatch({
    control: form.control,
    name: ['discountType', 'discountValue', 'cartItemUuids'],
  });

  const discountAmount = useMemo(() => {
    return calcAmountValue({
      type: discountType,
      value: discountValue,
      baseAmount: subTotalAmount,
    });
  }, [discountType, discountValue, subTotalAmount]);

  const options = useMemo(() => {
    return [
      {
        label: '%',
        value: AmountCalcType.PERCENTAGE,
      },
      {
        label: business.currencySymbol,
        value: AmountCalcType.AMOUNT,
      },
    ];
  }, [business.currencySymbol]);

  const isValid = form.formState.isValid;

  return (
    <Modal
      title={'Add discount'}
      isOpen
      onClose={onClose}
      autoCloseOnConfirm={false}
      confirmButtonProps={{
        isLoading: handleConfirm.isBusy(),
        isDisabled: !isValid || !discountValue,
      }}
      classNames={{
        container: 'moe-w-[480px]',
      }}
      confirmText={'Add'}
      onConfirm={handleConfirm}
    >
      <Form form={form} footer={null}>
        <div className={'moe-flex moe-gap-s'}>
          <div className={'moe-flex moe-flex-col moe-gap-xxs moe-flex-grow'}>
            <Form.Item
              name={'discountValue'}
              label="Discount"
              rules={{
                required: true,
                validate: (value) => {
                  return validatePositiveAmount({
                    value,
                    name: 'discount',
                    calcType: discountType,
                    maxAmount: subTotalAmount,
                  });
                },
              }}
            >
              <Input isRequired />
            </Form.Item>
            <Condition if={discountType === AmountCalcType.PERCENTAGE && !!discountValue && showPreviewAmount}>
              <FormItemExtra>
                Discount amount:&nbsp;
                {discountAmount ? <span className="value-price">{business.formatAmount(discountAmount)}</span> : '-'}
              </FormItemExtra>
            </Condition>
          </div>
          <div className={'moe-mt-m'}>
            <Form.Item name="discountType">
              <Select className="moe-w-[120px]">
                {options.map((option) => (
                  <Select.Item key={option.value} value={option.value} title={option.label} />
                ))}
              </Select>
            </Form.Item>
          </div>
        </div>
        <Form.Item name="cartItemUuids">
          <LineItemPromotionSelector
            label="Select applicable item(s)"
            allItemOptionTitle="All items"
            appliedLineItems={cartItemUuids}
            getAvailableCartItemUuids={getAvailableCartItemUuids}
            getCartItems={getCartItems}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
});

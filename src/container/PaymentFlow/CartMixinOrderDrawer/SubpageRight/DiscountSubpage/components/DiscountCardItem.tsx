import { type Coupon } from '@moego/api-web/moego/models/promotion/v1/coupon';
import { <PERSON><PERSON>, Heading } from '@moego/ui';
import { useSelector, useStore } from 'amos';
import React, { memo } from 'react';
import { selectDiscount } from '../../../../../../store/discount/discount.selectors';
import { CouponRecord } from '../../../../../../store/PaymentFlow/cart.boxes';
import {
  selectCoupon,
  selectPromotionAppliedLineItems,
  selectSelectedPromotion,
} from '../../../../../../store/PaymentFlow/cart.selectors';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { type useCartAppt } from '../../../Cart/useCartAppt';
import { useCartChangesPreview } from '../../../Cart/useCartChangesPreview';
import {
  GetCartItemUuidsType,
  PromotionType,
  type GetTargetCartItemUuidList,
} from '../../../Promotions/Promotion.types';
import { LineItemPromotionSelector } from '../../components/LineItemPromotionSelector';
import { NormalCard } from './NormalCard';

interface DiscountCardProps {
  coupon: Coupon;
  appointmentId: string;
  getTargetCartItemsUuid: GetTargetCartItemUuidList;
  getCartItems: ReturnType<typeof useCartAppt>['getCartItems'];
}

export const DiscountCardItem = memo<DiscountCardProps>((props) => {
  const { coupon, appointmentId, getTargetCartItemsUuid, getCartItems } = props;
  const couponId = CouponRecord.createKeyId(coupon.source);
  const store = useStore();
  const [discount, { discounts: selectedList }, appliedLineItems] = useSelector(
    selectDiscount(coupon.source.id),
    selectSelectedPromotion(appointmentId),
    selectPromotionAppliedLineItems(couponId),
  );

  const isSelected = selectedList.some((s) => couponId === s);
  const actionText = isSelected ? 'Remove' : 'Apply';
  const preview = useCartChangesPreview();

  const startPreview = useLatestCallback(
    async (selectedIdList: string[], currentCouponTargetCartItemExternalUuids: string[]) => {
      await preview({
        appliedPromotions: {
          type: PromotionType.Discount,
          promotions: selectedIdList.map((id) => {
            const { source, restrictions } = store.select(selectCoupon(id));
            let uuidList: string[] = [];
            if (id === couponId) {
              uuidList = currentCouponTargetCartItemExternalUuids.length
                ? currentCouponTargetCartItemExternalUuids
                : getTargetCartItemsUuid({
                    type: GetCartItemUuidsType.AVAILABLE,
                    targets: restrictions.targets,
                  });
            } else {
              uuidList = getTargetCartItemsUuid({
                type: GetCartItemUuidsType.SELECTED,
                cacheKeyId: id,
              });
            }

            return {
              cartItemExternalUuids: uuidList,
              couponSource: {
                id: source.id,
                type: source.type,
                discount: {
                  id: source.discount!.id,
                },
              },
            };
          }),
        },
      });
    },
  );

  const handleToggle = async () => {
    const newValIds = isSelected ? selectedList.filter((id) => id !== couponId) : selectedList.concat(couponId);
    await startPreview(newValIds, []);
  };

  const handleSelectItem = async (targetCartItemExternalUuids: string[]) => {
    await startPreview(selectedList, targetCartItemExternalUuids);
  };

  const getAvailableCartItemUuids = useLatestCallback(() => {
    return getTargetCartItemsUuid({
      type: GetCartItemUuidsType.AVAILABLE,
      targets: coupon.restrictions.targets,
    });
  });

  return (
    <NormalCard
      name={coupon.name}
      description={discount?.description}
      discount={discount}
      isSelected={isSelected}
      right={
        <Button variant="tertiary" onPress={handleToggle}>
          <Heading size="6">{actionText}</Heading>
        </Button>
      }
    >
      {isSelected && (
        <LineItemPromotionSelector
          className="moe-w-[300px] moe-ml-[28px] moe-mt-xxs"
          appliedLineItems={appliedLineItems}
          getCartItems={getCartItems}
          getAvailableCartItemUuids={getAvailableCartItemUuids}
          onChange={handleSelectItem}
        />
      )}
    </NormalCard>
  );
});

import { useSerialCallback } from '@moego/finance-utils';
import { Select } from '@moego/ui';
import { groupBy } from 'lodash';
import React, { memo, useMemo, useRef } from 'react';
import { type useCartAppt } from '../../Cart/useCartAppt';

interface LineItemPromotionSelectorProps {
  isMultiple?: boolean;
  getCartItems: ReturnType<typeof useCartAppt>['getCartItems'];
  getAvailableCartItemUuids: () => string[];
  appliedLineItems: string[];
  onChange?: (newTargetExternalUuids: string[]) => Promise<void> | void;
  label?: string;
  className?: string;
  allItemOptionTitle?: string;
}

export const SELECT_ALL_OPTION = {
  value: 'all',
  title: 'All applicable items',
};

export const LineItemPromotionSelector = memo<LineItemPromotionSelectorProps>((props) => {
  const {
    getAvailableCartItemUuids,
    label = '',
    getCartItems,
    appliedLineItems,
    onChange,
    className,
    allItemOptionTitle = SELECT_ALL_OPTION.title,
  } = props;

  const lastSelectRef = useRef<string>('');
  const { serviceItems, productItems, surchargeItems } = getCartItems();
  const { serviceByPet, serviceOptions, surchargeOptions, productOptions, optionsLength, allAvailableCartItemUuids } =
    useMemo(() => {
      const allAvailableCartItemUuids = getAvailableCartItemUuids();
      const filterOptions = <T extends { externalId?: string; externalUuid?: string }>(
        items: T[],
        targetKey: 'externalId' | 'externalUuid',
      ): T[] => {
        return items.filter((v) => !!v[targetKey] && allAvailableCartItemUuids.includes(v[targetKey] as string));
      };

      const serviceOptions = filterOptions(serviceItems, 'externalId');
      const serviceByPet = Object.entries(groupBy(serviceOptions, 'pet.id')).map(([_petId, options]) => {
        return {
          pet: options[0].pet,
          options,
        };
      });
      const surchargeOptions = filterOptions(surchargeItems, 'externalId');
      const productOptions = filterOptions(productItems, 'externalUuid');

      return {
        serviceByPet,
        serviceOptions,
        surchargeOptions,
        productOptions,
        allAvailableCartItemUuids,
        optionsLength: serviceOptions.length + surchargeOptions.length + productOptions.length,
      };
    }, [getAvailableCartItemUuids, serviceItems, productItems, surchargeItems]);

  const selectedValue = useMemo(() => {
    if (!appliedLineItems.length) {
      return SELECT_ALL_OPTION.value;
    }

    // 默认选中全部，如果选了全部，但实际命中多个(子集，非全集)，也当做全选展示
    if (
      (appliedLineItems.length > 1 || appliedLineItems.length === optionsLength) &&
      (!lastSelectRef.current || lastSelectRef.current === SELECT_ALL_OPTION.value)
    ) {
      return SELECT_ALL_OPTION.value;
    }

    // currently only support one item or all items, not support multiple items, so return the first one
    return appliedLineItems[0];
  }, [appliedLineItems, optionsLength]);

  // currently only support one item selection
  const handleSelectItem = useSerialCallback(async (value: string | null) => {
    if (value === selectedValue || !value) {
      return;
    }

    lastSelectRef.current = value;
    await onChange?.(value === SELECT_ALL_OPTION.value ? allAvailableCartItemUuids : [value]);
  });

  const renderOptions = () => {
    return (
      <>
        {serviceByPet.length
          ? serviceByPet.map(({ pet, options }) => {
              return (
                <Select.Section key={pet.id} value={pet.id} title={`${pet.petName}(${pet.breed})`} items={options}>
                  {(item) => <Select.Item key={item.externalId} value={item.externalId} title={item.name} />}
                </Select.Section>
              );
            })
          : null}
        {surchargeOptions.length ? (
          <Select.Section key="surcharge" title="Fees" items={surchargeOptions}>
            {(item) => <Select.Item key={item.externalId} value={item.externalId} title={item.name} />}
          </Select.Section>
        ) : null}
        {productOptions.length ? (
          <Select.Section key="product" title="Products" items={productOptions}>
            {(item) => <Select.Item key={item.externalUuid} value={item.externalUuid} title={item.name} />}
          </Select.Section>
        ) : null}
      </>
    );
  };

  return (
    <Select
      className={className}
      value={selectedValue}
      onChange={handleSelectItem}
      label={label}
      isRequired
      formatOptionLabel={(item) => {
        const service = serviceOptions.find((v) => v.externalId === item.key);
        const title = item.props?.title ?? '';
        if (service) {
          return `${service.pet.petName}: ${title}`;
        }

        return title;
      }}
    >
      <Select.Section>
        <Select.Item key={SELECT_ALL_OPTION.value} title={allItemOptionTitle} />
      </Select.Section>
      {renderOptions()}
    </Select>
  );
});

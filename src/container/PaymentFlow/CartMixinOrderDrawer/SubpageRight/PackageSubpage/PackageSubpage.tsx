import { CouponSearchConditionSourceType, type Coupon } from '@moego/api-web/moego/models/promotion/v1/coupon';
import { MajorPackageOutlined } from '@moego/icons-react';
import { Button, Empty, Heading, Markup, Spin, Text, cn } from '@moego/ui';
import { useSelector, useStore } from 'amos';
import React, { memo } from 'react';
import { useAsync } from 'react-use';
import { Condition } from '../../../../../components/Condition';
import { CouponRecord } from '../../../../../store/PaymentFlow/cart.boxes';
import {
  selectCartPackage,
  selectCoupon,
  selectCustomerPackage,
  selectSelectedPromotion,
} from '../../../../../store/PaymentFlow/cart.selectors';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { dateMessageToString } from '../../../../../utils/utils';
import { ColumnsBridgeEvent } from '../../Columns/ColumnsBridge';
import { GetCartItemUuidsType, PromotionType } from '../../Promotions/Promotion.types';
import { usePromotion } from '../../Promotions/usePromotion';
import { LayoutRightPageFooter } from '../../components/Layout/LayoutRightPageFooter';
import { LayoutRightPageHeader } from '../../components/Layout/LayoutRightPageHeader';
import { LayoutSubpage } from '../../components/Layout/LayoutSubpage';
import { LayoutSubpageScrollBody } from '../../components/Layout/LayoutSubpageScrollBody';
import { useDrawerScopeContext } from '../../context/DrawerScopeContext';
import { ContinueToPayButton } from '../components/ContinueToPay';

const PackageCard: React.FC<{
  subject: [string, Coupon[]];
  isAvailable?: boolean;
  appointmentId: string;
  customerId: string;
}> = memo((props) => {
  const { subject, isAvailable, appointmentId, customerId } = props;
  const { getTargetCartItemsUuid } = usePromotion(appointmentId);
  const [key, coupons] = subject;
  const store = useStore();
  const { columnsBridge } = useDrawerScopeContext();

  const [{ packageDetails, packageName, startDate, expirationDate }, business, { packages: selectedList }] =
    useSelector(
      selectCustomerPackage(customerId, key),
      selectCurrentBusiness(),
      selectSelectedPromotion(appointmentId),
    );

  const isSelected = selectedList.some((s) => coupons.some((c) => CouponRecord.createKeyId(c.source) === s));
  const actionText = isSelected ? 'Remove' : 'Apply';

  const handleToggle = useSerialCallback(async () => {
    const newVal = coupons.map((coupon) => CouponRecord.createKeyId(coupon.source));
    const newList = isSelected ? selectedList.filter((id) => !newVal.includes(id)) : selectedList.concat(newVal);
    await columnsBridge.runAsyncBail(ColumnsBridgeEvent.OnCartChanges, {
      appliedPromotions: {
        type: PromotionType.Package,
        promotions: newList.map((id) => {
          const { source, restrictions } = store.select(selectCoupon(id));
          return {
            // currently no applied to line item, so use all available targets
            cartItemExternalUuids: getTargetCartItemsUuid({
              type: GetCartItemUuidsType.AVAILABLE,
              targets: restrictions.targets,
            }),
            couponSource: {
              id: source.id,
              type: source.type,
              package: {
                id: source.package!.id,
              },
            },
          };
        }),
      },
    });
  });

  return (
    <div
      className={cn(
        'moe-rounded-[16px] moe-border moe-border-solid moe-mb-m',
        isAvailable && isSelected ? 'moe-border-brand moe-bg-brand-subtle' : 'moe-border-divider moe-bg-white',
      )}
    >
      <div className="moe-flex moe-items-center moe-gap-xs moe-p-spacing-s">
        <MajorPackageOutlined className={cn('moe-self-start', isAvailable ? 'moe-text-brand' : 'moe-text-disabled')} />
        <div className="moe-flex-1 moe-flex moe-flex-col moe-gap-xs">
          <div className="moe-flex moe-justify-between moe-items-center">
            <div className="moe-flex moe-gap-xs moe-items-center">
              <div className="moe-flex moe-flex-col moe-gap-xxs">
                <Markup variant="regular-short">{packageName}</Markup>
                <Text variant="small" className="moe-text-tertiary">
                  {business.formatDate(dateMessageToString(startDate))}
                  {expirationDate ? `- ${business.formatDate(dateMessageToString(expirationDate))}` : ''}
                </Text>
              </div>
            </div>
          </div>

          <div className="moe-flex moe-flex-col moe-gap-xxs">
            {packageDetails.map((item, index) => {
              return (
                <div key={index}>
                  <Text variant="regular" className="moe-text-primary">
                    {item.services.map((service) => service.name).join(', ')}
                    &nbsp;
                    {/* {'('}
                    <Text as="span" variant="small" className="moe-text-brand">
                      {item.totalQuantity - item.remainingQuantity}&nbsp;
                    </Text>
                    applied,&nbsp;
                    <Text as="span" variant="small" className="moe-text-success">
                      {item.remainingQuantity}&nbsp;
                    </Text>
                    available
                    {')'} */}
                  </Text>
                </div>
              );
            })}
          </div>
        </div>
        <Button
          variant="tertiary"
          className={cn('moe-min-w-[70px]', isAvailable ? 'moe-text-primary' : 'moe-text-disabled')}
          onPress={handleToggle}
          isDisabled={!isAvailable}
        >
          {actionText}
        </Button>
      </div>
    </div>
  );
});

// 包列表区块组件
const PackageSection: React.FC<{
  title: string;
  subjects: [string, Coupon[]][];
  isAvailable: boolean;
  appointmentId: string;
  customerId: string;
}> = memo((props) => {
  const { title, subjects, isAvailable, appointmentId, customerId } = props;
  return (
    <div className="moe-mb-l">
      <Heading size="5" className="moe-mb-m">
        {title}
      </Heading>
      {subjects.map((subject, index) => (
        <PackageCard
          appointmentId={appointmentId}
          customerId={customerId}
          key={index}
          subject={subject}
          isAvailable={isAvailable}
        />
      ))}
    </div>
  );
});

export const PackageSubpage = memo((props: { appointmentId: string; customerId: string }) => {
  const { appointmentId, customerId } = props;
  const { fetchData } = usePromotion(appointmentId);
  const [packageInfo] = useSelector(selectCartPackage(appointmentId));

  const availableSubjects = Object.entries(packageInfo.availableSubjects);
  const unavailableSubjects = Object.entries(packageInfo.unavailableSubjects);

  const { loading } = useAsync(async () => {
    await fetchData([CouponSearchConditionSourceType.PACKAGE]);
  }, [fetchData]);

  const isShowEmpty = !availableSubjects.length && !unavailableSubjects.length && !loading;

  return (
    <LayoutSubpage>
      <LayoutRightPageHeader title={'Package'} />
      <LayoutSubpageScrollBody>
        <Spin isLoading={loading} className="moe-min-h-[100px]">
          <Condition if={isShowEmpty}>
            <Empty className="moe-mt-8px-500" title="No package found" description="" />
          </Condition>

          {availableSubjects.length > 0 && (
            <PackageSection
              appointmentId={appointmentId}
              customerId={customerId}
              title={`${availableSubjects.length} available package(s)`}
              subjects={availableSubjects}
              isAvailable={true}
            />
          )}

          {unavailableSubjects.length > 0 && (
            <PackageSection
              appointmentId={appointmentId}
              customerId={customerId}
              title={`${unavailableSubjects.length} unavailable package(s)`}
              subjects={unavailableSubjects}
              isAvailable={false}
            />
          )}
        </Spin>
      </LayoutSubpageScrollBody>
      <LayoutRightPageFooter>
        <ContinueToPayButton />
      </LayoutRightPageFooter>
    </LayoutSubpage>
  );
});

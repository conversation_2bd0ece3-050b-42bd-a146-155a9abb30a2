import { CouponSearchConditionSourceType, type Coupon } from '@moego/api-web/moego/models/promotion/v1/coupon';
import { useSerialCallback } from '@moego/finance-utils';
import { Button, Empty, Heading, Markup, Spin, Text, cn } from '@moego/ui';
import { useSelector, useStore } from 'amos';
import React, { memo } from 'react';
import { useAsync } from 'react-use';
import IconMembershipBgSvg from '../../../../../assets/icon/membership-bg.svg';
import { Condition } from '../../../../../components/Condition';
import { CouponRecord } from '../../../../../store/PaymentFlow/cart.boxes';
import {
  selectCartMemberShip,
  selectCoupon,
  selectSelectedPromotion,
} from '../../../../../store/PaymentFlow/cart.selectors';
import { selectMembership } from '../../../../../store/membership/membership.selectors';
import { useCartChangesPreview } from '../../Cart/useCartChangesPreview';
import { GetCartItemUuidsType, PromotionType } from '../../Promotions/Promotion.types';
import { usePromotion } from '../../Promotions/usePromotion';
import { LayoutRightPageFooter } from '../../components/Layout/LayoutRightPageFooter';
import { LayoutRightPageHeader } from '../../components/Layout/LayoutRightPageHeader';
import { LayoutSubpage } from '../../components/Layout/LayoutSubpage';
import { LayoutSubpageScrollBody } from '../../components/Layout/LayoutSubpageScrollBody';
import { ContinueToPayButton } from '../components/ContinueToPay';

const MembershipCard: React.FC<{
  subject: [string, Coupon[]];
  isAvailable?: boolean;
  appointmentId: string;
}> = memo((props) => {
  const { appointmentId } = props;
  const { subject, isAvailable } = props;
  const [key, coupons] = subject;
  const store = useStore();
  const [membership, { memberships: selectedList }] = useSelector(
    selectMembership(key),
    selectSelectedPromotion(appointmentId),
  );
  const { getTargetCartItemsUuid } = usePromotion(appointmentId);

  const isSelected = selectedList.some((s) => coupons.some((c) => CouponRecord.createKeyId(c.source) === s));
  const actionText = isSelected ? 'Remove' : 'Apply';

  const preview = useCartChangesPreview();
  const handleToggle = useSerialCallback(async () => {
    const newVal = coupons.map((coupon) => CouponRecord.createKeyId(coupon.source));
    const newPromotions = isSelected ? selectedList.filter((id) => !newVal.includes(id)) : selectedList.concat(newVal);
    await preview({
      appliedPromotions: {
        type: PromotionType.Membership,
        promotions: newPromotions.map((id) => {
          const { source, restrictions } = store.select(selectCoupon(id));

          return {
            // no applied to line item, so use all available targets
            cartItemExternalUuids: getTargetCartItemsUuid({
              type: GetCartItemUuidsType.AVAILABLE,
              targets: restrictions.targets,
            }),
            couponSource: {
              id: source.id,
              type: source.type,
              membership: {
                id: source.membership!.id,
              },
            },
          };
        }),
      },
    });
  });

  return (
    <div
      className={cn(
        'moe-flex moe-justify-between moe-gap-m moe-mb-m moe-rounded-[16px] moe-border-[1.5px] moe-border-solid moe-p-spacing-m moe-bg-no-repeat moe-bg-right-top',
        isAvailable && isSelected ? 'moe-border-brand moe-bg-brand-subtle' : 'moe-border-button moe-bg-white',
      )}
      style={{ backgroundImage: `url(${IconMembershipBgSvg})` }}
    >
      <div className="moe-flex-1">
        <div
          className={cn(
            'moe-pl-s moe-border-solid moe-border-l-4',
            isAvailable ? 'moe-border-l-icon-warning' : 'moe-border-[#E6E6E6]',
          )}
        >
          <Markup variant="regular-short">{membership.name}</Markup>
          <Heading size="5">
            {membership.price}&nbsp;/&nbsp;
            <Text variant="regular" as="span">
              {membership.billingCycleName()}
            </Text>
          </Heading>
        </div>
      </div>

      <div className="moe-flex moe-items-center moe-justify-center">
        <Button
          variant="tertiary"
          className={!isAvailable ? 'moe-text-disabled' : ''}
          isDisabled={!isAvailable}
          onPress={handleToggle}
        >
          {actionText}
        </Button>
      </div>
    </div>
  );
});

// 会员列表区块组件
const MembershipSection: React.FC<{
  title: string;
  subjects: [string, Coupon[]][];
  isAvailable?: boolean;
  appointmentId: string;
}> = memo((props) => {
  const { title, subjects, isAvailable, appointmentId } = props;
  return (
    <div className={cn('moe-px-spacing-l', !isAvailable ? '' : 'moe-mb-m')}>
      <Heading className="moe-mb-m" size="5">
        {title}
      </Heading>
      {subjects.map((subject, index) => (
        <MembershipCard appointmentId={appointmentId} key={index} subject={subject} isAvailable={isAvailable} />
      ))}
    </div>
  );
});

export const MembershipSubpage = memo((props: { appointmentId: string }) => {
  const { appointmentId } = props;
  const { fetchData } = usePromotion(appointmentId);
  const [membership] = useSelector(selectCartMemberShip(appointmentId));

  const availableSubjects = Object.entries(membership.availableSubjects);
  const unavailableSubjects = Object.entries(membership.unavailableSubjects);

  const { loading } = useAsync(async () => {
    await fetchData([CouponSearchConditionSourceType.MEMBERSHIP]);
  }, [fetchData]);

  const isShowEmpty = !availableSubjects.length && !unavailableSubjects.length && !loading;

  return (
    <LayoutSubpage>
      <LayoutRightPageHeader title={'Membership'} />
      <LayoutSubpageScrollBody>
        <Spin isLoading={loading} className="moe-min-h-[100px]">
          <Condition if={isShowEmpty}>
            <Empty className="moe-mt-8px-500" title="No membership found" description="" />
          </Condition>

          <Condition if={availableSubjects.length > 0}>
            <MembershipSection
              appointmentId={appointmentId}
              title={`${availableSubjects.length} available membership(s)'`}
              subjects={availableSubjects}
              isAvailable={true}
            />
          </Condition>

          <Condition if={unavailableSubjects.length > 0}>
            <MembershipSection
              appointmentId={appointmentId}
              title={`${unavailableSubjects.length} unavailable membership(s)'`}
              subjects={unavailableSubjects}
              isAvailable={false}
            />
          </Condition>
        </Spin>
      </LayoutSubpageScrollBody>
      <LayoutRightPageFooter>
        <ContinueToPayButton />
      </LayoutRightPageFooter>
    </LayoutSubpage>
  );
});

import { action } from 'amos';
import { http } from '../../../../../../../../middleware/api';
import { getProductExternalUuid } from '../../../../../Cart/Cart.utils';
import { productItemInfoMapBox, productSelectedBox, type TProductSelected } from './orderProducts.boxes';

export const getProductItemDetail = action(async (dispatch, _select, productId: number) => {
  const { data } = await http.open('GET/retail/product/detail', { productId });
  dispatch(productItemInfoMapBox.mergeItem(productId, data));
  return data;
});

export const addProduct = action(async (dispatch, select, id: number) => {
  dispatch(
    productSelectedBox.setState((preState) => {
      const pre = preState.find((item) => item.id === id);
      if (!pre) {
        const { retailPrice, specialPrice, name } = select(productItemInfoMapBox.mustGetItem(id));
        // important: set externalUuid
        return preState.concat({
          id,
          externalUuid: getProductExternalUuid(id),
          price: specialPrice || retailPrice,
          quantity: 1,
          name,
        });
      }
      return preState.map((item) => {
        if (item.id === id) {
          return { ...item, quantity: item.quantity + 1 };
        }
        return item;
      });
    }),
  );
  dispatch([productItemInfoMapBox.updateItem(id, (pre) => pre.merge({ stock: pre.stock - 1 }))]);
});

/**
 * TODO(yueyue): update product 的处理可能需要优化，改了价格，是需要重新计算优惠的
 */
export const updateProduct = action(
  async (dispatch, _select, newVal: Omit<TProductSelected, 'externalUuid' | 'name'>) => {
    dispatch(
      productSelectedBox.setState((preState) =>
        preState.map((v) => {
          if (v.id === newVal.id) {
            return { ...v, ...newVal };
          }
          return v;
        }),
      ),
    );
    await dispatch(getProductItemDetail(newVal.id));
  },
);

export const deleteProduct = action(async (dispatch, _select, id: number) => {
  dispatch(productSelectedBox.setState((preState) => preState.filter((item) => item.id !== id)));
  await dispatch(getProductItemDetail(id));
});

export const resetProduct = action(async (dispatch) => {
  dispatch(productSelectedBox.setState([]));
});

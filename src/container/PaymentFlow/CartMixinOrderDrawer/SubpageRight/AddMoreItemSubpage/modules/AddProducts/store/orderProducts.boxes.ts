import { Record } from 'immutable';
import { createRecordMapBox } from '../../../../../../../../store/utils/RecordMap';
import { createBox } from '../../../../../../../../store/utils/utils';
import { type ProductItemInfo } from '../AddProducts.types';

export class ProductItemInfoRecord extends Record<ProductItemInfo>({
  id: 0,
  barcode: '',
  businessId: 0,
  categoryId: 0,
  categoryName: '',
  createTime: '',
  deleted: false,
  description: '',
  enableStaffCommission: false,
  imageUrl: '',
  lastPurchasedTime: '',
  name: '',
  retailPrice: 0,
  sku: '',
  specialPrice: 0,
  stock: 0,
  stockEvents: [],
  supplierId: 0,
  supplierName: '',
  supplyPrice: 0,
  taxId: 0,
  taxRate: 0,
  updateTime: '',
}) {}

export const productItemInfoMapBox = createRecordMapBox(
  'order/product/item/info/invoice/v4',
  ProductItemInfoRecord,
  'id',
);

export type TProductSelected = {
  id: number;
  quantity: number;
  price: number;
  taxId?: number;
  staffId?: string;
  externalUuid: string;
  name: string;
};
export const productSelectedBox = createBox<TProductSelected[]>('order/product/selected', []);

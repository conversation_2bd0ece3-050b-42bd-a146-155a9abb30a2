import { OrderSourceType } from '@moego/api-web/moego/models/order/v1/order_enums';
import { firstNorm<PERSON>, <PERSON><PERSON><PERSON> } from '@moego/finance-utils';
import { useChargeCapabilities } from '@moego/finance-web-kit';
import { cn, Drawer, Spin } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { ErrorBoundary } from '../../../components/ErrorBoundary/ErrorBoundary';
import { ApptTestIds } from '../../../config/testIds/apptDrawer';
import { selectPaymentSettingInfo } from '../../../store/payment/payment.selectors';
import { ID_LOADING, isNormal } from '../../../store/utils/identifier';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { PaymentDrawerCloseType } from '../interface';
import { type CartMixinOrderDrawerProps } from './CartMixinOrderDrawer.types';
import { ColumnsBridgeEvent, useColumnsBridge } from './Columns/ColumnsBridge';
import { LeftColumnCoordinator } from './Columns/LeftColumnCoordinator';
import { RightColumnCoordinator } from './Columns/RightColumnCoordinator';
import { RightColumnPage } from './Columns/RightColumnPage';
import { useRightColumnPageRouter } from './Columns/useSubpageRouter';
import { DrawerScopeContextProvider } from './context/DrawerScopeContext';
import { useBridgeEventData } from './hooks/use-bridge-hooks';
import { useDelayCloseDrawerListener } from './hooks/useDelayCloseDrawerListener';
import { useExistingOrder } from './hooks/useExistingOrder';
import { usePreviewOrder } from './hooks/usePreviewOrder';
import { useCustomer } from './useCustomer';

export const CartMixinOrderDrawer = memo<CartMixinOrderDrawerProps>(function CartMixinOrderDrawer(props) {
  const { orderId: propsOrderId, chargeType, appointmentId: sourceIdProps = ID_LOADING } = props;
  const columnsBridge = useColumnsBridge();
  const [paymentSetting] = useSelector(selectPaymentSettingInfo());
  const { customer, loading: customerLoading } = useCustomer({ sourceId: sourceIdProps, orderId: propsOrderId });
  const rightColumnPageRouter = useRightColumnPageRouter(RightColumnPage.DEFAULT);
  // NOTE: preview 初始是在 subpage 下面，需要判断 isStartedWithPreview 作为 isInPreview 的初始值
  // 如果直接设置为 false，会导致页面状态抖动
  // ATTENTION: 需要非常仔细，避免导致 loading 阻断
  const isBulkPayment = false; // ATTENTION(yueyue): bulk payment 模式下，没有 orderId，但也不是 preview
  const isStartedWithPreview = !isNormal(propsOrderId) && !isBulkPayment;
  const {
    isInPreview,
    previewedOrder,
    orderAllDetailView: previewedOrderAllDetailView,
  } = usePreviewOrder({ columnsBridge, initialIsInPreview: isStartedWithPreview });
  const {
    order: existingOrder,
    orderAllDetailView: existingOrderAllDetailView,
    isLoading: isExistingOrderLoading,
    isInitCompleted,
  } = useExistingOrder({
    columnsBridge,
    orderId: propsOrderId?.toString(),
  });

  const { mergedOrder, mergedOrderAllDetailView, mergedSourceId } = useMemo(() => {
    const order = existingOrder ?? previewedOrder;
    return {
      mergedOrder: order,
      mergedOrderAllDetailView: existingOrderAllDetailView ?? previewedOrderAllDetailView,
      mergedSourceId: firstNormal(order.sourceId, sourceIdProps),
    };
  }, [existingOrder, previewedOrder, existingOrderAllDetailView, previewedOrderAllDetailView, sourceIdProps]);

  const capabilities = useChargeCapabilities(chargeType, {
    skipTipping: !!paymentSetting.skipTipping,
    hasAppliedTips: !mergedOrder.tipsAmount.isZero(),
  });

  const handleClose = useLatestCallback(props.onClose);
  const { isClosing } = useDelayCloseDrawerListener({
    closeDrawer: handleClose,
    columnsBridge,
  });
  const ready = isInitCompleted && !customerLoading;
  const globalLoading = isInPreview || isExistingOrderLoading || isClosing || !ready;
  const contextValue = useMemo(
    () => ({
      columnsBridge,
      rightColumnPageRouter,
      chargeType,
      isStartedWithPreview,
      customer,
      sourceType: OrderSourceType.APPOINTMENT,
      sourceId: mergedSourceId.toString(),
      closeDrawer: (type: PaymentDrawerCloseType) => handleClose(type),
      globalLoading,
    }),
    [
      columnsBridge,
      rightColumnPageRouter,
      chargeType,
      isStartedWithPreview,
      customer,
      mergedSourceId,
      handleClose,
      globalLoading,
    ],
  );

  const shouldChargeAmount = useBridgeEventData(
    columnsBridge,
    ColumnsBridgeEvent.OnChargeAmountChange,
    MoeMoney.empty(),
  );

  return (
    <Drawer
      closeButtonProps={{
        'data-testid': ApptTestIds.ApptPaymentPopupCloseBtn,
      }}
      isOpen
      size="l"
      title={null}
      header={null}
      footer={null}
      classNames={{
        container: 'moe-max-h-none moe-w-[1200px]',
        body: cn('moe-flex moe-p-0 moe-rounded-8px-300 moe-border moe-justify-center moe-items-center', {
          'moe-opacity-50': globalLoading,
        }),
      }}
      onClose={() => {
        handleClose(PaymentDrawerCloseType.Cancel);
      }}
      shouldCloseOnInteractOutside={shouldCloseOnInteractOutside}
    >
      <ErrorBoundary>
        <DrawerScopeContextProvider value={contextValue}>
          <Spin classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }} isLoading={!ready}>
            {ready ? (
              <div className={'moe-flex moe-w-full moe-h-full'}>
                <LeftColumnCoordinator
                  columnsBridge={columnsBridge}
                  orderAllDetailView={mergedOrderAllDetailView}
                  order={mergedOrder}
                  capabilities={capabilities}
                  chargeType={chargeType}
                />
                <div className={'moe-w-px moe-bg-gray-100'}></div>
                <RightColumnCoordinator
                  shouldChargeAmount={shouldChargeAmount}
                  order={mergedOrder}
                  capabilities={capabilities}
                />
              </div>
            ) : null}
          </Spin>
        </DrawerScopeContextProvider>
      </ErrorBoundary>
    </Drawer>
  );
});

function shouldCloseOnInteractOutside(element: Element): boolean {
  return element.getAttribute('data-slot') === 'modal-base';
}

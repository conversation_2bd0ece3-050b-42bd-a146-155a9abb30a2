import { useLatestCallback } from '@moego/finance-utils';
import { useDispatch, useStore } from 'amos';
import { getListPetServices } from '../../../../store/PaymentFlow/cart.action';
import { selectCartList, selectSelectedCartDetailList } from '../../../../store/PaymentFlow/cart.selectors';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { getTaxList } from '../../../../store/business/tax.actions';
import { taxMapBox } from '../../../../store/business/tax.boxes';
import { serviceChargeMapBox } from '../../../../store/service/service.boxes';
import { isNormal } from '../../../../store/utils/identifier';
import { type LatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { getAppointment } from '../../../Appt/store/appt.api';
import { resetProduct } from '../SubpageRight/AddMoreItemSubpage/modules/AddProducts/store/orderProducts.actions';
import {
  productItemInfoMapBox,
  productSelectedBox,
} from '../SubpageRight/AddMoreItemSubpage/modules/AddProducts/store/orderProducts.boxes';
import { type CartItemsByType } from './Cart.types';
import {
  convertCartItemsToCreateOrderItems,
  convertFeesItemsToCreateOrderItems,
  convertProductToCreateOrderItem,
  defaultTax,
} from './Cart.utils';
import { useCartChangesPreview } from './useCartChangesPreview';

export const useCartAppt = (appointmentId: string) => {
  const dispatch = useDispatch();
  const store = useStore();
  const preview = useCartChangesPreview();

  const getCartItems: LatestCallback<() => CartItemsByType> = useLatestCallback(() => {
    const serviceItems = store.select(selectSelectedCartDetailList(appointmentId));
    const productItems = store.select(productSelectedBox);
    const { surcharges: surchargeItems } = store.select(selectCartList(appointmentId));
    return {
      serviceItems,
      productItems,
      surchargeItems,
    };
  });

  const resolveItems = () => {
    const { serviceItems, productItems, surchargeItems } = getCartItems();
    const services = convertCartItemsToCreateOrderItems(serviceItems);
    const servicesCharges = convertFeesItemsToCreateOrderItems(
      surchargeItems.map((s) => {
        const { taxId } = store.select(serviceChargeMapBox.mustGetItem(s.serviceChargeId));
        const tax = store.select(taxMapBox.mustGetItem(taxId));
        return {
          ...s,
          tax: isNormal(taxId)
            ? {
                id: String(tax.id),
                name: tax.taxName,
                rate: String(tax.taxRate),
              }
            : { ...defaultTax },
        };
      }),
    );
    const products = productItems.map((p) => {
      const productInfo = store.select(productItemInfoMapBox.mustGetItem(p.id));
      const tax = store.select(taxMapBox.mustGetItem(productInfo.taxId));
      const business = store.select(selectCurrentBusiness);
      return convertProductToCreateOrderItem(productInfo, {
        externalUuid: p.externalUuid,
        staffId: p.staffId || '0',
        petId: '0',
        quantity: p.quantity,
        currencyCode: business.currencyCode,
        tax: isNormal(productInfo.taxId)
          ? { id: String(tax.id), name: tax.taxName, rate: String(tax.taxRate) }
          : { ...defaultTax },
      });
    });

    const allItems = services.concat(servicesCharges).concat(products);

    return {
      allItems,
    };
  };

  const refresh = async () => {
    await Promise.all(dispatch([getListPetServices({ id: appointmentId }), getAppointment({ appointmentId })]));
  };

  const refreshCart = async () => {
    await dispatch(getListPetServices({ id: appointmentId }));
  };

  const updateCart = async () => {
    const { allItems } = resolveItems();
    await preview({ items: allItems });
  };

  const initCart = useLatestCallback(async () => {
    await Promise.all([refreshCart(), dispatch(getTaxList())]);
    const { allItems } = resolveItems();
    return {
      previewItems: allItems,
    };
  });

  const clearCart = useLatestCallback(() => {
    dispatch(resetProduct());
  });

  return {
    refresh,
    initCart,
    refreshCart,
    getCartItems,
    updateCart,
    clearCart,
  };
};

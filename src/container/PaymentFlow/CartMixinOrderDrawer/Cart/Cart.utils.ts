import { type CreateOrderParams, type PreviewCreateOrderParams } from '@moego/api-web/moego/api/order/v2/order_api';
import { type SurchargeItem } from '@moego/api-web/moego/models/fulfillment/v1/fulfillment_defs';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { ItemType } from '@moego/api-web/moego/models/order/v1/order_enums';
import { PriceDetailModelPriceItemOperator } from '@moego/api-web/moego/models/order/v1/order_line_item_models';
import { MoeMoney, getProductSalePrice } from '@moego/finance-utils';
import { v4 as uuidv4 } from 'uuid';
import { type CartServiceRecord } from '../../../../store/PaymentFlow/cart.boxes';
import { type ProductItemInfo } from '../SubpageRight/AddMoreItemSubpage/modules/AddProducts/AddProducts.types';

export const defaultTax = { id: '0', name: 'Tax', rate: '0' };

export function convertCartItemsToCreateOrderItems(cartItems: CartServiceRecord[]): PreviewCreateOrderParams['items'] {
  return cartItems.map((c) => {
    return {
      externalUuid: c.externalId,
      description: c.description,
      itemId: c.serviceId,
      quantity: c.quantity,
      itemType: c.serviceItemType === ServiceItemType.EVALUATION ? ItemType.EVALUATION_SERVICE : ItemType.SERVICE,
      unitPrice: c.unitPrice,
      name: c.name,
      staffId: c.staffId,
      petId: c.pet.id,
      subtotalDetail: {
        priceItems: c.lineItems.map((v) => {
          return {
            name: v.itemName,
            operator: PriceDetailModelPriceItemOperator.ADD,
            quantity: v.quantity,
            unitPrice: v.unitPrice,
            subTotal: v.lineTotal,
            // 后端加参数时没想到前端有用到此类型作为入参，传空值即可
            // https://moegoworkspace.slack.com/archives/C02L7M55Z09/p1754377228490389
            objectType: 0,
            objectId: '0',
          };
        }),
      },
      tax: {
        ...c.tax,
        rate: {
          value: c.tax.rate.toString(),
        },
      },
    };
  });
}

export function convertFeesItemsToCreateOrderItems(
  items: Array<Omit<SurchargeItem, 'tax'> & { tax: typeof defaultTax }>,
): PreviewCreateOrderParams['items'] {
  return items.map((c) => {
    return {
      externalUuid: c.externalId,
      description: c.description,
      itemId: c.serviceChargeId,
      quantity: c.quantity,
      itemType: ItemType.SERVICE_CHARGE,
      unitPrice: c.unitPrice,
      name: c.name,
      staffId: '0',
      petId: '0',
      subtotalDetail: { priceItems: [] },
      tax: {
        id: c.tax.id,
        name: c.tax.name,
        rate: {
          value: c.tax.rate,
        },
      },
    };
  });
}

export const getProductExternalUuid = (id: string | number) => {
  return `product_${id}_${uuidv4()}`;
};

export function convertProductToCreateOrderItem(
  product: ProductItemInfo,
  options: {
    staffId: string;
    currencyCode: string;
    quantity: number;
    petId: string;
    tax: {
      id: string;
      name: string;
      rate: string;
    };
    externalUuid: string;
  },
): CreateOrderParams['items'][number] {
  const { staffId, petId, currencyCode, tax, quantity, externalUuid } = options;
  return {
    externalUuid,
    itemType: ItemType.PRODUCT,
    itemId: String(product.id),
    description: product.name,
    quantity,
    unitPrice: MoeMoney.fromAmount(getProductSalePrice(product), currencyCode),
    name: product.name,
    staffId,
    petId,
    subtotalDetail: { priceItems: [] },
    tax: {
      id: tax.id,
      name: tax.name,
      rate: {
        value: tax.rate,
      },
    },
  };
}

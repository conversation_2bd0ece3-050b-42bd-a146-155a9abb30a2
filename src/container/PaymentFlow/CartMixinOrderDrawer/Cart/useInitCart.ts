/**
 * 初始化购物车
 * 1. 从 source 获取原始的 items，preview items
 * 2. 根据 items 获取可用的优惠券，pricing rules 等依赖
 * 3. preview，返回约束好的 preview result 结构体
 * 4. 根据 preview 结果回写 promotions 等应用结果
 * 5. 注册“创建订单”，创建订单返回标准结构
 * 6. 清理可能的缓存数据，比如购物车用 source id 为 key 的缓存
 */
import { type PreviewCreateOrderParams } from '@moego/api-web/moego/api/order/v2/order_api';
import { isNormal, useLatestCallback } from '@moego/finance-utils';
import { useEffect, useRef } from 'react';
import { type KitOrderV2Model } from '../CartMixinOrderDrawer.types';
import { ColumnsBridgeEvent, type IColumnsBridge } from '../Columns/ColumnsBridge';
import { type OptimizedPreviewOrderParams, type PreviewOrderResult } from '../hooks/Preview.types';
import { useBridgeListener } from '../hooks/use-bridge-hooks';
import { useCreateOrderListener } from '../hooks/useHandleCreateOrder';
import { type Bridge } from '../utils/bridge';

interface UseInitCartProps {
  sourceId: string;
  /**
   * 初始化购物车，获取 source 资源
   */
  initCart: (sourceId: string) => Promise<{
    previewItems: PreviewCreateOrderParams['items'];
  }>;

  /**
   * 根据 source 资源，获取可用的 promotions
   */
  beforeInitialPreview?: (sourceData: {
    previewItems: PreviewCreateOrderParams['items'];
  }) => Promise<void> | void;
  /**
   * 根据 source 资源，进行预览
   */
  preview: (params: Partial<OptimizedPreviewOrderParams>) => Promise<PreviewOrderResult>;
  /**
   * 清空暂存数据
   */
  cleanCart?: () => void;
  /**
   * 创建订单
   */
  createOrder: () => Promise<KitOrderV2Model>;
  /**
   * 通信桥接
   */
  columnsBridge: Bridge<IColumnsBridge>;
  autoApplyWhenCartChanges?: boolean;
}

export function useInitCart(props: UseInitCartProps) {
  const {
    sourceId,
    initCart,
    preview,
    cleanCart,
    autoApplyWhenCartChanges = true,
    columnsBridge,
    createOrder,
    beforeInitialPreview,
  } = props;

  const dataRef = useRef<{
    previewItems: PreviewCreateOrderParams['items'];
  }>({
    previewItems: [],
  });

  const init = useLatestCallback(async (id: string, params?: Partial<OptimizedPreviewOrderParams>) => {
    const { previewItems } = await initCart(id);
    dataRef.current = { previewItems };
    await beforeInitialPreview?.(dataRef.current);
    const result = await preview({
      items: previewItems,
      autoApplyPromotions: autoApplyWhenCartChanges,
      ...params,
    });
    return result;
  });

  useEffect(() => {
    if (isNormal(sourceId)) {
      init(sourceId);
    }

    return () => {
      cleanCart?.();
    };
  }, [sourceId]);

  useBridgeListener(
    columnsBridge,
    ColumnsBridgeEvent.OnCartChanges,
    async (params: Partial<OptimizedPreviewOrderParams>) => {
      if (params.items) {
        return await init(sourceId);
      }

      return await preview(params);
    },
  );

  useCreateOrderListener(columnsBridge, async () => {
    return await createOrder();
  });
}

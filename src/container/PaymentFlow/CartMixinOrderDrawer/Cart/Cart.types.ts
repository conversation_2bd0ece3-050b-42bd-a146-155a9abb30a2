import { type SurchargeItem } from '@moego/api-web/moego/models/fulfillment/v1/fulfillment_defs';
import { type CartServiceRecord } from '../../../../store/PaymentFlow/cart.boxes';
import { type TProductSelected } from '../SubpageRight/AddMoreItemSubpage/modules/AddProducts/store/orderProducts.boxes';

export interface CartItemsByType {
  serviceItems: CartServiceRecord[];
  productItems: TProductSelected[];
  surchargeItems: SurchargeItem[];
}

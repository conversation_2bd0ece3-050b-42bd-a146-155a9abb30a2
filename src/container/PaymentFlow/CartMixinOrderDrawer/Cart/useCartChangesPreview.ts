/**
 * provide the cart changes preview callback
 * use for
 * 1. cart items changes
 * 2. promotions changes
 */
import { toast } from '@moego/ui';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { ColumnsBridgeEvent } from '../Columns/ColumnsBridge';
import { useDrawerScopeContext } from '../context/DrawerScopeContext';
import { type OptimizedPreviewOrderParams } from '../hooks/Preview.types';

export function useCartChangesPreview() {
  const { columnsBridge } = useDrawerScopeContext();

  return useLatestCallback(async (params: Partial<OptimizedPreviewOrderParams>) => {
    const result = await columnsBridge.runAsyncBail(ColumnsBridgeEvent.OnCartChanges, params);
    if (!result) {
      toast({
        title: 'Error',
        description: 'No handler registered',
      });
      throw new Error('No handler registered');
    }

    return result;
  });
}

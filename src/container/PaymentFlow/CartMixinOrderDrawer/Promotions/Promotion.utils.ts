/**
 * promotions 相关的工具函数
 */

import { type OrderPromotionModel } from '@moego/api-web/moego/models/order/v1/order_promotion_models';
import { SourceType, TargetType, type Targets } from '@moego/api-web/moego/models/promotion/v1/promotion';
import { type PreviewCreateOrderRequestPromotion } from '@moego/api-web/moego/service/order/v2/order_service';
import { MoeMoney } from '@moego/finance-utils';
import { AmountCalcType } from '@moego/finance-web-kit';
import { v4 as uuidv4 } from 'uuid';
import { CouponRecord } from '../../../../store/PaymentFlow/cart.boxes';
import { type CartItemsByType } from '../Cart/Cart.types';
import { type PromotionsByTypeMap } from '../hooks/Preview.utils';
import { type TOneTimeDiscountValue } from '../SubpageRight/DiscountSubpage/components/OneTimeDiscount/type';
import {
  PromotionType,
  type PromotionItemInPreview,
  type PromotionItemInPreviewWithKeyId,
  type PromotionItemOrderApplied,
  type PromotionItemOrderAppliedWithAppliedIndex,
} from './Promotion.types';

/**
 * 根据类型分组 promotions
 * @param promotions
 * @returns
 */
export function groupPreviewPromotionsByType(promotions: PromotionItemInPreview[]): PromotionsByTypeMap {
  const discounts = promotions.filter((i) => i.couponSource?.type === SourceType.DISCOUNT);
  const oneTimeDiscounts = promotions.filter((i) => !!i.oneTimeDiscount);
  const membershipPromotions = promotions.filter(
    (i) =>
      i.couponSource?.type === SourceType.MEMBERSHIP_DISCOUNT ||
      i.couponSource?.type === SourceType.MEMBERSHIP_QUANTITY,
  );
  const packagePromotions = promotions.filter((i) => i.couponSource?.type === SourceType.PACKAGE);
  const storeCreditPromotions = promotions.filter((i) => !!i.storeCredit);
  const map = new Map<PromotionType, PromotionItemInPreview[]>();
  map.set(PromotionType.Discount, discounts);
  map.set(PromotionType.Membership, membershipPromotions);
  map.set(PromotionType.Package, packagePromotions);
  map.set(PromotionType.StoreCredit, storeCreditPromotions);
  map.set(PromotionType.OneTimeDiscount, oneTimeDiscounts);

  return map;
}

export function groupOrderAppliedPromotionsByType(
  promotions: (PromotionItemOrderApplied & { appliedIndex?: number })[],
): PromotionsByTypeMap<PromotionItemOrderAppliedWithAppliedIndex> {
  const newList: PromotionItemOrderAppliedWithAppliedIndex[] = [...promotions].map((p, index) => ({
    ...p,
    appliedIndex: index,
  }));

  const discounts = newList.filter((p) => !!p.discount);
  const oneTimeDiscounts = newList.filter((p) => !!p.oneTimeDiscount);
  const membershipPromotions = newList.filter((p) => !!p.membership);
  const packagePromotions = newList.filter((p) => !!p.package);
  const storeCreditPromotions = newList.filter((p) => !!p.storeCredit);

  const map = new Map<PromotionType, PromotionItemOrderAppliedWithAppliedIndex[]>();
  map.set(PromotionType.Discount, discounts);
  map.set(PromotionType.Membership, membershipPromotions);
  map.set(PromotionType.Package, packagePromotions);
  map.set(PromotionType.StoreCredit, storeCreditPromotions);
  map.set(PromotionType.OneTimeDiscount, oneTimeDiscounts);

  return map;
}

export function getAppliedStoreCredit(promotions: OrderPromotionModel[]): MoeMoney {
  const storeCreditPromotions = promotions.filter((p) => !!p.storeCredit);
  if (storeCreditPromotions.length === 0) {
    return MoeMoney.empty();
  }

  return MoeMoney.fromMoney(storeCreditPromotions[0].storeCredit?.amount ?? MoeMoney.empty());
}

export function normalizeOneTimeDiscount(
  discount: PreviewCreateOrderRequestPromotion['oneTimeDiscount'],
): TOneTimeDiscountValue {
  const type = discount?.discountAmount ? AmountCalcType.AMOUNT : AmountCalcType.PERCENTAGE;
  return {
    discountType: type,
    discountValue:
      type === AmountCalcType.AMOUNT
        ? MoeMoney.fromMoney(discount?.discountAmount ?? MoeMoney.empty()).valueOf()
        : +(discount?.discountPercentage?.value ?? 0),
  };
}

export function transOneTimeDiscountToPreview(
  discount: TOneTimeDiscountValue,
  currencyCode: string,
): PreviewCreateOrderRequestPromotion['oneTimeDiscount'] {
  return discount.discountType === AmountCalcType.AMOUNT
    ? {
        discountAmount: MoeMoney.fromAmount(discount.discountValue, currencyCode),
      }
    : {
        discountPercentage: {
          value: discount.discountValue.toString(),
        },
      };
}

export function appendKeyIdToAppliedPromotions(
  type: PromotionType,
  promotions: PromotionItemInPreview[],
): Array<PromotionItemInPreviewWithKeyId> {
  if (type === PromotionType.OneTimeDiscount) {
    return promotions.map((p, index) => ({
      ...p,
      keyId: `one-time-discount-${index}-${uuidv4()}`,
    }));
  }

  if (type === PromotionType.StoreCredit) {
    return promotions.map((p, index) => ({
      ...p,
      keyId: `store-credit-${index}-${uuidv4()}`,
    }));
  }

  return promotions
    .map((p) => ({
      ...p,
      keyId: p.couponSource ? CouponRecord.createKeyId(p.couponSource) : '',
    }))
    .filter((s) => !!s.keyId);
}

const getTargetMatchUuids = (
  list: Array<{
    externalId?: string | number;
    externalUuid?: string;
  }>,
  targetKey: 'externalId' | 'externalUuid',
) => {
  return list.map((v) => String(v[targetKey] ?? '')).filter((v) => !!v);
};

function getSingleTargetExternalUuids(
  target: Targets,
  { serviceItems, surchargeItems, productItems }: CartItemsByType,
): string[] {
  if (target.all) {
    if (target.type === TargetType.SERVICE) {
      return getTargetMatchUuids(serviceItems, 'externalId');
    }

    if (target.type === TargetType.SERVICE_CHARGE) {
      return getTargetMatchUuids(surchargeItems, 'externalId');
    }

    if (target.type === TargetType.PRODUCT) {
      return getTargetMatchUuids(productItems, 'externalUuid');
    }

    return [];
  }

  if (target.type === TargetType.SERVICE) {
    const list = serviceItems.filter((v) => target.id.includes(String(v.serviceId)));
    return getTargetMatchUuids(list, 'externalId');
  }

  // 理论上不会走到这个分支，因为 discount 设计上不会应用到指定的 service charge 集合
  if (target.type === TargetType.SERVICE_CHARGE) {
    const list = surchargeItems.filter((v) => target.id.includes(String(v.serviceChargeId)));
    return getTargetMatchUuids(list, 'externalId');
  }

  if (target.type === TargetType.PRODUCT) {
    const list = productItems.filter((v) => target.id.includes(String(v.id)));
    return getTargetMatchUuids(list, 'externalUuid');
  }

  return [];
}

export function getAllCartItemExternalUuids({ serviceItems, surchargeItems, productItems }: CartItemsByType) {
  return [
    ...getTargetMatchUuids(serviceItems, 'externalId'),
    ...getTargetMatchUuids(surchargeItems, 'externalId'),
    ...getTargetMatchUuids(productItems, 'externalUuid'),
  ];
}

/**
 * get externalUuids from targets config
 * @param targets empty array means all items
 * @param options
 * @returns externalUuids
 */
export function getCartItemExternalUuidsByTargetConfig(targets: Targets[], options: CartItemsByType) {
  const res = targets.map((target) => getSingleTargetExternalUuids(target, options));

  return Array.from(new Set(res.flat()));
}

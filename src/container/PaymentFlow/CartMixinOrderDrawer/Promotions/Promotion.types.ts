import { type Money } from '@moego/api-web/google/type/money';
import { type PreviewCreateOrderResult } from '@moego/api-web/moego/api/order/v2/order_api';
import { type OrderDetailModelV1 } from '@moego/api-web/moego/models/order/v1/order_detail_models';
import type { Targets } from '@moego/api-web/moego/models/promotion/v1/promotion';

export enum PromotionType {
  OneTimeDiscount = 'OneTimeDiscount',
  Discount = 'Discount',
  Package = 'Package',
  Membership = 'Membership',
  StoreCredit = 'StoreCredit',
  Unknown = 'Unknown',
}

export type PromotionItemInPreview = PreviewCreateOrderResult['appliedPromotions']['promotions'][number];
export type PromotionItemInPreviewWithKeyId = PromotionItemInPreview & {
  keyId: string;
};
export type PromotionItemOrderApplied = OrderDetailModelV1['orderPromotions'][number];
export type PromotionItemOrderAppliedWithAppliedIndex = PromotionItemOrderApplied & {
  appliedIndex: number;
};

export interface PromotionDisplayItem {
  name: string;
  value: React.ReactNode;
  type: PromotionType;
  subjectId: string;
  appliedIndex: number;
  appliedAmount: Money;
}

export type GetTargetCartItemUuidList = <T extends GetCartItemUuidsType>(
  options: T extends GetCartItemUuidsType.SELECTED
    ? {
        type: GetCartItemUuidsType.SELECTED;
        cacheKeyId: string;
      }
    : T extends GetCartItemUuidsType.AVAILABLE
      ? {
          type: GetCartItemUuidsType.AVAILABLE;
          targets: Targets[];
        }
      : {
          type: GetCartItemUuidsType.ALL;
        },
) => string[];

export enum GetCartItemUuidsType {
  ALL = 'all',
  AVAILABLE = 'available',
  SELECTED = 'selected',
}

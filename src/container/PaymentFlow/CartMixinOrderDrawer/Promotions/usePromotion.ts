import {
  CouponSearchConditionSourceType,
  type CouponSearchConditionTarget,
} from '@moego/api-web/moego/models/promotion/v1/coupon';
import { TargetType } from '@moego/api-web/moego/models/promotion/v1/promotion';
import { useDispatch, useStore } from 'amos';
import { isEqual, uniqWith } from 'lodash';
import { useCallback } from 'react';
import {
  getPromotion,
  setPromotionAppliedLineItemList,
  setSelectedOneTimeDiscountPromotion,
  setSelectedPromotion,
} from '../../../../store/PaymentFlow/cart.action';
import { PromotionSelectedRecord } from '../../../../store/PaymentFlow/cart.boxes';
import { selectPromotionAppliedLineItems } from '../../../../store/PaymentFlow/cart.selectors';
import { isNormal } from '../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { selectApptInfo } from '../../../Appt/store/appt.selectors';
import { useCartAppt } from '../Cart/useCartAppt';
import { type PromotionsByTypeMap } from '../hooks/Preview.utils';
import { GetCartItemUuidsType, PromotionType, type GetTargetCartItemUuidList } from './Promotion.types';
import {
  appendKeyIdToAppliedPromotions,
  getAllCartItemExternalUuids,
  getCartItemExternalUuidsByTargetConfig,
} from './Promotion.utils';

/**
 * ⚠️ ATTENTION: do not use Context here.
 * @param appointmentId
 * @returns
 */
export const usePromotion = (appointmentId: string) => {
  const dispatch = useDispatch();
  const store = useStore();
  const { getCartItems } = useCartAppt(appointmentId);

  const fetchData = useCallback(
    async (sourceTypes: CouponSearchConditionSourceType[], nameKeyword?: string) => {
      const { serviceItems, productItems, surchargeItems } = getCartItems();
      const { customerId } = store.select(selectApptInfo(appointmentId));
      const targets: CouponSearchConditionTarget[] = serviceItems
        .map((item) => ({
          targetType: TargetType.SERVICE,
          targetId: item.serviceId,
        }))
        .concat(
          productItems.map((item) => ({
            targetType: TargetType.PRODUCT,
            targetId: String(item.id),
          })),
        )
        .concat(
          surchargeItems.map((item) => ({
            targetType: TargetType.SERVICE_CHARGE,
            targetId: String(item.serviceChargeId),
          })),
        );

      if (!isNormal(customerId) || !targets.length) return;

      await dispatch(
        getPromotion({
          appointmentId,
          searchCondition: {
            customerId,
            sourceTypes,
            targets: uniqWith(targets, isEqual),
            nameKeyword,
          },
        }),
      );
    },
    [appointmentId],
  );

  const fetchAllPromotion = useLatestCallback(async () => {
    await fetchData([
      CouponSearchConditionSourceType.DISCOUNT,
      CouponSearchConditionSourceType.MEMBERSHIP,
      CouponSearchConditionSourceType.PACKAGE,
    ]);
  });

  const updateCartPromotions = useLatestCallback(async (map: PromotionsByTypeMap) => {
    const discountsWithKeyId = appendKeyIdToAppliedPromotions(
      PromotionType.Discount,
      map.get(PromotionType.Discount) ?? [],
    );
    const oneTimeDiscountsWithKeyId = appendKeyIdToAppliedPromotions(
      PromotionType.OneTimeDiscount,
      map.get(PromotionType.OneTimeDiscount) ?? [],
    );
    const membershipsWithKeyId = appendKeyIdToAppliedPromotions(
      PromotionType.Membership,
      map.get(PromotionType.Membership) ?? [],
    );
    const packagesWithKeyId = appendKeyIdToAppliedPromotions(
      PromotionType.Package,
      map.get(PromotionType.Package) ?? [],
    );

    await dispatch(setSelectedOneTimeDiscountPromotion(oneTimeDiscountsWithKeyId));
    await dispatch(setPromotionAppliedLineItemList([...discountsWithKeyId, ...oneTimeDiscountsWithKeyId]));
    await dispatch(
      setSelectedPromotion(appointmentId, {
        appointmentId,
        memberships: membershipsWithKeyId.map((v) => v.keyId),
        packages: packagesWithKeyId.map((v) => v.keyId),
        discounts: discountsWithKeyId.map((v) => v.keyId),
        oneTimeDiscounts: oneTimeDiscountsWithKeyId.map((v) => v.keyId),
      }),
    );
  });

  const clearCartPromotions = useLatestCallback(() => {
    dispatch(
      setSelectedPromotion(
        appointmentId,
        new PromotionSelectedRecord({
          appointmentId: appointmentId,
          memberships: [],
          packages: [],
          discounts: [],
          oneTimeDiscounts: [],
        }),
      ),
    );
    dispatch(setPromotionAppliedLineItemList([]));
    dispatch(setSelectedOneTimeDiscountPromotion([]));
  });

  const getTargetCartItemsUuid: GetTargetCartItemUuidList = (options) => {
    const cartItems = getCartItems();
    if (options.type === GetCartItemUuidsType.ALL) {
      return getAllCartItemExternalUuids(cartItems);
    }

    if (options.type === GetCartItemUuidsType.SELECTED) {
      return store.select(selectPromotionAppliedLineItems(options.cacheKeyId));
    }

    if (options.type === GetCartItemUuidsType.AVAILABLE) {
      return getCartItemExternalUuidsByTargetConfig(options.targets, cartItems);
    }

    return [];
  };

  return {
    fetchData,
    fetchAllPromotion,
    updateCartPromotions,
    clearCartPromotions,
    getTargetCartItemsUuid,
  };
};

import { type OrderSourceType } from '@moego/api-web/moego/models/order/v1/order_enums';
/**
 * 整个 drawer 层级的 context
 * 只提供全局通用的数据
 */
import { type StrictEnumValues } from '@moego/finance-utils';
import { type OrderChargeType } from '@moego/finance-web-kit';
import { createContext, useContext } from 'react';
import { type CustomerRecord } from '../../../../store/customer/customer.boxes';
import { type PaymentDrawerCloseType } from '../../interface';
import { type IColumnsBridge } from '../Columns/ColumnsBridge';
import { type useRightColumnPageRouter } from '../Columns/useSubpageRouter';
import { type Bridge } from '../utils/bridge';

export interface DrawerScopeContextModel {
  isStartedWithPreview: boolean;
  chargeType: StrictEnumValues<typeof OrderChargeType>;
  rightColumnPageRouter: ReturnType<typeof useRightColumnPageRouter>;
  closeDrawer: (closeType: PaymentDrawerCloseType) => void;
  customer: CustomerRecord;
  sourceId: string;
  sourceType: OrderSourceType;
  columnsBridge: Bridge<IColumnsBridge>;
  globalLoading: boolean;
}

export const DrawerScopeContext = createContext<DrawerScopeContextModel | null>(null);
export const DrawerScopeContextProvider = DrawerScopeContext.Provider;

export function useDrawerScopeContext() {
  const ctx = useContext(DrawerScopeContext);
  if (!ctx && __DEV__) {
    throw new Error('useDrawerScopeContext must be used within a DrawerScopeContextProvider');
  }

  return {
    ...ctx!,
  };
}

import { OrderSourceType } from '@moego/api-web/moego/models/order/v1/order_enums';
import { <PERSON><PERSON><PERSON>, isNormal, useLatestCallback } from '@moego/finance-utils';
import { OrderChargeType, PrefabPaymentChannel } from '@moego/finance-web-kit';
import { useSerialCallback } from '@moego/tools';
import { AlertDialog, cn, toast } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useMemo } from 'react';
import { completeOrder } from '../../../../../store/PaymentFlow/order.actions';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../../../store/business/role.selectors';
import { PaymentActionName } from '../../../../../utils/reportData/payment';
import { useViewOrder } from '../../../ViewOrderDrawer/useViewOrder';
import { useInvoiceReinventReport } from '../../../hooks/useInvoiceReinvent.report';
import { useSetTips } from '../../../hooks/useSetTips';
import { PaymentDrawerCloseType } from '../../../interface';
import { type KitOrderV2Model } from '../../CartMixinOrderDrawer.types';
import { ColumnsBridgeEvent } from '../../Columns/ColumnsBridge';
import { RightColumnPage } from '../../Columns/RightColumnPage';
import { useDrawerScopeContext } from '../../context/DrawerScopeContext';
import { useCashierDeskContext } from '../../context/useCashierDeskContext';
import { type IHandleTakePaymentOptions } from '../../hooks/take-payment/HandlePayment.types';
import { useHandlePayment } from '../../hooks/take-payment/useHandlePayment';
import { useDelayCloseDrawer } from '../../hooks/useDelayCloseDrawer';
import { LayoutRightPageFooter } from '../Layout/LayoutRightPageFooter';
import { ChargeNowButton, CheckoutWithoutPaymentButton, DeductionButton } from './ActionButtons';
import { getOrderChargeSuccessToast } from './ChargeFooter.utils';
import { ConvenienceFees } from './ConvenienceFees';

export interface IChargeFooterProps {
  isDisabled?: boolean;
  chargeButtonText?: React.ReactNode;
  saveAsCreditAmount?: number;
}

export const ChargeFooter = (props: IChargeFooterProps) => {
  const { isDisabled: propsDisabled, saveAsCreditAmount, chargeButtonText } = props;
  const {
    amounts,
    paymentChannels,
    order: contextOrder,
    pay,
    checkout,
    hasCheckedOut,
    canSetTipsInPaymentFlow,
  } = useCashierDeskContext();
  const { chargeType, rightColumnPageRouter, columnsBridge, globalLoading } = useDrawerScopeContext();
  const isPreviewMode = !isNormal(contextOrder.id);
  const dispatch = useDispatch();
  const [business, permissions] = useSelector(selectCurrentBusiness, selectCurrentPermissions());
  const convenienceFees = amounts.convenienceFees;
  const { openViewOrderDrawer } = useViewOrder();
  const openSetTipsModal = useSetTips();
  const reportPaymentData = useInvoiceReinventReport();
  const withPaymentChannel = Object.keys(paymentChannels.attachedChannels).length > 0;
  const attachedChannel = useMemo(() => {
    return Object.values(paymentChannels.attachedChannels)[0]?.channel;
  }, [paymentChannels.attachedChannels]);
  const chargeAmount = amounts.realChargeAmount.plus(
    convenienceFees.processingFeeEnabled ? MoeMoney.fromAmount(convenienceFees.processingFee) : MoeMoney.empty(),
  );

  const containerBgColor = withPaymentChannel ? 'moe-bg-brand-subtle' : '';
  const canControlProcessingFee = permissions.has('canControlProcessingFeeInInvoice');
  const { takePayment } = useHandlePayment();
  const isExtraCharge = chargeType === OrderChargeType.ExtraCharge;

  const openViewReceipt = (sourceId: string) => {
    openViewOrderDrawer({
      sourceId,
      sourceType: OrderSourceType.APPOINTMENT,
      businessId: business.id.toString(),
      module: 'grooming',
    });
  };

  const { delayCloseDrawer } = useDelayCloseDrawer({
    columnsBridge,
  });

  const handleTakePayment = useSerialCallback(
    async (
      options: Partial<Pick<IHandleTakePaymentOptions, 'order'>> &
        Pick<IHandleTakePaymentOptions, 'amounts'> & {
          isPreview: boolean;
        },
    ) => {
      const { order: _createdOrder, amounts, isPreview } = options;
      const createdOrder = _createdOrder ?? (await columnsBridge.runAsyncBail(ColumnsBridgeEvent.OnRequestOrder));
      const order = createdOrder ?? contextOrder;
      if (!order) {
        toast({
          title: 'Failed to create order',
          type: 'error',
        });
        return;
      }

      pay.updateContextConfig({
        invoiceId: +order.id,
        customerId: +order.customerId,
      });

      // preview 模式下，先创单，然后跳到 Pay online 页面确认消息
      if (attachedChannel?.methodId === PrefabPaymentChannel.PayOnline && isPreview) {
        rightColumnPageRouter.push(RightColumnPage.PAY_ONLINE);
        return;
      }

      reportPaymentData(PaymentActionName.ChargeNowSubmit, {
        orderId: order.id,
        isExtraOrder: isExtraCharge,
        ctaId: 'payment_modal',
      });

      await takePayment({
        order,
        amounts,
        async onSuccess(options = {}) {
          const {
            shouldCloseDrawer = true,
            toastWithReceiptLink = false,
            shouldToast = true,
            shouldResetRightSidePage = true,
          } = options;

          if (shouldToast) {
            toast({
              title: getOrderChargeSuccessToast(chargeType, attachedChannel?.methodId),
              type: 'success',
              actionText: toastWithReceiptLink ? 'View receipt(s)' : undefined,
              onActionClick: toastWithReceiptLink ? () => openViewReceipt(order.sourceId) : undefined,
            });
          }
          if (shouldCloseDrawer) {
            delayCloseDrawer(PaymentDrawerCloseType.Paid);
          }
          columnsBridge.runParallel(ColumnsBridgeEvent.OnTakePayment, 'success');
          if (shouldResetRightSidePage) {
            rightColumnPageRouter.reset();
          }
        },
        onCancel() {
          columnsBridge.runParallel(ColumnsBridgeEvent.OnTakePayment, 'cancel');
        },
      });
    },
  );

  const getChargeDataSnapshot: () => {
    amounts: IHandleTakePaymentOptions['amounts'];
    isPreview: boolean;
  } = useLatestCallback(() => {
    return {
      amounts: {
        realChargeAmount: MoeMoney.fromMoney(amounts.realChargeAmount),
        shouldChargeAmount: MoeMoney.fromMoney(amounts.shouldChargeAmount),
        convenienceFees: { ...amounts.convenienceFees },
        saveAsCreditAmount: MoeMoney.fromAmount(saveAsCreditAmount ?? 0, business.currencyCode),
      },
      isPreview: isPreviewMode,
    };
  });

  const createOrderIfNeeded = useLatestCallback(async () => {
    return isPreviewMode ? await columnsBridge.runAsyncBail(ColumnsBridgeEvent.OnRequestOrder) : contextOrder;
  });

  const handleCheckoutWithoutPayment = useSerialCallback(async () => {
    AlertDialog.open({
      title: 'Check out without payment',
      content:
        'By proceeding, order details will be locked and no further modifications are allowed. \nAre you sure you want to check out?',
      confirmText: 'Check out',
      onConfirm: async () => {
        await createOrderIfNeeded();
        await checkout();
        toast({
          title: 'Check out successful',
          type: 'success',
        });
        await delayCloseDrawer(PaymentDrawerCloseType.Cancel);
      },
    });
  });

  const handleChargeNow = useSerialCallback(async (needCheckout: boolean) => {
    const snapshot = getChargeDataSnapshot();
    const order = await createOrderIfNeeded();
    if (needCheckout) {
      await checkout();
    }
    await handleTakePayment({ order: order!, ...snapshot });
  });

  const handleRemainZeroDeduction = useSerialCallback(async (needCheckout: boolean) => {
    const tipBasedAmount = contextOrder.tipsBasedAmount.valueOf();
    let addedTipsAmount: number | undefined;
    if (isPreviewMode && canSetTipsInPaymentFlow && tipBasedAmount > 0) {
      addedTipsAmount = await openSetTipsModal({
        tipBasedAmount,
      });

      // cancel
      if (typeof addedTipsAmount === 'undefined') {
        return;
      }

      // add tips
      if (addedTipsAmount > 0) {
        await columnsBridge.runAsyncBail(ColumnsBridgeEvent.OnTipsAmountChange, addedTipsAmount);
        toast({
          type: 'success',
          title: 'Tips added',
        });
        return;
      }
    }

    let createdOrder: KitOrderV2Model | null = null;
    if (isPreviewMode) {
      // if sales order, will be automatically complete after created
      createdOrder = await createOrderIfNeeded();
    } else {
      createdOrder = contextOrder;
      // if existing order, complete it manually
      await dispatch(completeOrder(contextOrder.id));
    }

    if (needCheckout) {
      await checkout();
    }

    toast({
      title: chargeType === OrderChargeType.Checkout ? 'Check out successful' : 'Charged successfully!',
      type: 'success',
      actionText: 'View receipt(s)',
      onActionClick: () => openViewReceipt(createdOrder?.sourceId ?? ''),
    });
    await delayCloseDrawer(PaymentDrawerCloseType.Paid);
  });

  const chargeNowButtonLoading = handleChargeNow.isBusy() || amounts.convenienceFees?.processingFeeLoading;
  const chargeNowButtonDisabled =
    chargeNowButtonLoading || !withPaymentChannel || chargeAmount.valueOf() <= 0 || !!propsDisabled;
  const redeemRemainZero = amounts.shouldChargeAmount.isZero();
  const isComputedCheckout = chargeType === OrderChargeType.Checkout && !hasCheckedOut;

  const renderActionButtons = () => {
    if (redeemRemainZero) {
      const isDisabled = chargeType === OrderChargeType.Deposit;
      return (
        <DeductionButton
          isDisabled={isDisabled || globalLoading}
          text={isComputedCheckout ? 'Check out' : `Charge now ${business.formatMoney(chargeAmount)}`}
          onPress={() => handleRemainZeroDeduction(isComputedCheckout)}
          isLoading={handleRemainZeroDeduction.isBusy()}
        />
      );
    }

    return (
      <>
        {/* checkout without payment */}
        {isComputedCheckout && rightColumnPageRouter.is(RightColumnPage.DEFAULT) && !withPaymentChannel && (
          <CheckoutWithoutPaymentButton
            isLoading={handleCheckoutWithoutPayment.isBusy()}
            isDisabled={globalLoading}
            onPress={handleCheckoutWithoutPayment}
          />
        )}

        {/* charge now, with amount */}
        <ChargeNowButton
          isLoading={chargeNowButtonLoading}
          onPress={() => handleChargeNow(isComputedCheckout)}
          isDisabled={chargeNowButtonDisabled || globalLoading}
          buttonText={isComputedCheckout ? 'Check out and charge' : chargeButtonText}
          amountString={business.formatMoney(chargeAmount)}
        />
      </>
    );
  };

  return (
    <LayoutRightPageFooter className={cn('moe-flex-col moe-gap-m moe-py-[25px]', containerBgColor)}>
      {convenienceFees.processingFeeAvailable && (
        <ConvenienceFees
          canControl={canControlProcessingFee}
          amount={convenienceFees.processingFee}
          feeName={amounts.convenienceFees.customizedFeeName}
          enabled={convenienceFees.processingFeeEnabled}
          loading={convenienceFees.processingFeeLoading}
          onEnabledChange={amounts.convenienceFees.setProcessingFeeEnabled}
        />
      )}
      <section className="moe-w-full moe-flex moe-gap-s">{renderActionButtons()}</section>
    </LayoutRightPageFooter>
  );
};

import { Heading, Modal, Text, Spin } from '@moego/ui';
import React from 'react';
import { useFloatableHost } from '../../../../utils/hooks/useFloatableHost';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';

export const ProcessingPaymentModal = (props: {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  description?: string;
}) => {
  const { isOpen, onClose, title, description } = props;

  return (
    <Modal isOpen={isOpen} onClose={onClose} footer={null} isMaskCloseable={false} title="Confirm payment">
      {title && (
        <Heading size="3" className="moe-mt-l moe-text-center">
          {title}
        </Heading>
      )}
      {description && (
        <Text variant="regular-short" className="moe-mt-xs moe-text-center">
          {description}
        </Text>
      )}

      <div className="moe-flex moe-items-center moe-justify-center moe-h-[350px] moe-flex-col">
        <Spin isLoading size="l" />
        <Text variant="regular-short" className="moe-mt-s moe-text-tertiary">
          Processing payment…
        </Text>
      </div>
    </Modal>
  );
};

export const useProcessingPaymentModal = () => {
  const { mountModal } = useFloatableHost<boolean>();
  return useLatestCallback((props?: { title?: string; description?: string }) => {
    const { title, description } = props ?? {};
    const { closeFloatable } = mountModal(() => {
      return (
        <ProcessingPaymentModal
          isOpen={true}
          onClose={() => closeFloatable(false)}
          title={title}
          description={description}
        />
      );
    });

    return closeFloatable;
  });
};

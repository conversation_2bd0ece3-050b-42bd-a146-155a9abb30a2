import { useDispatch } from 'amos';
import React, { useState } from 'react';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { Modal, Heading, Text, Spin, Button } from '@moego/ui';
import ReactSignatureCanvas from 'react-signature-canvas';
import { useRefObject } from '../../../../utils/hooks/hooks';
import { useBool } from '../../../../utils/hooks/useBool';
import { base64ToBlob, uploadFile } from '../../../../store/business/business.actions';
import { isFunction } from 'lodash';
import { useSerialCallback } from '@moego/finance-utils';
import { alertError } from '../../../../components/Alert/Alert.utils';
import { useMount, useUnmount } from 'ahooks';

export interface SignatureModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSign: (url: string) => Promise<void>;
  title?: string;
  description?: string;
  customizedSignFileName?: string | (() => string);
}

export const SignatureModal = (props: SignatureModalProps) => {
  const { isOpen, onClose, onSign, title, description, customizedSignFileName } = props;
  const canvas = useRefObject<ReactSignatureCanvas>();
  const canvasContainer = useRefObject<HTMLDivElement>();
  const [canvasWidth, setCanvasWidth] = useState(0);
  const [canvasHeight, setCanvasHeight] = useState(0);
  const isBeginSign = useBool();
  const dispatch = useDispatch();

  const handleSubmit = useSerialCallback(async () => {
    try {
      const base64Data = canvas.current!.toDataURL('image/jpeg');
      const customizedFileName = isFunction(customizedSignFileName) ? customizedSignFileName() : customizedSignFileName;
      const filename = customizedFileName ?? `signature_${Math.floor(Math.random() * 100000000)}`;
      const blob = base64ToBlob(filename, base64Data);
      const sign = await dispatch(uploadFile(blob));
      await onSign(sign.url);
      onClose();
    } catch (e) {
      alertError(e);
    }
  });

  const isSubmitting = handleSubmit.isBusy();

  const handleResize = useLatestCallback(() => {
    if (!canvasContainer.current) return;
    const w = canvasContainer.current.clientWidth;
    const h = canvasContainer.current.clientHeight;
    setCanvasWidth(w);
    setCanvasHeight(h);
  });

  const handleClearSignature = useLatestCallback(() => {
    canvas.current?.clear();
    isBeginSign.close();
  });

  useMount(() => {
    // 设置 canvas 宽高为容器宽高
    if (canvasContainer.current) {
      const w = canvasContainer.current.clientWidth;
      const h = canvasContainer.current.clientHeight;
      setCanvasWidth(w);
      setCanvasHeight(h);
      window.addEventListener('resize', handleResize);
    }
  });

  useUnmount(() => {
    window.removeEventListener('resize', handleResize);
  });

  return (
    <Modal
      onClose={onClose}
      title="Sign to confirm payment"
      isOpen={isOpen}
      onConfirm={handleSubmit}
      confirmButtonProps={{
        isDisabled: !isBeginSign.value,
      }}
      confirmText="Done and continue to pay"
      autoCloseOnConfirm={false}
      tertiaryText="Back to checkout"
      onTertiary={onClose}
      showTertiaryButton
      size="l"
      showCancelButton={false}
      showCloseButton={false}
      isMaskCloseable={false}
      footer={handleSubmit.isBusy() ? null : undefined}
    >
      {title && (
        <Heading size="3" className="moe-mt-l moe-text-center">
          {title}
        </Heading>
      )}
      {(description || isSubmitting) && (
        <Text variant="regular-short" className="moe-mt-xs moe-text-center">
          {isSubmitting ? 'Continue to payment' : description}
        </Text>
      )}

      {isSubmitting ? (
        <div className="moe-flex moe-items-center moe-justify-center moe-h-[350px] moe-flex-col">
          <Spin isLoading size="l" />
          <Text variant="regular-short" className="moe-mt-s moe-text-tertiary">
            Processing payment…
          </Text>
        </div>
      ) : (
        <div
          className="moe-mt-m moe-bg-neutral-sunken-0 moe-relative moe-rounded-m moe-h-[500px]"
          ref={canvasContainer}
        >
          {!isBeginSign.value && (
            <Text
              className="moe-text-[#D2D2D2] moe-text-[56px] moe-font-[500] moe-absolute moe-left-[50%] moe-top-[50%] moe-translate-x-[-50%] moe-translate-y-[-50%] moe-text-nowrap moe-select-none moe-pointer-events-none"
              variant="regular"
            >
              Your awesome signature here
            </Text>
          )}
          <Button variant="secondary" className="moe-absolute moe-left-s moe-bottom-s" onPress={handleClearSignature}>
            Clear
          </Button>
          <ReactSignatureCanvas
            onBegin={isBeginSign.open}
            penColor="#F96B18"
            ref={canvas}
            canvasProps={{ width: canvasWidth, height: canvasHeight }}
          />
        </div>
      )}
    </Modal>
  );
};

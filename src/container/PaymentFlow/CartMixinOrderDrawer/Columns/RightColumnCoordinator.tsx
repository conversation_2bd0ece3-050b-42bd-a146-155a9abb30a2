import { is<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@moego/finance-utils';
import {
  OrderChargeType,
  RealmType,
  useConvenienceFee,
  usePaymentChannelsV2,
  type useChargeCapabilities,
} from '@moego/finance-web-kit';
import { Spin } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { Condition } from '../../../../components/Condition';
import { Switch } from '../../../../components/SwitchCase';
import { FinanceKit } from '../../../../service/finance-kit';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { changeGroomingTicketStatus } from '../../../../store/grooming/grooming.actions';
import { getTicketPreAuthDetail } from '../../../../store/stripe/preAuth.actions';
import { selectPreAuth } from '../../../../store/stripe/preAuth.selectors';
import { isStripeSmartReaderServerDrivenAvailableCountry } from '../../../../store/stripe/stripeTerminal.boxes';
import { useBool } from '../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { usePaymentVersionInfo } from '../../../../utils/hooks/usePaymentVersionInfo';
import { useMoePay } from '../../../Payment/components/useMoePay';
import { AppointmentStatus } from '../../../TicketDetail/AppointmentStatus';
import { type KitOrderV2Model } from '../CartMixinOrderDrawer.types';
import { useDrawerScopeContext } from '../context/DrawerScopeContext';
import { CashierDeskContext } from '../context/useCashierDeskContext';
import { VenderContext } from '../context/VenderContext';
import { useProcessingFeesProcessor } from '../hooks/useProcessingFeesProcessor';
import { AddMoreItemSubpage } from '../SubpageRight/AddMoreItemSubpage/AddMoreItemSubpage';
import { DiscountSubpage } from '../SubpageRight/DiscountSubpage/DiscountSubpage';
import { MembershipSubpage } from '../SubpageRight/MembershipSubpage/MembershipSubpage';
import { MoeGoPayIntegrateSubpage } from '../SubpageRight/MoeGoPayIntegrateSubpage';
import { PackageSubpage } from '../SubpageRight/PackageSubpage/PackageSubpage';
import { PaymentChannelSubPage } from '../SubpageRight/PaymentChannel/PaymentChannelSubpage';
import { CashTenderedSubpage } from '../SubpageRight/SpecificPaymentChannelSubpage/CashTenderedSubpage';
import { CheckNumberSubpage } from '../SubpageRight/SpecificPaymentChannelSubpage/CheckNumberSubpage';
import { CreditOrDebitCardSubpage } from '../SubpageRight/SpecificPaymentChannelSubpage/CreditOrDebitCardSubpage';
import { PayOnlineSubpage } from '../SubpageRight/SpecificPaymentChannelSubpage/PayOnlineSubpage';
import { StripeSmartReaderSubpage } from '../SubpageRight/SpecificPaymentChannelSubpage/StripeSmartReaderSubpage';
import { SplitPaymentSubpage } from '../SubpageRight/SplitPaymentSubpage';
import { RightColumnPage } from './RightColumnPage';
import { usePreAuthPaymentChannel } from './usePreAuthPaymentChannel';

interface RightColumnCoordinatorProps {
  order: KitOrderV2Model;
  capabilities: ReturnType<typeof useChargeCapabilities>;
  shouldChargeAmount: MoeMoney;
}

export const RightColumnCoordinator = memo<RightColumnCoordinatorProps>((props) => {
  const { order, capabilities, shouldChargeAmount } = props;
  const { rightColumnPageRouter, columnsBridge, customer, chargeType, sourceId } = useDrawerScopeContext();
  const [realChargeAmount, setRealChargeAmount] = useState<MoeMoney>(shouldChargeAmount);
  const hasCheckedOut = useBool(false);
  const dispatch = useDispatch();
  useEffect(() => {
    setRealChargeAmount(shouldChargeAmount);
  }, [shouldChargeAmount]);

  useEffect(() => {
    if (isNormal(sourceId)) {
      dispatch(getTicketPreAuthDetail(Number(sourceId)));
    }
  }, [sourceId]);

  const checkout = useLatestCallback(async () => {
    try {
      await dispatch(changeGroomingTicketStatus({ groomingId: +sourceId, status: AppointmentStatus.FINISHED }));
    } catch {
      // edge case, checkout failed, do not block flow
      // TODO(yueyue): add log later
    } finally {
      hasCheckedOut.as(true);
    }
  });

  const [preAuth, business] = useSelector(selectPreAuth(Number(sourceId)), selectCurrentBusiness());
  const { isLoading: isLoadingPaymentVersion } = usePaymentVersionInfo();
  const channelsWithoutPreAuth = usePaymentChannelsV2(
    FinanceKit,
    FinanceKit.buildModel(RealmType.Business, business),
    customer.customerId,
  );

  const preAuthAsDefaultIfEnabled = chargeType === OrderChargeType.Deposit;
  const channelsWithPreAuth = usePreAuthPaymentChannel({
    kit: FinanceKit,
    originPaymentChannels: channelsWithoutPreAuth,
    preAuth: FinanceKit.buildModel(RealmType.PreAuth, preAuth),
    preAuthAsDefaultIfEnabled,
  });
  const paymentChannels = preAuthAsDefaultIfEnabled ? channelsWithPreAuth : channelsWithoutPreAuth;

  const convenienceFees = useConvenienceFee(
    FinanceKit,
    business.preferMoeGoPay(),
    FinanceKit.derivedModel.currentPaymentSetting,
    paymentChannels.attachedChannels,
    realChargeAmount.valueOf(),
  );
  useProcessingFeesProcessor(columnsBridge, convenienceFees);

  const pay = useMoePay({
    invoiceId: Number(order.id),
    customerId: customer.customerId,
    module: 'grooming',
    vendor: business.primaryPayType,
    useStripeSmartReaderServerDriven: isStripeSmartReaderServerDrivenAvailableCountry(business.country),
    businessId: business.id.toString(),
    // 这个组件，必是 true
    isNewOrderV4Flow: true,
  });

  const amounts = useMemo(() => {
    return {
      shouldChargeAmount,
      realChargeAmount,
      convenienceFees,
      updateRealChargeAmount: setRealChargeAmount,
    };
  }, [shouldChargeAmount, realChargeAmount, convenienceFees, setRealChargeAmount]);

  const oneTimeDiscountBaseAmount = useMemo(() => {
    return MoeMoney.fromMoney(order.subTotalAmount);
  }, [order]);

  const cashierContext = useMemo(() => {
    return {
      pay,
      paymentChannels,
      amounts,
      order,
      canSetTipsInPaymentFlow: capabilities.canSetTipsInPaymentFlow,
      checkout,
      hasCheckedOut: hasCheckedOut.value,
    };
  }, [amounts, pay, paymentChannels, order, capabilities.canSetTipsInPaymentFlow, checkout, hasCheckedOut.value]);

  // TODO(yueyue): 这里需要优化，isReady 状态反复，导致抖动
  // console.info('isReady', paymentChannels.loading, isLoadingPaymentVersion);
  const isReady = [paymentChannels.loading, isLoadingPaymentVersion].every((v) => !v);

  return (
    <div className={'moe-h-full moe-w-[600px] moe-bg-[#fafafa] moe-flex moe-flex-col moe-justify-center'}>
      <Spin isLoading={!isReady} classNames={{ base: 'moe-h-full', container: 'moe-h-full' }}>
        <Condition if={isReady}>
          {() => {
            return (
              <>
                <VenderContext>
                  <CashierDeskContext.Provider value={cashierContext}>
                    <Switch shortCircuit>
                      <Switch.Case if={rightColumnPageRouter.is(RightColumnPage.DEFAULT)}>
                        <PaymentChannelSubPage canUseSplitPayment={capabilities.canUseSplitPayment} />
                      </Switch.Case>
                      <Switch.Case if={rightColumnPageRouter.is(RightColumnPage.CASH_TENDERED)}>
                        <CashTenderedSubpage
                          canSaveAsCredit={true}
                          canPartialPayment={capabilities.canPartialPayment}
                        />
                      </Switch.Case>
                      <Switch.Case if={rightColumnPageRouter.is(RightColumnPage.CREDIT_OR_DEBIT_CARD)}>
                        <CreditOrDebitCardSubpage />
                      </Switch.Case>
                      <Switch.Case if={rightColumnPageRouter.is(RightColumnPage.CHECK_NUMBER)}>
                        <CheckNumberSubpage />
                      </Switch.Case>
                      <Switch.Case if={rightColumnPageRouter.is(RightColumnPage.PAY_ONLINE)}>
                        <PayOnlineSubpage customer={customer} />
                      </Switch.Case>
                      <Switch.Case if={rightColumnPageRouter.is(RightColumnPage.STRIPE_SMART_READER)}>
                        <StripeSmartReaderSubpage />
                      </Switch.Case>
                      <Switch.Case if={rightColumnPageRouter.is(RightColumnPage.SPLIT_PAYMENT)}>
                        <SplitPaymentSubpage />
                      </Switch.Case>
                      <Switch.Case if={rightColumnPageRouter.is(RightColumnPage.MGP_INTEGRATE_LANDING)}>
                        <MoeGoPayIntegrateSubpage />
                      </Switch.Case>
                      {/* promotion */}
                      <Switch.Case if={rightColumnPageRouter.is(RightColumnPage.SET_DISCOUNT)}>
                        <DiscountSubpage baseAmount={oneTimeDiscountBaseAmount} appointmentId={sourceId} />
                      </Switch.Case>
                      <Switch.Case if={rightColumnPageRouter.is(RightColumnPage.SET_MEMBERSHIP)}>
                        <MembershipSubpage appointmentId={sourceId} />
                      </Switch.Case>
                      <Switch.Case if={rightColumnPageRouter.is(RightColumnPage.SET_PACKAGE)}>
                        <PackageSubpage appointmentId={sourceId} customerId={String(customer.customerId)} />
                      </Switch.Case>
                      <Switch.Case if={rightColumnPageRouter.is(RightColumnPage.ADD_MORE_ITEM)}>
                        <AddMoreItemSubpage canSetItems={capabilities.canSetItems} />
                      </Switch.Case>
                    </Switch>
                  </CashierDeskContext.Provider>
                </VenderContext>
              </>
            );
          }}
        </Condition>
      </Spin>
    </div>
  );
});

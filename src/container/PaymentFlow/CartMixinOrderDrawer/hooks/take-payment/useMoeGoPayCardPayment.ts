import { <PERSON><PERSON><PERSON>, useSerialCallback } from '@moego/finance-utils';
import {
  type FNK_PaymentChannelModel,
  type KitModelType,
  type PaymentChannelPayload,
  PrefabPaymentChannel,
  type RealmType,
} from '@moego/finance-web-kit';
import { toast } from '@moego/ui';
import { useElements, useStripe } from '@stripe/react-stripe-js';
import { type FinanceKit } from '../../../../../service/finance-kit';
import { type CustomerRecord } from '../../../../../store/customer/customer.boxes';
import { type MoePay, type TakePaymentInput } from '../../../../Payment/components/MoePay';
import { useSetTips } from '../../../hooks/useSetTips';
import { type KitOrderV2Model } from '../../CartMixinOrderDrawer.types';
import { useCashierDeskContext } from '../../context/useCashierDeskContext';
import { useChooseSignature, useSignature } from '../useSignature';
import { type IHandleTakePaymentOptions } from './HandlePayment.types';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { useSelector } from 'amos';
import { type BusinessRecord } from '../../../../../store/business/business.boxes';
import { useProcessingPaymentModal } from '../../components/ProcessingPaymentModal';

interface IHandleMoeGoPayCardPaymentOptions<C extends MoeGoPayCardPaymentChannel> extends IHandleTakePaymentOptions {
  methodId: C;
  showTipsScreen?: boolean;
  canSetTipsInPaymentFlow: boolean;
}

type MoeGoPayCardPaymentChannel =
  | typeof PrefabPaymentChannel.MoeGoPayCardOnFile
  | typeof PrefabPaymentChannel.CreditCard
  | typeof PrefabPaymentChannel.PreAuth;

export const useMoeGoPayCardPayment = () => {
  const { pay } = useCashierDeskContext();
  const chooseSignature = useChooseSignature();
  const getSignature = useSignature();
  const setTips = useSetTips();
  const stripe = useStripe();
  const elements = useElements();
  const [business] = useSelector(selectCurrentBusiness);
  const openProcessingPaymentModal = useProcessingPaymentModal();
  return useSerialCallback(
    async <C extends MoeGoPayCardPaymentChannel>(
      options: IHandleMoeGoPayCardPaymentOptions<C> & {
        order: KitOrderV2Model;
      },
    ) => {
      const {
        takePaymentInput,
        attachedChannel,
        methodId,
        onSuccess,
        onCancel,
        order,
        amounts,
        showTipsScreen = true,
        canSetTipsInPaymentFlow,
        customer,
      } = options;
      const orderId = +order.id;
      pay.attachStripe(stripe, elements);
      const payload = attachedChannel.payload as unknown as PaymentChannelPayload[C];
      const isPreAuth = options.methodId === PrefabPaymentChannel.PreAuth;
      const preAuthInfo = isPreAuth
        ? (payload as PaymentChannelPayload<typeof FinanceKit.model>[typeof PrefabPaymentChannel.PreAuth]).preAuth
        : undefined;

      const isSuccessful = await normalizePayment(pay, takePaymentInput, methodId, payload);
      if (!isSuccessful) return onCancel();

      const withSignature = await chooseSignature({
        amount: MoeMoney.fromAmount(takePaymentInput.paymentAmount)
          .plus(
            amounts.convenienceFees.processingFeeEnabled
              ? MoeMoney.fromAmount(amounts.convenienceFees.processingFee)
              : MoeMoney.empty(),
          )
          .valueOf(),
        showProcessingFeePrompt: amounts.convenienceFees.showProcessingFeePrompt,
      });
      if (withSignature === undefined) return onCancel();
      pay.set('withSignature', withSignature);
      if (canSetTipsInPaymentFlow && showTipsScreen) {
        const tipsAmount = await setTips({
          tipBasedAmount: order.tipsBasedAmount.valueOf(),
          handleSubmitTips: async (amount) => {
            pay.set('tipsAmountValue', amount);
            Object.assign(takePaymentInput, {
              tipsAmountValue: amount,
            });
          },
        });
        if (tipsAmount === undefined) return onCancel();
      }
      return handleCharge({
        withSignature,
        amounts,
        pay,
        channel: attachedChannel.channel,
        customer: customer,
        orderId,
        preAuthInfo,
        getSignature,
        onSuccess,
        onCancel,
        business,
        openProcessingPaymentModal,
      });
    },
  );
};

async function handleCharge(
  props: {
    withSignature: boolean;
    pay: MoePay;
    amounts: IHandleTakePaymentOptions['amounts'];
    channel: FNK_PaymentChannelModel;
    customer: CustomerRecord;
    orderId: number;
    preAuthInfo?: KitModelType<typeof FinanceKit.model, RealmType.PreAuth>;
    getSignature: ReturnType<typeof useSignature>;
    business: BusinessRecord;
    openProcessingPaymentModal: ReturnType<typeof useProcessingPaymentModal>;
  } & Pick<IHandleTakePaymentOptions, 'onSuccess' | 'onCancel'>,
) {
  const {
    withSignature,
    pay,
    amounts,
    customer,
    preAuthInfo,
    orderId,
    getSignature,
    onSuccess,
    onCancel,
    business,
    openProcessingPaymentModal,
  } = props;

  pay.set('paymentAmount', amounts.realChargeAmount.valueOf());
  const isAddProcessingFee = pay.get('addProcessingFee');
  const paymentAmount = pay.get('paymentAmount');
  const tipsAmount = pay.get('tipsAmountValue');
  const tipsAmountText = business.formatAmount(tipsAmount);
  const processingFeeAmount = amounts.convenienceFees.processingFee;
  const paymentAmountWithProcessingFee = isAddProcessingFee ? paymentAmount + processingFeeAmount : paymentAmount;
  const paymentAmountWithProcessingFeeText = business.formatAmount(paymentAmountWithProcessingFee);
  const totalAmount = paymentAmountWithProcessingFee + tipsAmount;
  const totalAmountText = business.formatAmount(totalAmount);

  const title = tipsAmount
    ? `${totalAmountText}(${paymentAmountWithProcessingFeeText} + ${tipsAmountText})`
    : `${totalAmountText}`;

  if (withSignature) {
    const signature = await getSignature({
      onSign: async (url) => {
        pay.set('signature', url);
        await submitPay(pay, preAuthInfo && { preAuthId: preAuthInfo.preAuthId, orderId });
        onSuccess();
      },
      customizedSignFileName: () =>
        `${customer.firstName}_${customer.lastName}_signature_${Math.floor(Math.random() * 100000000)}`,
      title,
      description: 'I agree to pay the above total amount',
    });

    if (signature === false) return onCancel();
  } else {
    const close = openProcessingPaymentModal({
      title,
      description: 'Continue to payment',
    });
    try {
      await submitPay(pay, preAuthInfo && { preAuthId: preAuthInfo.preAuthId, orderId });
      onSuccess();
    } finally {
      close();
    }
  }
}

async function submitPay(
  pay: MoePay,
  preAuthOptions?: {
    preAuthId: number;
    orderId: number;
  },
) {
  if (preAuthOptions) {
    await pay.submitPreAuthPay(preAuthOptions.orderId, preAuthOptions.preAuthId);
  } else {
    await pay.submitPay();
  }
}

async function normalizePayment<C extends MoeGoPayCardPaymentChannel>(
  pay: MoePay,
  takePaymentInput: TakePaymentInput,
  methodId: C,
  payload: PaymentChannelPayload[C],
) {
  switch (methodId) {
    case PrefabPaymentChannel.MoeGoPayCardOnFile: {
      const typedPayload = payload as PaymentChannelPayload[typeof PrefabPaymentChannel.MoeGoPayCardOnFile];
      Object.assign(takePaymentInput, {
        stripeCardOnFile: typedPayload.card,
        cardOnFileId: typedPayload.id,
      });
      pay.startPay(takePaymentInput);
      return true;
    }
    case PrefabPaymentChannel.CreditCard: {
      const typedPayload = payload as PaymentChannelPayload[typeof PrefabPaymentChannel.CreditCard];
      const tokenInfo = await typedPayload.getStripeToken?.();
      pay.startPay(takePaymentInput);
      if (!tokenInfo) {
        toast({
          title: 'Failed to get stripe token',
          type: 'error',
        });
        return false;
      }
      if (tokenInfo.tokenError) {
        toast({
          title: tokenInfo.tokenError,
          type: 'error',
        });
        return false;
      }
      Object.assign(takePaymentInput, {
        stripeToken: tokenInfo.token,
      });
      return true;
    }
    case PrefabPaymentChannel.PreAuth: {
      pay.startPreAuthPay(takePaymentInput);
    }
  }
  return true;
}

import { type PreviewCreateOrderParams } from '@moego/api-web/moego/api/order/v2/order_api';
import { ItemType } from '@moego/api-web/moego/models/order/v1/order_enums';
import { SourceType } from '@moego/api-web/moego/models/promotion/v1/promotion';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@moego/finance-utils';
import { AmountCalcType, RealmType } from '@moego/finance-web-kit';
import { filter } from 'lodash';
import { FinanceKit } from '../../../../service/finance-kit';
import { CouponRecord } from '../../../../store/PaymentFlow/cart.boxes';
import {
  PromotionType,
  type PromotionItemInPreview,
  type PromotionItemOrderApplied,
} from '../Promotions/Promotion.types';
import { type OptimizedPreviewOrderParams, type PreviewOrderResult } from './Preview.types';

const DEFAULT_ORDER = FinanceKit.buildModel(RealmType.OrderV2, {});

export type PromotionsByTypeMap<
  ItemModel extends PromotionItemInPreview | PromotionItemOrderApplied = PromotionItemInPreview,
> = Map<PromotionType, ItemModel[]>;

export type ItemsByTypeMap = Map<ItemType, PreviewCreateOrderParams['items']>;

export const DEFAULT_ITEMS_BY_TYPE_MAP: ItemsByTypeMap = new Map(
  Object.values(ItemType).reduce((acc, type) => {
    acc.set(type as ItemType, []);
    return acc;
  }, new Map<ItemType, PreviewCreateOrderParams['items']>()),
);

export const DEFAULT_PROMOTIONS_BY_TYPE_MAP: PromotionsByTypeMap = new Map(
  Object.values(PromotionType).reduce((acc, type) => {
    acc.set(type as PromotionType, []);
    return acc;
  }, new Map<PromotionType, PromotionItemInPreview[]>()),
);

/**
 * default preview result, used for initial preview
 * make sure the result is not null
 */
export const DEFAULT_PREVIEW_RESULT: PreviewOrderResult = {
  normalizedOrder: DEFAULT_ORDER,
  appliedPromotions: {
    promotions: [],
  },
  // itemsByTypeMap: DEFAULT_ITEMS_BY_TYPE_MAP,
  promotionsByTypeMap: DEFAULT_PROMOTIONS_BY_TYPE_MAP,
  order: {
    orderDetail: {
      order: DEFAULT_ORDER,
      orderItems: [],
      tipsDetail: [],
      orderPayments: [],
      refundOrderPayments: [],
      orderDiscounts: [],
      orderPromotions: [],
    },
    petBriefs: [],
    staffBriefs: [],
    subtotalByItemType: {},
    subtotalByTax: [],
    preDiscountSubtotalByItemType: {},
  },
  originalResult: null,
};

export function flatPromotions(
  promotionsByType: PromotionsByTypeMap,
): Exclude<PreviewCreateOrderParams['appliedPromotions'], undefined>['promotions'] {
  return Object.values(promotionsByType).flat();
}

export function flatItems(itemsByType: ItemsByTypeMap): PreviewCreateOrderParams['items'] {
  return [...itemsByType.values()].flat();
}

export function flatMapValues<T, R>(map: Map<T, R[]>): R[] {
  return [...map.values()].flat();
}

export function groupItemsByType(items: PreviewCreateOrderParams['items'], fullMap: ItemsByTypeMap): ItemsByTypeMap {
  const newMap = items.reduce((acc, item) => {
    acc.set(item.itemType as ItemType, [...(acc.get(item.itemType as ItemType) ?? []), item]);
    return acc;
  }, new Map());

  fullMap.forEach((_, key) => {
    if (!newMap.has(key)) {
      newMap.set(key, []);
    }
  });

  return newMap;
}

export function generatePromotionSortKey(p: PromotionItemInPreview): string {
  let key = p.couponSource ? CouponRecord.createKeyId(p.couponSource) : '';
  if (!key && p.oneTimeDiscount) {
    const type = p.oneTimeDiscount.discountAmount ? AmountCalcType.AMOUNT : AmountCalcType.PERCENTAGE;
    const value =
      type === AmountCalcType.AMOUNT
        ? MoeMoney.fromMoney(p.oneTimeDiscount.discountAmount ?? MoeMoney.empty()).valueOf()
        : MoeDecimal.fromDecimal(p.oneTimeDiscount.discountPercentage ?? MoeDecimal.empty()).valueOf();
    key = `one-time-discount-${type}-${value}`;
  }

  if (!key && p.storeCredit) {
    const value = MoeMoney.fromMoney(p.storeCredit ?? MoeMoney.empty()).valueOf() + '';
    key = `store-credit-${value}`;
  }

  return key;
}

export function getPromotionType(promotion: PromotionItemInPreview) {
  if (promotion.couponSource?.type === SourceType.DISCOUNT) {
    return PromotionType.Discount;
  }
  if (
    promotion.couponSource?.type === SourceType.MEMBERSHIP_DISCOUNT ||
    promotion.couponSource?.type === SourceType.MEMBERSHIP_QUANTITY
  ) {
    return PromotionType.Membership;
  }

  if (promotion.couponSource?.type === SourceType.PACKAGE) {
    return PromotionType.Package;
  }

  if (promotion.storeCredit) {
    return PromotionType.StoreCredit;
  }

  if (promotion.oneTimeDiscount) {
    return PromotionType.OneTimeDiscount;
  }

  // should not reach here
  return PromotionType.Unknown;
}

export interface PromotionWithSortHelperData extends PromotionItemInPreview {
  helper: {
    key: string;
    type: PromotionType;
    updateTime: number;
    originIndex: number;
    // 在后续的排序中，用来处理key相同的排序
    usedFlag: boolean;
  };
}

export function appendSortHelperDataToPromotions(
  promotions: PromotionItemInPreview[],
  startIndex: number,
): PromotionWithSortHelperData[] {
  const withSortDateList = [...promotions].map((p, index) => {
    return {
      ...p,
      helper: {
        key: generatePromotionSortKey(p),
        type: getPromotionType(p),
        updateTime: new Date().getTime(),
        originIndex: startIndex + index,
        usedFlag: false,
      },
    };
  });

  return withSortDateList;
}

export function addPromotions(
  prevPromotions: PromotionWithSortHelperData[],
  addedPromotions: OptimizedPreviewOrderParams['appliedPromotions'],
): PromotionWithSortHelperData[] {
  const otherPromotions = filter(prevPromotions, (p) => p.helper.type !== addedPromotions.type);
  const prevPromotionsOfSameType = filter(prevPromotions, (p) => p.helper.type === addedPromotions.type);
  const nextPromotions = appendSortHelperDataToPromotions(addedPromotions.promotions, otherPromotions.length);
  nextPromotions.forEach((p) => {
    const findPrev = prevPromotionsOfSameType.find((t) => t.helper.key === p.helper.key && !t.helper.usedFlag);
    if (findPrev) {
      findPrev.helper.usedFlag = true;
      p.helper = findPrev.helper;
      p.helper.updateTime = new Date().getTime();
    }
  });

  // sort promotions
  const promotions = [...otherPromotions, ...nextPromotions].sort((a, b) => {
    if (a.helper.originIndex !== b.helper.originIndex) {
      return a.helper.originIndex - b.helper.originIndex;
    }

    return a.helper.updateTime - b.helper.updateTime;
  });

  return promotions;
}

import { useSelector } from 'amos';
import React from 'react';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { useFloatableHost } from '../../../../utils/hooks/useFloatableHost';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { SelectSignatureModal } from '../../../Calendar/Grooming/TakePaymentModal/SelectSignatureModal';
import { SignatureModal, type SignatureModalProps } from '../components/SignatureModal';

export const useSignature = () => {
  const { mountModal } = useFloatableHost<boolean>();

  return useLatestCallback((props: Omit<SignatureModalProps, 'isOpen' | 'onClose'>) => {
    const { onSign, title, description } = props;
    const { promise, closeFloatable } = mountModal(() => {
      return (
        <SignatureModal
          isOpen
          onClose={() => {
            closeFloatable(false);
          }}
          onSign={async (url) => {
            await onSign(url);
            closeFloatable(true);
          }}
          title={title}
          description={description}
        />
      );
    });
    return promise;
  });
};

export const useChooseSignature = () => {
  const { mountModal } = useFloatableHost<boolean>();
  const [business] = useSelector(selectCurrentBusiness);

  return useLatestCallback((props: { amount: number; showProcessingFeePrompt: boolean }) => {
    const { amount, showProcessingFeePrompt } = props;
    const { promise, closeFloatable } = mountModal(() => {
      return (
        <SelectSignatureModal
          amount={amount}
          business={business}
          onSubmit={(withSignature) => {
            closeFloatable(withSignature);
          }}
          onClose={() => {
            closeFloatable();
          }}
          showProcessingFeePrompt={showProcessingFeePrompt}
        />
      );
    });
    return promise;
  });
};

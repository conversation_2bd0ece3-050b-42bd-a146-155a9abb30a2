/**
 * preview order and status
 */
import { type GetOrderDetailResult } from '@moego/api-web/moego/api/order/v1/order_api';
import { RealmType } from '@moego/finance-web-kit';
import { useRef, useState } from 'react';
import { FinanceKit } from '../../../../service/finance-kit';
import { useBool } from '../../../../utils/hooks/useBool';
import { ColumnsBridgeEvent, type IColumnsBridge } from '../Columns/ColumnsBridge';
import { type Bridge } from '../utils/bridge';
import { useBridgeEventData, useBridgeListener } from './use-bridge-hooks';

export const usePreviewOrder = (options: {
  columnsBridge: Bridge<IColumnsBridge>;
  initialIsInPreview?: boolean;
}) => {
  const isInPreview = useBool(options.initialIsInPreview ?? false);
  const isFirstPreview = useRef(true);
  const [orderAllDetailView, setOrderAllDetailView] = useState<GetOrderDetailResult | null>(null);
  const { columnsBridge } = options;
  useBridgeListener(columnsBridge, ColumnsBridgeEvent.OnBeforePreviewOrder, () => {
    isInPreview.as(true);
  });
  useBridgeListener(columnsBridge, ColumnsBridgeEvent.OnAfterPreviewOrder, () => {
    isInPreview.as(false);
    isFirstPreview.current = false;
  });

  const previewedOrder = useBridgeEventData(
    columnsBridge,
    ColumnsBridgeEvent.OnPreviewOrderReady,
    FinanceKit.buildModel(RealmType.OrderV2, {}),
  );

  useBridgeListener(columnsBridge, ColumnsBridgeEvent.OnPreviewOrderReady, (order, original) => {
    setOrderAllDetailView(original ?? null);
  });

  return {
    isInPreview: isInPreview.value,
    isFirstPreview: isFirstPreview.current,
    orderAllDetailView,
    previewedOrder,
  };
};

import { useMemo } from 'react';
import { useViewInvoiceDrawerContext } from '../ViewInvoiceDrawer.context';
import { OrderPaymentStatus } from '@moego/api-web/moego/models/order/v1/order_enums';
import { RefundOrderPaymentStatus } from '@moego/api-web/moego/models/order/v1/refund_order_enums';
import { MoeMoney } from '@moego/finance-utils';
import { uniqBy } from 'lodash';

const ProcessingPaymentStatus = [OrderPaymentStatus.CREATED, OrderPaymentStatus.TRANSACTION_CREATED];
const DisplayedPaymentStatus = [...ProcessingPaymentStatus, OrderPaymentStatus.PAID];

const RefundProcessingPaymentStatus = [RefundOrderPaymentStatus.CREATED, RefundOrderPaymentStatus.TRANSACTION_CREATED];
const DisplayedRefundOrderPaymentStatus = [...RefundProcessingPaymentStatus, RefundOrderPaymentStatus.REFUNDED];

export const useDepositCollectedPaymentItems = () => {
  const { invoiceDetail } = useViewInvoiceDrawerContext();
  const { deposit, invoice } = invoiceDetail;
  const mergedOrderPayments = uniqBy(
    [...(invoice.orderDetail.orderPayments || []), ...(deposit.orderDetail?.orderPayments || [])],
    'id',
  ).sort((a, b) => b.createTime.localeCompare(a.createTime));

  const paymentList = mergedOrderPayments.filter(
    (payment) => DisplayedPaymentStatus.includes(payment.paymentStatus) && payment.isDeposit,
  );

  const items = useMemo(() => {
    return paymentList.map((payment) => ({
      id: payment.id,
      name: `Paid by ${payment.paymentMethodDisplayName} - Deposit`,
      amount: payment.totalAmount,
      isProcessing: ProcessingPaymentStatus.includes(payment.paymentStatus),
      createTime: payment.createTime,
    }));
  }, [paymentList]);

  return items;
};

export const useTotalCollectedPaymentItems = () => {
  const { invoiceDetail } = useViewInvoiceDrawerContext();
  const { invoice } = invoiceDetail;
  const { orderPayments } = invoice.orderDetail;
  const paymentList = orderPayments.filter(
    (payment) => DisplayedPaymentStatus.includes(payment.paymentStatus) && !payment.isDeposit,
  );
  const items = useMemo(() => {
    return paymentList.map((payment) => ({
      id: payment.id,
      name: `Paid by ${payment.paymentMethodDisplayName}`,
      amount: payment.totalAmount,
      isProcessing: ProcessingPaymentStatus.includes(payment.paymentStatus),
      createTime: payment.createTime,
    }));
  }, [paymentList]);

  return items;
};

export const useRefundedPaymentItems = () => {
  const { invoiceDetail } = useViewInvoiceDrawerContext();
  const { invoice, deposit } = invoiceDetail;

  const paymentList = uniqBy(
    [
      ...(deposit.orderDetail?.refundOrderPayments || []).map((payment) => ({
        ...payment,
        isDeposit: true,
      })),
      ...invoice.orderDetail.refundOrderPayments.map((payment) => ({
        ...payment,
        isDeposit: false,
      })),
    ],
    'id',
  ).filter((payment) => DisplayedRefundOrderPaymentStatus.includes(payment.refundOrderPaymentStatus));

  const items = paymentList.map((payment) => ({
    id: payment.id,
    name: `Refunded to ${payment.refundPaymentMethodDisplayName} ${payment.isDeposit ? '- Deposit' : ''}`,
    amount: payment.refundTotalAmount.multiply(MoeMoney.fromAmount(-1)),
    isProcessing: RefundProcessingPaymentStatus.includes(payment.refundOrderPaymentStatus),
    createTime: payment.createTime,
    isRefunded: true,
  }));

  return items;
};

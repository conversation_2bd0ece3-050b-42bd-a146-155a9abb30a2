import React from 'react';
import { PaymentActivityList } from '../components/PaymentActivityList';
import {
  useDepositCollectedPaymentItems,
  useRefundedPaymentItems,
  useTotalCollectedPaymentItems,
} from './PaymentDetails.hooks';

export const PaymentDetails = () => {
  const depositCollectedPaymentItems = useDepositCollectedPaymentItems();
  const totalCollectedPaymentItems = useTotalCollectedPaymentItems();
  const refundedPaymentItems = useRefundedPaymentItems();
  const allPaymentItems = [
    ...depositCollectedPaymentItems,
    ...totalCollectedPaymentItems,
    ...refundedPaymentItems,
  ].sort((a, b) => {
    return b.createTime.localeCompare(a.createTime);
  });

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-m">
      <PaymentActivityList items={allPaymentItems} />
    </div>
  );
};

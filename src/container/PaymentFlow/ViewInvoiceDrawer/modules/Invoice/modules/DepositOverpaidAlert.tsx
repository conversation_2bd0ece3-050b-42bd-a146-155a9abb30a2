import { <PERSON><PERSON>, But<PERSON> } from '@moego/ui';
import React from 'react';
import { useViewInvoiceDrawerContext } from '../../../ViewInvoiceDrawer.context';
import { OrderSourceType } from '@moego/api-web/moego/models/order/v1/order_enums';
import { useSelector } from 'amos';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { useOverpaidDepositOrders } from '@moego/finance-web-kit';
import { FinanceKit } from '../../../../../../service/finance-kit';
import { useRefundByItems } from '../../../../hooks/useRefundByItems';

export const DepositOverpaidAlert = () => {
  const [business] = useSelector(selectCurrentBusiness);
  const { invoiceDetail, refresh } = useViewInvoiceDrawerContext();
  const haveDeposit = invoiceDetail.deposit.orderDetail;
  const { refundByItems } = useRefundByItems();
  const overpaidAlertReady = [OrderSourceType.APPOINTMENT, OrderSourceType.NO_SHOW].includes(invoiceDetail.sourceType);
  const { overpaidDepositOrders, overpaidTotalAmount, isOverpaid } = useOverpaidDepositOrders(FinanceKit, {
    businessId: invoiceDetail.businessId,
    sourceId: overpaidAlertReady ? invoiceDetail.sourceId : '',
    sourceType: invoiceDetail.sourceType,
  });

  if (!isOverpaid || !haveDeposit) return null;
  return (
    <Alert
      color="warning"
      isCloseable={false}
      description={`${business.formatMoney(overpaidTotalAmount)} deposit overpaid.`}
      isBordered
      action={
        <Button
          size="s"
          variant="secondary"
          onPress={async () => {
            await refundByItems(overpaidDepositOrders[0].order);
            await refresh();
          }}
        >
          Refund deposit
        </Button>
      }
    />
  );
};

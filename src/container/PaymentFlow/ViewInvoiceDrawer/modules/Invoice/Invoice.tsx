import React from 'react';
import { BasicInfo } from './modules/BasicInfo';
import { Items } from './modules/Items';
import { PaymentSummary } from './modules/PaymentSummary';
import { cn } from '@moego/ui';
import { InvoiceMode } from '../../const';
import { useViewInvoiceDrawerContext } from '../../ViewInvoiceDrawer.context';
import { NoShowItems } from './modules/NoShowItems';
import { InvoiceType } from '../../InvoiceDetailModel';
import { DepositOverpaidAlert } from './modules/DepositOverpaidAlert';

export interface InvoiceProps {
  className?: string;
  mode?: InvoiceMode;
  showItemStaff?: boolean;
  showItemDescription?: boolean;
}

export const Invoice = (props: InvoiceProps) => {
  const { className, mode, showItemStaff, showItemDescription } = props;
  const { invoiceDetail } = useViewInvoiceDrawerContext();
  const isEstimateType = invoiceDetail.type === InvoiceType.Estimate;
  const isExportMode = mode === InvoiceMode.Print || mode === InvoiceMode.Email;

  return (
    // gap-y 写死，Email 模式下期望不随 css 变量变化而变化
    <div className={cn('moe-flex moe-flex-col moe-gap-y-[16px]', className)}>
      <BasicInfo mode={mode} />
      <DepositOverpaidAlert />
      <Items mode={mode} showItemStaff={showItemStaff} showItemDescription={showItemDescription} />
      <NoShowItems />
      <PaymentSummary readonly={isExportMode || isEstimateType || invoiceDetail.isOrderV0} mode={mode} />
    </div>
  );
};

Invoice.displayName = 'Invoice';

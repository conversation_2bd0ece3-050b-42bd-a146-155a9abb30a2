import { Button, Heading, Steps, Text, cn } from '@moego/ui';
import { MajorDollarFilled, MajorInvoiceRefundFilled } from '@moego/icons-react';
import React from 'react';
import { type <PERSON><PERSON><PERSON> } from '@moego/finance-utils';
import { useSelector } from 'amos';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import { useViewInvoiceDrawerContext } from '../ViewInvoiceDrawer.context';
import { useViewReceipts } from '../hooks/useViewReceipts';

export interface PaymentActivityItem {
  id: string;
  name: string;
  amount: MoeMoney;
  createTime: string;
  /** 金额灰色展示，不计入 Total 中 */
  isProcessing?: boolean;
  isRefunded?: boolean;
}

export interface PaymentActivityListProps {
  items: PaymentActivityItem[];
}

export const PaymentActivityList = ({ items }: PaymentActivityListProps) => {
  const openViewReceipts = useViewReceipts();
  const { permissions } = useViewInvoiceDrawerContext();
  const [business] = useSelector(selectCurrentBusiness);

  const renderItem = (item: PaymentActivityItem) => {
    return (
      <Steps.Item
        key={item.id}
        title={
          <div className="moe-flex moe-items-center moe-justify-between moe-w-full moe-text-primary">
            <Text variant="regular-short">{item.name}</Text>
            <Text variant="regular-short" className={cn({ 'moe-text-disabled': item.isProcessing })}>
              {business.formatMoney(item.amount)}
            </Text>
          </div>
        }
        description={
          <div>
            <Text variant="small" className="moe-text-tertiary">
              Completed at {business.formatDateTime(Number(item.createTime) * T_SECOND)}
            </Text>
          </div>
        }
        classNames={{
          stepIcon: cn(
            item.isRefunded ? 'moe-text-icon-warning' : 'moe-text-icon-success',
            'moe-w-[24px] moe-h-[24px] moe-bg-neutral-sunken-0',
          ),
        }}
        icon={item.isRefunded ? <MajorInvoiceRefundFilled size={24} /> : <MajorDollarFilled size={24} />}
      />
    );
  };

  if (items.length === 0) {
    return null;
  }

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-xs">
      <div className="moe-flex moe-items-center moe-justify-between">
        <Heading size="5">Payment activity</Heading>
        {permissions.viewReceipts.visible && (
          <Button variant="tertiary" onPress={openViewReceipts} isDisabled={permissions.viewReceipts.disabled} size="s">
            View receipt(s)
          </Button>
        )}
      </div>
      <div className="moe-p-s moe-rounded-m moe-bg-neutral-sunken-0">
        <Steps orientation="vertical" variant="icon" isFinished>
          {items.map(renderItem)}
        </Steps>
      </div>
    </div>
  );
};

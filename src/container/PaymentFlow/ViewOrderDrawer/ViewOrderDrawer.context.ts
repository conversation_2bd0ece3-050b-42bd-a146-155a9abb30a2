import { type StrictEnumValues, createStrictEnum } from '@moego/finance-utils';
import {
  type IGeneralOrderDetailV2,
  type FNK_OrderV1Model,
  type FNK_OrderV2Model,
  type FNK_RefundOrderModel,
} from '@moego/finance-web-kit';
import { createContext, useContext } from 'react';
import { type TabKey } from './ViewOrderDrawer';
import { type FinanceKit } from '../../../service/finance-kit';

export type RefreshOrderIdType = number | 'all';
export const ViewOrderDrawerSubpage = createStrictEnum({
  DEFAULT: ['default', {}],
  SEND_RECEIPT: ['send_receipt', {}],
});

export type TypeofViewOrderDrawerSubpage = StrictEnumValues<typeof ViewOrderDrawerSubpage>;
export interface ViewOrderDrawerContextModal {
  enterOrderId?: string;
  // enterOrder: OrderContextModel['order'] | null;
  onSendReceipt: (order: FNK_OrderV1Model | FNK_OrderV2Model | FNK_RefundOrderModel) => void;
  onPrint: (orderDetail: IGeneralOrderDetailV2<typeof FinanceKit.model>) => void;
  refreshOrderList: () => Promise<void>;
  onChangeTab: (tab: TabKey) => void;
  overpaidDepositOrderDetail?: IGeneralOrderDetailV2<typeof FinanceKit.model>;
  onRefundOverpaidDeposit?: () => Promise<void>;
  // toRefreshId: RefreshOrderIdType;
  // activityRefreshToken: number;
  // setToRefreshId: React.Dispatch<React.SetStateAction<RefreshOrderIdType>>;
  // refreshActivityToken: ReturnType<typeof useCounter>[1]['inc'];
}

export const ViewOrderDrawerContext = createContext<ViewOrderDrawerContextModal | null>(null);

export const useViewOrderDrawerContext = () => {
  const context = useContext(ViewOrderDrawerContext);
  if (!context) {
    throw new Error('ViewOrderDrawerContext is not found');
  }
  return context;
};

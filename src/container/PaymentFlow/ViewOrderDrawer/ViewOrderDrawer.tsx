import { useLatestCallback, useSerialCallback } from '@moego/finance-utils';
import {
  RealmType,
  assertModel,
  usePaymentList,
  type GenericOrderDetail,
  type IGeneralOrderDetailV2,
  type KitModelType,
  type IRefundOrderDetail,
} from '@moego/finance-web-kit';
import {
  Badge,
  Button,
  Condition,
  Drawer,
  Dropdown,
  Heading,
  MajorChevronDownOutlined,
  Tabs,
  Tooltip,
  toast,
  type Key,
  type ModalProps,
} from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import ErrorBoundary from 'antd/lib/alert/ErrorBoundary';
import { noop } from 'lodash';
import React, { memo, useMemo, useState } from 'react';
import { type FinanceKit } from '../../../service/finance-kit';
import { createExtraOrder } from '../../../store/PaymentFlow/order.actions';
import { selectCurrentPermissions } from '../../../store/business/role.selectors';
import { type InvoiceModuleType } from '../../../store/payment/payment.selectors';
import { useDrawer } from '../../../utils/Drawer';
import { PaymentActionName } from '../../../utils/reportData/payment';
import { LayoutSubpage } from '../TakePaymentDrawer/components/Layout/LayoutSubpage';
import { LayoutSubpageHeader } from '../TakePaymentDrawer/components/Layout/LayoutSubpageHeader';
import { LayoutSubpageScrollBody } from '../TakePaymentDrawer/components/Layout/LayoutSubpageScrollBody';
import { useTakePayment } from '../TakePaymentDrawer/useTakePayment';
import { type OrderContextModel } from '../hooks/OrderContext';
import { useInvoiceReinventReport } from '../hooks/useInvoiceReinvent.report';
import { OrderListSubpage } from './OrderListSubpage/OrderListSubpage';
import { PaymentActivityList } from './PaymentActivitySubpage/PaymentActivityList';
import { SendReceiptSubpage } from './SendReceiptSubpage/SendReceiptSubpage';
import { TipsCollected } from './TipsCollected/TipsCollected';
import {
  ViewOrderDrawerContext,
  ViewOrderDrawerSubpage,
  type TypeofViewOrderDrawerSubpage,
  type ViewOrderDrawerContextModal,
} from './ViewOrderDrawer.context';
import { useViewOrderListAndTips } from './hooks/useViewOrderListAndTips';
import { TIP_FOR_EDIT_STAFF_IN_INVOICE, isShowEditStaff } from './utils';

import { useEditStaffAndSplitTip } from '../EditStaffAndSplitTip/EditStaffAndSplitTip';

import { type OrderSourceType } from '@moego/api-web/moego/models/order/v1/order_enums';
import { captureException } from '@sentry/browser';
import { InvoiceTestIds } from '../../../config/testIds/invoice';
import { useAsyncCallback } from '../../../utils/hooks/useAsyncCallback';
import { useExtraTipFlow } from '../hooks/useExtraTipFlow';
import { useInvoiceReinvent } from '../hooks/useInvoiceReinvent';
import { useRefundByItems } from '../hooks/useRefundByItems';
import { usePrint } from './hooks/usePrint';
import { PrintReceiptContent } from './OrderListSubpage/PrintReceiptContent';

export interface ViewInvoiceDrawerProps {
  readonly orderId?: string;
  readonly sourceId?: string;
  readonly sourceType?: OrderSourceType;
  readonly businessId?: string;
  readonly module: InvoiceModuleType;
  onClose?: () => void;
}

export enum TabKey {
  OrderList = 'orderList',
  PaymentActivity = 'paymentActivity',
  Tips = 'tips',
}

enum ExtraChangeAction {
  AddTips = 'Add tips',
  AddExtraItems = 'Add extra items',
}

const useOverpaidDepositOrder = (props: {
  orderList: GenericOrderDetail<typeof FinanceKit.model>[];
  onRefunded: () => void;
}) => {
  const { orderList, onRefunded } = props;
  const { refundByItems } = useRefundByItems();

  const overpaidDepositOrder = orderList.find((orderDetail) => {
    const { order } = orderDetail;
    return assertModel(order, RealmType.OrderV2) && order.isOverPaid && order.isDepositOrder;
  }) as IGeneralOrderDetailV2<typeof FinanceKit.model>;

  const handleRefundDeposit = useSerialCallback(async () => {
    if (!overpaidDepositOrder) return;
    const result = await refundByItems(overpaidDepositOrder.order);
    if (result) {
      onRefunded();
      toast({
        type: 'success',
        title: 'Items refunded!',
      });
    }
  });

  return {
    overpaidDepositOrder,
    onRefundOverpaidDeposit: handleRefundDeposit,
  };
};

export const ViewOrderDrawer = memo<ModalProps & ViewInvoiceDrawerProps>(function CalendarDrawer(props) {
  const { zIndex } = useDrawer('viewInvoice');
  const { orderId: enterOrderId, ...restProps } = props;
  const [permissions] = useSelector(selectCurrentPermissions());
  const [subpage, setSubpage] = useState<TypeofViewOrderDrawerSubpage>(ViewOrderDrawerSubpage.DEFAULT);
  const [sendingReceiptOrder, setSendingReceiptOrder] = useState<OrderContextModel['order'] | null>(null);
  const [activeTab, setActiveTab] = useState<TabKey>(TabKey.OrderList);
  const dispatch = useDispatch();
  const {
    originOrder,
    orderList,
    loading,
    refreshOrderList,
    tipsSplitInfo,
    informTipsUpdated,
    refreshTipsSplitInfo,
    isTipsSplitInitLoading,
  } = useViewOrderListAndTips({
    enterOrderId,
    sourceId: props.sourceId,
    sourceType: props.sourceType,
    businessId: props.businessId,
  });
  const { overpaidDepositOrder, onRefundOverpaidDeposit } = useOverpaidDepositOrder({
    orderList,
    onRefunded: refreshOrderList,
  });
  const { isNewOrderV4Flow } = useInvoiceReinvent();

  const addExtraTip = useExtraTipFlow({
    apptId: String(
      props.sourceId ||
        (originOrder?.order && assertModel(originOrder?.order, RealmType.OrderV1) ? originOrder?.order.groomingId : ''),
    ),
    orderId: String(originOrder?.order.id),
    businessId: String(originOrder?.order.businessId),
    customerId: String(originOrder?.order.customerId),
  });
  const paymentList = usePaymentList(orderList);

  const { requestTakeExtraCharge } = useTakePayment();
  const reportPaymentData = useInvoiceReinventReport();

  const handleAddTips = useLatestCallback(async () => {
    reportPaymentData(PaymentActionName.InvoiceAddTipsApptBase, { orderId: enterOrderId, ctaId: 'invoice' });
    await addExtraTip();
    refreshOrderList();
  });

  const handleAddExtraItems = useLatestCallback(async () => {
    reportPaymentData(PaymentActionName.InvoiceExtraCharge, { orderId: enterOrderId, ctaId: 'invoice' });
    if (!enterOrderId) return;
    const extraOrderId = await dispatch(createExtraOrder(enterOrderId));
    await requestTakeExtraCharge({
      originOrderId: enterOrderId,
      invoiceId: +extraOrderId,
      module: 'grooming',
      onClose: noop,
    });

    refreshOrderList();
  });

  const handleRefunded = useLatestCallback(async () => {
    refreshOrderList();
  });

  const handleSendReceipt = useLatestCallback((targetOrder) => {
    setSubpage(ViewOrderDrawerSubpage.SEND_RECEIPT);
    setSendingReceiptOrder(targetOrder);
  });

  const { print } = usePrint();

  const handlePrintReceipt = useLatestCallback(async (orderDetail: IGeneralOrderDetailV2<typeof FinanceKit.model>) => {
    const refundedOrderDetails = orderList.filter((item) => {
      return assertModel(item.order, RealmType.RefundOrder) && item.order.orderId === orderDetail.order.id;
    }) as IRefundOrderDetail<typeof FinanceKit.model>[];
    await print({
      element: ({ print }) => (
        <PrintReceiptContent orderDetail={orderDetail} refundedOrderDetails={refundedOrderDetails} onLoaded={print} />
      ),
      manual: true,
    });
  });

  // 现在接口实现不好，暂时通过这个减少请求，精准刷新而非全量刷新
  const context: ViewOrderDrawerContextModal = useMemo(() => {
    return {
      enterOrderId,
      onSendReceipt: handleSendReceipt,
      onPrint: handlePrintReceipt,
      refreshOrderList,
      onChangeTab: setActiveTab,
      overpaidDepositOrderDetail: overpaidDepositOrder,
      onRefundOverpaidDeposit,
    };
  }, [enterOrderId, refreshOrderList]);

  const showEditStaff = isShowEditStaff(orderList, enterOrderId) && !isNewOrderV4Flow;
  const hasEditStaffPermission = permissions.has('manageInvoiceStaff');
  const editStaffAndSplitTip = useEditStaffAndSplitTip();
  const handleEditStaff = useAsyncCallback(async () => {
    if (!grabOrder) {
      captureException(new Error('grabOrder is undefined'));
      return;
    }
    reportPaymentData(PaymentActionName.InvoiceEditStaffInAppt, { orderId: enterOrderId });
    await editStaffAndSplitTip(String(grabOrder.businessId), String(grabOrder.id));
    refreshOrderList();
  });

  const grabOrder = orderList.find((item) => {
    return (
      (assertModel(item.order, RealmType.OrderV2) || assertModel(item.order, RealmType.OrderV1)) &&
      !item.order.isExtraOrder &&
      !item.order.isExtraTipsOrder
    );
  })?.order as KitModelType<typeof FinanceKit.model, RealmType.OrderV2> | undefined;
  const canExtraCharge = !!grabOrder?.invoiceActionCapabilities?.canExtraCharge;

  const actionList = [
    {
      key: ExtraChangeAction.AddTips,
      title: ExtraChangeAction.AddTips,
      action: handleAddTips,
      permission: permissions.has('addExtraTips'),
    },
    {
      key: ExtraChangeAction.AddExtraItems,
      title: ExtraChangeAction.AddExtraItems,
      action: handleAddExtraItems,
      permission: permissions.has('addExtraItems'),
    },
  ].filter((item) => item.permission);

  const handleExtraChangeAction = (key: ExtraChangeAction) => {
    const actionConfig = actionList.find((item) => item.key === key);
    if (actionConfig) {
      actionConfig.action();
    }
  };

  const TabsList = [
    {
      label: 'Details',
      value: TabKey.OrderList,
    },
    {
      label: 'Payment activity',
      value: TabKey.PaymentActivity,
    },
    {
      label: 'Tips',
      value: TabKey.Tips,
      badge: tipsSplitInfo?.tipsUpdated ? <Badge.Dot /> : undefined,
    },
  ];

  const renderExtraChargeActions = () => {
    if (!actionList.length || !canExtraCharge || isNewOrderV4Flow) {
      return null;
    }
    if (actionList.length === 1) {
      const actionConfig = actionList[0];
      return (
        <Button variant="secondary" onPress={actionConfig.action}>
          {actionConfig.title}
        </Button>
      );
    }
    return (
      <Dropdown>
        <Dropdown.TriggerButton
          variant="secondary"
          suffix={<MajorChevronDownOutlined />}
          classNames={{ base: 'moe-pr-s' }}
        >
          Extra charge
        </Dropdown.TriggerButton>
        <Dropdown.Menu
          classNames={{ base: 'moe-min-w-[160px]' }}
          onAction={handleExtraChangeAction as (k: Key) => void}
        >
          {actionList.map((item) => (
            <Dropdown.Item key={item.key} title={item.title} />
          ))}
        </Dropdown.Menu>
      </Dropdown>
    );
  };

  return (
    <Drawer
      size="s"
      isDismissable
      isMaskCloseable={false}
      title={null}
      header={null}
      footer={null}
      isOpen
      zIndex={zIndex}
      {...restProps}
      closeButtonProps={{
        'data-testid': InvoiceTestIds.ViewInvoicesDrawerCloseBtn,
        ...restProps.closeButtonProps,
      }}
      classNames={{
        body: 'moe-flex moe-p-0 moe-rounded-8px-300 moe-border moe-flex-col',
        container: 'moe-max-h-none moe-w-[600px]',
        header: 'moe-hidden',
      }}
    >
      <ErrorBoundary>
        <ViewOrderDrawerContext.Provider value={context}>
          <LayoutSubpage className={subpage === ViewOrderDrawerSubpage.DEFAULT ? '' : 'moe-hidden'}>
            <LayoutSubpageHeader className={'moe-justify-between'}>
              <Heading size={'3'}>{isNewOrderV4Flow ? 'Receipts' : 'Invoices'}</Heading>
              <div className="moe-flex moe-gap-8px-100">
                {renderExtraChargeActions()}
                <Condition if={showEditStaff}>
                  <Tooltip isDisabled={hasEditStaffPermission} content={TIP_FOR_EDIT_STAFF_IN_INVOICE}>
                    <Button
                      variant="secondary"
                      onPress={handleEditStaff}
                      isLoading={handleEditStaff.loading}
                      isDisabled={!hasEditStaffPermission}
                      data-testid={InvoiceTestIds.InvoiceEditStaffBtn}
                    >
                      Edit staff
                    </Button>
                  </Tooltip>
                </Condition>
              </div>
            </LayoutSubpageHeader>
            <Tabs
              orientation="horizontal"
              className={'moe-px-m moe-flex-grow'}
              classNames={{
                base: 'moe-pr-none',
                panel: 'moe-flex-grow moe-overflow-y-hidden moe-pt-none -moe-mr-m',
              }}
              selectedKey={activeTab}
              onChange={(v) => setActiveTab(v as TabKey)}
            >
              {TabsList.map((t) => {
                return <Tabs.Item label={t.label} key={t.value} badge={t.badge} />;
              })}
            </Tabs>
            <LayoutSubpageScrollBody
              classNames={{
                base: 'moe-px-none moe-h-full',
                viewPort: 'moe-pt-m moe-px-m',
              }}
            >
              <OrderListSubpage visible={activeTab === 'orderList'} orderList={orderList} loading={loading} />
              <PaymentActivityList
                visible={activeTab === 'paymentActivity'}
                paymentList={paymentList}
                loading={loading}
                onRefunded={handleRefunded}
              />
              <TipsCollected
                visible={activeTab === 'tips'}
                tipsSplitInfo={tipsSplitInfo}
                onVisible={() => {
                  informTipsUpdated();
                  refreshTipsSplitInfo();
                }}
                isLoading={isTipsSplitInitLoading}
                entryOrder={grabOrder}
              />
            </LayoutSubpageScrollBody>
          </LayoutSubpage>

          <SendReceiptSubpage
            className={subpage === ViewOrderDrawerSubpage.SEND_RECEIPT ? '' : 'moe-hidden'}
            order={sendingReceiptOrder}
            // set key to clear cache
            key={sendingReceiptOrder?.id ?? ''}
            onClose={() => {
              setSubpage(ViewOrderDrawerSubpage.DEFAULT);
            }}
          />
        </ViewOrderDrawerContext.Provider>
      </ErrorBoundary>
    </Drawer>
  );
});

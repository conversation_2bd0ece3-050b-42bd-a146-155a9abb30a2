import { cn } from '@moego/ui';
import React, { useCallback, useRef } from 'react';
import { type IReactToPrintProps, useReactToPrint } from 'react-to-print';
import { useFloatableHost } from '../../../../utils/hooks/useFloatableHost';
import { usePromise } from '../../../../utils/hooks/usePromise';
import { UsePrintStyle } from './usePrint.style';

interface PrintElementOptions {
  /** 打印前的回调 */
  onBeforePrint?: () => void;
  /** 打印后的回调 */
  onAfterPrint?: () => void;
  /** 打印出错的回调 */
  onPrintError?: IReactToPrintProps['onPrintError'];
  /** 打印内容准备前的回调 */
  onBeforeGetContent?: () => void | Promise<void>;
  /** 打印的标题 */
  pageTitle?: string;
  /** 要添加到打印 window 的类名 */
  bodyClass?: string;
}

export const usePrint = (options: PrintElementOptions = {}) => {
  const contentRef = useRef<HTMLDivElement | null>(null);
  const { mountFloatablePage } = useFloatableHost();
  const closeFloatable = useRef<() => void>(() => {});
  const { start, resolve } = usePromise();

  const handlePrint = useReactToPrint({
    content: () => contentRef.current,
    documentTitle: options.pageTitle,
    onBeforeGetContent: options.onBeforeGetContent,
    onBeforePrint: () => {
      resolve(undefined);
      options.onBeforePrint?.();
    },
    onAfterPrint: () => {
      options.onAfterPrint?.();
      closeFloatable.current();
    },
    onPrintError: (...args) => {
      resolve(undefined);
      options.onPrintError?.(...args);
    },
    bodyClass: cn('light-theme core-theme', options.bodyClass),
    removeAfterPrint: true,
  });

  const print = useCallback(
    async (options: {
      element:
        | React.ReactElement
        | ((props: {
            print: () => void;
          }) => React.ReactElement);
      manual?: boolean;
    }) => {
      const { element, manual = false } = options;
      const content =
        typeof element === 'function'
          ? element({
              print: () => {
                handlePrint?.();
              },
            })
          : element;
      const contentElement = (
        <>
          <UsePrintStyle />
          <div className="moe-fixed moe-z-[-1]">
            <div ref={contentRef} className="print:moe-w-full">
              {content}
            </div>
          </div>
        </>
      );
      closeFloatable.current = mountFloatablePage(contentElement).closeFloatable;

      if (manual) {
        await start();
        return;
      }
      handlePrint?.();
    },
    [handlePrint],
  );

  return {
    print,
  };
};

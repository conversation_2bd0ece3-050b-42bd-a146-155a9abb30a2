import {
  assertModel,
  FNK_OrderItemType,
  type IRefundOrderDetail,
  type KitModelType,
  RealmType,
  type IGeneralOrderDetailV2,
} from '@moego/finance-web-kit';
import { Avatar, cn, Heading, Separator, Spin, Text } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useEffect } from 'react';
import { getLocationDetail } from '../../../../store/business/location.actions';
import { selectLocationDetailByBusiness } from '../../../../store/business/location.selectors';
import { useBool } from '../../../../utils/hooks/useBool';
import { getAppointment } from '../../../Appt/store/appt.api';
import { selectApptInfo } from '../../../Appt/store/appt.selectors';
import { sleep } from 'monofile-utilities/lib/sleep';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { type FinanceKit } from '../../../../service/finance-kit';
import { MoeMoney } from '@moego/finance-utils';
import { selectPaymentSettingInfo } from '../../../../store/payment/payment.selectors';
import { T_SECOND } from '@moego/reporting';

export const PrintReceiptContent = (props: {
  orderDetail: IGeneralOrderDetailV2<typeof FinanceKit.model>;
  refundedOrderDetails?: IRefundOrderDetail<typeof FinanceKit.model>[];
  onLoaded?: () => void;
}) => {
  const { orderDetail, onLoaded, refundedOrderDetails } = props;
  const order = orderDetail.order;
  const items = orderDetail.items;
  const dispatch = useDispatch();
  const [location, apptInfo, business, paymentSettingInfo] = useSelector(
    selectLocationDetailByBusiness(Number(order.businessId)),
    selectApptInfo(order.sourceId),
    selectCurrentBusiness,
    selectPaymentSettingInfo,
  );

  const loading = useBool(true);
  const businessId = order.businessId;

  useEffect(() => {
    if (!businessId) {
      return;
    }

    Promise.all([
      dispatch(getLocationDetail(businessId)),
      dispatch(
        getAppointment({
          appointmentId: order.sourceId,
        }),
      ),
    ]).then(async ([location]) => {
      let img: HTMLImageElement | null = new Image();
      img.onload = async () => {
        loading.close();
        // 确保要打印的内容已经加载完成。
        await sleep(1000);
        onLoaded?.();
        img = null;
      };
      img.src = location.avatarPath;
    });
  }, [businessId]);

  const isOriginOrder = order.isOriginOrder;
  const showCheckedIn = apptInfo.hasCheckedIn && isOriginOrder;
  const showCheckedOut = apptInfo.hasCheckedOut && isOriginOrder;
  const showDate = showCheckedIn || showCheckedOut;
  const serviceSubtotal = orderDetail.preDiscountSubtotalByItemType?.[FNK_OrderItemType.All] ?? MoeMoney.empty();

  const discounts = orderDetail.promotions.filter((promotion) => promotion.oneTimeDiscount || promotion.discount);
  const discountTotalAmount = discounts.reduce((acc, promotion) => {
    return acc.plus(promotion.appliedAmount);
  }, MoeMoney.empty());

  const storeCredits = orderDetail.promotions.filter((promotion) => promotion.storeCredit);
  const storeCreditTotalAmount = storeCredits.reduce((acc, promotion) => {
    return acc.plus(promotion.storeCredit?.amount ?? MoeMoney.empty());
  }, MoeMoney.empty());

  // serviceSubtotal 中已经包括 membership 后的价格。
  const promotedSubtotal = serviceSubtotal.minus(discountTotalAmount).minus(storeCreditTotalAmount);

  const taxTotalAmount = orderDetail.subtotalByTax.reduce((acc, item) => acc.plus(item.amount), MoeMoney.empty());

  const orderPayments = orderDetail.payments;
  const refundedOrderPayments = refundedOrderDetails?.map((detail) => detail.payments).flat();
  const allOrderPayments = [...orderPayments, ...(refundedOrderPayments ?? [])];
  const showDiscountOrStoreCredit = discountTotalAmount.valueOf() > 0 || storeCreditTotalAmount.valueOf() > 0;
  const isDepositOrder = order.isDepositOrder;
  const title = order.isDepositOrder ? 'This Deposit has been paid.' : 'This Appointment has been paid.';

  return loading.value ? (
    <Spin isLoading={loading.value} />
  ) : (
    <div className="moe-flex moe-justify-center">
      <div className="moe-shadow-elevated moe-p-s moe-mt-[50px] print:moe-w-[502px]">
        <div className="moe-flex moe-flex-col moe-justify-center moe-items-center moe-w-full">
          <Avatar.Business
            src={location.avatarPath}
            size="l"
            classNames={{
              base: 'moe-w-[100px] moe-h-[100px]',
            }}
            className="moe-mt-[-50px]"
          />
          <Heading size="3" className="moe-mt-s">
            {location.name}
          </Heading>
          <Heading size="4" className="moe-font-bold moe-mt-l">
            {title}
          </Heading>
          <Heading size="4" className="moe-font-bold moe-mt-xxs">
            Thank you
          </Heading>
        </div>

        <div className="moe-flex moe-flex-col moe-gap-y-s moe-mt-l">
          <Text variant="regular-short" className="moe-text-center">
            Receipt detail
          </Text>
          <Separator />
          <Heading size="5">Receipt #{order.id}</Heading>
          {showDate ? (
            <div className="moe-flex moe-flex-col moe-gap-y-xxs">
              {showCheckedIn && (
                <Heading size="5">Checked in at {business.formatDateTime(apptInfo.appointment.checkInTime)}</Heading>
              )}
              {showCheckedOut && (
                <Heading size="5">Checked out at {business.formatDateTime(apptInfo.appointment.checkOutTime)}</Heading>
              )}
            </div>
          ) : null}
          <Separator />
          <Heading size="5">Items</Heading>
          <div className="moe-flex moe-flex-col moe-gap-y-xs">
            {items.map((item) => (
              <Item key={item.id} name={item.name} amount={item.subTotalAmount.valueOf()} quantity={item.quantity} />
            ))}
          </div>
          {showDiscountOrStoreCredit && (
            <>
              <Separator />
              <Item name="Discount:" amount={-discountTotalAmount.valueOf()} showZero={false} />
              <Item name="Store credit:" amount={-storeCreditTotalAmount.valueOf()} showZero={false} />
            </>
          )}
          <Separator />
          <Item name="Sub total:" amount={promotedSubtotal.valueOf()} />
          <Item name="Tax total:" amount={taxTotalAmount.valueOf()} />
          <Item name="Tips:" amount={order.tipsAmount.valueOf()} />
          <Item name="Deposit:" amount={order.depositAmount.valueOf()} showZero={false} />
          <Item name={`${paymentSettingInfo.customizedFeeName || 'Fees'}:`} amount={order.convenienceFee.valueOf()} />
          <Separator />
          <Item name="Total:" amount={order.totalAmount.valueOf()} isBold={true} />
          <Separator />
          <Item name="Total paid:" amount={order.totalAmount.valueOf()} />
          <Item name="Refund:" amount={-order.refundedAmount.valueOf()} showZero={false} />
          {allOrderPayments.map((payment) => (
            <Payment key={payment.id} payment={payment} isDepositOrder={isDepositOrder} />
          ))}
        </div>
      </div>
    </div>
  );
};

const Item = (props: {
  name: string;
  amount: number;
  quantity?: number;
  isBold?: boolean;
  showZero?: boolean;
}) => {
  const { name, amount, quantity, isBold = false, showZero = true } = props;
  const [business] = useSelector(selectCurrentBusiness);

  if (!showZero && amount === 0) {
    return null;
  }
  return (
    <div className="moe-flex moe-justify-between">
      <Text variant="regular-short" className={cn('moe-font-medium', isBold && 'moe-font-bold')}>
        {name}
      </Text>
      <div className="moe-flex moe-gap-x-s">
        {quantity && (
          <Text variant="regular-short" className={cn('moe-font-medium', isBold && 'moe-font-bold')}>
            {quantity}
          </Text>
        )}
        <Text
          variant="regular-short"
          className={cn('moe-min-w-[74px] moe-text-right moe-font-medium', isBold && 'moe-font-bold')}
        >
          {business.formatAmount(amount)}
        </Text>
      </div>
    </div>
  );
};

const Payment = (props: {
  payment: KitModelType<typeof FinanceKit.model, RealmType.OrderPayment | RealmType.RefundOrderPayment>;
  isDepositOrder: boolean;
}) => {
  const { payment, isDepositOrder } = props;
  const [business] = useSelector(selectCurrentBusiness);
  const isRefundOrderPayment = assertModel(payment, RealmType.RefundOrderPayment);
  return (
    <div>
      <div className="moe-flex moe-justify-between">
        <Text variant="regular-short" className="moe-font-medium">
          {isRefundOrderPayment
            ? `Refunded to ${payment.refundPaymentMethodDisplayName}`
            : `${isDepositOrder ? 'Deposit collected' : 'Paid'} by ${payment.paymentMethodDisplayName}`}
        </Text>
        <Text variant="regular-short" className="moe-font-medium">
          {business.formatAmount(
            isRefundOrderPayment ? -payment.refundTotalAmount?.valueOf() : payment.totalAmount?.valueOf(),
          )}
        </Text>
      </div>
      <Text variant="regular-short" className="moe-font-medium">
        {business.formatDateTime(Number(payment.createTime) * T_SECOND)}
      </Text>
    </div>
  );
};

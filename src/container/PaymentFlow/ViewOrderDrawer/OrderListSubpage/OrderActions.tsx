import { RefundMode } from '@moego/api-web/moego/models/order/v1/refund_order_enums';
import { isNormal, useAsync, useSerialCallback } from '@moego/finance-utils';
import {
  type IGeneralOrderDetail,
  type IGeneralOrderDetailV2,
  RealmType,
  assertModel,
  type KitModelType,
} from '@moego/finance-web-kit';
import { MinorMoreOutlined, MinorPrintOutlined, MinorSendOutlined } from '@moego/icons-react';
import { Button, Condition, Dropdown, IconButton, Text, Tooltip, toast } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import React, { useMemo, type FC } from 'react';
import { OrderTestIds } from '../../../../config/testIds/order';
import { type FinanceKit } from '../../../../service/finance-kit';
import { completeOrder } from '../../../../store/PaymentFlow/order.actions';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { getInvoiceReceiptSentInfo } from '../../../../store/message/message.actions';
import { TARGET_TYPE_RECEIPT, invoiceSentInfoBox } from '../../../../store/message/message.boxes';
import { PaymentActionName } from '../../../../utils/reportData/payment';
import { useInvoiceReinvent } from '../../hooks/useInvoiceReinvent';
import { useInvoiceReinventReport } from '../../hooks/useInvoiceReinvent.report';
import { useRefundByItems } from '../../hooks/useRefundByItems';
import { useViewOrderDrawerContext } from '../ViewOrderDrawer.context';
import { ChargeNowButton } from './ChargeNowButton';

export interface OrderActionsProps {
  orderDetail: IGeneralOrderDetailV2<typeof FinanceKit.model> | IGeneralOrderDetail<typeof FinanceKit.model>;
  onMarkCompleted?: () => void;
  onRefundItems?: () => void;
  isCancelledAppt?: boolean;
}

interface LocalMenuItem {
  isDisabled?: boolean;
  title: string;
  key: string;
  testId?: string;
  render?: FC<{
    order:
      | KitModelType<typeof FinanceKit.model, RealmType.OrderV2>
      | KitModelType<typeof FinanceKit.model, RealmType.OrderV1>;
  }>;
}
const DropdownMenuOptionEnum: Record<string, LocalMenuItem> = {
  completeInvoice: {
    isDisabled: false,
    title: 'Close invoice',
    key: 'completeInvoice',
    testId: OrderTestIds.CloseInvoiceBtnInViewOrder,
  },
  sendReceipt: {
    isDisabled: false,
    title: 'Send receipt',
    key: 'sendReceipt',
    testId: OrderTestIds.SendReceiptBtnInViewOrder,
    render: function Comp({ order }) {
      const [business, invoiceReceiptSentInfo] = useSelector(
        selectCurrentBusiness,
        invoiceSentInfoBox.mustGetItem(Number(order.id)),
      );
      return (
        <Tooltip
          isDisabled={!invoiceReceiptSentInfo.createTime}
          content={`Receipt sent at ${business.formatDateTime(invoiceReceiptSentInfo.createTime * T_SECOND)}`}
          side="top"
        >
          <Text variant="regular-short" className="moe-text-s-20 moe-flex moe-items-center moe-gap-1">
            <MinorSendOutlined />
            {invoiceReceiptSentInfo.createTime ? 'Resend receipt' : 'Send receipt'}
          </Text>
        </Tooltip>
      );
    },
  },
} as const;

export const OrderActions = (props: OrderActionsProps) => {
  const { orderDetail, onMarkCompleted, onRefundItems, isCancelledAppt = false } = props;
  const order = orderDetail.order;
  const isDepositOrder = assertModel(order, RealmType.OrderV2) && order.isDepositOrder;
  const isOverpaidDeposit = assertModel(order, RealmType.OrderV2) && order.isDepositOrder && order.isOverPaid;
  const isSalesOrder = assertModel(order, RealmType.OrderV2) && order.isOriginOrder;
  const [permissions] = useSelector(selectCurrentPermissions());
  const dispatch = useDispatch();
  const capabilities = order.invoiceActionCapabilities;
  const { refundByItems } = useRefundByItems();
  const reportPaymentData = useInvoiceReinventReport();
  const { isNewOrderV4Flow } = useInvoiceReinvent();
  const { overpaidDepositOrderDetail, onSendReceipt, onPrint } = useViewOrderDrawerContext();

  const refundItemText = useMemo(() => {
    if (isOverpaidDeposit) {
      return 'Refund overpayment';
    }
    return 'Refund item';
  }, [isOverpaidDeposit]);

  const handleCompleteInvoice = useSerialCallback(async () => {
    await dispatch(completeOrder(order.id.toString()));
    toast({
      type: 'success',
      title: 'Invoice closed!',
    });
    reportPaymentData(PaymentActionName.CompleteInvoice, { orderId: order.id, isExtraOrder: order.isExtraOrder });
    onMarkCompleted?.();
  });

  const handleSelectionChange = useSerialCallback(async (keys: Set<string>) => {
    const arr = Array.from(keys);
    if (arr.includes(DropdownMenuOptionEnum.completeInvoice.key)) {
      await handleCompleteInvoice();
    }
    if (arr.includes(DropdownMenuOptionEnum.sendReceipt.key)) {
      await handleSendReceipt();
    }
  });

  const isRefundItemsVisible = useMemo(() => {
    return order.isCompleted && order.orderVersion > 1;
  }, [order.isCompleted, order.orderVersion]);

  const refundItemDisabledReason = useMemo(() => {
    if (!permissions.has('canProcessRefund')) {
      return 'Please request “Can process refund” permission from the owner';
    }
    if (!order.refundableModes.includes(RefundMode.BY_ITEM)) {
      return 'Payment has been refunded: Further item refunds are not allowed.';
    }

    if (isSalesOrder && overpaidDepositOrderDetail) {
      return 'You need to refund overpayment first.';
    }

    if (order.refundedAmount.valueOf() >= order.paidAmount.valueOf()) {
      // 一个特殊情况：如果 order 里面收了 deposit 但是 deposit 没有完全退完，这时候也是需要可以去 refund。
      if (
        assertModel(order, RealmType.OrderV2) &&
        !order.depositAmount.isZero() &&
        order.refundedDepositAmount.valueOf() < order.depositAmount.valueOf()
      ) {
        return '';
      }

      if (isDepositOrder) {
        return 'Deposit has been refunded: Further deposit refunds are not allowed.';
      }
      return 'All items have already been refunded: No further refunds are allowed';
    }

    // 如果 deposit 被抵扣过且非 overpaid，则不能退款。
    if (
      assertModel(order, RealmType.OrderV2) &&
      order.isDepositOrder &&
      order.depositDeductedAmount.valueOf() > 0 &&
      !order.isOverPaid
    ) {
      return 'Deposit has been deducted: Further deposit refunds are not allowed.';
    }
    return null;
  }, [order.refundableModes, order.refundedAmount, order.paidAmount]);

  const handleRefundItems = useSerialCallback(async () => {
    const result = await refundByItems(order);
    if (result) {
      toast({
        type: 'success',
        title: ' Items refunded!',
      });
      onRefundItems?.();
    }
  });

  const isOverPaid = order.isOverPaid;
  const dropdownItems = useMemo(() => {
    const list: LocalMenuItem[] = [];
    if (capabilities.canCompleteInvoice && permissions.has('completeInvoice') && !isOverPaid) {
      list.push(DropdownMenuOptionEnum.completeInvoice);
    }
    if (capabilities.canSendReceipt) {
      list.push(DropdownMenuOptionEnum.sendReceipt);
    }
    return list;
  }, [capabilities, permissions.has('completeInvoice')]);

  const handleSendReceipt = useSerialCallback(() => {
    reportPaymentData(PaymentActionName.InvoiceSendReceipt, {
      orderId: order.id,
      isExtraOrder: order.isExtraOrder,
      ctaId: 'drawer_payment_activity',
    });
    onSendReceipt(order);
  });

  const handlePrint = useSerialCallback(async () => {
    await onPrint(orderDetail as IGeneralOrderDetailV2<typeof FinanceKit.model>);
  });

  useAsync(async () => {
    if (!isNormal(order.id)) {
      return;
    }

    await dispatch(getInvoiceReceiptSentInfo(TARGET_TYPE_RECEIPT, order.id));
  }, [order.id]);

  if (isNewOrderV4Flow) {
    return (
      <div className={'moe-flex moe-items-center moe-gap-xs moe-flex-wrap'}>
        <Condition if={capabilities.canPrintReceipt && assertModel(order, RealmType.OrderV2)}>
          <IconButton
            onPress={handlePrint}
            isLoading={handlePrint.isBusy()}
            variant="secondary"
            icon={<MinorPrintOutlined />}
          />
        </Condition>

        <Condition if={capabilities.canSendReceipt && !isCancelledAppt}>
          <IconButton onPress={handleSendReceipt} variant="secondary" icon={<MinorSendOutlined />} />
        </Condition>
        <Condition if={isRefundItemsVisible}>
          <Tooltip
            isDisabled={!refundItemDisabledReason}
            content={refundItemDisabledReason || 'Payment has been refunded: Further item refunds are not allowed.'}
          >
            <Button
              isDisabled={!!refundItemDisabledReason}
              variant={isOverpaidDeposit ? 'primary' : 'secondary'}
              size="s"
              onPress={handleRefundItems}
            >
              {refundItemText}
            </Button>
          </Tooltip>
        </Condition>
      </div>
    );
  }

  return (
    <div className={'moe-flex moe-items-center moe-gap-s moe-flex-wrap'}>
      <Condition if={capabilities.canChargeNow}>
        <ChargeNowButton order={order} />
      </Condition>
      <Condition if={isRefundItemsVisible}>
        <Tooltip
          isDisabled={!refundItemDisabledReason}
          content={refundItemDisabledReason || 'Payment has been refunded: Further item refunds are not allowed.'}
        >
          <Button
            isDisabled={!!refundItemDisabledReason}
            variant={isOverpaidDeposit ? 'primary' : 'secondary'}
            size="s"
            onPress={handleRefundItems}
          >
            {refundItemText}
          </Button>
        </Tooltip>
      </Condition>
      <Condition if={dropdownItems.length > 0}>
        <Dropdown defaultOpen={false}>
          <Dropdown.Trigger>
            <IconButton variant="secondary" size={'m'} icon={<MinorMoreOutlined />} />
          </Dropdown.Trigger>
          <Dropdown.Menu
            selectedKeys={[]}
            onSelectionChange={(keys) => {
              if (typeof keys !== 'string' && keys.size > 0) {
                handleSelectionChange(keys as Set<string>);
              }
            }}
          >
            {dropdownItems.map((item) => {
              const ItemRenderer = item.render;
              if (ItemRenderer) {
                return (
                  <Dropdown.MenuItem key={item.key} textValue={item.title} data-testid={item.testId}>
                    <ItemRenderer order={order} />
                  </Dropdown.MenuItem>
                );
              }
              return <Dropdown.MenuItem key={item.key} title={item.title} data-testid={item.testId} />;
            })}
          </Dropdown.Menu>
        </Dropdown>
      </Condition>
    </div>
  );
};

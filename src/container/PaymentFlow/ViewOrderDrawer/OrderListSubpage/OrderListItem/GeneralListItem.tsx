import { EXTRA_CHARGE_REASON, FNK_OrderType, type IGeneralOrderDetail, RealmType } from '@moego/finance-web-kit';
import { MinorLocationOutlined, MinorWarningFilled } from '@moego/icons-react';
import { Button, Condition, Heading, Text, cn } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo, useContext, useMemo } from 'react';
import { FinanceKit } from '../../../../../service/finance-kit';
import { type WeekDay, WeekDayMap } from '../../../../../store/business/business.boxes';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { selectCurrentStaff } from '../../../../../store/staff/staff.selectors';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { ViewInQuickBooks } from '../../../../Calendar/Grooming/TakePaymentModal/InvoiceInfo/components/ViewInQuickBooks';
import { PureOrderAdditionalInfo } from '../../../TakePaymentDrawer/OrderAdditional/PureOrderAdditionalInfo';
import { PureOrderItemsInfo } from '../../../TakePaymentDrawer/OrderItems/PureOrderItemsInfo';
import { OrderPaymentSummaryList } from '../../../TakePaymentDrawer/OrderSummary/OrderPaymentSummaryList';
import { OrderTotal } from '../../../TakePaymentDrawer/OrderSummary/OrderTotal';
import { TotalAmountDue } from '../../../TakePaymentDrawer/OrderSummary/TotalAmountDue';
import { LayoutCard } from '../../../TakePaymentDrawer/components/Layout/LayoutCard';
import { LayoutDivider } from '../../../TakePaymentDrawer/components/Layout/LayoutDivider';
import { OrderPaymentStatus } from '../../../shared/OrderPaymentStatus';
import { useRefundByAmountModal } from '../../RefundByAmount/useRefundByAmountModal';
import { ViewOrderDrawerContext } from '../../ViewOrderDrawer.context';
import { OrderTags } from '../../components/OrderTags/OrderTags';
import { OrderTypeHeader } from '../../components/OrderTypeHeader';
import { OrderActions } from '../OrderActions';
import { useChargeNow } from '../useChargeNow';

import { isExtraOrderInitial } from '../../utils';

interface ServiceInfoProps {
  className?: string;
  orderDetail: IGeneralOrderDetail<typeof FinanceKit.model>;
}

export const GeneralListItem = memo<ServiceInfoProps>((props) => {
  const { orderDetail } = props;
  const order = orderDetail.order;
  const orderId = orderDetail.order.id;
  const orderPayments = orderDetail.payments;
  const context = useContext(ViewOrderDrawerContext);
  const [business, staff] = useSelector(selectCurrentBusiness, selectCurrentStaff);

  const handleRefreshList = useSerialCallback(async () => {
    context?.refreshOrderList();
  });

  const { openRefundByAmountModal } = useRefundByAmountModal();
  const { openPaymentModal } = useChargeNow();
  const handleRefund = useSerialCallback(async () => {
    const result = await openRefundByAmountModal({ order });
    if (order && !order.isCompleted) {
      openPaymentModal(order);
      return;
    }

    if (result?.refundSuccessful) {
      handleRefreshList();
    }
  });

  const isEnterOrder = String(context?.enterOrderId) === String(orderId);
  const extraChargeReason = useMemo(() => {
    if (!order?.isExtraOrder || !order.extraChargeReason) {
      return null;
    }

    return EXTRA_CHARGE_REASON.find((s) => s.value === order.extraChargeReason)?.label;
  }, [order?.isExtraOrder, order?.extraChargeReason]);

  // hide the extra charge order if it was created but not actually used.
  const isExtraOrder = isExtraOrderInitial(orderDetail);

  const apptBasedOrder = useMemo(() => {
    return FinanceKit.buildModel(RealmType.Order, order.toJSON());
  }, [order]);

  if (!order) {
    return null;
  }

  if (isExtraOrder && !isEnterOrder) {
    return null;
  }

  const isOverPaid = order.remainAmountV2 < 0;

  // TODO: 检测这个判断是否有必要。可能组件内部已经判断 service 空了
  const isExtraTip = order.orderType === FNK_OrderType.EXTRA_TIPS;

  return (
    <LayoutCard border className={cn('moe-flex-1 moe-p-0', props.className)}>
      <OrderTypeHeader order={order} />
      <section className="moe-p-[16px]">
        <div data-slot="status-and-actions" className={'moe-flex moe-justify-between moe-items-center moe-max-w-full'}>
          <div className="moe-flex moe-gap-xs">
            <OrderTags order={order} />
            <OrderPaymentStatus labelType="full" status={order.paymentStatus} isOverPaid={isOverPaid} />
          </div>
          <OrderActions
            onMarkCompleted={handleRefreshList}
            onRefundItems={handleRefreshList}
            orderDetail={orderDetail}
          />
        </div>
        <div data-slot="invoice-header" className={'moe-mt-s'}>
          <Heading size={'4'}>Invoice #{orderId}</Heading>
          <div className="moe-flex moe-gap-xxs moe-mt-xs moe-items-center">
            <Text variant={'small'} className="moe-text-secondary">
              {WeekDayMap[dayjs(order.appointmentInfo.appointmentDate).get('day') as WeekDay]},{' '}
              {business.formatDate(order.appointmentInfo.appointmentDate)}
            </Text>
            <Text variant={'small'} className="moe-text-tertiary">
              ·
            </Text>
            <Text variant={'small'} className="moe-flex moe-items-center moe-text-secondary">
              <MinorLocationOutlined className="moe-mr-xxs" />
              {order.businessName}
            </Text>
          </div>
          <Condition if={!!extraChargeReason}>
            <div className="moe-bg-neutral-sunken-0 moe-px-s moe-py-8px-150 moe-rounded-8px-100 moe-mt-m">
              <Text variant="regular-short">{extraChargeReason}</Text>
            </div>
          </Condition>
        </div>

        <Condition if={isOverPaid}>
          <div className="moe-flex moe-justify-between moe-items-center moe-mt-s moe-bg-warning-subtle moe-rounded-s moe-p-s moe-border moe-border-warning">
            <div className="moe-flex moe-gap-xs moe-items-center">
              <MinorWarningFilled color="#DE921F" />
              <Text as="span" variant="small">
                {business.formatAmount(Math.abs(order.remainAmountV2))} over charged.
              </Text>
            </div>
            <Button variant="secondary" size="s" onPress={handleRefund}>
              Refund payment
            </Button>
          </div>
        </Condition>
        <Condition if={!isExtraTip}>
          <PureOrderItemsInfo className={'moe-mt-m'} order={apptBasedOrder} />
        </Condition>
        <PureOrderAdditionalInfo
          className="moe-mt-xl"
          order={apptBasedOrder}
          subtotalAmount={order.subTotalAmountMap?.all_no_tip ?? 0}
          tipsAmount={order.tipsAmount}
          showSplitTips={false}
          isExtraTip={isExtraTip}
        />
        <div className="moe-flex moe-flex-col moe-gap-s moe-mt-m">
          <LayoutDivider type={'solid'} />
          <OrderTotal totalString={business.formatAmount(order.totalAmount)} />
          <OrderPaymentSummaryList orderPayments={orderPayments} />
          <Condition if={!order.isCompleted}>
            <>
              <LayoutDivider />
              <TotalAmountDue totalDueString={business.formatAmount(order.remainAmountV2)} />
            </>
          </Condition>
        </div>
        <Condition if={staff.isOwner() && order.isSync}>
          <ViewInQuickBooks className="moe-mt-m !moe-p-none" link={order.qbInvoiceLink} syncTime={order.syncTime} />
        </Condition>
      </section>
    </LayoutCard>
  );
});

import { type PetDetailDef } from '@moego/api-web/moego/models/appointment/v1/pet_detail_defs';
import { useDispatch, useSelector, useStore } from 'amos';
import { cloneDeep } from 'lodash';
import { type ServicePriceDurationInfo } from '../../../../../../components/ServiceApplicablePicker/hooks/useServiceInfo';
import { type ServiceEntry } from '../../../../../../components/ServiceApplicablePicker/types/serviceEntry';
import { type ServiceOperationEntry } from '../../../../../../components/ServiceApplicablePicker/types/serviceOperation';
import { getDefaultService } from '../../../../../../components/ServiceApplicablePicker/utils/getDefaultService';
import { isMultipleStaffService } from '../../../../../../components/ServiceApplicablePicker/utils/isMultipleStaffService';
import { type ApptInfoPetServiceInfoWithTime } from '../../../../../../store/calendarLatest/calendar.types';
import { serviceMapBox } from '../../../../../../store/service/service.boxes';
import { currentStaffIdBox } from '../../../../../../store/staff/staff.boxes';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { calcServiceDuration } from '../../../../../Appt/components/EditPetServiceList/EditPetServiceList.utils';
import { useCalcGroomingOnlySchedule } from '../../../../../Appt/hooks/useCalcGroomingOnlySchedule';
import { useGetPetsInfoForApi } from '../../../../../Appt/hooks/useGetPetsInfoForApi';
import { addApptPetService } from '../../../../../Appt/store/appt.actions';
import { getAppointment } from '../../../../../Appt/store/appt.api';
import { apptInfoMapBox, apptPetMapBox } from '../../../../../Appt/store/appt.boxes';
import { matchApptFlowScene } from '../../../../../Appt/store/appt.options';
import { selectMainServiceInAppt, selectServiceListWithPet } from '../../../../../Appt/store/appt.selectors';
import { ApptFlowScene } from '../../../../../Appt/store/appt.types';
import { useSyncOperationPrice } from '../../../../../CreateTicket/hooks/useSyncOperationPrice';
import { useOrderAppointmentInfo } from '../../../../hooks/useOrderAppointmentInfo';

export const useBuildPetDetailParams = () => {
  const store = useStore();
  const dispatch = useDispatch();
  const { getAppointmentIdByOrderId } = useOrderAppointmentInfo();
  const [currentStaffId] = useSelector(currentStaffIdBox);
  const getPetsInfoForApi = useGetPetsInfoForApi();
  const calcGroomingOnlySchedule = useCalcGroomingOnlySchedule();
  const { syncOperationPrice } = useSyncOperationPrice();

  /**
   * 针对添加新的 grooming service 构建 petDetail 参数
   * 主要是模拟 edit pets 涉及的交互流程，直观至上，复用性适当会降低
   *
   * @param skipFetchApptFirst skip 好处是少调一个接口，提升体验，代价是要在外面处理 appt 数据的刷新
   */
  const buildPetDetailWithNewGroomingService = useLatestCallback(
    async (options: {
      orderId: number;
      petId: string;
      serviceId: string;
      serviceSavedInfo: ServicePriceDurationInfo;
      skipFetchApptFirst?: boolean;
      staffId?: number;
    }) => {
      const { orderId, petId, serviceId, serviceSavedInfo, skipFetchApptFirst, staffId: presetStaffId } = options;
      const appointmentId = getAppointmentIdByOrderId(orderId);
      /**
       * 1. 先取出当前 appt 中对应 petId 的 serviceList 的信息：
       *
       * 取自原来的 apptDetailDrawerEditBox 的数据，
       * 来源 <PetInfoDetail />: handleEdit -> setApptDetailDrawerEdit 设置 serviceList / petId
       *
       * serviceList 来源：getServiceListByPet -> map 到 getDefaultService
       *   getServiceListByPet 的参数：pet
       *     1. 先 getAppointment，存入 apptDetailPetMapBox
       *     2. 从 apptDetailPetMapBox 通过 appointmentId 获取 pets
       *     3. 过滤 petId 得到 pet
       */
      !skipFetchApptFirst && (await dispatch(getAppointment({ appointmentId })));
      const { pets } = store.select(apptPetMapBox.mustGetItem(appointmentId));
      const pet = pets.find((p) => p.petId === petId);
      const newService = store.select(serviceMapBox.mustGetItem(+serviceId));
      // 两种情况单独处理：1. petId 存在 pets，2. petId 不存在 pets 中
      if (pet) {
        const serviceList = store.select(selectServiceListWithPet(pet, appointmentId));
        const firstService = serviceList[0] as ApptInfoPetServiceInfoWithTime;
        const petFirstServiceStartTime = firstService?.startTime || 0;

        /**
         * 2. 模拟添加一组 default service，计算时间，作为 draft service
         */
        const { staffId: defaultStaffId, startDate } = serviceList[serviceList.length - 1] || {};
        const staffId = presetStaffId ?? defaultStaffId;
        const draftServiceList = [
          ...serviceList,
          getDefaultService({
            staffId: staffId ?? currentStaffId,
            startDate,
            endDate: startDate,
          }),
        ];
        const draftService = (() => {
          let startTime = petFirstServiceStartTime;
          const list = draftServiceList.map((item) => {
            const { serviceTime } = item;
            const endTime = startTime + serviceTime;
            const nextItem = {
              ...item,
              startTime,
              endTime,
            };
            startTime = nextItem.endTime;
            return nextItem;
          });
          return list[list.length - 1];
        })();

        /**
         * 3. 填充新的 service 信息，模拟 <EditPetService /> 的 onChangeService 交互，给 draft service 填充新 service 相关信息
         */
        const serviceInfo = getDefaultService({
          ...draftService,
          ...serviceSavedInfo,
          serviceId: +serviceId,
          serviceName: newService.name,
          serviceType: newService.type,
        });
        // 单个 service 也许不用考虑 multi staff
        const nextService: ServiceEntry = {
          ...serviceInfo,
          operationList: [],
        };
        const nextServiceTime = calcServiceDuration(nextService);
        const finalService = {
          ...nextService,
          serviceTime: nextServiceTime,
          // 默认不设置 multi staff 所以 operation 为空
          enableOperation: false,
        };
        const finalPetServiceInfo = {
          petId: pet.petId,
          services: [...serviceList, finalService],
        };

        /**
         * 4. 生成 petDetail 信息，模拟 <EditPetService /> 的 onSave 后的 submit 动作
         */
        const apptInfo = store.select(apptInfoMapBox.mustGetItem(appointmentId)).appointment;
        await calcGroomingOnlySchedule({
          petIdsServiceList: [{ petId: +finalPetServiceInfo.petId, serviceList: finalPetServiceInfo.services }],
          allPetsStartAtSameTime: apptInfo.startAtSameTime,
          customerId: apptInfo.customerId,
          appointmentId,
        });
        const newPetDetail = getPetsInfoForApi(appointmentId).find((pet) => pet.petId === String(petId));
        if (!newPetDetail) {
          throw new Error('pet detail not found');
        }
        return newPetDetail;
      } else {
        /**
         * 上一个 pet 的 staffId，作为新 pet 的默认 staff
         */
        const lastPet = pets[pets.length - 1];
        const serviceList = store.select(selectServiceListWithPet(lastPet, appointmentId));
        const lastServiceInfo = serviceList[serviceList.length - 1];
        const lastServiceStaffId = lastServiceInfo?.staffId ? Number(lastServiceInfo?.staffId) : currentStaffId;

        /**
         * 2. 模拟添加一组 default service，计算时间，作为 draft service
         */
        const serviceInfo = getDefaultService({
          ...serviceSavedInfo,
          serviceId: +serviceId,
          serviceName: newService.name,
          serviceType: newService.type,
          staffId: lastServiceStaffId,
        });

        /**
         * 3. 生成 PetDetail
         */
        const apptInfo = store.select(apptInfoMapBox.mustGetItem(appointmentId)).appointment;
        await calcGroomingOnlySchedule({
          petIdsServiceList: [{ petId: +petId, serviceList: [serviceInfo] }],
          allPetsStartAtSameTime: apptInfo.startAtSameTime,
          customerId: apptInfo.customerId,
          appointmentId,
        });
        const newPetDetail = getPetsInfoForApi(appointmentId).find((pet) => pet.petId === String(petId));
        if (!newPetDetail) {
          throw new Error('pet detail not found');
        }
        return newPetDetail as PetDetailDef;
      }
    },
  );

  const buildPetDetailWithEditingGroomingService = useLatestCallback(
    async (
      orderId: number,
      petId: string,
      serviceId: string,
      updatePayload: {
        servicePrice: number;
        staffId: number;
      },
      skipFetchApptFirst?: boolean,
    ) => {
      const appointmentId = getAppointmentIdByOrderId(orderId);
      !skipFetchApptFirst && (await dispatch(getAppointment({ appointmentId })));
      const { pets } = store.select(apptPetMapBox.mustGetItem(appointmentId));
      const pet = pets.find((p) => p.petId === petId);
      if (!pet) {
        throw new Error('pet not found');
      }
      /**
       * 找到目标 service
       */
      const serviceList = store.select(selectServiceListWithPet(pet, appointmentId));
      const serviceIndex = serviceList.findIndex((s) => s.serviceId === +serviceId);
      const service = serviceList[serviceIndex];
      const draftService = cloneDeep(service);

      /**
       * 更新 service 信息
       */
      draftService.servicePrice = updatePayload.servicePrice;
      // multi staff 各个 price 需要重新计算
      if (draftService.enableOperation) {
        draftService.operationList = syncOperationPrice(
          draftService.operationList?.map((i) => ({ ...i, price: i.price + '' })),
          draftService.servicePrice,
        ).map((i) => ({ ...i, price: +i.price })) as ServiceOperationEntry[];
      }
      if (!draftService.enableOperation || !isMultipleStaffService(draftService)) {
        draftService.staffId = updatePayload.staffId;
      }
      const newServiceList = serviceList.map((s, i) => (i === serviceIndex ? draftService : s));

      /**
       * 生成 PetDetail
       */
      const { appointment: apptInfo } = store.select(apptInfoMapBox.mustGetItem(appointmentId));
      const { serviceItemType } = store.select(selectMainServiceInAppt(appointmentId));
      if (matchApptFlowScene(ApptFlowScene.AutoCalc, serviceItemType)) {
        await calcGroomingOnlySchedule({
          petIdsServiceList: [{ petId: +petId, serviceList: newServiceList }],
          allPetsStartAtSameTime: apptInfo.startAtSameTime,
          customerId: apptInfo.customerId,
          appointmentId,
        });
      } else {
        // 处理非 grooming only 的情况
        await dispatch(addApptPetService(petId, newServiceList, appointmentId, true));
      }
      const newPetDetail = getPetsInfoForApi(appointmentId).find((pet) => pet.petId === String(petId));
      if (!newPetDetail) {
        throw new Error('pet detail not found');
      }
      return newPetDetail as PetDetailDef;
    },
  );

  const buildPetDetailWithRemovingGroomingService = useLatestCallback(
    async (orderId: number, petId: string, serviceId: string, skipFetchApptFirst?: boolean) => {
      const appointmentId = getAppointmentIdByOrderId(orderId);
      !skipFetchApptFirst && (await dispatch(getAppointment({ appointmentId })));
      const { pets } = store.select(apptPetMapBox.mustGetItem(appointmentId));
      const pet = pets.find((p) => p.petId === petId);
      if (!pet) {
        throw new Error('pet not found');
      }
      /**
       * 找到目标 service
       */
      const serviceList = store.select(selectServiceListWithPet(pet, appointmentId));
      const firstService = serviceList[0] as ApptInfoPetServiceInfoWithTime;
      const petFirstServiceStartTime = firstService?.startTime || 0;
      const serviceIndex = serviceList.findIndex((s) => s.serviceId === +serviceId);
      const newServiceList = (() => {
        let startTime = petFirstServiceStartTime;
        return serviceList
          .filter((s, i) => i !== serviceIndex)
          .map((item) => {
            const { serviceTime } = item;
            const endTime = startTime + serviceTime;
            const nextItem = {
              ...item,
              startTime,
              endTime,
            };
            startTime = nextItem.endTime;
            return nextItem;
          });
      })();

      if (newServiceList.length === 0) {
        return {
          petId,
          services: [],
          addOns: [],
          evaluations: [],
        } as PetDetailDef;
      }
      /**
       * 生成 PetDetail
       */
      const { appointment: apptInfo } = store.select(apptInfoMapBox.mustGetItem(appointmentId));
      const { serviceItemType } = store.select(selectMainServiceInAppt(appointmentId));
      if (matchApptFlowScene(ApptFlowScene.AutoCalc, serviceItemType)) {
        await calcGroomingOnlySchedule({
          petIdsServiceList: [{ petId: +petId, serviceList: newServiceList }],
          allPetsStartAtSameTime: apptInfo.startAtSameTime,
          customerId: apptInfo.customerId,
          appointmentId,
        });
      } else {
        // 处理非 grooming only 的情况
        await dispatch(addApptPetService(petId, newServiceList, appointmentId, true));
      }
      const newPetDetail = getPetsInfoForApi(appointmentId).find((pet) => pet.petId === String(petId));
      if (!newPetDetail) {
        throw new Error('pet detail not found');
      }
      return newPetDetail as PetDetailDef;
    },
  );

  const purePetDetailLeftMatchOneOnly = useLatestCallback((petDetail: PetDetailDef, serviceId: number) => {
    const petDetailWithAddedItem: PetDetailDef = {
      petId: petDetail.petId,
      services: [],
      addOns: [],
      evaluations: [],
    };

    const addedItem1 = petDetail.services.find((s) => {
      return s.serviceId === String(serviceId);
    });
    if (!addedItem1) {
      petDetailWithAddedItem.services = [];
    } else {
      petDetailWithAddedItem.services = [addedItem1];
      return petDetailWithAddedItem;
    }

    const addedItem2 = petDetail.addOns.find((s) => {
      return s.addOnId === String(serviceId);
    });
    if (!addedItem2) {
      petDetailWithAddedItem.addOns = [];
    } else {
      petDetailWithAddedItem.addOns = [addedItem2];
      return petDetailWithAddedItem;
    }

    const addedItem3 = petDetail.evaluations.find((s) => {
      return s.serviceId === String(serviceId);
    });
    if (!addedItem3) {
      petDetailWithAddedItem.evaluations = [];
    } else {
      petDetailWithAddedItem.evaluations = [addedItem3];
      return petDetailWithAddedItem;
    }

    return petDetailWithAddedItem;
  });

  return {
    buildPetDetailWithNewGroomingService,
    buildPetDetailWithEditingGroomingService,
    buildPetDetailWithRemovingGroomingService,
    purePetDetailLeftMatchOneOnly,
  };
};

import { memo } from 'react';
import { type PetQuestionKey } from 'types/question';
import { Input, TextArea as InputTextArea, type InputController } from 'widgets/Input/Input';
import { type FormExtraProps, type FormFieldProps } from '../FieldRegistry';

function TextComponent({
  input,
  title,
  placeholder,
  required,
  maxLength,
}: FormFieldProps<PetQuestionKey, unknown> & FormExtraProps) {
  if (input) {
  }

  return (
    <Input
      className="input-moe"
      input={input as InputController<string>}
      label={title}
      showRequiredIndicator={required}
      placeholder={placeholder}
      maxLength={maxLength}
    />
  );
}

export const Text = memo(TextComponent);

function TextAreaComponent({ input, title, placeholder, required }: FormFieldProps<PetQuestionKey, unknown>) {
  return (
    <InputTextArea
      className="input-moe"
      input={input as InputController<string>}
      label={title}
      showRequiredIndicator={required}
      placeholder={placeholder}
    />
  );
}

export const TextArea = memo(TextAreaComponent);

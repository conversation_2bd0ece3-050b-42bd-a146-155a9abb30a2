import { useBoolean } from '@moego/client-lib-hooks/dist/useBoolean';
import { useLazyEntryNode } from '@moego/client-lib-hooks/dist/useEntryNode';
import { useRandomId } from '@moego/client-lib-hooks/dist/useRandomId';
import { DropdownSelector, type DropdownSelectorProps } from '@moego/client-lib-widgets/dist/Selector/DropdownSelector';
import { default as classNames } from 'classnames';
import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  type MouseEvent,
  type MutableRefObject,
  type PropsWithChildren,
  type RefObject,
} from 'react';
import { type PetQuestionKey } from 'types/question';
import { preventSyntheticEvent } from 'utils/eventHelper';
import { scrollToSafePosition } from 'utils/node';
import { Icon, IconType } from 'widgets/Icon';
import { InputErrorMessageLabel, Label } from 'widgets/Label';
import { type FormFieldProps } from '../FieldRegistry';
import { type FormQuestionItem } from '../QuestionForm';
import { fieldInputClassNames } from '../utils';

const openingDropdowns = new Map<string, () => unknown>();

function DropdownComponent<T>(
  {
    title,
    required,
    options,
    onChange: onChangeCallback,
    renderKey,
    children,
    outline,
    className,
    optionsClassName,
    defaultValue,
    readonly,
    syncFocus,
  }: PropsWithChildren<
    Pick<FormFieldProps<PetQuestionKey, number | string>, 'title' | 'required'> &
      Pick<
        DropdownSelectorProps<T>,
        'options' | 'onChange' | 'renderKey' | 'defaultValue' | 'className' | 'optionsClassName'
      >
  > & {
    ref?: RefObject<HTMLDivElement>;
    outline?: boolean;
    readonly?: boolean;
    syncFocus?: (v: boolean) => void;
  },
  ref: React.ForwardedRef<HTMLDivElement>,
) {
  const dropdownId = useRandomId();
  const defaultRef = useRef<HTMLDivElement>(null);
  const internalRef = (ref || defaultRef) as MutableRefObject<HTMLDivElement>;
  const [focus, setFocus] = useBoolean(false);

  useEffect(() => {
    document.addEventListener('click', setFocus.disable);
    return () => document.removeEventListener('click', setFocus.disable);
  }, []);

  const onChange = useCallback(
    (v: T, index?: number) => {
      onChangeCallback?.(v, index);
      setFocus.disable();
    },
    [onChangeCallback],
  );

  const onClick = useCallback((e: MouseEvent) => {
    preventSyntheticEvent(e);
    setFocus.enable();
  }, []);

  useEffect(() => {
    syncFocus?.(focus);
    if (!focus) {
      openingDropdowns.delete(dropdownId.current);
      return;
    }
    Array.from(openingDropdowns.values()).forEach((close) => close());
    openingDropdowns.set(dropdownId.current, () => {
      setFocus.disable();
      openingDropdowns.delete(dropdownId.current);
    });
    return scrollToSafePosition(internalRef.current);
  }, [focus]);

  useEffect(
    () => () => {
      openingDropdowns.delete(dropdownId.current);
    },
    [],
  );

  return (
    <div
      className="w-full"
      ref={internalRef}
      data-component-name="Dropdown"
      data-input-role="root"
      onClick={readonly ? undefined : onClick}
    >
      {title ? <Label showRequiredIndicator={required}>{title}</Label> : null}
      <DropdownSelector
        className="w-full"
        optionsClassName={classNames(
          '!border-solid !border-moe-primary !rounded-t-none max-h-[40vh]',
          optionsClassName,
        )}
        options={options}
        renderKey={renderKey}
        onChange={onChange}
        open={readonly ? false : focus}
        defaultValue={defaultValue}
      >
        <div
          className={classNames(
            'relative !border-moe-secondary',
            fieldInputClassNames(focus, outline),
            focus ? '!rounded-b-none !border-b-0' : null,
            readonly ? '!bg-white unclickable text-[#c8cbd2]' : 'cursor-pointer',
            className,
          )}
          onClick={focus ? setFocus.disable.withPreventEvent : undefined}
        >
          {children}
          <Icon
            name={IconType.arrowDown}
            className={classNames(
              'duration-300ms absolute right-[20px] top-1/2 h-[20px] w-[20px] -translate-y-1/2 transition-all',
              focus ? 'fill-primary' : 'fill-text-secondary',
            )}
          />
        </div>
      </DropdownSelector>
    </div>
  );
}

export const Dropdown = memo(forwardRef(DropdownComponent)) as unknown as typeof DropdownComponent;

export interface CreateDropdownOptions<T> {
  getOptions: (fields: FormQuestionItem[], extra?: string) => T[];
  valueKey?: keyof T;
  renderKey?: keyof T;
  displayName?: string;
}

export function createDropdown<T>({
  getOptions,
  valueKey: valueKeyProp,
  renderKey: renderKeyProp,
  displayName,
}: CreateDropdownOptions<T>) {
  const valueKey = valueKeyProp || ('id' as keyof T);
  const renderKey = renderKeyProp || ('name' as keyof T);
  const Component = ({ input, title, fields, extra, required }: FormFieldProps<PetQuestionKey, string | number>) => {
    const options = getOptions(fields, extra);
    const onChange = useCallback((value: T) => input.setValue(value[valueKey] as number | string), [input.setState]);
    const defaultValue = useMemo(() => options.find((i) => i[valueKey]?.toString() === input.state.value), []);
    const readOnly = !!input.state.readOnly;

    return (
      <Dropdown
        ref={input.ref}
        title={title}
        required={required}
        options={options}
        onChange={onChange}
        renderKey={renderKey}
        defaultValue={defaultValue}
        readonly={readOnly}
      >
        {options.find((i) => i[valueKey] === input.state.value)?.[renderKey]?.toString()}
        <InputErrorMessageLabel input={input} portalEntry={useLazyEntryNode(() => input.ref.current!)} />
      </Dropdown>
    );
  };
  Component.displayName = `creteDropdown(${displayName ?? Component.name ?? 'Unknown'})`;
  return memo(Component);
}

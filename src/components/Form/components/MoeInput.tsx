import { Label } from '@moego/client-lib-widgets/dist/Label';
import { forwardRef, memo } from 'react';
import { filterNonNegativeNumberInput } from 'utils/number';
import { Input, type InputProps } from 'widgets/Input/Input';
import { ErrorMessageLabel } from 'widgets/Label';

export interface MoeInputProps extends Omit<InputProps<string>, 'value' | 'onChange'> {
  value?: string;
  onChange?: (value: string) => void;
  errorMessage?: string;
  label?: string;
  required?: boolean;
  type?: 'number' | 'text';
}

function MoeInputComponent(props: MoeInputProps, ref: React.Ref<HTMLInputElement>) {
  const { label, type = 'text', required, errorMessage, onChange, ...rest } = props;

  return (
    <div ref={ref}>
      {label && <Label showRequiredIndicator={required}>{label}</Label>}
      <Input
        {...rest}
        className="input-moe"
        onInput={(e) => {
          const value = e.target.value;
          onChange?.(type === 'number' ? filterNonNegativeNumberInput(value) : value);
        }}
      />
      {errorMessage ? <ErrorMessageLabel>{errorMessage}</ErrorMessageLabel> : null}
    </div>
  );
}

export const MoeInput = memo(forwardRef(MoeInputComponent)) as typeof MoeInputComponent;

import { useResetInputCursor } from '@moego/client-lib-hooks/dist/useResetInputCursor';
import { default as classNames } from 'classnames';
import { useCountryCode } from 'hooks/useCountryCode';
import { useCountryPhoneFormatter } from 'hooks/useCountryPhoneFormat';
import { memo, useCallback, useEffect, useRef, type ChangeEvent } from 'react';
import { type PetQuestionKey } from 'types/question';
import { Input, type InputController, type InputProps } from 'widgets/Input/Input';
import { type FormFieldProps } from '../FieldRegistry';

export interface PhoneInputProps<T extends string | number>
  extends Omit<
    InputProps<T>,
    'prefix' | 'pattern' | 'type' | 'validator' | 'onInput' | 'maxLength' | 'showRequiredIndicator'
  > {
  required?: boolean;
  input?: InputController<T>;
}

function PhoneInputComponent(props: PhoneInputProps<string>) {
  const countryCode = useCountryCode('+');
  const formatPhone = useCountryPhoneFormatter();
  const inputRef = useRef(null);
  const resetCursor = useResetInputCursor();

  useEffect(() => {
    if (props.input?.state?.value) {
      props.input?.setValue(formatPhone(props.input?.state?.value));
    }
  }, []);

  const onInput = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      if (inputRef?.current) {
        resetCursor(() => {
          const { value: originInputValue = '' } = e.target;
          const newVal = formatPhone(originInputValue.replace(/\D/g, ''));
          if (e.target) {
            e.target.value = newVal;
          }
        }, inputRef.current);
      }
    },
    [formatPhone],
  );

  return (
    <Input
      {...props}
      ref={inputRef}
      pattern="\d*"
      onInput={onInput}
      validator={props.required ? 'phone' : 'allowEmpty(phone)'}
      showRequiredIndicator={props.required}
      prefix={countryCode}
      prefixClassName={classNames(props?.readOnly ? '!text-[#C8CBD2]' : null)}
      maxLength={17}
    />
  );
}

export const PhoneInput = memo(PhoneInputComponent);

export interface CreatePhoneOptions {
  accept?: string;
  displayName?: string;
}

export function createPhone({ displayName }: CreatePhoneOptions) {
  const Component = ({ input, title, required }: FormFieldProps<PetQuestionKey, string>) => {
    return (
      <PhoneInput label={title} input={input} placeholder="************" className="input-moe" required={required} />
    );
  };
  Component.displayName = `createPhone(${displayName ?? Component.name ?? 'Unknown'})`;
  return memo(Component);
}

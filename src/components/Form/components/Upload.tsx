import { IconType } from 'assets/icons/types';
import { Upload as UploadResource, type UploadProps } from 'components/Upload';
import {
  forwardRef,
  memo,
  useCallback,
  useState,
  type ForwardedRef,
  type MutableRefObject,
  type PropsWithChildren,
} from 'react';
import { type PetQuestionKey } from 'types/question';
import { Icon } from 'widgets/Icon';
import { InputErrorMessageLabel, Label } from 'widgets/Label';
import { type FormFieldProps } from '../FieldRegistry';

function UploadComponent(
  {
    title,
    required,
    onChange,
    className,
    defaultValue,
    beforeUpload,
    children,
    accept = 'image/*,application/pdf,application/msword',
  }: PropsWithChildren<
    Pick<FormFieldProps<PetQuestionKey, number | string>, 'title' | 'required' | 'defaultValue'> &
      Pick<UploadProps, 'className' | 'accept' | 'beforeUpload'> & {
        onChange: (v: string) => unknown;
        ref?: MutableRefObject<HTMLDivElement>;
      }
  >,
  ref: ForwardedRef<HTMLDivElement>,
) {
  const [file, setFile] = useState<string>(defaultValue?.toString() || '');

  const onUploadSuccess = useCallback(([file]: string[]) => {
    setFile(file);
    onChange(file);
  }, []);

  const onClearFile = useCallback(() => {
    setFile('');
    onChange('');
  }, []);

  return (
    <div data-component-name="Upload" data-input-role="root" ref={ref}>
      {title ? <Label showRequiredIndicator={required}>{title}</Label> : null}
      <div className="relative inline-block w-[80px] h-[80px]">
        <UploadResource
          className={className}
          accept={accept}
          onUploadSuccess={onUploadSuccess}
          beforeUpload={beforeUpload}
          disabled={!!file}
          multiple
        >
          {file ? <FilePreview file={file} /> : null}
        </UploadResource>
        {file ? (
          <div
            onClick={onClearFile}
            className="absolute right-[-8px] top-[-4px] w-[20px] h-[20px] bg-[#333] rounded-full text-white flex-center pt-[2px]"
          >
            ×
          </div>
        ) : null}
      </div>
      {children}
    </div>
  );
}

const IMAGE_FORMATS = new Set(['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp']);

function FilePreview({ file }: { file: string }) {
  const names = file.split('.');
  const format = names[names.length - 1];
  const isImage = IMAGE_FORMATS.has(format);

  return isImage ? (
    <div className="w-full h-full bg-cover bg-center bg-no-repeat" style={{ backgroundImage: `url(${file})` }} />
  ) : (
    <div className="w-full h-full rounded-[16px] bg-base-100 border-[1px] border-solid border-secondary-line flex-center">
      <Icon className="w-[24px] h-[24px] fill-[#323232]" name={IconType.documentsFile} />
    </div>
  );
}

export const Upload = memo(forwardRef(UploadComponent));

export interface CreateUploadOptions {
  accept?: string;
  displayName?: string;
}

export function createUpload({ displayName, accept }: CreateUploadOptions) {
  const Component = ({ input, title, required }: FormFieldProps<PetQuestionKey, string>) => {
    const onChange = useCallback((url: string) => input.setValue(url), [input.setState]);
    return (
      <Upload
        ref={input.ref}
        accept={accept}
        title={title}
        required={required}
        defaultValue={input.state.value}
        onChange={onChange}
      >
        <InputErrorMessageLabel input={input} />
      </Upload>
    );
  };
  Component.displayName = `createUpload(${displayName ?? Component.name ?? 'Unknown'})`;
  return memo(Component);
}

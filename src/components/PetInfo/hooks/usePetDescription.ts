import { useSelector } from 'amos';
import { petMapBox } from '../../../store/pet/pet.boxes';
import { isNormal } from '../../../store/utils/identifier';
import { useWeightSuffix } from '../../../utils/hooks/useWeightSuffix';
import { printAge } from '../../../utils/DateTimeUtil';
import dayjs from 'dayjs';

export function usePetDescription(petId: number) {
  const [petMap] = useSelector(petMapBox);
  const pet = petMap.mustGetItem(petId);
  const weightUnit = useWeightSuffix();
  const petWeight = pet.weight ? pet.weight + weightUnit : '';
  const petAge = pet.birthday ? printAge(dayjs(pet.birthday)) : '';
  const petDescription = [pet.breed, petWeight, petAge, pet.hairLength, pet.fixed]
    .filter((item) => !!item)
    .join('\u{20}·\u{20}');
  return isNormal(petId) ? petDescription : '';
}

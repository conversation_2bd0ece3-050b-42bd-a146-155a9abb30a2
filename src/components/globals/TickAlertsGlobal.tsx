import { useDispatch, useSelector } from 'amos';
import React, { memo } from 'react';
import { TicketCreatedAlerts } from '../../container/Calendar/Grooming/TicketCreatedAlerts/TicketCreatedAlerts';
import { AutoMessageType } from '../../store/autoMessage/autoMessage.boxes';
import { calendarNotificationBox } from '../../store/calendarLatest/calendar.boxes';
import { ID_ANONYMOUS, isNormal } from '../../store/utils/identifier';

export const TickAlertsGlobal = memo(() => {
  const [notificationConfig] = useSelector(calendarNotificationBox);
  const dispatch = useDispatch();

  if (!(isNormal(notificationConfig.ticketId) && isNormal(notificationConfig.customerId))) {
    return null;
  }

  return (
    <TicketCreatedAlerts
      ticketId={notificationConfig.ticketId}
      customerId={notificationConfig.customerId}
      mode={notificationConfig.mode}
      isRepeat={notificationConfig.isRepeat}
      onClose={() => {
        dispatch(
          calendarNotificationBox.setState({
            ticketId: ID_ANONYMOUS,
            customerId: ID_ANONYMOUS,
            mode: AutoMessageType.AppointmentBooked,
          }),
        );
      }}
    />
  );
});

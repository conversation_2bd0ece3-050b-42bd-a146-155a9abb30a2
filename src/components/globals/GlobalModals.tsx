/*
 * @since 2020-12-15 13:35:49
 * <AUTHOR> <<EMAIL>>
 */

import { useDispatch, useSelector } from 'amos';
import React, { memo } from 'react';
import { ModalA2PRegistration } from '../../container/Account/AccountInfo/CompanyInfo/components/ModalA2PRegistration/ModalA2PRegistration';
import { ApptDetailCommonDrawer } from '../../container/Appt/components/ApptDetailCommonDrawer/ApptDetailCommonDrawer';
import { PreAuthTryAgainModal } from '../../container/Calendar/Grooming/PreAuthForAppt/TryAgainModal/PreAuthTryAgainModal';
import { TakePaymentModal } from '../../container/Calendar/Grooming/TakePaymentModal/TakePaymentModal';
import { SelectMessagePackageModal } from '../../container/Message/MessageCenter/components/SelectMessagePackageModal';
import { OnboardingDrawer } from '../../container/Onboarding/OnboardingDrawer';
import { OnlineBookingRequestModal } from '../../container/OnlineBooking/modules/OnlineBookingRequests/components/OnlineBookingRequestModal/OnlineBookingRequestModal';
import { RequestCardAuthModal } from '../../container/Payment/components/RequestCardAuthModal';
import { RequestCofModal } from '../../container/Payment/components/RequestCofModal';
import { SmartSchedulingStartModal } from '../../container/SmartScheduling/SmartSchedulingStartModal';
import { getMessageReport } from '../../store/message/message.actions';
import { Condition } from '../Condition';
import { PetIncidentModal } from '../PetIncidentModal/PetIncidentModal';
import { PricingUpgradeModal } from '../Pricing/PricingUpgradeModal';
import { globalModalsBox, openGlobalModal } from './GlobalModals.store';
import { RepeatDrawer } from './RepeatDrawer';
import { TickAlertsGlobal } from './TickAlertsGlobal';

export interface GlobalModalsProps {
  didLogin: boolean;
}

export const GlobalModals = memo<GlobalModalsProps>(({ didLogin }) => {
  const [
    {
      takePayment,
      obRequest,
      a2pRegister,
      requestCof,
      requestCardAuth,
      preAuthTryAgain,
      onBoardingDrawer,
      pricingUpgrade,
      onClose,
      smsLimitUpgrade,
      petIncident,
    },
  ] = useSelector(globalModalsBox);
  const dispatch = useDispatch();

  return (
    <>
      {/* 
      TODO(vision,p1) 造孽啊，怎么还有这种东西，exist appt 的 reschedule 等操作也会弹出这个，
      这个之前放在了quick add portal 里面，神奇在全局生效了
      后续重构记得在其他使用测，用 useAlertTicket替换它，目前先临时改一下
      */}
      <TickAlertsGlobal />

      {takePayment && (
        <TakePaymentModal {...takePayment} onClose={() => dispatch(openGlobalModal({ takePayment: undefined }))} />
      )}
      {obRequest && (
        <OnlineBookingRequestModal
          {...obRequest}
          onClose={(updated) => {
            dispatch(openGlobalModal({ obRequest: undefined }));
            obRequest.onClose(updated);
          }}
        />
      )}
      {a2pRegister && (
        <ModalA2PRegistration
          {...a2pRegister}
          onClose={() => {
            dispatch(openGlobalModal({ a2pRegister: undefined }));
            onClose?.();
          }}
        />
      )}
      {requestCof && (
        <RequestCofModal
          {...requestCof}
          onClose={(sent) => {
            dispatch(openGlobalModal({ requestCof: undefined }));
            requestCof.onClose?.(sent);
          }}
        />
      )}
      {requestCardAuth && (
        <RequestCardAuthModal
          {...requestCardAuth!}
          onClose={() => {
            dispatch(openGlobalModal({ requestCardAuth: undefined }));
            onClose?.();
          }}
        />
      )}
      {preAuthTryAgain && (
        <PreAuthTryAgainModal
          {...preAuthTryAgain}
          onClose={() => {
            dispatch(openGlobalModal({ preAuthTryAgain: undefined }));
            onClose?.();
          }}
        />
      )}
      <SmartSchedulingStartModal />
      <Condition if={onBoardingDrawer?.visible}>
        {() => (
          <OnboardingDrawer
            {...onBoardingDrawer!}
            onClose={() => {
              dispatch(openGlobalModal({ onBoardingDrawer: undefined }));
            }}
          />
        )}
      </Condition>
      <Condition if={didLogin}>
        <ApptDetailCommonDrawer />
        <RepeatDrawer />
      </Condition>
      {/* pricingUpgrade 升级弹窗 */}
      {pricingUpgrade && (
        <PricingUpgradeModal
          {...pricingUpgrade}
          visible={true}
          onClose={() => {
            dispatch(openGlobalModal({ pricingUpgrade: undefined }));
          }}
        ></PricingUpgradeModal>
      )}
      {/* sms升级弹窗 */}
      {smsLimitUpgrade && (
        <>
          {smsLimitUpgrade.pricingModal.visible && (
            <PricingUpgradeModal
              {...smsLimitUpgrade.pricingModal}
              visible={true}
              onClose={() => {
                dispatch(openGlobalModal({ smsLimitUpgrade: undefined }));
              }}
              btn={{
                text: 'Get more messages',
                onClick: () => {
                  dispatch(
                    openGlobalModal({
                      smsLimitUpgrade: {
                        messagePackageModal: {
                          ...smsLimitUpgrade.messagePackageModal,
                          visible: true,
                        },
                        pricingModal: {
                          ...smsLimitUpgrade.pricingModal,
                          visible: false,
                        },
                      },
                    }),
                  );
                },
              }}
            ></PricingUpgradeModal>
          )}
          {smsLimitUpgrade.messagePackageModal.visible && (
            <SelectMessagePackageModal
              visible={true}
              onClose={() => {
                dispatch(openGlobalModal({ smsLimitUpgrade: undefined }));
              }}
              companyId={smsLimitUpgrade.messagePackageModal.companyId}
              onChargeSuccess={() => {
                dispatch(getMessageReport());
                dispatch(openGlobalModal({ smsLimitUpgrade: undefined }));
              }}
            />
          )}
        </>
      )}
      <Condition if={petIncident?.visible}>
        <PetIncidentModal
          {...petIncident}
          onClose={() => dispatch(openGlobalModal({ petIncident: { visible: false } }))}
        />
      </Condition>
    </>
  );
});

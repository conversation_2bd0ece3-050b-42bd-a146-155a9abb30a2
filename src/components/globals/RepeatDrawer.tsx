import { useDispatch } from 'amos';
import React, { memo } from 'react';
import { RepeatSeriesPreviewDrawer } from '../../container/Appt/components/RepeatSeries/RepeatSeriesPreviewDrawer/RepeatSeriesPreviewDrawer';
import {
  setDrivingTimeOpenState,
  setTicketAlertsProps,
} from '../../store/calendarLatest/actions/private/calendar.actions';
import { emitReloadMapViewAppts } from '../../store/mapView/actions/public/mapView.actions';
import { globalEvent } from '../../utils/events/events';
import { useCloseAllDrawer } from '../../utils/hooks/useCloseAllDrawer';

export interface RepeatDrawerProps {
  className?: string;
}

export const RepeatDrawer = memo<RepeatDrawerProps>(function RepeatDrawer() {
  const dispatch = useDispatch();
  const { closeAllDrawer } = useCloseAllDrawer();

  return (
    <RepeatSeriesPreviewDrawer
      onCreated={(alertProps) => {
        // TODO(vision,p1) 临时fix 目前这里不支持关闭 created appt，我们先通过事件去实现
        // repeat drawer 应该通过 useModal 替换
        closeAllDrawer();
        globalEvent.closeCreateAppt.emit({ doubleConfirm: false });
        globalEvent.refresh.emit();
        dispatch([...(alertProps?.mode ? [setTicketAlertsProps(alertProps)] : []), setDrivingTimeOpenState(false)]);
        dispatch(emitReloadMapViewAppts());
      }}
    />
  );
});

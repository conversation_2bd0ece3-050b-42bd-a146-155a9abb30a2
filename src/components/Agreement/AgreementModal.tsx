/*
 * @since 2020-09-08 17:56:47
 * <AUTHOR> <<EMAIL>>
 */

import { CloseOutlined } from '@ant-design/icons';
import { useNextZIndex } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import html2canvas from 'html2canvas';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import React, { memo, useEffect, useMemo, useRef } from 'react';
import { createPortal } from 'react-dom';
import { useUnmount } from 'react-use';
import IconDownloadWhiteSvg from '../../assets/icon/download-white.svg';
import IconGroomingHeaderPrinterWhiteSvg from '../../assets/icon/grooming-header-printer-white.svg';
import { type AgreementRecord, agreementMapBox } from '../../store/agreement/agreement.boxes';
import { getSignedDetail } from '../../store/agreement/sign.actions';
import { type SignRecord, signMapBox } from '../../store/agreement/sign.boxes';
import { type BusinessRecord } from '../../store/business/business.boxes';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { ID_ANONYMOUS, isNormal } from '../../store/utils/identifier';
import { s3url } from '../../utils/cdn';
import { ImgIcon } from '../Icon/Icon';
import { Fill } from '../Style/Style';
import { AgreementModalGlobalStyle, AgreementModalView } from './AgreementModal.style';
import { useOpenInAppControl } from '../../utils/hooks/useOpenInApp';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';

export interface AgreementModalProps {
  className?: string;
  id: number | undefined;
  mode: 'sign' | 'agreement';
  onClose: () => void;
}

export const AgreementModal = memo<AgreementModalProps>(({ className, id, mode, onClose }) => {
  const visible = id !== void 0;
  const zIndex = useNextZIndex();

  const signId = (mode === 'sign' && id) || ID_ANONYMOUS;
  const agreementId = (mode === 'agreement' && id) || ID_ANONYMOUS;

  const [sign, business, agreement] = useSelector(
    signMapBox.mustGetItem(signId),
    selectCurrentBusiness,
    agreementMapBox.mustGetItem(agreementId),
  ) as [SignRecord, BusinessRecord, AgreementRecord];
  const dispatch = useDispatch();

  useEffect(() => {
    if (isNormal(signId)) {
      dispatch(getSignedDetail(signId));
    }
  }, [signId]);

  const container = useRef<HTMLDivElement>();
  if (!container.current) {
    container.current = document.createElement('div');
    container.current.className = 'agreement-modal';
    document.body.appendChild(container.current);
  }
  container.current.classList.toggle('visible', visible);
  useUnmount(() => {
    if (container.current) {
      document.body.removeChild(container.current);
    }
  });
  const contentRef = useRef<HTMLDivElement>(null);
  const title = (
    mode === 'sign'
      ? `${sign.agreementHeader} - ${sign.customerFirstName} - ${sign.customerLastName}`
      : agreement.agreementHeader
  ).replace(/[<>:"/\\|?*]+/g, '_');

  const handleDownload = () => {
    if (!contentRef.current) {
      return;
    }
    html2canvas(contentRef.current, { scale: 2, useCORS: true, allowTaint: true }).then((canvas) => {
      const imageData = canvas.toDataURL('image/png');
      const anchor = document.createElement('a');
      anchor.href = imageData;
      anchor.download = title + '.png';
      anchor.target = '_blank';
      anchor.click();
    });
  };

  const handlePrint = () => {
    const originalTitle = document.title;
    document.title = title;
    print();
    document.title = originalTitle;
  };

  const showContext = useMemo(() => {
    const reFindnew = /<p>/g;
    const reReplace = /(\r\n)|(\n\r)|(\r)|(\n)/g; // To match the \r\n, \n, \n\r and \r
    const values = sign.inputs;
    const replaceInputSpan = /<span is-input-span="true">.*?<\/span>/g;
    let context = mode === 'sign' ? sign.agreementContent : agreement.agreementContent;
    if (context?.match(reFindnew) === null) {
      context = context?.replace(reReplace, '<br/>');
    }
    let i = 0;
    context = context?.replace(replaceInputSpan, () => {
      const replacement = values?.[i] ?? '';
      i++;
      return replacement;
    });
    return context;
  }, [sign.inputs, sign.agreementContent, mode, agreement.agreementContent]);

  // 避免 mobile 浏览器打开时，被 Open in App 遮挡住
  const [, setOpenInAppVisible] = useOpenInAppControl();
  useEffect(() => {
    if (visible) {
      setOpenInAppVisible(false);
    }
  }, [visible]);

  const handleClose = useLatestCallback(() => {
    setOpenInAppVisible(true);
    onClose();
  });

  if (!visible) {
    return null;
  }
  return (
    <>
      {<AgreementModalGlobalStyle />}
      {createPortal(
        <AgreementModalView
          className={className}
          style={{
            zIndex,
          }}
        >
          <div className="container">
            <div className="content" ref={contentRef}>
              <div className="header">{business.businessName}</div>
              <div className="title">{mode === 'sign' ? sign.agreementHeader : agreement.agreementHeader}</div>
              <div
                className="body"
                dangerouslySetInnerHTML={{
                  __html: showContext,
                }}
              />
              {mode === 'sign' ? (
                <>
                  <div className="footer">
                    <div>Date: {business.formatDateTime(sign.signedTime * T_SECOND)}</div>
                    <div>
                      Name: {sign.customerFirstName} {sign.customerLastName}
                    </div>
                    <div>Signature</div>
                  </div>
                  <div className="signature rr-block">
                    {sign.signature ? (
                      <img src={s3url(sign.signature)} alt="signature" crossOrigin="anonymous" />
                    ) : null}
                  </div>
                </>
              ) : null}
            </div>
          </div>
          <CloseOutlined className="close" onClick={handleClose} />
          <div className="actions">
            <ImgIcon src={IconDownloadWhiteSvg} width={44} onClick={handleDownload} />
            <Fill width={20} />
            <ImgIcon src={IconGroomingHeaderPrinterWhiteSvg} width={33} onClick={handlePrint} />
          </div>
        </AgreementModalView>,
        container.current,
      )}
    </>
  );
});

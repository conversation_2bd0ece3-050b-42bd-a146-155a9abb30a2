/*
 * @since 2021-05-20 18:03:59
 * <AUTHOR> <<EMAIL>>
 */

import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import { PATH_SIGN_IN, PATH_SIGN_UP } from '../router/paths';
import { selectViewportFitInMobileEnable } from '../store/company/company.selectors';
import { useRouteQuery } from '../utils/RoutePath';
import { isMobile } from '../utils/device';
import { useOpenInApp, useOpenInAppControl } from '../utils/hooks/useOpenInApp';
import { OpenInMoeGoView } from './OpenInMoeGo.style';
import { OPEN_IN_MOEGO_PAGE_BLACK_LIST } from './OpenInMoeGo.utils';

export interface OpenInMoeGoProps {
  className?: string;
}

export const OpenInMoeGo = memo<OpenInMoeGoProps>(({ className }) => {
  const { pathname } = useLocation();
  const [openInAppVisible] = useOpenInAppControl();
  const [viewportFitInMobile] = useSelector(selectViewportFitInMobileEnable());

  const isSignUpOrSignIn = useMemo(() => PATH_SIGN_IN.match(pathname) || PATH_SIGN_UP.match(pathname), [pathname]);
  const query = useRouteQuery(isSignUpOrSignIn ? PATH_SIGN_IN : undefined);
  const isBlack = OPEN_IN_MOEGO_PAGE_BLACK_LIST.some((v) => v.match(pathname)) || !!query?.inviteCode;

  const openInApp = useOpenInApp();

  if (isBlack || !isMobile() || !openInAppVisible || viewportFitInMobile) {
    return null;
  }

  return (
    <OpenInMoeGoView className={className} onClick={openInApp}>
      Open in MoeGo app
    </OpenInMoeGoView>
  );
});

import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { cn } from '@moego/ui';
import React, { Fragment, type PropsWithChildren, memo } from 'react';
import { ApptTestIds } from '../../../config/testIds/apptDrawer';
import { optionalFunction } from '../../../utils/utils';
import { Condition } from '../../Condition';
import { PetDetailCard, type PetDetailCardProps } from '../../PetInfo/PetDetailCard';
import { BoardingService } from '../../ServiceApplicablePicker/components/ServiceCard/BoardingService';
import { DaycareService } from '../../ServiceApplicablePicker/components/ServiceCard/DaycareService';
import { ServiceCard } from '../../ServiceApplicablePicker/components/ServiceCard/ServiceCard';
import { ServiceChildAddon } from '../../ServiceApplicablePicker/components/ServiceCard/ServiceChildAddon';
import { type ServiceEntry } from '../../ServiceApplicablePicker/types/serviceEntry';
import { useGroupByService } from '../hooks/groupByService';
import { useCategoryService } from '../hooks/useCategoryService';
import { type PetServicePriceContentProps } from '../types/types';
import { ServiceCardWithOrder } from './ServiceCardWithOrder';

export interface PetServiceCardProps
  extends Pick<
      PetDetailCardProps,
      'onDelete' | 'onEdit' | 'disabled' | 'showIncident' | 'appointmentDate' | 'appointmentEndDate'
    >,
    PetServicePriceContentProps {
  className?: string;
  petId: string;
  /** ticket状态为可以editable，但是没有权限 */
  noPermissionEdit?: boolean;
  serviceList: ServiceEntry[];
  prefix?: (petId: string) => React.ReactNode;
  suffix?: (petId: string, serviceItemType: ServiceItemType) => React.ReactNode;
  onClick?: () => void;
  onGo2PetNotes?: () => void;
  renderExtraTips?: (petId: string) => React.ReactNode;
}

export const PetServiceCard = memo<PropsWithChildren<PetServiceCardProps>>((props) => {
  const {
    className,
    petId,
    children,
    disabled,
    serviceList,
    noPermissionEdit = false,
    prefix,
    suffix,
    onClick,
    onDelete,
    onEdit,
    onGo2PetNotes,
    priceContent,
    renderExtraTips,
    ...rest
  } = props;

  const groupByService = useGroupByService();
  const { groupedServiceList } = groupByService(serviceList);

  const { boarding, daycare, grooming, dogWalking, mainServiceItemType } = useCategoryService(groupedServiceList);
  return (
    <>
      {renderExtraTips?.(petId)}
      <div
        className={cn(
          'moe-p-[16px] moe-flex moe-flex-col moe-gap-y-[16px] moe-border moe-border-solid moe-border-[#CDCDCD] moe-rounded-m moe-group',
          noPermissionEdit ? 'moe-cursor-not-allowed' : disabled ? 'moe-cursor-default' : 'moe-cursor-pointer',
          !disabled && 'hover:moe-bg-neutral-sunken-light',
          className,
        )}
        onClick={onClick}
        data-testid={ApptTestIds.ApptPetService}
      >
        {prefix?.(petId)}
        <PetDetailCard
          {...rest}
          petId={petId}
          onDelete={optionalFunction(onDelete, !noPermissionEdit)}
          onEdit={optionalFunction(onEdit, !noPermissionEdit)}
          go2PetNotes={onGo2PetNotes}
          disabled={disabled}
          showIncident
        />
        <Condition if={boarding.length}>
          {boarding.map((s) => {
            return (
              <ServiceCard key={s.id} childClassName="moe-flex moe-flex-col moe-gap-y-[12px]" disabled={disabled}>
                <BoardingService service={s} priceContent={priceContent} petId={petId} />
                <ServiceChildAddon list={s.children} mainServiceItemType={mainServiceItemType} />
                {suffix?.(petId, ServiceItemType.BOARDING)}
              </ServiceCard>
            );
          })}
        </Condition>
        <Condition if={daycare.length}>
          <ServiceCard childClassName="moe-flex moe-flex-col moe-gap-y-[12px]" disabled={disabled}>
            {daycare.map((s) => {
              return (
                <Fragment key={s.id}>
                  <DaycareService service={s} mainServiceItemType={mainServiceItemType} priceContent={priceContent} />
                  <ServiceChildAddon list={s.children} mainServiceItemType={mainServiceItemType} />
                  <Condition if={boarding.length === 0}>{suffix?.(petId, ServiceItemType.DAYCARE)}</Condition>
                </Fragment>
              );
            })}
          </ServiceCard>
        </Condition>
        <Condition if={grooming.length}>
          <ServiceCardWithOrder
            petId={petId}
            mainServiceItemType={mainServiceItemType}
            serviceItemType={ServiceItemType.GROOMING}
            priceContent={priceContent}
            disabled={disabled}
            list={grooming}
            suffix={suffix}
          />
        </Condition>
        <Condition if={dogWalking.length}>
          <ServiceCardWithOrder
            petId={petId}
            mainServiceItemType={mainServiceItemType}
            serviceItemType={ServiceItemType.DOG_WALKING}
            priceContent={priceContent}
            disabled={disabled}
            list={dogWalking}
            suffix={suffix}
          />
        </Condition>
        {children}
      </div>
    </>
  );
});

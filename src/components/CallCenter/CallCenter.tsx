import { AudioDev<PERSON>, Call, CallInvite, Voice } from '@twilio/voice-react-native-sdk';
import { useDebounceFn, useLatest, useMemoizedFn } from 'ahooks';
import { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { AppState, Linking, Platform } from 'react-native';
import Modal from 'react-native-modal';
import { currentBusinessIdBox } from '../../modules/Business/store/business.boxes';
import { META_DATA_KEY_LIST } from '../../modules/Common/store/metadata.config';
import { useMetaDataOnce } from '../../modules/Common/store/metadata.hooks';
import { PATH_CALLING_ACTIVITY_LIST } from '../../modules/Message/Message.api';
import {
  getClientAndPetInfo,
  getToken,
  type ClientAndInfoOriginModel,
} from '../../modules/Message/store/calling.actions';
import { confirmAsync } from '../../utils/alert';
import { memoForwardRef } from '../../utils/hooks/react';
import { LoggerModule, moegoLogger } from '../../utils/logger';
import { useDrawerNavigation } from '../../utils/navigation';
import { grantMicrophonePermissions } from '../../utils/permissions';
import { useListSelector } from '../../utils/store/selector';
import { toast } from '../../utils/toast';
import { Condition } from '../Condition';
import {
  CallCenterDirection,
  CallCenterErrorCode,
  CallCenterErrorCodeMessage,
  CallCenterMessageType,
  CallStatusEnum,
  WindowsStatus,
  devicePriority,
} from './CallCenter.constant';
import { getAudioDeviceType, getTimeDifference, isBluetoothDevice } from './CallCenter.utils';
import { CallCenterFloatView } from './CallCenterFloatView';
import { CallCenterFullScreen } from './CallCenterFullScreen';
import { CallCenterHeaderBar } from './CallCenterHeaderBar';

// Module-level variable to persist the last logged selected audio device UUID across component instances.
// This ensures that we only log when the selected device actually changes.
let lastLoggedSelectedDeviceUUID: string | null | undefined;
const LOG_THROTTLE_INTERVAL_MS = 5000; // 5 seconds

interface CallCenterProps {}
export interface CallCenterCallParams {
  companyId: number;
  businessId: number;
  staffId: number;
  clientId?: number;
  customerId?: number;
  phoneNumber?: string;
  immediate?: boolean;
}

export interface MoeGoDevice {
  type: AudioDevice.Type;
  originDevice: AudioDevice;
}
export interface CallCenterRef {
  call: (props: CallCenterCallParams) => void;
  refresh: () => void;
  destroy: () => void;
  getStatus: () => CallStatusEnum;
  onStatusChanged?: (id: string, callback: (status: CallStatusEnum, message?: string) => void) => void;
  removeStatusChanged?: (id: string) => void;
}

export const CallCenter = memoForwardRef<CallCenterRef, CallCenterProps>((_props, ref) => {
  const [show, setShow] = useState(false);
  const [messageCode, setMessageCode] = useState(CallCenterMessageType.IncomingCall);
  const [durationMessage, setDurationMessage] = useState('');
  const [direction, setDirection] = useState<CallCenterDirection>(CallCenterDirection.Incoming);
  const [currentMute, setCurrentMute] = useState<boolean>(false);
  const [audioDevices, setAudioDevices] = useState<MoeGoDevice[]>([]);
  const audioDevicesRef = useLatest<MoeGoDevice[]>(audioDevices);
  const [audioDeviceType, setAudioDeviceType] = useState<AudioDevice.Type>(AudioDevice.Type.Earpiece);
  const currentAudioDeviceRef = useLatest<AudioDevice.Type>(audioDeviceType);
  const voiceRef = useRef<Voice | null>(null);
  const callInviteRef = useRef<CallInvite | null>(null);
  const [windowsStatus, setWindowsStatus] = useState<WindowsStatus>(WindowsStatus.HeaderBar);
  const currentCall = useRef<Call | null>(null);
  const [linked, setLinked] = useState<boolean>(false);
  const linkedRef = useLatest<boolean>(linked);
  const currentConnectTime = useRef<Date | null>(null);
  const timer = useRef<NodeJS.Timeout | null>(null);
  const tokenUpdate = useRef<NodeJS.Timeout | null>(null);
  const [info, setInfo] = useState<ClientAndInfoOriginModel | null>(null);
  const { data: engagementCenterAvailable } = useMetaDataOnce<boolean>(META_DATA_KEY_LIST.EngagementCenter);
  const { data: voipAvailable } = useMetaDataOnce<boolean>(META_DATA_KEY_LIST.CallCenterVoip);
  const [businessId] = useListSelector(currentBusinessIdBox);
  const messageTimer = useRef<NodeJS.Timeout | null>(null);
  const tokenRef = useRef<string>('');
  const [status, setStatus] = useState<CallStatusEnum>(CallStatusEnum.Offline);
  const navigation = useDrawerNavigation();
  const [statusChangedPool, setStatusChangedPool] = useState<
    Record<string, (status: CallStatusEnum, message?: string) => void>
  >({});
  const missedCallsRef = useRef<number>(0);
  const lastLoggedWarningRef = useRef<{ warning: Call.QualityWarning | undefined; timestamp: number }>({
    warning: undefined,
    timestamp: 0,
  }); // 减少大量重复日志上报
  const logger = moegoLogger.get(LoggerModule.CALL);

  const logQualityWarningWithThrottle = useMemoizedFn((currentQualityWarnings: Call.QualityWarning[]) => {
    const lastCurrentWarning =
      currentQualityWarnings.length > 0 ? currentQualityWarnings[currentQualityWarnings.length - 1] : undefined;

    const now = Date.now();
    const lastLogged = lastLoggedWarningRef.current;

    // fix: The OS watchdog terminated your app, possibly because it overused RAM.
    if (
      lastCurrentWarning !== lastLogged.warning ||
      (lastCurrentWarning && now - lastLogged.timestamp > LOG_THROTTLE_INTERVAL_MS)
    ) {
      logger.info(`[Call center RN info] [quality warnings changed]`, {
        id: 'twilio_voice_call_quality_warn',
        result: {
          current: lastCurrentWarning,
        },
      });
      lastLoggedWarningRef.current = { warning: lastCurrentWarning, timestamp: now };
    }
  });

  const onStatusChanged = useMemoizedFn((id: string, callback: (status: CallStatusEnum, message?: string) => void) => {
    setStatusChangedPool((prev) => {
      if (prev[id]) {
        return prev;
      } else {
        return { ...prev, [id]: callback };
      }
    });
  });

  const removeStatusChanged = useMemoizedFn((id: string) => {
    setStatusChangedPool((prev) => {
      const newPool = { ...prev };
      delete newPool[id];
      return newPool;
    });
  });

  const { run: callCenterOnStatusChanged } = useDebounceFn(
    (status: CallStatusEnum, message?: string) => {
      setStatus(status);
      Object.values(statusChangedPool).forEach((callback) => {
        callback(status, message);
      });
    },
    {
      wait: 100,
    },
  );

  const getCustomerInfo = useMemoizedFn(async (param: { customerId?: string; clientId?: string }) => {
    //  get customer info
    const data = await getClientAndPetInfo(
      param.customerId
        ? {
            customerId: param.customerId,
          }
        : {
            clientId: param.clientId,
          },
    );
    setInfo(data);
    return data;
  });

  useEffect(() => {
    // register Twilio , only once temp hidden logic
    if (!engagementCenterAvailable) {
      return;
    }
    if (!businessId) {
      return;
    }
    registerTwilio();
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    //  register back service for call center
    return () => {
      // unregister Twilio
      destroy();
      subscription.remove(); // 清除监听器
    };
  }, [engagementCenterAvailable, businessId]);

  const handleAppStateChange = (nextAppState) => {
    if (nextAppState === 'active') {
      tokenUpdateAction();
      twilioAutoTokenUpdate();
      // 在这里处理应用从后台切换到前台的逻辑
    } else if (nextAppState === 'background') {
      if (tokenUpdate.current) {
        clearInterval(tokenUpdate.current);
      }
      // 在这里处理应用从前台切换到后台的逻辑
    }
  };

  const refreshRegister = useMemoizedFn(async () => {
    // temp hidden logic
    await destroy();
    const hasMicrophonePermissions = await grantMicrophonePermissions();
    if (!hasMicrophonePermissions) {
      toast('fail', CallCenterErrorCodeMessage[CallCenterErrorCode.MicrophonePermissionDenied]);
      callCenterOnStatusChanged(
        CallStatusEnum.Error,
        CallCenterErrorCodeMessage[CallCenterErrorCode.MicrophonePermissionDenied],
      );
      return;
    }
    await registerTwilio();
  });

  const { run: tokenUpdateAction } = useDebounceFn(
    async () => {
      const newToken = await getToken();
      // 暂时只加了更新 token 过程(其他过程根据反馈再添加)中的详细日志
      logger.info(`[Call center RN info] [update token] token fetched`, {
        params: {
          oldToken: tokenRef.current,
          newToken: newToken,
        },
      });
      if (newToken !== tokenRef.current && newToken !== '') {
        if (voiceRef.current) {
          try {
            await voiceRef.current.register(newToken);
          } catch (e) {
            logger.error(`[Call center RN error] [update token] failed to register new token`, { error: e });
            throw e;
          }

          tokenRef.current = newToken;
          logger.info(`[Call center RN info] [update token] token registered`, { result: { token: newToken } });
        }
      }
    },
    {
      wait: 5000,
    },
  );
  const twilioAutoTokenUpdate = useMemoizedFn(async () => {
    if (tokenUpdate.current) {
      clearInterval(tokenUpdate.current);
    }
    tokenUpdate.current = setInterval(
      tokenUpdateAction,
      //  update token every 50 minutes, token will expire in 1 hour
      1000 * 60 * 50,
    );
  });

  const updateAudioDevices = (gotDevices: AudioDevice[]): MoeGoDevice[] => {
    const tempDevices: MoeGoDevice[] = [];
    for (const device of gotDevices) {
      const type = getAudioDeviceType(device.type);
      if (!type) {
        continue;
      }
      tempDevices.push({
        type: type,
        originDevice: device,
      });
    }

    // 按优先级排序
    tempDevices.sort((a, b) => {
      const priorityA = devicePriority[a.type] || 4;
      const priorityB = devicePriority[b.type] || 4;
      return priorityA - priorityB;
    });

    setAudioDevices(tempDevices);
    return tempDevices;
  };

  const registerTwilio = useMemoizedFn(async () => {
    callCenterOnStatusChanged(CallStatusEnum.Loading);
    // 已注册过，则更新 token
    if (voiceRef.current) {
      const registerToken = await getToken();
      tokenRef.current = registerToken;
      await voiceRef.current.register(registerToken);
      callCenterOnStatusChanged(CallStatusEnum.Online);
      return;
    }
    // 未注册过，则注册
    const registerVoice = new Voice();
    try {
      if (Platform.OS === 'ios') {
        await registerVoice.initializePushRegistry();
      }
    } catch {
      toast('fail', CallCenterErrorCodeMessage[CallCenterErrorCode.PushRegistryError]);
      callCenterOnStatusChanged(
        CallStatusEnum.Error,
        CallCenterErrorCodeMessage[CallCenterErrorCode.PushRegistryError],
      );
      return;
    }
    const registerToken = await getToken();
    if (registerToken === '') {
      callCenterOnStatusChanged(CallStatusEnum.Error, CallCenterErrorCodeMessage[CallCenterErrorCode.PermissionDenied]);
      return;
    }
    tokenRef.current = registerToken;

    try {
      // Allow incoming calls
      // await registerVoice.setCallKitConfiguration({
      //   callKitIconTemplateImageData: '',
      //   callKitIncludesCallsInRecents: false,
      //   callKitMaximumCallGroups: 1,
      //   callKitMaximumCallsPerCallGroup: 1,
      //   callKitRingtoneSound: '',
      //   callKitSupportedHandleTypes: [],
      // });
      await registerVoice.register(registerToken);

      twilioAutoTokenUpdate();

      registerVoice.addListener(Voice.Event.AudioDevicesUpdated, (gotDevices) => {
        const tempDevices = updateAudioDevices(gotDevices);
        if (!linkedRef.current) {
          return;
        }
        if (Platform.OS !== 'ios' || AppState.currentState === 'active') {
          if (!tempDevices.find((device) => device.type === currentAudioDeviceRef.current)) {
            bindDevice(AudioDevice.Type.Earpiece, tempDevices);
            setAudioDeviceType(AudioDevice.Type.Earpiece);
          }
        }
      });

      // Handle incoming calls
      registerVoice.on(Voice.Event.CallInvite, async (incomeCallInvite: CallInvite) => {
        logger.info(`[Call center RN Info] [call invite]`, {
          params: {
            token: tokenRef.current,
            inviteMsg: incomeCallInvite.getCustomParameters(),
            willAutoReject: linkedRef.current || callInviteRef.current,
          },
        });
        if (linkedRef.current || isBusy()) {
          missedCallsRef.current += 1;
          incomeCallInvite.reject();
          return;
        }

        await handleIncomingCall(incomeCallInvite);
      });
      registerVoice.on(Voice.Event.Error, (error) => {
        callCenterOnStatusChanged(CallStatusEnum.Error, error.message);
        logger.error(`[Call center RN error] [voice] ${error.message}`, { params: { token: tokenRef.current } });
      });
      registerVoice.on(Voice.Event.Registered, () => {
        logger.info(`[Call center RN info] [voice] registered`);
      });
      registerVoice.on(Voice.Event.Unregistered, () => {
        logger.info(`[Call center RN info] [voice] unregistered`);
      });

      // 获取音频设备
      registerVoice.getAudioDevices().then((devices) => {
        const tempDevices = updateAudioDevices(devices.audioDevices);

        if (tempDevices.length > 0) {
          if (Platform.OS !== 'ios' || AppState.currentState === 'active') {
            if (
              devices.selectedDevice &&
              devices.selectedDevice.type &&
              devices.audioDevices.find((device) => device.uuid === devices.selectedDevice?.uuid)
            ) {
              if (isBluetoothDevice(devices.selectedDevice.type)) {
                setAudioDeviceType(AudioDevice.Type.Bluetooth);
                bindDevice(AudioDevice.Type.Bluetooth, tempDevices);
              } else {
                let currentAudioDeviceType = getAudioDeviceType(devices.selectedDevice.type);
                if (!currentAudioDeviceType) {
                  currentAudioDeviceType = AudioDevice.Type.Earpiece;
                }
                setAudioDeviceType(currentAudioDeviceType);
                bindDevice(currentAudioDeviceType, tempDevices);
              }
            } else if (tempDevices.find((device) => device.type === AudioDevice.Type.Bluetooth)) {
              setAudioDeviceType(AudioDevice.Type.Bluetooth);
              bindDevice(AudioDevice.Type.Bluetooth, tempDevices);
            } else {
              setAudioDeviceType(AudioDevice.Type.Earpiece);
              bindDevice(AudioDevice.Type.Earpiece, tempDevices);
            }
          }
        }
      });
      voiceRef.current = registerVoice;
      callCenterOnStatusChanged(CallStatusEnum.Online);

      // 初始化同步：检查是否有正在进行的通话或待处理的来电
      try {
        // 1. 检查是否有已连接的通话
        const calls = await registerVoice.getCalls();
        if (calls && calls.size > 0) {
          const callsArray = Array.from(calls.values());
          for (const call of callsArray) {
            const state = call.getState();
            if (state === Call.State.Connected) {
              logger.info(`[Call center RN info] [init sync] Found connected call`);
              await handleConnectedCall(call);
              break; // 只处理第一个已连接的通话
            }
          }
        } else {
          // 2. 如果没有已连接的通话，检查是否有待处理的来电
          const callInvites = await registerVoice.getCallInvites();
          if (callInvites && callInvites.size > 0) {
            const callInvitesArray = Array.from(callInvites.values());
            // 只处理第一个待处理的来电
            const firstCallInvite = callInvitesArray[0];

            logger.info(`[Call center RN info] [init sync] Found pending call invite`);

            // 使用与正常来电处理相同的逻辑
            if (linkedRef.current || isBusy()) {
              missedCallsRef.current += 1;
              firstCallInvite.reject();
              return;
            }

            await handleIncomingCall(firstCallInvite);
          }
        }
      } catch (error) {
        logger.error(`[Call center RN error] [init sync error] ${error}`, {
          params: { token: tokenRef.current },
        });
        // 初始化同步失败不应该影响正常的注册流程
      }
    } catch (e) {
      callCenterOnStatusChanged(CallStatusEnum.Error, CallCenterErrorCodeMessage[CallCenterErrorCode.InternalError]);
      toast('fail', CallCenterErrorCodeMessage[CallCenterErrorCode.InternalError]);
      logger.error(`[Call center RN error] [register] ${e}`, { error: e, params: { token: tokenRef.current } });
    }
  });

  // 提取公共的CallInvite事件监听器设置逻辑
  const setupCallInviteListeners = useMemoizedFn((callInvite: CallInvite) => {
    callInvite.on(CallInvite.Event.Accepted, (call) => {
      if (callInviteRef.current) {
        callInviteRef.current.removeAllListeners();
      }
      callInviteRef.current = null;
      setMessageCode(CallCenterMessageType.CallConnecting);
      setCall(call);
    });

    callInvite.addListener(CallInvite.Event.Rejected, () => {
      if (callInviteRef.current) {
        callInviteRef.current.removeAllListeners();
      }
      callInviteRef.current = null;
      setMessageCode(CallCenterMessageType.CallRejected);
      setDurationMessage('');
      setShow(false);
    });

    callInvite.addListener(CallInvite.Event.Cancelled, () => {
      if (callInviteRef.current) {
        callInviteRef.current.removeAllListeners();
      }
      callInviteRef.current = null;
      setMessageCode(CallCenterMessageType.CallCancelled);
      setDurationMessage('');
      setShow(false);
    });
  });

  // 提取公共的来电处理逻辑
  const handleIncomingCall = useMemoizedFn(async (callInvite: CallInvite) => {
    // ios 会使用铃声，不需要切换响铃设备
    if (Platform.OS !== 'ios') {
      if (currentAudioDeviceRef.current === AudioDevice.Type.Earpiece) {
        if (audioDevicesRef.current.find((device) => device.type === AudioDevice.Type.Bluetooth)) {
          bindDevice(AudioDevice.Type.Bluetooth);
        } else if (audioDevicesRef.current.find((device) => device.type === AudioDevice.Type.Speaker)) {
          bindDevice(AudioDevice.Type.Speaker);
        }
      }
    }

    callInviteRef.current = callInvite;
    setDirection(CallCenterDirection.Incoming);
    setMessageCode(CallCenterMessageType.IncomingCall);
    setDurationMessage('');

    const customParam = callInvite.getCustomParameters();
    const data = await getCustomerInfo({
      clientId: customParam?.clientId,
      customerId: customParam?.customerId,
    });
    if (Platform.OS === 'ios') {
      callInvite.updateCallerHandle(data.name);
    }

    setupCallInviteListeners(callInvite);

    setWindowsStatus(WindowsStatus.HeaderBar);
    setShow(true);
  });

  // 提取公共的已连接通话处理逻辑
  const handleConnectedCall = useMemoizedFn(async (call: Call) => {
    setDirection(CallCenterDirection.Incoming);
    setDurationMessage('');

    const customParam = call.getCustomParameters();
    if (customParam) {
      await getCustomerInfo({
        clientId: customParam?.clientId,
        customerId: customParam?.customerId,
      });
    }

    setWindowsStatus(WindowsStatus.HeaderBar);
    setShow(true);
    setLinked(true);
    setMessageCode(CallCenterMessageType.None);

    const timestamp = call.getInitialConnectedTimestamp();
    if (timestamp) {
      currentConnectTime.current = new Date(timestamp);
    } else {
      currentConnectTime.current = new Date();
    }

    timer.current = setInterval(() => {
      const subMessage = getTimeDifference(new Date(), currentConnectTime.current!);
      setDurationMessage(subMessage);
    }, 1000);

    currentCall.current = call;
    setCall(call);
  });

  const destroy = useMemoizedFn(async () => {
    if (voiceRef.current) {
      if (tokenRef.current) {
        try {
          await voiceRef.current.unregister(tokenRef.current);
        } catch (e) {
          logger.error(`[Call center RN error] [destroy] failed to unregister old token`, { error: e });
        }
      }
      voiceRef.current.removeAllListeners();
      voiceRef.current = null;
    }
    if (currentCall.current) {
      currentCall.current.disconnect();
      currentCall.current.removeAllListeners();
      currentCall.current = null;
    }
    if (callInviteRef.current) {
      callInviteRef.current.removeAllListeners();
      callInviteRef.current = null;
    }
    if (timer.current) {
      clearInterval(timer.current);
    }
    if (tokenUpdate.current) {
      clearInterval(tokenUpdate.current);
    }
    callCenterOnStatusChanged(CallStatusEnum.Offline);
    setLinked(false);
    setShow(false);
    setInfo(null);
  });

  const setCall = useMemoizedFn(async (call: Call) => {
    call.addListener(Call.Event.Ringing, () => {
      setMessageCode(CallCenterMessageType.CallConnecting);
      setDurationMessage('');
    });
    call.addListener(Call.Event.Connected, () => {
      if (Platform.OS !== 'ios' || AppState.currentState === 'active') {
        if (currentAudioDeviceRef.current === AudioDevice.Type.Speaker) {
          if (audioDevicesRef.current.find((device) => device.type === AudioDevice.Type.Bluetooth)) {
            bindDevice(AudioDevice.Type.Bluetooth);
          } else if (audioDevicesRef.current.find((device) => device.type === AudioDevice.Type.Earpiece)) {
            bindDevice(AudioDevice.Type.Earpiece);
          }
        }
      }
      setLinked(true);
      currentConnectTime.current = new Date();
      setMessageCode(CallCenterMessageType.CallConnected);
      messageTimer.current = setTimeout(() => {
        setMessageCode(CallCenterMessageType.None);
      }, 2000);
      timer.current = setInterval(() => {
        const subMessage = getTimeDifference(new Date(), currentConnectTime.current!);
        setDurationMessage(subMessage);
      }, 1000);
    });

    call.addListener(Call.Event.Disconnected, () => {
      if (timer.current) {
        clearInterval(timer.current);
      }
      setMessageCode(CallCenterMessageType.CallEnded);
      setDurationMessage('');
      callEnd();
    });
    call.addListener(Call.Event.ConnectFailure, (error) => {
      if (error.code === 31208) {
        toast('fail', CallCenterErrorCodeMessage[CallCenterErrorCode.MicrophonePermissionDenied]);
        callCenterOnStatusChanged(
          CallStatusEnum.Error,
          CallCenterErrorCodeMessage[CallCenterErrorCode.MicrophonePermissionDenied],
        );
      }
      logger.error(`[Call center RN error] [connect error] ${error.message}`, {
        error,
        params: { token: tokenRef.current },
      });
      if (timer.current) {
        clearInterval(timer.current);
      }
      setMessageCode(CallCenterMessageType.CallError);
      setDurationMessage('');
      callEnd();
    });
    call.on(
      Call.Event.QualityWarningsChanged,
      (currentQualityWarnings: Call.QualityWarning[], _previousQualityWarnings: Call.QualityWarning[]) => {
        logQualityWarningWithThrottle(currentQualityWarnings);
        //   TODO Channon 通话质量警告
      },
    );
    currentCall.current = call;
  });

  const missedCallsAlert = useMemoizedFn(async () => {
    const navigationText = `Check now`;
    const cancelText = `Check later`;
    const confirmText = await confirmAsync(
      `You have ${missedCallsRef.current} missed calls during the call. Would you like to check them?`,
      [
        { text: cancelText, style: 'cancel' },
        { text: navigationText, style: 'default' },
      ],
      'Missed calls',
    );
    missedCallsRef.current = 0;
    if (confirmText === navigationText) {
      navigation.dispatch(PATH_CALLING_ACTIVITY_LIST.navigate({}));
    }
  });

  const callEnd = useMemoizedFn(() => {
    setTimeout(() => {
      setShow(false);
      setLinked(false);
      setInfo(null);
      if (currentCall.current) {
        currentCall.current.removeAllListeners();
        currentCall.current = null;
      }
      if (callInviteRef.current) {
        callInviteRef.current.removeAllListeners();
        callInviteRef.current = null;
      }
      lastLoggedWarningRef.current = {
        warning: undefined,
        timestamp: 0,
      };
      if (missedCallsRef.current > 0) {
        missedCallsAlert();
      }
    }, 1600);
  });

  const acceptCall = useMemoizedFn(async () => {
    if (callInviteRef.current) {
      callInviteRef.current.accept();
    }
  });

  const onMuteChange = useMemoizedFn(async () => {
    if (currentCall.current) {
      try {
        if (messageTimer.current) {
          clearTimeout(messageTimer.current);
        }
        const result = await currentCall.current.mute(!currentMute);
        setCurrentMute(result);
        setMessageCode(result ? CallCenterMessageType.CallMute : CallCenterMessageType.CallUnmute);
        messageTimer.current = setTimeout(() => {
          setMessageCode(CallCenterMessageType.None);
        }, 2000);
      } catch (e) {
        logger.error(`[Call center RN error] [mute error] ${e}`, { params: { token: tokenRef.current } });
      }
    }
  });

  // ios后台切换设备会失败，所以需要判断是否在后台在能调用该function
  const bindDevice = useMemoizedFn(async (type: AudioDevice.Type, newDevices?: MoeGoDevice[]) => {
    const useAudioDevices = newDevices || audioDevicesRef.current;
    if (useAudioDevices.length === 0) {
      return;
    }
    const device = useAudioDevices.find((d) => d.type === type);
    if (typeof device === 'undefined') {
      return;
    }
    try {
      await device.originDevice.select();
      setAudioDeviceType(type);

      const newSelectedUUID = device.originDevice.uuid;
      if (newSelectedUUID !== lastLoggedSelectedDeviceUUID) {
        logger.info(`[Call center RN Info] [selected audio device changed]`, {
          id: 'twilio_voice_selected_audio_device_changed',
          result: {
            selectedDevice: device.originDevice,
          },
        });
        lastLoggedSelectedDeviceUUID = newSelectedUUID;
      }
    } catch (e) {
      logger.error(`[Call center RN error] [bind device error] ${e}`, {
        params: {
          token: tokenRef.current,
          type,
          device: useAudioDevices,
          storedDevices: audioDevicesRef.current,
        },
      });
    }
  });

  const changeAudioDevice = useMemoizedFn(async (type: AudioDevice.Type) => {
    if (messageTimer.current) {
      clearTimeout(messageTimer.current);
    }

    try {
      switch (type) {
        case AudioDevice.Type.Speaker:
          setMessageCode(CallCenterMessageType.CallSpeaker);
          break;
        case AudioDevice.Type.Earpiece:
          setMessageCode(CallCenterMessageType.CallEarpiece);
          break;
        case AudioDevice.Type.Bluetooth:
          setMessageCode(CallCenterMessageType.CallBluetooth);
          break;
      }
      await bindDevice(type);
      messageTimer.current = setTimeout(() => {
        setMessageCode(CallCenterMessageType.None);
      }, 2000);
    } catch (error) {
      logger.error(`[Call center RN error] [toggle speakerphone error] ${error}`, {
        params: { token: tokenRef.current },
      });
    }
  });

  const hangUp = useMemoizedFn(async () => {
    try {
      if (callInviteRef.current) {
        callInviteRef.current.reject();
      }
      if (currentCall.current) {
        await currentCall.current.disconnect();
      } else {
        setShow(false);
      }
    } catch (error) {
      logger.error(`[Call center RN error] [hang up error] ${error}`, { params: { patoken: tokenRef.current } });
      setShow(false);
    }
  });

  const isBusy = useMemoizedFn(() => {
    // 判断是否存在正在未处理的来电
    if (callInviteRef.current) {
      if (callInviteRef.current?.getState() === CallInvite.State.Pending) {
        return true;
      } else {
        callInviteRef.current.removeAllListeners();
        callInviteRef.current = null;
      }
    }
    // 判断是否存在正在进行的通话
    if (currentCall.current) {
      if (currentCall.current.getState() !== Call.State.Disconnected) {
        return true;
      } else {
        currentCall.current.removeAllListeners();
        currentCall.current = null;
        setLinked(false);
        return false;
      }
    }
    setLinked(false);
    return false;
  });

  const callAction = useMemoizedFn(async (param: CallCenterCallParams) => {
    logger.info(`[Call center RN info] [call action]`, {
      params: {
        param,
        voipAvailable,
        engagementCenterAvailable,
        token: tokenRef.current,
      },
    });
    if (!voipAvailable || !engagementCenterAvailable || param.immediate) {
      if (!param?.phoneNumber) {
        toast('fail', CallCenterErrorCodeMessage[CallCenterErrorCode.InvalidPhoneNumber]);
        return;
      }
      const url = `tel:${param?.phoneNumber}`;
      Linking.canOpenURL(url)
        .then((supported) => {
          if (supported) {
            Linking.openURL(url);
          } else {
            toast('fail', CallCenterErrorCodeMessage[CallCenterErrorCode.InvalidPhoneNumber]);
          }
        })
        .catch(() => {
          toast('fail', CallCenterErrorCodeMessage[CallCenterErrorCode.InvalidPhoneNumber]);
        });
      return;
    }

    const hasMicrophonePermissions = await grantMicrophonePermissions();
    if (!hasMicrophonePermissions) {
      toast('fail', CallCenterErrorCodeMessage[CallCenterErrorCode.MicrophonePermissionDenied]);
      callCenterOnStatusChanged(
        CallStatusEnum.Error,
        CallCenterErrorCodeMessage[CallCenterErrorCode.MicrophonePermissionDenied],
      );
      return;
    }

    if (!voiceRef.current) {
      await registerTwilio();
    }

    if (!tokenRef.current || tokenRef.current === '') {
      toast('fail', CallCenterErrorCodeMessage[CallCenterErrorCode.PermissionDenied]);
      callCenterOnStatusChanged(CallStatusEnum.Error, CallCenterErrorCodeMessage[CallCenterErrorCode.PermissionDenied]);
      logger.error(`[Call center RN error] [token error] token empty`);
      return;
    }

    if (!voiceRef.current) {
      toast('fail', CallCenterErrorCodeMessage[CallCenterErrorCode.PermissionDenied]);
      callCenterOnStatusChanged(CallStatusEnum.Error, CallCenterErrorCodeMessage[CallCenterErrorCode.PermissionDenied]);
      return;
    }

    if (isBusy()) {
      toast('fail', CallCenterErrorCodeMessage[CallCenterErrorCode.IsBusy]);
      return;
    }

    setDirection(CallCenterDirection.Outgoing);
    setMessageCode(CallCenterMessageType.OutingCall);
    setDurationMessage('');
    const params = {
      companyId: param.companyId + '',
      businessId: param.businessId + '',
      staffId: param.staffId + '',
    };
    if (param.customerId) {
      params['customerId'] = param.customerId + '';
    }
    if (param.clientId) {
      params['clientId'] = param.clientId + '';
    }
    const customerInfo = await getCustomerInfo({
      clientId: param.clientId ? param.clientId + '' : undefined,
      customerId: param.customerId ? param.customerId + '' : undefined,
    });
    setWindowsStatus(WindowsStatus.FullScreen);
    setShow(true);
    try {
      const call: Call = await voiceRef.current.connect(tokenRef.current, {
        params,
        ...(customerInfo?.name ? { contactHandle: customerInfo.name } : {}),
      });
      setCall(call);
    } catch (e) {
      callCenterOnStatusChanged(CallStatusEnum.Error);
      logger.error(`[Call center RN error] [call error] ${e}`, { params: { token: tokenRef.current } });
    }
  });

  const getStatus = useMemoizedFn(() => {
    return status;
  });

  useImperativeHandle(ref, () => ({
    call: callAction,
    refresh: refreshRegister,
    destroy: destroy,
    getStatus: getStatus,
    onStatusChanged: onStatusChanged,
    removeStatusChanged: removeStatusChanged,
  }));

  return (
    <Modal isVisible={show} hasBackdrop={false} animationIn="slideInDown" coverScreen={false} animationOut="slideOutUp">
      <Condition if={windowsStatus === WindowsStatus.HeaderBar}>
        <CallCenterHeaderBar
          info={info}
          messageCode={messageCode}
          durationMessage={durationMessage}
          direction={direction}
          linked={linked}
          acceptCall={() => {
            acceptCall();
            setWindowsStatus(WindowsStatus.FullScreen);
          }}
          hangUp={hangUp}
          onChangeWindowsStatusChange={setWindowsStatus}
        />
      </Condition>
      <Condition if={windowsStatus === WindowsStatus.FullScreen}>
        <CallCenterFullScreen
          info={info}
          messageCode={messageCode}
          durationMessage={durationMessage}
          direction={direction}
          linked={linked}
          acceptCall={acceptCall}
          hangUp={hangUp}
          isMute={currentMute}
          onMuteChange={onMuteChange}
          audioDeviceType={audioDeviceType}
          audioDevices={audioDevices}
          onAudioDeviceChange={changeAudioDevice}
          onChangeWindowsStatusChange={setWindowsStatus}
        />
      </Condition>
      <Condition if={windowsStatus === WindowsStatus.FloatView}>
        <CallCenterFloatView
          messageCode={messageCode}
          durationMessage={durationMessage}
          linked={linked}
          onChangeWindowsStatusChange={setWindowsStatus}
        />
      </Condition>
    </Modal>
  );
});

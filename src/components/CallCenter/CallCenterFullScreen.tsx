import { AudioDevice } from '@twilio/voice-react-native-sdk';
import { useBoolean, useMemoizedFn } from 'ahooks';
import { BlurView } from 'expo-blur';
import React, { useEffect, useMemo, useState } from 'react';
import { ActivityIndicator, Dimensions, Platform, TouchableOpacity, View } from 'react-native';
import IconIcBluetoothDarkSvg from '../../../assets/images/ic-bluetooth-dark.svg';
import IconIcBluetoothWhiteSvg from '../../../assets/images/ic-bluetooth-white.svg';
import IconIcCheckboxCheckSvg from '../../../assets/images/ic-checkbox-check.svg';
import IconIcEarpieceWhiteSvg from '../../../assets/images/ic-earpiece-white.svg';
import IconIcRightArrowColorfulSvg from '../../../assets/images/ic-right-arrow-colorful.svg';
import IconIcSpeakerDarkSvg from '../../../assets/images/ic-speaker-dark.svg';
import IconIcSpeakerWhiteSvg from '../../../assets/images/ic-speaker-white.svg';
import IconMajorCallDisconnectedOutlinedSvg from '../../../assets/images/major-call-disconnected-outlined.svg';
import IconMajorCallOutlinedSvg from '../../../assets/images/major-call-outlined.svg';
import IconMajorCollapseOutlinedSvg from '../../../assets/images/major-collapse-outlined.svg';
import IconMajorMicMuteOutlinedSvg from '../../../assets/images/major-mic-mute-outlined.svg';
import IconMajorMicOutlinedSvg from '../../../assets/images/major-mic-outlined.svg';
import IconMajorMicSpeakerOutlinedSvg from '../../../assets/images/major-mic-speaker-outlined.svg';
import IconMinorLocationWhiteOutlinedSvg from '../../../assets/images/minor-location-white-outlined.svg';
import IconMinorPawsOutlinedSvg from '../../../assets/images/minor-paws-outlined.svg';
import { PATH_CUSTOMER_DETAIL } from '../../modules/Customer/Customer.api';
import { type ClientAndInfoOriginModel } from '../../modules/Message/store/calling.actions';
import { useStackNavigation } from '../../utils/navigation';
import { COL_WHITE } from '../../utils/style/consts';
import { Condition } from '../Condition';
import { MoeText } from '../MoeText';
import { SafeBottom, SafeTop } from '../SafeView';
import { UserAvatar } from '../images/UserAvatar';
import { type MoeGoDevice } from './CallCenter';
import { CallCenterDirection, CallCenterMessageType, WindowsStatus, getCallCenterMessage } from './CallCenter.constant';
import { styles } from './CallCenterFullScreen.style';

export interface CallCenterFullScreenProps {
  info: ClientAndInfoOriginModel | null;
  messageCode: CallCenterMessageType;
  durationMessage: string;
  direction: CallCenterDirection;
  linked: boolean;
  acceptCall: () => void;
  hangUp: () => void;
  isMute: boolean;
  onMuteChange: () => void;
  audioDeviceType: AudioDevice.Type;
  audioDevices?: MoeGoDevice[];
  onAudioDeviceChange: (type: AudioDevice.Type) => void;
  onChangeWindowsStatusChange: (status: WindowsStatus) => void;
}

export const CallCenterFullScreen = (props: CallCenterFullScreenProps) => {
  const {
    info,
    messageCode,
    durationMessage,
    direction,
    linked,
    acceptCall,
    hangUp,
    isMute,
    onMuteChange,
    onChangeWindowsStatusChange,
    audioDeviceType,
    onAudioDeviceChange,
    audioDevices,
  } = props;
  const [width, setWidth] = useState(Dimensions.get('window').width);
  const [height, setHeight] = useState(Dimensions.get('window').height);
  const navigation = useStackNavigation();
  const [multiDeviceSelectVisible, multiDeviceSelectVisibleAction] = useBoolean(false);
  const hasBluetooth = useMemo(() => {
    return audioDevices?.some((device) => device.type === AudioDevice.Type.Bluetooth);
  }, [audioDevices]);

  const viewPetDetail = useMemoizedFn(() => {
    multiDeviceSelectVisibleAction.setFalse();
    if (!info?.customerId) return;
    onChangeWindowsStatusChange?.(WindowsStatus.FloatView);
    navigation.dispatch(PATH_CUSTOMER_DETAIL.navigate({ customerId: info?.customerId }));
  });

  const onAudioDeviceClick = useMemoizedFn(() => {
    if (hasBluetooth) {
      multiDeviceSelectVisibleAction.toggle();
      return;
    }
    if (audioDeviceType === AudioDevice.Type.Speaker) {
      onAudioDeviceChange(AudioDevice.Type.Earpiece);
    } else {
      onAudioDeviceChange(AudioDevice.Type.Speaker);
    }
  });

  const endButtonText = useMemo(() => {
    if (linked || messageCode === CallCenterMessageType.CallEnded) {
      return 'End';
    }
    if (direction === CallCenterDirection.Outgoing) {
      return 'Cancel';
    }
    return 'Dismiss';
  }, [linked, messageCode, direction]);

  useEffect(() => {
    const updateWidth = () => {
      setWidth(Dimensions.get('window').width);
      setHeight(Dimensions.get('window').height);
    };
    Dimensions.addEventListener('change', updateWidth);
  }, []);
  // iPhone SE (1st/2nd): 667, iPhone 8: 667, iPhone 12 mini: 812
  const lowResolution = Dimensions.get('window').height < 812;
  return (
    <View
      style={[
        styles.container,
        {
          width: Platform.OS === 'android' ? width + 6 : width + 4,
          height: Platform.OS === 'android' ? height + 6 : height + 4,
        },
      ]}
    >
      <BlurView
        style={[styles.blur, Platform.OS === 'android' ? { backgroundColor: 'rgba(0, 0, 0, 0.9)' } : {}]}
        intensity={25}
      >
        <SafeTop />
        <TouchableOpacity
          onPress={() => {
            multiDeviceSelectVisibleAction.setFalse();
            onChangeWindowsStatusChange?.(WindowsStatus.FloatView);
          }}
        >
          <View style={styles.shrinkButton}>
            <IconMajorCollapseOutlinedSvg width={24} height={24} color="#fff" />
          </View>
        </TouchableOpacity>
        <Condition if={linked}>
          <View style={styles.durationContainer}>
            {info?.isRecording ? (
              <View style={styles.recordingContainer}>
                <MoeText style={styles.durationText}>REC {durationMessage}</MoeText>
                <View style={styles.recordingDot} />
              </View>
            ) : (
              <MoeText style={styles.durationText}>{durationMessage}</MoeText>
            )}
          </View>
        </Condition>

        <View style={styles.topContainer}>
          <UserAvatar
            user={{
              avatarPath: info?.avatar,
              firstName: info?.name,
              lastName: '',
            }}
            size={100}
            style={styles.avatar}
          />
          <View style={styles.infoContainer}>
            <MoeText numberOfLines={1} ellipsizeMode="tail" style={styles.name}>
              {info?.name}
            </MoeText>
          </View>
          <View style={[styles.callDetailContainer, { width: width - 64 }]}>
            <Condition if={!!info?.businessUuid}>
              <View style={styles.callDetailItem}>
                <IconMinorLocationWhiteOutlinedSvg width={20} height={20} color="#fff" />
                <MoeText style={styles.callDetailText}>{info?.businessName}</MoeText>
                <MoeText style={styles.callDetailGrayText}>({info?.companyName})</MoeText>
              </View>
            </Condition>
            <View style={styles.callDetailItem}>
              <IconMinorPawsOutlinedSvg width={20} height={20} color="#fff" />
              {info?.petInfos?.length && info?.petInfos?.length > 0 ? (
                <>
                  {info?.petInfos?.length > 2 ? (
                    <MoeText style={styles.callDetailText}>{info?.petInfos?.length + ' pets'}</MoeText>
                  ) : (
                    <>
                      <MoeText style={styles.callDetailText}>{info.petInfos[0].petName}</MoeText>
                      <MoeText style={styles.callDetailGrayText}>({info.petInfos[0].petType})</MoeText>
                    </>
                  )}
                </>
              ) : (
                <MoeText style={styles.callDetailText}>No pet info</MoeText>
              )}
            </View>
            <Condition if={info?.petInfos?.length === 2}>
              <View style={[styles.callDetailItem, { marginTop: -12 }]}>
                <MoeText style={[styles.callDetailText, { marginLeft: 28 }]}>{info?.petInfos[1]?.petName}</MoeText>
                <MoeText style={styles.callDetailGrayText}>({info?.petInfos[1]?.petType})</MoeText>
              </View>
            </Condition>

            <Condition if={info?.customerId}>
              <TouchableOpacity onPress={viewPetDetail}>
                <View style={styles.viewDetailButton}>
                  <MoeText style={styles.viewDetailButtonText}>View detail</MoeText>
                  <IconIcRightArrowColorfulSvg width={12} height={12} color="#fff" />
                </View>
              </TouchableOpacity>
            </Condition>
          </View>
          <View style={[styles.messageContainer, { marginTop: lowResolution ? 48 : 69 }]}>
            <MoeText style={styles.message}>{getCallCenterMessage(messageCode)}</MoeText>
          </View>
        </View>

        <View style={[styles.buttonContainer, { marginBottom: lowResolution ? 48 : 64 }]}>
          <Condition if={hasBluetooth && multiDeviceSelectVisible}>
            <View style={styles.deviceSelectContainer}>
              {audioDevices?.map((device, index) => (
                <TouchableOpacity
                  key={device.originDevice.uuid}
                  onPress={() => {
                    multiDeviceSelectVisibleAction.setFalse();
                    onAudioDeviceChange(device.type);
                  }}
                >
                  <View style={[styles.deviceSelectContainerItem, { borderTopWidth: index === 0 ? 0 : 1 }]}>
                    <View style={styles.deviceSelectContainerItemCheck}>
                      {device.type === audioDeviceType && (
                        <IconIcCheckboxCheckSvg width={16} height={16} color="#fff" />
                      )}
                    </View>
                    <MoeText numberOfLines={1} style={styles.deviceSelectContainerItemText}>
                      {device.originDevice.name}
                    </MoeText>
                    <View style={styles.deviceSelectContainerItemIcon}>{getAudioDeviceIcon(device.type)}</View>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </Condition>
          <Condition if={linked && messageCode !== CallCenterMessageType.CallEnded}>
            <View style={styles.actionButtonContainer}>
              <TouchableOpacity
                onPress={() => {
                  multiDeviceSelectVisibleAction.setFalse();
                  onMuteChange();
                }}
              >
                <View
                  style={[styles.grayButton, { backgroundColor: isMute ? 'rgba(255, 255, 255, 0.15)' : COL_WHITE }]}
                >
                  {isMute ? (
                    <IconMajorMicMuteOutlinedSvg width={40} height={40} color="#fff" />
                  ) : (
                    <IconMajorMicOutlinedSvg width={40} height={40} color="#333333" />
                  )}
                </View>
              </TouchableOpacity>
              <MoeText style={styles.actionButtonText}>{'Mic'}</MoeText>
            </View>
          </Condition>
          <View style={styles.actionButtonContainer}>
            <TouchableOpacity
              onPress={() => {
                multiDeviceSelectVisibleAction.setFalse();
                hangUp();
              }}
            >
              <View style={styles.hangUpButton}>
                <IconMajorCallDisconnectedOutlinedSvg width={40} height={40} color="#fff" />
              </View>
            </TouchableOpacity>
            <MoeText style={styles.actionButtonText}>{endButtonText}</MoeText>
          </View>
          <Condition if={linked && messageCode !== CallCenterMessageType.CallEnded}>
            <View style={styles.actionButtonContainer}>
              <TouchableOpacity onPress={onAudioDeviceClick}>
                <View
                  style={[
                    styles.grayButton,
                    {
                      backgroundColor:
                        audioDeviceType === AudioDevice.Type.Earpiece ? 'rgba(255, 255, 255, 0.15)' : COL_WHITE,
                      opacity: hasBluetooth && multiDeviceSelectVisible ? 0.73 : 1,
                    },
                  ]}
                >
                  {getAudioDeviceButtonIcon(audioDeviceType)}
                </View>
              </TouchableOpacity>
              <MoeText numberOfLines={1} style={styles.actionButtonText}>
                {getAudioDeviceText(audioDeviceType, audioDevices || [])}
              </MoeText>
            </View>
          </Condition>
          <Condition
            if={
              direction === CallCenterDirection.Incoming && !linked && messageCode !== CallCenterMessageType.CallEnded
            }
          >
            <View style={styles.actionButtonContainer}>
              <TouchableOpacity
                disabled={messageCode === CallCenterMessageType.CallConnecting}
                onPress={() => {
                  multiDeviceSelectVisibleAction.setFalse();
                  acceptCall();
                }}
              >
                <View
                  style={[styles.callButton, messageCode === CallCenterMessageType.CallConnecting && { opacity: 0.6 }]}
                >
                  {messageCode === CallCenterMessageType.CallConnecting ? (
                    <ActivityIndicator color="#fff" size="small" />
                  ) : (
                    <IconMajorCallOutlinedSvg width={40} height={40} color="#fff" />
                  )}
                </View>
              </TouchableOpacity>
              <MoeText style={styles.actionButtonText}>
                {messageCode === CallCenterMessageType.CallConnecting ? 'Connecting...' : 'Accept'}
              </MoeText>
            </View>
          </Condition>
        </View>
        <SafeBottom />
      </BlurView>
    </View>
  );
};

const getAudioDeviceIcon = (type: AudioDevice.Type) => {
  if (type === AudioDevice.Type.Speaker) {
    return <IconIcSpeakerWhiteSvg width={24} height={24} color={'#fff'} />;
  }
  if (type === AudioDevice.Type.Earpiece) {
    return <IconIcEarpieceWhiteSvg width={24} height={24} color={'#fff'} />;
  }
  if (type === AudioDevice.Type.Bluetooth) {
    return <IconIcBluetoothWhiteSvg width={24} height={24} color={'#fff'} />;
  }
  return <></>;
};

const getAudioDeviceButtonIcon = (type: AudioDevice.Type) => {
  if (type === AudioDevice.Type.Speaker) {
    return <IconIcSpeakerDarkSvg width={40} height={40} color={'#fff'} />;
  }
  if (type === AudioDevice.Type.Earpiece) {
    return <IconMajorMicSpeakerOutlinedSvg width={40} height={40} color={'#333'} />;
  }
  return <IconIcBluetoothDarkSvg width={40} height={40} color={'#333'} />;
};

const getAudioDeviceText = (type: AudioDevice.Type, devices: MoeGoDevice[]) => {
  if (devices.length > 2) {
    return devices.find((device) => device.type === type)?.originDevice.name || 'Speaker';
  }
  return 'Speaker';
};

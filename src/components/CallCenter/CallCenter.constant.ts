import { AudioDevice } from '@twilio/voice-react-native-sdk';
import { COL_DANGER, COL_SUCCESS } from '../../utils/style/consts';

export const enum CallStatusEnum {
  Online = 'Online',
  Offline = 'Offline',
  Error = 'Error',
  Loading = 'Loading',
}

export enum CallCenterInfoCode {
  TokenUpdated = 'CCI-10001',
  tokenWillExpire = 'CCI-10002',
  CallIncoming = 'CCI-10003',
}

export const CallCenterInfoCodeMessage: Record<CallCenterInfoCode, string> = {
  [CallCenterInfoCode.TokenUpdated]: CallCenterInfoCode.TokenUpdated + ': Token updated',
  [CallCenterInfoCode.tokenWillExpire]: CallCenterInfoCode.tokenWillExpire + ': Token will expire soon',
  [CallCenterInfoCode.CallIncoming]: CallCenterInfoCode.CallIncoming + ': Incoming call',
};
// 定义设备类型优先级
export const devicePriority = {
  [AudioDevice.Type.Bluetooth]: 1,
  [AudioDevice.Type.Speaker]: 2,
  [AudioDevice.Type.Earpiece]: 3,
};

export enum CallCenterWarningCode {
  QualityWarning = 'CCW-10001',
}

export const CallCenterWarningCodeMessage: Record<CallCenterWarningCode, string> = {
  [CallCenterWarningCode.QualityWarning]: CallCenterWarningCode.QualityWarning + ': Poor call quality',
};

export enum CallCenterErrorCode {
  // 10001 - 19999: Call center error
  InternalError = 'CCE-10001',
  UpdateTokenWhenCalling = 'CCE-10002',
  MicrophonePermissionDenied = 'CCE-10003',
  PermissionDenied = 'CCE-10004',
  InvalidPhoneNumber = 'CCE-10005',
  NetworkInvalid = 'CCE-10006',
  PushRegistryError = 'CCE-10007',
  // 20001 - 29999: Device error

  DeviceNotRegistered = 'CCE-20001',
  // 30001 - 39999: call error
  CallExists = 'CCE-30001',
  CallMissed = 'CCE-30002',
  CalloutError = 'CCE-30003',
  IsBusy = 'CCE-30004',
  // 40001 - 49999: action error
  CanNotSendDTMF = 'CCE-40001',
}

export const CallCenterErrorCodeMessage: Record<CallCenterErrorCode, string> = {
  [CallCenterErrorCode.InternalError]:
    CallCenterErrorCode.InternalError + ': Recovering from internal error. Please wait a few minutes and try again.',
  [CallCenterErrorCode.CallMissed]:
    CallCenterErrorCode.CallMissed + ': Current call has ended, please refresh the page and dial again if needed.',
  [CallCenterErrorCode.CanNotSendDTMF]:
    CallCenterErrorCode.CanNotSendDTMF + ': Failed to send signal. Please try typing the number again.',
  [CallCenterErrorCode.MicrophonePermissionDenied]:
    CallCenterErrorCode.MicrophonePermissionDenied + ': Please enable microphone permissions in your device.',
  [CallCenterErrorCode.PermissionDenied]:
    CallCenterErrorCode.PermissionDenied + ': You have denied the permission to make calls.',
  [CallCenterErrorCode.InvalidPhoneNumber]:
    CallCenterErrorCode.InvalidPhoneNumber + ': Invalid phone number, please check and try again.',
  [CallCenterErrorCode.CallExists]:
    CallCenterErrorCode.CallExists + ': This call already exists. Please do not dial again.',
  [CallCenterErrorCode.DeviceNotRegistered]:
    CallCenterErrorCode.DeviceNotRegistered + ': Call center is not registered yet.',
  [CallCenterErrorCode.IsBusy]:
    CallCenterErrorCode.IsBusy +
    ': Your colleague is calling this client. Please wait till they finish before dial again.',
  [CallCenterErrorCode.UpdateTokenWhenCalling]:
    CallCenterErrorCode.UpdateTokenWhenCalling + ': Call in progress. Cannot update status.',
  [CallCenterErrorCode.CalloutError]: CallCenterErrorCode.CalloutError + ': Call out error',
  [CallCenterErrorCode.NetworkInvalid]: CallCenterErrorCode.NetworkInvalid + ': No network currently',
  [CallCenterErrorCode.PushRegistryError]:
    CallCenterErrorCode.PushRegistryError + ': Push registry error, please check your device settings.',
};

export enum WindowsStatus {
  FullScreen = 'FullScreen',
  HeaderBar = 'HeaderBar',
  FloatView = 'FloatView',
}

export enum CallCenterMessageType {
  None = 'none',
  IncomingCall = 'incomingCall',
  OutingCall = 'outingCall',
  CallEnded = 'callEnded',
  CallError = 'calloutError',
  CallRejected = 'callRejected',
  CallCancelled = 'callCancelled',
  CallConnected = 'callConnected',
  CallRinging = 'callRinging',
  CallConnecting = 'callConnecting',
  CallMute = 'callMute',
  CallUnmute = 'callUnmute',
  CallSpeaker = 'callSpeaker',
  CallEarpiece = 'callEarpiece',
  CallBluetooth = 'callBluetooth',
}

export enum CallCenterDirection {
  Incoming = 'incoming',
  Outgoing = 'outgoing',
}

export const CallCenterMessageTypeMessage: Record<CallCenterMessageType, string> = {
  [CallCenterMessageType.None]: '',
  [CallCenterMessageType.IncomingCall]: 'Waiting to answer',
  [CallCenterMessageType.OutingCall]: 'Waiting to answer',
  [CallCenterMessageType.CallEnded]: 'Call ended',
  [CallCenterMessageType.CallError]: 'Call error occurred',
  [CallCenterMessageType.CallRejected]: 'Call rejected',
  [CallCenterMessageType.CallCancelled]: 'Call cancelled',
  [CallCenterMessageType.CallConnected]: 'Connected',
  [CallCenterMessageType.CallRinging]: 'Ringing',
  [CallCenterMessageType.CallConnecting]: 'Connecting',
  [CallCenterMessageType.CallMute]: 'Mic muted',
  [CallCenterMessageType.CallUnmute]: 'Mic on',
  [CallCenterMessageType.CallSpeaker]: 'Speaker turned on',
  [CallCenterMessageType.CallEarpiece]: 'Speaker turned off',
  [CallCenterMessageType.CallBluetooth]: 'Bluetooth turned on',
};

export const getCallCenterMessage = (messageCode: CallCenterMessageType): string => {
  return CallCenterMessageTypeMessage[messageCode];
};

export const CallCenterTypeTinyMessage: Record<CallCenterMessageType, string> = {
  [CallCenterMessageType.None]: '',
  [CallCenterMessageType.CallEnded]: 'End',
  [CallCenterMessageType.CallConnected]: 'Connected',
  [CallCenterMessageType.CallRinging]: 'Ringing',
  [CallCenterMessageType.CallConnecting]: 'Ringing',
  [CallCenterMessageType.IncomingCall]: 'Ringing',
  [CallCenterMessageType.OutingCall]: 'Ringing',
  [CallCenterMessageType.CallError]: 'Error',
  [CallCenterMessageType.CallRejected]: 'Rejected',
  [CallCenterMessageType.CallCancelled]: 'Cancelled',
  [CallCenterMessageType.CallMute]: 'Connected',
  [CallCenterMessageType.CallUnmute]: 'Connected',
  [CallCenterMessageType.CallSpeaker]: 'Connected',
  [CallCenterMessageType.CallEarpiece]: 'Connected',
  [CallCenterMessageType.CallBluetooth]: 'Connected',
};

export const CallCenterTinyMessageColor: Record<CallCenterMessageType, typeof COL_DANGER | typeof COL_SUCCESS> = {
  [CallCenterMessageType.None]: COL_SUCCESS,
  [CallCenterMessageType.CallEnded]: COL_DANGER,
  [CallCenterMessageType.CallConnected]: COL_SUCCESS,
  [CallCenterMessageType.CallRinging]: COL_SUCCESS,
  [CallCenterMessageType.CallConnecting]: COL_SUCCESS,
  [CallCenterMessageType.IncomingCall]: COL_SUCCESS,
  [CallCenterMessageType.OutingCall]: COL_SUCCESS,
  [CallCenterMessageType.CallError]: COL_DANGER,
  [CallCenterMessageType.CallRejected]: COL_DANGER,
  [CallCenterMessageType.CallCancelled]: COL_DANGER,
  [CallCenterMessageType.CallMute]: COL_SUCCESS,
  [CallCenterMessageType.CallUnmute]: COL_SUCCESS,
  [CallCenterMessageType.CallSpeaker]: COL_SUCCESS,
  [CallCenterMessageType.CallEarpiece]: COL_SUCCESS,
  [CallCenterMessageType.CallBluetooth]: COL_SUCCESS,
};

# Moego React Native Logger

一个用于在 React Native 应用中记录日志并上报到 Datadog 的模块。

## 功能特性

- 支持 Android 和 iOS 平台
- 集成 Datadog 日志系统
- 支持自定义时间戳
- 支持多种日志级别（DEBUG、INFO、WARN、ERROR）
- 支持上下文和标签管理
- 支持错误信息记录
- **基于 MMKV 的日志缓存和离线上报**
- **低优先级后台线程批量上报**
- **保证日志时序性**
- **自动追踪应用状态**（foreground/background）

## 安装

```bash
npm install @moego/react-native-logger
```

## 使用方法

### 基本用法

```typescript
import { moegoLogger } from '@moego/react-native-logger';

// 记录信息日志
moegoLogger.info('用户登录成功', {
  tag: 'user',
  id: 'login_001',
  params: { userId: '12345' },
  result: { success: true }
});

// 记录错误日志
moegoLogger.error('网络请求失败', {
  tag: 'api',
  id: 'api_001',
  error: new Error('Network timeout'),
  context: { endpoint: '/api/users' }
});
```

### 使用自定义时间戳

```typescript
import { moegoLogger } from '@moego/react-native-logger';

// 记录带有自定义时间戳的日志
const customTimestamp = Date.now() - 60000; // 1分钟前的时间戳

moegoLogger.info('延迟的日志记录', {
  tag: 'delayed',
  id: 'delayed_001',
  timestamp: customTimestamp, // 毫秒时间戳
  params: { reason: 'offline_sync' }
});
```

### 设置全局上下文

```typescript
import { moegoLogger } from '@moego/react-native-logger';

// 为特定标签设置全局上下文
moegoLogger.addContext({ 
  userId: '12345',
  sessionId: 'session_001'
}, 'user');

// 所有带有 'user' 标签的日志都会自动包含这些上下文信息
moegoLogger.info('用户操作', {
  tag: 'user',
  params: { action: 'click_button' }
});
```

### 在原生代码中使用

#### Android (Kotlin)

```kotlin
import com.moego.logger.MGOLogEvent
import com.moego.logger.MGOLogger

// 创建日志事件
val event = MGOLogEvent.info(
    message = "Android 原生日志",
    tag = "native_android",
    eventId = "native_001",
    params = mapOf("platform" to "android"),
    context = mapOf("version" to "1.0.0")
)

// 记录日志（会自动缓存和上报）
MGOLogger.logEvent(event)

// 使用 Builder 模式
val eventWithTimestamp = MGOLogEvent.infoBuilder("带时间戳的日志", "event_002", "native_android")
    .params(mapOf("custom" to "data"))
    .timestamp(System.currentTimeMillis() - 30000) // 30秒前
    .build()

MGOLogger.logEvent(eventWithTimestamp)
```

#### iOS (Objective-C)

```objc
#import "MGOLogger.h"
#import "MGOLogEvent.h"

// 创建日志事件
MGOLogEvent *event = [MGOLogEvent infoWithMessage:@"iOS 原生日志"
                                         eventId:@"native_001"
                                          params:@{@"platform": @"ios"}
                                          result:nil
                                         context:@{@"version": @"1.0.0"}
                                             tag:@"native_ios"];

// 记录日志
[MGOLogger logEvent:event];
```

## 缓存和上报机制

### 工作原理

1. **初始化前缓存**：在 `bindWithDdLog` 之前，所有日志都会保存到 MMKV 中
2. **初始化后上报**：绑定 DdLogs 后，会从 MMKV 加载缓存的日志并开始上报
3. **立即上报**：初始化后的新日志会立即上报（避免内存队列丢失）
4. **批量处理**：使用低优先级线程批量上报缓存的日志，每批最多 50 条日志
5. **时序保证**：按时间戳和序列号排序，确保日志顺序正确

### 上报策略

系统采用**立即上报策略**，确保日志的可靠性和实时性：

- **有 ddLogs 时**：立即调用 API 上报，避免内存队列丢失
- **无 ddLogs 时**：保存到 MMKV 持久化存储
- **上报失败时**：自动回退到 MMKV 缓存

**优势**：
- 避免程序闪退导致的数据丢失
- 实时性好，日志立即上报
- 减少内存占用
- 简化代码逻辑

### 配置参数

```kotlin
// 在 MGOLogCache 中可以调整以下参数：
private const val MAX_CACHE_SIZE = 1000 // 最大缓存数量
private const val BATCH_SIZE = 50 // 批量上报大小
private const val REPORT_INTERVAL_MS = 5000L // 上报间隔（毫秒）
```

### 缓存清理

- 当缓存数量超过 `MAX_CACHE_SIZE` 时，会自动删除最旧的日志
- 应用重启后，缓存的日志会在初始化时自动上报
- 可以通过 `MGOLogCache.cleanup()` 手动清理所有缓存

## API 参考

### TypeScript 接口

```typescript
interface EventType {
  tag: string;           // 必需：日志标签
  id?: string;           // 可选：事件ID
  params?: object;       // 可选：参数
  result?: object;       // 可选：结果
  error?: unknown;       // 可选：错误信息
  context?: object;      // 可选：上下文（仅本次有效）
  timestamp?: number;    // 可选：时间戳（毫秒）
}
```

### 日志方法

- `moegoLogger.debug(message: string, event: EventType)`
- `moegoLogger.info(message: string, event: EventType)`
- `moegoLogger.warn(message: string, event: EventType)`
- `moegoLogger.error(message: string, event: EventType)`

### 上下文管理

- `moegoLogger.addContext(context: object, tag: string)`
- `moegoLogger.resetTagContext(tag: string)`

## 配置

确保在应用启动时正确配置了 Datadog SDK：

```typescript
import { DatadogProvider } from '@datadog/mobile-react-native';

// 在你的 App 组件中包装 DatadogProvider
<DatadogProvider configuration={config}>
  <YourApp />
</DatadogProvider>
```

## 注意事项

1. 时间戳必须是毫秒级的 Unix 时间戳
2. 如果不提供时间戳，将使用当前时间
3. 标签（tag）是必需的，用于分类和过滤日志
4. 上下文信息会自动合并全局上下文和本地上下文
5. **缓存功能需要 MMKV 依赖**：`implementation 'com.tencent:mmkv-static:1.3.4'`
6. **日志上报是异步的**，不会阻塞主线程
7. **离线日志会在网络恢复后自动上报**

## 应用状态追踪

### 自动 app_status 字段

系统会在每条日志的 `context` 中自动添加 `app_status` 字段，值为：
- `"foreground"`：应用在前台运行
- `"background"`：应用在后台运行

### 实现原理

#### Android
- 使用 **Jetpack ProcessLifecycleOwner** 监听应用级别的生命周期事件
- 通过 `Lifecycle.Event.ON_START` 和 `Lifecycle.Event.ON_STOP` 事件检测应用状态
- **更简洁的实现**：无需手动维护 Activity 计数器，系统自动处理
- **智能初始化**：通过 `ProcessLifecycleOwner.currentState` 检测应用启动时的初始状态
- **更高的可靠性**：利用 Android 官方 Jetpack 组件，避免手动实现的错误

#### iOS  
- 监听 `UIApplicationDidEnterBackgroundNotification` 和 `UIApplicationWillEnterForegroundNotification` 通知
- **智能初始化**：通过 `UIApplication.applicationState` 检测应用启动时的初始状态
- 自动更新应用状态标志

### 性能优化
- 状态检测使用原子操作，确保线程安全
- 每次记录日志时只获取一次状态值，避免重复计算
- **懒加载设计**：只在真正需要日志时才检测应用状态，减少初始化开销
- 不影响日志记录的性能

### 使用示例

```typescript
// 无需特别处理，系统自动添加
moegoLogger.info('VOIP 来电', {
  tag: 'voip',
  params: { callId: '12345' }
});

// 上报的日志会自动包含：
// {
//   "context": {
//     "app_status": "foreground" // 或 "background"
//     // ... 其他 context
//   }
// }
```

## 性能优化

- 使用低优先级线程进行上报，不影响应用性能
- 批量上报减少网络请求次数
- MMKV 提供高性能的本地存储
- 自动清理过期缓存，避免内存泄漏
- 应用状态检测采用高效的原子操作

## 更新日志

### v0.2.0
- **新增应用状态追踪功能**：自动在每条日志的 context 中添加 `app_status` 字段
- **Android**: 使用 `ActivityLifecycleCallbacks` 实现高效的前台/后台状态检测
- **iOS**: 使用系统通知实现应用状态监听
- **性能优化**: 使用原子操作保证线程安全，不影响日志记录性能

### v0.1.0
- 添加了时间戳支持
- 改进了 Android 和 iOS 的原生集成
- 优化了日志事件的数据结构
- **新增基于 MMKV 的日志缓存功能**
- **新增低优先级后台上报机制**
- **保证日志时序性**

## 架构优化

### 简化设计

系统采用简化的设计，直接使用 `MGOLogEvent` 而不需要额外的封装：

1. **直接使用 MGOLogEvent**：利用事件内部的时间戳，无需额外排序
2. **移除 CachedLogEvent 封装**：减少代码复杂度，提高性能
3. **使用 ConcurrentLinkedQueue**：替代 PriorityBlockingQueue，简化队列操作
4. **依赖 Datadog 排序**：利用 Datadog 的时间戳排序功能

### 数据流简化

```
React Native 代码
       ↓
   MGOLogger._logEvent()
       ↓
   MGOLogCache.cacheLogEvent()
       ↓
   检查 ddLogs 状态
       ↓
   ┌─────────────────┬─────────────────┐
   ↓                 ↓                 ↓
立即上报          批量上报          保存到 MMKV
(同步)            (异步队列)         (持久化)
       ↓                 ↓                 ↓
   直接调用          后台线程          下次启动时
   ddLogs API       批量处理          加载上报
```

### 性能优化

- **减少对象创建**：不再创建额外的 `CachedLogEvent` 对象
- **简化序列化**：直接序列化 `MGOLogEvent` 和 `source`
- **移除排序开销**：依赖 Datadog 的时间戳排序
- **更高效队列**：使用 `ConcurrentLinkedQueue` 替代优先级队列

## 线程安全

### 多线程保护机制

系统采用多层保护机制确保在多线程环境下的安全性：

1. **Volatile 变量**：`ddLogs` 使用 `@Volatile` 注解确保多线程可见性
2. **读写锁**：使用 `ReentrantReadWriteLock` 保护 `ddLogs` 的读写操作
3. **原子操作**：使用 `AtomicBoolean` 确保状态标志的原子性
4. **线程安全队列**：使用 `PriorityBlockingQueue` 确保队列操作的线程安全

### 保护策略

```kotlin
// 写操作（绑定 ddLogs）
ddLogsLock.writeLock().lock()
try {
    ddLogs = logs
    isInitialized.set(true)
} finally {
    ddLogsLock.writeLock().unlock()
}

// 读操作（检查状态）
ddLogsLock.readLock().lock()
val currentDdLogs: DdLogs?
val isDdLogsReady: Boolean
try {
    currentDdLogs = ddLogs
    isDdLogsReady = isInitialized.get()
} finally {
    ddLogsLock.readLock().unlock()
}
```

### 调试支持

```kotlin
// 获取当前状态用于调试
val status = MGOLogCache.getDdLogsStatus()
println("ddLogs available: ${status["ddLogsAvailable"]}")
println("is initialized: ${status["isInitialized"]}")
println("upload queue size: ${status["uploadQueueSize"]}")
``` 
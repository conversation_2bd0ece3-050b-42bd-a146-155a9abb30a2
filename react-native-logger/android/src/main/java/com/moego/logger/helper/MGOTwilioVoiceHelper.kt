package com.moego.logger.helper

import android.content.Context
import android.media.AudioManager
import com.moego.logger.MGOLogEvent
import com.moego.logger.MGOLogger
import java.net.URLDecoder

const val TwilioVoiceLoggerTag = "call"

fun twilioVoiceLogInvoke(functionName: String) {
    MGOLogger.message(functionName, null, TwilioVoiceLoggerTag)
}


object MGOTwilioVoiceHelper {

    private var selectedDevice: Map<String, String>? = null

    /**
     * Sets the incoming context for the logger.
     * This corresponds to Objective-C's `+ (void)setIncomingContext:(NSDictionary *)payload`.
     *
     * @param payload The incoming push notification payload.
     */
    @JvmStatic
    fun setIncomingContext(payload: Map<String, String>?) {
        val context = incomingPushPayloadToDict(payload)
        MGOLogger.addContext(context, TwilioVoiceLoggerTag)
    }

    /**
     * Resets (removes specific keys from) the incoming context.
     * This corresponds to Objective-C's `+ (void)resetIncomingContext`.
     */
    @JvmStatic
    fun resetIncomingContext() {
        val keys = listOf("twi_account_sid", "twi_call_sid", "twi_message_id", "twi_from", "twi_to",
            "customerId", "clientId")
        MGOLogger.removeKeysFromContext(keys, TwilioVoiceLoggerTag)
    }

    @JvmStatic
    fun setSelectedDevice(deviceInfo: Map<String, String>?) {
        selectedDevice = deviceInfo
    }

    @JvmStatic
    fun sendAudioStatusEvent(context: Context) {
        val result = collectAudioInfo(context)

        val event = if (result["error"] != null) {
            MGOLogEvent.error(
                message = "current audio status",
                eventId = "twilio_voice_current_audio_status",
                error = result["error"],
                tag = TwilioVoiceLoggerTag
            )
        } else {
            MGOLogEvent.info(
                message = "current audio status",
                eventId = "twilio_voice_current_audio_status",
                params = result,
                tag = TwilioVoiceLoggerTag
            )
        }

        MGOLogger.logEvent(event)
    }

    /**
     * Converts the incoming Twilio push payload to a dictionary of key-value pairs.
     * This corresponds to Objective-C's `+ (NSDictionary *)incomingPushPayloadToDict:(NSDictionary *)payload`.
     *
     * @param payload The incoming push notification payload as a Map (equivalent to NSDictionary).
     * @return A Map containing the parsed parameters and specific fields.
     */
    private fun incomingPushPayloadToDict(payload: Map<String, String>?): Map<String, String> {
        val result = mutableMapOf<String, String>()

        // 解析 twi_params 字符串为 key-value 对
        val paramsString = payload?.get("twi_params")
        if (!paramsString.isNullOrEmpty()) {
            val paramsPairs = paramsString.split("&")
            for (pair in paramsPairs) {
                val keyValue = pair.split("=", limit = 2) // limit = 2 防止值中包含等号被错误分割
                if (keyValue.size == 2) {
                    val key = keyValue[0]
                    // Twilio 参数通常是 URL 编码的，需要解码
                    val value = URLDecoder.decode(keyValue[1], "UTF-8")
                    if (key.isNotEmpty() && value.isNotEmpty()) { // 检查 key 和 value 不为空
                        result[key] = value
                    }
                }
            }
        }

        // 逐个添加特定字段
        // 使用 Elvis 操作符 ?: 和 let 函数来简化空值检查和赋值
        payload?.get("twi_account_sid")?.let { result["twi_account_sid"] = it }
        payload?.get("twi_call_sid")?.let { result["twi_call_sid"] = it }
        payload?.get("twi_message_id")?.let { result["twi_message_id"] = it }
        payload?.get("twi_from")?.let { result["twi_from"] = it }
        payload?.get("twi_to")?.let { result["twi_to"] = it }

        return result
    }

    private fun collectAudioInfo(context: Context): Map<String, Any?> {
        val result = mutableMapOf<String, Any?>()
        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

        // 判断是否有活跃的音频输出设备
        val outputDevices = audioManager.getDevices(AudioManager.GET_DEVICES_OUTPUTS)
        if (outputDevices.isEmpty()) {
            result["error"] = "No active audio output route"
            return result
        }

        try {
            val mode = audioManager.mode
            val modeString = when (mode) {
                AudioManager.MODE_NORMAL -> "NORMAL"
                AudioManager.MODE_RINGTONE -> "RINGTONE"
                AudioManager.MODE_IN_CALL -> "IN_CALL"
                AudioManager.MODE_IN_COMMUNICATION -> "IN_COMMUNICATION"
                else -> "UNKNOWN($mode)"
            }
            result["mode"] = modeString

            val streamType = when (mode) {
                AudioManager.MODE_IN_COMMUNICATION, AudioManager.MODE_IN_CALL -> AudioManager.STREAM_VOICE_CALL
                AudioManager.MODE_RINGTONE -> AudioManager.STREAM_RING
                else -> AudioManager.STREAM_MUSIC
            }

            val currentVolume = audioManager.getStreamVolume(streamType)
            val maxVolume = audioManager.getStreamMaxVolume(streamType)
            val volumePercent = if (maxVolume > 0) currentVolume.toFloat() / maxVolume else 0f
            result["volume"] = volumePercent
        } catch (e: Exception) {
            System.err.println("Failed to get volume info: ${e.message}")
        }

        if (selectedDevice != null) {
            result["output"] = selectedDevice
        }

        return result
    }

}
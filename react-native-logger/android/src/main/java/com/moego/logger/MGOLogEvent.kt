package com.moego.logger

enum class MGOLogEventType {
    DEBUG,
    INFO,
    WARN,
    ERROR
}

data class MGOLogEvent(
    val type: MGOLogEventType,
    val message: String,
    val tag: String,
    val eventId: String? = null,
    val params: Map<String, Any?>? = null,
    val result: Any? = null,
    val error: Any? = null,
    val context: Map<String, Any?>? = null,
    val timestamp: Long = System.currentTimeMillis()
) {
    companion object {
        fun debug(
            message: String,
            tag: String,
            eventId: String? = null,
            params: Map<String, Any?>? = null,
            result: Any? = null,
            context: Map<String, Any?>? = null
        ) = MGOLogEvent(MGOLogEventType.DEBUG, message, tag, eventId, params, result, null, context)

        fun info(
            message: String,
            tag: String,
            eventId: String? = null,
            params: Map<String, Any?>? = null,
            result: Any? = null,
            context: Map<String, Any?>? = null
        ) = MGOLogEvent(MGOLogEventType.INFO, message, tag, eventId, params, result, null, context)

        fun warn(
            message: String,
            tag: String,
            eventId: String? = null,
            params: Map<String, Any?>? = null,
            result: Any? = null,
            context: Map<String, Any?>? = null
        ) = MGOLogEvent(MGOLogEventType.WARN, message, tag, eventId, params, result, null, context)

        fun error(
            message: String,
            tag: String,
            eventId: String? = null,
            params: Map<String, Any?>? = null,
            error: Any? = null,
            context: Map<String, Any?>? = null
        ) = MGOLogEvent(MGOLogEventType.ERROR, message, tag, eventId, params, null, error, context)

        @JvmStatic
        fun debugBuilder(message: String, eventId: String, tag: String) =
            Builder(MGOLogEventType.DEBUG, message, eventId, tag)
        @JvmStatic
        fun infoBuilder(message: String, eventId: String, tag: String) =
            Builder(MGOLogEventType.INFO, message, eventId, tag)
        @JvmStatic
        fun warnBuilder(message: String, eventId: String, tag: String) =
            Builder(MGOLogEventType.WARN, message, eventId, tag)
        @JvmStatic
        fun errorBuilder(message: String, eventId: String, tag: String) =
            Builder(MGOLogEventType.ERROR, message, eventId, tag)
    }

    class Builder internal constructor(
        private val type: MGOLogEventType,
        private val message: String,
        private val eventId: String,
        private val tag: String
    ) {
        private var params: Map<String, Any?>? = null
        private var result: Any? = null
        private var error: Any? = null
        private var context: Map<String, Any?>? = null
        private var timestamp: Long = System.currentTimeMillis()

        fun params(params: Map<String, Any?>?) = apply { this.params = params }
        fun result(result: Any?) = apply { this.result = result }
        fun error(error: Any?) = apply { this.error = error }
        fun context(context: Map<String, Any?>?) = apply { this.context = context }
        fun timestamp(timestamp: Long) = apply { this.timestamp = timestamp }

        fun build(): MGOLogEvent {
            return MGOLogEvent(type, message, tag, eventId, params, result, error, context, timestamp)
        }
    }
}

package com.moego.logger

import android.content.Context
import android.os.Build
import com.datadog.reactnative.DdLogs
import java.util.concurrent.locks.ReentrantReadWriteLock
import com.facebook.react.bridge.Dynamic
import com.facebook.react.bridge.ReadableMap
import com.facebook.react.bridge.ReadableType
import java.util.concurrent.ConcurrentHashMap
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import java.util.concurrent.atomic.AtomicBoolean


object MGOLogger {
  private var isLoggerEnabled = true
  private var ddLogs: DdLogs? = null
  private val globalContexts = ConcurrentHashMap<String, Any>()
  private val presetContexts = ConcurrentHashMap<String, Map<String, Any>>()
  private val contextLock = ReentrantReadWriteLock()
  
  // App state tracking
  private val isAppInForeground = AtomicBoolean(false) // 默认为后台，待检测
  private var lifecycleObserver: LifecycleEventObserver? = null
  private var isStateInitialized = AtomicBoolean(false) // 状态是否已初始化

  @JvmStatic
  internal fun init(context: Context) {
    // 防止重复初始化
    if (globalContexts.containsKey("build_version")) {
      return
    }
    try {
      val packageManager = context.packageManager
      val packageName = context.packageName
      val packageInfo = packageManager.getPackageInfo(packageName, 0)

      val versionCode =
         if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            packageInfo.longVersionCode
         } else {
            @Suppress("DEPRECATION") packageInfo.versionCode.toLong()
        }

      val appInfo = mutableMapOf("build_version" to versionCode.toString())
      @Suppress("DiscouragedApi")
      val resId = context.resources.getIdentifier("expo_runtime_version", "string", packageName)
      if (resId != 0) {
          val expoRuntimeVersion = context.getString(resId)
          appInfo["runtime_version"] = expoRuntimeVersion
      }

      globalContexts.putAll(appInfo)
      
      // 初始化应用状态跟踪
      initAppStateTracking(context)
    } catch (e: Exception) {
      System.err.println("MGOLogger: Failed to get package info during init: ${e.message}")
    }
  }

  /**
   * 初始化应用状态跟踪（使用 Jetpack ProcessLifecycleOwner）
   */
  private fun initAppStateTracking(context: Context) {
    try {
      // 避免重复注册
      if (lifecycleObserver != null) return
      
      // 使用 ProcessLifecycleOwner 监听应用级别的生命周期事件
      lifecycleObserver = LifecycleEventObserver { _: LifecycleOwner, event: Lifecycle.Event ->
        when (event) {
          Lifecycle.Event.ON_START -> {
            // 应用进入前台
            isAppInForeground.set(true)
            isStateInitialized.set(true)
            System.out.println("MGOLogger: App entered foreground")
          }
          Lifecycle.Event.ON_STOP -> {
            // 应用进入后台
            isAppInForeground.set(false)
            isStateInitialized.set(true)
            System.out.println("MGOLogger: App entered background")
          }
          else -> {
            // 其他事件不关心
          }
        }
      }
      
      ProcessLifecycleOwner.get().lifecycle.addObserver(lifecycleObserver!!)
      
      System.out.println("MGOLogger: ProcessLifecycleOwner observer registered successfully")
    } catch (e: Exception) {
      System.err.println("MGOLogger: Failed to initialize app state tracking: ${e.message}")
    }
  }
  
  /**
   * 棅测初始应用状态（使用 ProcessLifecycleOwner 的当前状态）
   */
  private fun detectInitialAppState() {
    try {
      // 使用 ProcessLifecycleOwner 的当前状态
      val currentState = ProcessLifecycleOwner.get().lifecycle.currentState
      
      when {
        currentState.isAtLeast(Lifecycle.State.STARTED) -> {
          // 应用在前台
          isAppInForeground.set(true)
          isStateInitialized.set(true)
          System.out.println("MGOLogger: Lazy detection - app is in foreground")
        }
        else -> {
          // 应用在后台或未启动
          isAppInForeground.set(false)
          isStateInitialized.set(true)
          System.out.println("MGOLogger: Lazy detection - app is in background")
        }
      }
    } catch (e: Exception) {
      // 检测失败，使用默认值
      System.err.println("MGOLogger: Failed to detect initial app state: ${e.message}")
      isAppInForeground.set(false) // 安全起见，默认为后台
      isStateInitialized.set(false) // 等待生命周期事件
    }
  }
  
  /**
   * 获取当前应用状态
   */
  private fun getCurrentAppStatus(): String {
    // 如果状态还没初始化，尝试重新检测（与 iOS 保持一致）
    if (!isStateInitialized.get()) {
      System.err.println("MGOLogger: App state not initialized, attempting lazy detection")
      detectInitialAppState()
    }
    
    return if (isAppInForeground.get()) "foreground" else "background"
  }

  @JvmStatic
  fun setLoggerEnabled(enabled: Boolean) {
    isLoggerEnabled = enabled
  }

  @JvmStatic
  fun bindWithDdLog(logs: DdLogs) {
    ddLogs = logs
    // 同时绑定到缓存管理器
    MGOLogCache.bindWithDdLog(logs)
  }

  @JvmStatic
  fun addGlobalContext(context: Map<String, Any>) {
    contextLock.writeLock().lock()
    try {
      globalContexts.putAll(context)
    } finally {
      contextLock.writeLock().unlock()
    }
  }

  @JvmStatic
  fun addContext(context: Map<String, Any>, tag: String) {
    if (tag.isEmpty() || context.isEmpty()) return

    contextLock.writeLock().lock()
    try {
      val existingContext = presetContexts[tag]?.toMutableMap() ?: mutableMapOf()
      existingContext.putAll(context)
      presetContexts[tag] = existingContext
    } finally {
      contextLock.writeLock().unlock()
    }
  }

  @JvmStatic
  fun removeKeysFromContext(keys: List<String>, tag: String) {
    if (tag.isEmpty() || keys.isEmpty()) return

    contextLock.writeLock().lock()
    try {
      val existingContext = presetContexts[tag]?.toMutableMap() ?: return
      keys.forEach { existingContext.remove(it) }
      if (existingContext.isEmpty()) {
        presetContexts.remove(tag)
      } else {
        presetContexts[tag] = existingContext
      }
    } finally {
      contextLock.writeLock().unlock()
    }
  }

  @JvmStatic
  fun resetContextForTag(tag: String) {
    if (tag.isEmpty()) return

    contextLock.writeLock().lock()
    try {
      presetContexts.remove(tag)
    } finally {
      contextLock.writeLock().unlock()
    }
  }

  fun getMergedContext(context: Map<String, Any?>?, tag: String): Map<String, Any?> {
    contextLock.readLock().lock()
    val globalContext = globalContexts.toMap()
    val existingContext = presetContexts[tag] ?: emptyMap()
    contextLock.readLock().unlock()

    val merged = mutableMapOf<String, Any?>()
    merged.putAll(globalContext)
    merged.putAll(existingContext)

    if (context != null) {
      merged.putAll(context)
    }

    return merged
  }

  private fun serializeError(error: Any?): Map<String, Any?>? {
    return when (error) {
      null -> null
      is ReadableMap -> error.toHashMap()  // 转成普通 Map
      is Throwable -> mapOf(
        "code" to -1,
        "message" to (error.message ?: error.javaClass.simpleName ?: "Unknown error"),
        "stacktrace" to error.stackTraceToString()
      )
      is String -> mapOf(
        "code" to -1,
        "message" to if (error.isNotEmpty()) error else "Unknown error"
      )
      else -> mapOf(
        "code" to -1,
        "message" to error.toString()
      )
    }
  }

  /**
   * Lightweight logger for function call tracing, similar to iOS _message.
   */
  @JvmStatic
  fun message(message: String, params: HashMap<String, Any>?, tag: String) {
    if (!isLoggerEnabled) return

    val event = MGOLogEvent(
      type = MGOLogEventType.INFO,
      message = message,
      tag = tag,
      params = params
    )
    
    _logEvent(event, "android")
  }

  /**
   * Processes and logs an MGOLogEvent from native Android code.
   */
  @JvmStatic
  fun logEvent(event: MGOLogEvent) {
    _logEvent(event, "android")
  }

  /**
   * Processes and logs an event originating from React Native.
   */
  @JvmStatic
  fun rnLog(message: String, eventData: ReadableMap, eventType: MGOLogEventType) {
    if (!isLoggerEnabled) return

    val tag = eventData.getString("tag") ?: "default"

    val errorValue = if (eventData.hasKey("error") && !eventData.isNull("error")) {
      eventData.getDynamic("error").asGuess()
    } else {
      null
    }

   val event = MGOLogEvent(
     type = eventType,
     message = message,
     tag = tag,
     eventId = eventData.getString("id"),
     params = eventData.getMap("params")?.toHashMap(),
     result = eventData.getMap("result")?.toHashMap(),
     error = errorValue,
     context = eventData.getMap("context")?.toHashMap()
   )
   _logEvent(event, "rn")
  }

  /**
   * Internal logging function, similar to _logEvent in iOS.
   */
  @JvmStatic
  fun _logEvent(event: MGOLogEvent, source: String) {
    if (!isLoggerEnabled) return

    val dict = mutableMapOf<String, Any?>(
      "type" to event.type.name,
      "message" to event.message,
      "tag" to event.tag,
      "timestamp" to event.timestamp,
      "source" to source
    )
    if (event.eventId != null) dict["eventId"] = event.eventId
    if (event.params != null) dict["params"] = event.params
    if (event.result != null) dict["result"] = event.result
    val mergedContext = getMergedContext(event.context, event.tag)
    dict["context"] = mergedContext
    val serializedError = serializeError(event.error)
    if (serializedError != null) dict["error"] = serializedError
    dict["app_status"] = getCurrentAppStatus()
    MGOLogCache.cacheLogEvent(dict)
  }

  // Helper to convert ReadableDynamic to a common type
  private fun Dynamic.asGuess(): Any? {
    return when (this.type) {
      ReadableType.Null -> null
      ReadableType.Boolean -> this.asBoolean()
      ReadableType.Number -> this.asDouble()
      ReadableType.String -> this.asString()
      ReadableType.Array -> this.asArray()
      ReadableType.Map -> this.asMap()
      else -> null
    }
  }
}

package com.moego.logger

import android.content.ContentProvider
import android.content.ContentValues
import android.database.Cursor
import android.net.Uri
/**
 * 利用 ContentProvider 的启动时机来自动配置全局属性。
 */
internal class LoggerInitializer : ContentProvider() {
    override fun onCreate(): <PERSON><PERSON><PERSON> {
        // 获取 context 并初始化 Logger
        context?.let {
            MGOLogger.init(it)
        }
        // 我们不实际提供任何内容，所以返回 false
        return false
    }

    // --- 以下方法保持为空 ---
    override fun query(uri: Uri, p1: Array<String>?, p2: String?, p3: Array<String>?, p4: String?): Cursor? = null
    override fun getType(uri: Uri): String? = null
    override fun insert(uri: Uri, values: ContentValues?): Uri? = null
    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<String>?): Int = 0
    override fun update(uri: Uri, values: ContentValues?, selection: String?, selectionArgs: Array<String>?): Int = 0
}
package com.moego.logger

import com.datadog.reactnative.DdLogs
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.WritableMap
import com.tencent.mmkv.MMKV
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import org.json.JSONObject

/** 基于 MMKV 的日志缓存和上报管理器 */
object MGOLogCache {
    private const val LOG_MMKV_ID = "moego_log_cache"

    private var logMMKV: MMKV? = null
    private val uploadExecutor: ExecutorService = // Changed type to ExecutorService
            Executors.newSingleThreadExecutor { r ->
                Thread(r, "MGOLogCache-Uploader").apply {
                    priority = Thread.MIN_PRIORITY
                    isDaemon = true
                }
            }
    private val scheduledExecutor: ScheduledExecutorService = // Added scheduled executor
            Executors.newSingleThreadScheduledExecutor()

    @Volatile private var ddLogs: DdLogs? = null
    private val isInitialized = AtomicBoolean(false)
    private val isUploading = AtomicBoolean(false)

    private val emptyPromise: Promise =
        object : Promise {
            override fun resolve(value: Any?) {}

            @Deprecated("Deprecated in Java")
            override fun reject(message: String) {}
            override fun reject(code: String, message: String?) {}
            override fun reject(code: String, message: String?, userInfo: WritableMap) {}
            override fun reject(code: String, throwable: Throwable?) {}
            override fun reject(code: String, throwable: Throwable?, userInfo: WritableMap) {}
            override fun reject(
                code: String?,
                message: String?,
                throwable: Throwable?,
                userInfo: WritableMap?
            ) {}
            override fun reject(throwable: Throwable) {}
            override fun reject(throwable: Throwable, userInfo: WritableMap) {}
            override fun reject(code: String, message: String?, throwable: Throwable?) {}
            override fun reject(code: String, userInfo: WritableMap) {}
        }

    private fun getOrCreateLogMMKV(): MMKV {
        if (logMMKV == null) {
            logMMKV = MMKV.mmkvWithID(LOG_MMKV_ID, MMKV.SINGLE_PROCESS_MODE)
        }
        return logMMKV!!
    }

    /** 绑定 DdLogs 并开始上报 */
    fun bindWithDdLog(logs: DdLogs?) {
        if (logs == null) {
            ddLogs = null
            isInitialized.set(false)
            return
        }
        ddLogs = logs
        isInitialized.set(true)
        scheduledExecutor.schedule(
                { startUploadThread() },
                10,
                TimeUnit.SECONDS
        ) // Use scheduledExecutor
    }

    /** 缓存日志事件 */
    fun cacheLogEvent(event: Map<String, Any?>) {
        if (isInitialized.get()) {
            uploadSingleLogImmediately(event)
        } else {
            saveToLogFile(event)
        }
    }

    /** 立即上报单个日志（同步方式） */
    private fun uploadSingleLogImmediately(event: Map<String, Any?>) {
        try {
            val type = event["type"] as? String ?: return
            val message = event["message"] as? String ?: return

            // 去除 type 和 message 字段后构建上传用的 map
            val filteredEvent = event.filterKeys { it != "type" && it != "message" }
            val finalMap = mapOf("event" to filteredEvent)

            val nativeMap = Arguments.makeNativeMap(finalMap)

            when (type) {
                "INFO" -> ddLogs?.info(message, nativeMap, emptyPromise)
                "WARN" -> ddLogs?.warn(message, nativeMap, emptyPromise)
                "ERROR" -> ddLogs?.error(message, nativeMap, emptyPromise)
                "DEBUG" -> ddLogs?.debug(message, nativeMap, emptyPromise)
            }
        } catch (e: Exception) {
            System.err.println("MGOLogCache: Failed to upload log immediately: ${e.message}")
        }
    }

    /** 保存日志到 MMKV */
    private fun saveToLogFile(event: Map<String, Any?>) {
        val mmkv = getOrCreateLogMMKV()
        val key = System.currentTimeMillis().toString()
        mmkv.encode(key, JSONObject(event).toString())
    }

    /** 上传所有缓存的日志 */
    private fun uploadAllCachedLogs() {
        val mmkv = getOrCreateLogMMKV()
        mmkv.allKeys()?.let { keys ->
            for (key in keys) {
                val json = mmkv.decodeString(key)
                if (json != null) {
                    uploadSingleJsonEvent(json)
                    mmkv.removeValueForKey(key) // 上传后删除该条日志
                }
            }
        }
    }

    private fun uploadSingleJsonEvent(jsonString: String) {
        try {
            val json = JSONObject(jsonString)
            val event = json.toMap()
            uploadSingleLogImmediately(event)
        } catch (e: Exception) {
            System.err.println("MGOLogCache: Failed to upload json log: ${e.message}")
        }
    }

    /** 启动上传线程 */
    private fun startUploadThread() {
        uploadExecutor.submit {
            try {
                isUploading.set(true)

                uploadAllCachedLogs()
                cleanup()
            } catch (e: Exception) {
                System.err.println("MGOLogCache: Upload thread error: ${e.message}")
            } finally {
                isUploading.set(false)
            }
        }
    }

    /** 清理资源 */
    fun cleanup() {
        uploadExecutor.shutdown()
        logMMKV?.clearAll()
    }
}

/** JSONObject 扩展函数，转换为 Map */
private fun JSONObject.toMap(): Map<String, Any?> {
    val map = mutableMapOf<String, Any?>()
    val keys = this.keys()
    while (keys.hasNext()) {
        val key = keys.next()
        var value = this.get(key)
        when (value) {
            is JSONObject -> value = value.toMap()
            is org.json.JSONArray -> value = value.toList()
        }
        map[key] = value
    }
    return map
}

/** JSONArray 扩展函数，转换为 List */
private fun org.json.JSONArray.toList(): List<Any?> {
    val list = mutableListOf<Any?>()
    for (i in 0 until this.length()) {
        var value = this.get(i)
        when (value) {
            is JSONObject -> value = value.toMap()
            is org.json.JSONArray -> value = value.toList()
        }
        list.add(value)
    }
    return list
}

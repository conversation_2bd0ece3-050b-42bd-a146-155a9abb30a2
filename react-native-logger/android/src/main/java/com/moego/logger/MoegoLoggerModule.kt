package com.moego.logger

import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.ReadableMap

class MoegoLoggerModule(reactContext: ReactApplicationContext) :
        ReactContextBaseJavaModule(reactContext) {

  override fun getName(): String {
    return "MoegoLoggerReactNative"
  }

  @ReactMethod
  fun setLoggerEnabled(isEnabled: <PERSON>olean) {
    MGOLogger.setLoggerEnabled(isEnabled)
  }

  @ReactMethod
  fun debug(message: String, event: ReadableMap) {
    MGOLogger.rnLog(message, event, MGOLogEventType.DEBUG)
  }

  @ReactMethod
  fun info(message: String, event: ReadableMap) {
    MGOLogger.rnLog(message, event, MGOLogEventType.INFO)
  }

  @ReactMethod
  fun warn(message: String, event: ReadableMap) {
    MGOLogger.rnLog(message, event, MGOLogEventType.WARN)
  }

  @ReactMethod
  fun error(message: String, event: ReadableMap) {
    MGOLogger.rnLog(message, event, MGOLogEventType.ERROR)
  }

  @ReactMethod
  fun addGlobalContext(context: ReadableMap) {
    MGOLogger.addGlobalContext(context.toHashMap())
  }

  @ReactMethod
  fun addContext(context: ReadableMap, tag: String) {
    MGOLogger.addContext(context.toHashMap(), tag)
  }

  @ReactMethod
  fun resetTagContext(tag: String) {
    MGOLogger.resetContextForTag(tag)
  }
}

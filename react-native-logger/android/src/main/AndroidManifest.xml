<manifest xmlns:android="http://schemas.android.com/apk/res/android" 
    package="com.moego.logger">
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <application>
        <provider
            android:name="com.moego.logger.LoggerInitializer"
            android:authorities="${applicationId}.logger-initializer"
            android:exported="false" />
    </application>

</manifest>

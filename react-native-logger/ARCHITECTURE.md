# MGOLogger 架构设计文档

## 概述

MGOLogger 是一个基于 MMKV 的智能日志缓存和上报系统，支持时间戳日志和多种上报策略，确保日志的可靠性和性能。

## 核心组件

### 1. MGOLogEvent
日志事件的数据结构，包含：
- 基本信息：消息、类型、标签
- 上下文：参数、结果、错误信息
- 时间戳：支持自定义时间戳（Datadog 会自动排序）
- 元数据：事件ID、来源等

### 2. MGOLogCache
基于 MMKV 的缓存管理器，提供：
- **统一入口**：所有日志都通过 `cacheLogEvent()` 处理
- 持久化存储：使用 MMKV 确保数据不丢失
- **立即上报**：有 ddLogs 时立即上报，避免数据丢失
- **简化队列**：使用 `ConcurrentLinkedQueue` 存储 `Pair<MGOLogEvent, String>`
- 自动清理：防止缓存无限增长

### 3. MGOLogger
主要的日志管理器，负责：
- 日志格式化：统一日志格式
- 上下文合并：合并全局和局部上下文
- 错误序列化：处理异常信息
- **统一分发**：所有日志都转发给 MGOLogCache

## 数据流

```
React Native 代码
       ↓
   MGOLogger._logEvent()
       ↓
   MGOLogCache.cacheLogEvent()
       ↓
   检查 ddLogs 状态
       ↓
   ┌─────────────────┬─────────────────┐
   ↓                 ↓                 ↓
立即上报          批量上报          保存到 MMKV
(同步)            (异步队列)         (持久化)
       ↓                 ↓                 ↓
   直接调用          后台线程          下次启动时
   ddLogs API       批量处理          加载上报
```

## 上报策略

### 立即上报
- **适用场景**：所有日志
- **优点**：实时性好，避免数据丢失
- **实现**：有 ddLogs 时同步调用 API，无 ddLogs 时保存到 MMKV

## 缓存机制

### MMKV 存储
- **键格式**：`cached_log_{timestamp}`
- **值格式**：JSON 序列化的 `MGOLogEvent` 和 `source`
- **清理策略**：超过最大数量时删除最旧的日志

### 内存队列
- **数据结构**：ConcurrentLinkedQueue<Pair<MGOLogEvent, String>>
- **处理方式**：低优先级后台线程批量处理
- **排序依赖**：依赖 Datadog 的时间戳排序功能
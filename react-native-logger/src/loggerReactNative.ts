import { NativeModules } from 'react-native';

interface EventType {
  tag?: string;
  id?: string;
  params?: object;
  result?: object;
  error?: unknown;
  context?: object;
}

export interface Spec {
  setLoggerEnabled(enabled: boolean): void;
  debug(message: string, event: EventType): void;
  info(message: string, event: EventType): void;
  warn(message: string, event: EventType): void;
  error(message: string, event: EventType): void;
  addGlobalContext(context: object): void;
  addContext(context: object, tag: string): void;
  resetTagContext(tag: string): void;
  removeKeysFromContext(keys: string[], tag: string): void;
}

const nativeLogger = NativeModules.MoegoLoggerReactNative as Spec;

// 用于 .get(tag) 返回的实例
type TaggedEventDetails = Omit<EventType, 'tag'>;

// 这个类由 moegoLogger.get(tag) 返回
class TaggedLogger {
  private readonly tag: string;
  private readonly logger: Spec;

  constructor(tag: string, logger: Spec) {
    this.tag = tag;
    this.logger = logger;
  }

  // 日志方法自动注入 tag
  debug(message: string, details: TaggedEventDetails = {}): void {
    this.logger.debug(message, { ...details, tag: this.tag });
  }

  info(message: string, details: TaggedEventDetails = {}): void {
    this.logger.info(message, { ...details, tag: this.tag });
  }

  warn(message: string, details: TaggedEventDetails = {}): void {
    this.logger.warn(message, { ...details, tag: this.tag });
  }

  error(message: string, details: TaggedEventDetails = {}): void {
    this.logger.error(message, { ...details, tag: this.tag });
  }

  // 上下文方法自动注入 tag
  addContext(context: object): void {
    this.logger.addContext(context, this.tag);
  }

  resetContext(): void {
    this.logger.resetTagContext(this.tag);
  }

  removeKeysFromContext(keys: string[]): void {
    this.logger.removeKeysFromContext(keys, this.tag);
  }
}

// Logger 管理器 (添加了直接调用的方法)
class LoggerManager {
  private readonly loggerCache = new Map<string, TaggedLogger>();
  private readonly logger: Spec = nativeLogger;
  private readonly DEFAULT_TAG = 'default'; // 为直接调用设置一个默认标签

  constructor() {
    if (!this.logger) {
      console.warn('[MoegoLogger] Native module not found. Using mock implementation.');
      // 模拟实现，防止在非 RN 环境下崩溃
      this.logger = {
        setLoggerEnabled: (e) => console.log(`[MockLogger] setLoggerEnabled(${e})`),
        debug: (m, e) => console.debug(`[MockLogger] [${e?.tag || 'none'}] ${m}`, e),
        info: (m, e) => console.info(`[MockLogger] [${e?.tag || 'none'}] ${m}`, e),
        warn: (m, e) => console.warn(`[MockLogger] [${e?.tag || 'none'}] ${m}`, e),
        error: (m, e) => console.error(`[MockLogger] [${e?.tag || 'none'}] ${m}`, e),
        addGlobalContext: (c) => console.log(`[MockLogger] addGlobalContext`, c),
        addContext: (c, t) => console.log(`[MockLogger] addContext for ${t}`, c),
        resetTagContext: (t) => console.log(`[MockLogger] resetTagContext for ${t}`),
        removeKeysFromContext: (k, t) => console.log(`[MockLogger] removeKeysFromContext for ${t}`, k),
      };
    }
  }

  /**
   * 获取一个带特定标签的记录器实例，用于链式调用。
   */
  get(tag: string): TaggedLogger {
    if (this.loggerCache.has(tag)) {
      return this.loggerCache.get(tag)!;
    }
    const newLogger = new TaggedLogger(tag, this.logger);
    this.loggerCache.set(tag, newLogger);
    return newLogger;
  }

  // --- 新增：可以直接调用的方法 ---
  // 这些方法直接挂在管理器上，行为和原始 Native Module 类似。
  // 如果用户没有在 event 参数中提供 tag，则使用默认的 tag。

  debug(message: string, event: EventType = {}): void {
    this.logger.debug(message, { ...event, tag: event.tag || this.DEFAULT_TAG });
  }

  info(message: string, event: EventType = {}): void {
    this.logger.info(message, { ...event, tag: event.tag || this.DEFAULT_TAG });
  }

  warn(message: string, event: EventType = {}): void {
    this.logger.warn(message, { ...event, tag: event.tag || this.DEFAULT_TAG });
  }

  error(message: string, event: EventType = {}): void {
    this.logger.error(message, { ...event, tag: event.tag || this.DEFAULT_TAG });
  }

  // --- 全局方法 (无变化) ---
  setLoggerEnabled(enabled: boolean): void {
    this.logger.setLoggerEnabled(enabled);
  }

  addGlobalContext(context: object): void {
    this.logger.addGlobalContext(context);
  }
}

// 4. 导出名为 moegoLogger 的单例
export const moegoLogger = new LoggerManager();

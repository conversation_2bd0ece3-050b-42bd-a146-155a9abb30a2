//
//  MGOLogEvent.m
//  MoegoLogger
//
//  Created by wang<PERSON> on 2025/6/10.
//

#import "MGOLogEvent.h"

@implementation MGOLogEvent

- (instancetype)init {
    self = [super init];
    if (self) {
        _timestamp = [[NSDate date] timeIntervalSince1970] * 1000;
    }
    return self;
}

- (instancetype)initWithType:(MGOLogEventType)type
                     message:(NSString *)message
                    eventId:(nullable NSString *)eventId
                     params:(nullable NSDictionary *)params
                     result:(nullable NSObject<NSCopying> *)result
                      error:(nullable id)error
                    context:(nullable NSDictionary *)context
                        tag:(NSString *)tag {
    self = [super init];
    if (self) {
        _type = type;
        _message = [message copy];
        _eventId = [eventId copy];
        _params = [params copy];
        _result = [result copy];
        _error = error;
        _context = [context copy];
        _tag = [tag copy];
        _timestamp = [[NSDate date] timeIntervalSince1970] * 1000;
    }
    return self;
}

+ (instancetype)debugWithMessage:(NSString *)message
                         eventId:(nullable NSString *)eventId
                          params:(nullable NSDictionary *)params
                          result:(nullable NSObject<NSCopying> *)result
                         context:(nullable NSDictionary *)context
                             tag:(NSString *)tag {
    return [[self alloc] initWithType:MGOLogEventTypeDebug
                              message:message
                             eventId:eventId
                              params:params
                              result:result
                               error:nil
                             context:context
                                 tag:tag];
}

+ (instancetype)infoWithMessage:(NSString *)message
                        eventId:(nullable NSString *)eventId
                         params:(nullable NSDictionary *)params
                         result:(nullable NSObject<NSCopying> *)result
                        context:(nullable NSDictionary *)context
                            tag:(NSString *)tag {
    return [[self alloc] initWithType:MGOLogEventTypeInfo
                              message:message
                             eventId:eventId
                              params:params
                              result:result
                               error:nil
                             context:context
                                 tag:tag];
}

+ (instancetype)warningWithMessage:(NSString *)message
                           eventId:(nullable NSString *)eventId
                            params:(nullable NSDictionary *)params
                            result:(nullable NSObject<NSCopying> *)result
                           context:(nullable NSDictionary *)context
                               tag:(NSString *)tag {
    return [[self alloc] initWithType:MGOLogEventTypeWarn
                              message:message
                             eventId:eventId
                              params:params
                              result:result
                               error:nil
                             context:context
                                 tag:tag];
}

+ (instancetype)errorWithMessage:(NSString *)message
                         eventId:(nullable NSString *)eventId
                          params:(nullable NSDictionary *)params
                           error:(nullable id)error
                         context:(nullable NSDictionary *)context
                             tag:(NSString *)tag {
    return [[self alloc] initWithType:MGOLogEventTypeError
                              message:message
                             eventId:eventId
                              params:params
                              result:nil
                               error:error
                             context:context
                                 tag:tag];
}

+ (instancetype)infoWithMessage:(NSString *)message
                        eventId:(nullable NSString *)eventId
                            tag:(NSString *)tag {
    return [[self alloc] initWithType:MGOLogEventTypeInfo
                              message:message
                             eventId:eventId
                              params:nil
                              result:nil
                               error:nil
                             context:nil
                                 tag:tag];
}

@end    

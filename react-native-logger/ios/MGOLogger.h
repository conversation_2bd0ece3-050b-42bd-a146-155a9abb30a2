//
//  MGOLogger.h
//  MoegoLogger
//
//  Created by wa<PERSON><PERSON> on 2025/5/28.
//

#import <Foundation/Foundation.h>
#import "MGOLogEvent.h"

NS_ASSUME_NONNULL_BEGIN

@class DdLogs;
@interface MGOLogger : NSObject

+ (void)bindWithDdLog:(DdLogs *)ddLog;
+ (void)setLoggerEnabled:(BOOL)isEnabled;

/// 轻量级logger，用于函数调用打点
+ (void)message:(NSString *)message params:(nullable NSDictionary *)params tag:(NSString *)tag;
/// native的logger，用于上报
+ (void)logEvent:(MGOLogEvent *)event;
/// RN的logger，用于上报
+ (void)rnLog:(NSString *)message event:(NSDictionary *)event type:(MGOLogEventType)type;

/// 全局上下文设置，上报event时会自动带上
+ (void)addGlobalContext:(NSDictionary *)context;
+ (void)addContext:(NSDictionary *)context forTag:(NSString *)tag;
+ (void)removeKeys:(NSArray *)keys fromContextForTag:(NSString *)tag;
+ (void)resetContextForTag:(NSString *)tag;
@end

NS_ASSUME_NONNULL_END

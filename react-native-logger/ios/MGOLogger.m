//
//  MGOLogger.m
//  MoegoLogger
//
//  Created by wa<PERSON><PERSON> on 2025/5/28.
//
#import "MGOLogger.h"
#import "MGOLogCache.h"
#import <DatadogSDKReactNative/DdLogs.h>
#import <React/RCTBridgeModule.h>
#import <pthread.h>
#import <UIKit/UIKit.h>

@interface MGOLogger ()
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSDictionary *> *presetContexts;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSString *> *globalContexts;
@property (nonatomic, assign) pthread_rwlock_t contextLock;
@property (nonatomic, assign) BOOL isEnabled;
// App state tracking
@property (nonatomic, assign) BOOL isAppInForeground;
@property (nonatomic, assign) BOOL isStateInitialized;
@end

@implementation MGOLogger

+ (instancetype)sharedInstance {
    static MGOLogger *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _presetContexts = [NSMutableDictionary dictionary];
        _globalContexts = [NSMutableDictionary dictionary];
        pthread_rwlock_init(&_contextLock, NULL);
        _isEnabled = YES;
        
        // 初始化应用状态跟踪
        _isStateInitialized = NO;
        [self _setupAppStateNotifications];
        
        // Load the Expo.plist dictionary
        NSString *expoPlistPath = [[NSBundle mainBundle] pathForResource:@"Expo" ofType:@"plist"];
        NSDictionary *expoPlist = [NSDictionary dictionaryWithContentsOfFile:expoPlistPath];
        
        if (expoPlist) {
            NSString *runtimeVersion = expoPlist[@"EXUpdatesRuntimeVersion"];
            if (runtimeVersion) {
                _globalContexts[@"runtime_version"] = runtimeVersion;
            }
        }
    }
    return self;
}

- (void)dealloc {
    pthread_rwlock_destroy(&_contextLock);
    // 移除通知监听
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

+ (void)setLoggerEnabled:(BOOL)isEnabled {
    MGOLogger *logger = [self sharedInstance];
    logger.isEnabled = isEnabled;
}

+ (void)bindWithDdLog:(DdLogs *)ddLog {
    NSAssert([ddLog isKindOfClass:[DdLogs class]], @"ddLog must be of type DdLogs");
    [[MGOLogCache sharedInstance] bindWithDdLog:ddLog];
}

+ (void)message:(NSString *)message params:(nullable NSDictionary *)params tag:(NSString *)tag {
    MGOLogEvent *event = [MGOLogEvent infoWithMessage:message eventId:nil params:params result:nil context:nil tag:tag];
    [[self sharedInstance] _logEvent:event source:@"ios"];
}

+ (void)logEvent:(MGOLogEvent *)event {
    [[self sharedInstance] _logEvent:event source:@"ios"];
}

+ (void)rnLog:(NSString *)message event:(NSDictionary *)event type:(MGOLogEventType)type {
    [[self sharedInstance] rnLog:message event:event type:type];
}

+ (void)addGlobalContext:(NSDictionary *)context {
    [[self sharedInstance] _addGlobalContext:context];
}

+ (void)addContext:(NSDictionary *)context forTag:(NSString *)tag {
    [[self sharedInstance] _addContext:context forTag:tag];
}

+ (void)removeKeys:(NSArray *)keys fromContextForTag:(NSString *)tag {
    [[self sharedInstance] _removeKeys:keys fromContextForTag:tag];
}

+ (void)resetContextForTag:(NSString *)tag {
    [[self sharedInstance] _resetContextForTag:tag];
}

#pragma mark - private

- (void)rnLog:(NSString *)message event:(NSDictionary *)content type:(MGOLogEventType)type {
    MGOLogEvent *event = [MGOLogEvent new];
    event.message = message;
    event.type = type;
    event.tag = content[@"tag"] ?: @"default";
    event.eventId = content[@"id"];
    event.params = content[@"params"];
    event.result = content[@"result"];
    event.error = content[@"error"];
    event.context = content[@"context"];
    
    [self _logEvent:event source:@"rn"];
}

- (NSString *)_stringFromEventType:(MGOLogEventType)type {
    switch (type) {
        case MGOLogEventTypeDebug: return @"DEBUG";
        case MGOLogEventTypeInfo: return @"INFO";
        case MGOLogEventTypeWarn: return @"WARN";
        case MGOLogEventTypeError: return @"ERROR";
    }
}

- (void)_logEvent:(MGOLogEvent *)event source:(NSString *)source {
    if (!_isEnabled) return;
    
    NSAssert(event.message != nil, @"message cannot be empty.");
    NSAssert(event.tag != nil, @"tag cannot be empty.");
    
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    
    dict[@"type"] = [self _stringFromEventType:event.type];
    dict[@"message"] = event.message;
    dict[@"tag"] = event.tag;
    dict[@"timestamp"] = @(event.timestamp);
    dict[@"source"] = source;
    
    if (event.eventId) {
        dict[@"eventId"] = event.eventId;
    }
    if (event.params) {
        dict[@"params"] = event.params;
    }
    if (event.result) {
        if ([event.result isKindOfClass:[NSObject class]] && [NSJSONSerialization isValidJSONObject:@{@"k":event.result}]) {
            dict[@"result"] = event.result;
        } else {
            dict[@"result"] = [event.result description];
        }
    }
    
    NSDictionary *mergedContext = [self _mergedContext:event.context forTag:event.tag];
    dict[@"context"] = mergedContext;
    
    NSDictionary *error = [self _serializedError:event.error];
    if (error) {
        dict[@"error"] = error;
    }
    
    dict[@"app_status"] = [self _getCurrentAppStatus];
    
    [[MGOLogCache sharedInstance] cacheLogEvent:dict];
}

- (void)_addGlobalContext:(NSDictionary *)context {
    if (!context) return;
    
    pthread_rwlock_wrlock(&_contextLock);
    [self.globalContexts addEntriesFromDictionary:context];
    pthread_rwlock_unlock(&_contextLock);
}

- (void)_addContext:(NSDictionary *)context forTag:(NSString *)tag {
    if (!tag || !context) return;
    
    pthread_rwlock_wrlock(&_contextLock);
    NSMutableDictionary *existingContext = [self.presetContexts[tag] mutableCopy];
    if (!existingContext) {
        existingContext = [NSMutableDictionary dictionary];
    }
    [existingContext addEntriesFromDictionary:context];
    self.presetContexts[tag] = [existingContext copy];
    pthread_rwlock_unlock(&_contextLock);
}

- (void)_removeKeys:(NSArray *)keys fromContextForTag:(NSString *)tag {
    if (!tag || !keys) return;
    
    pthread_rwlock_wrlock(&_contextLock);
    NSDictionary *originalContext = self.presetContexts[tag];
    if (originalContext) {
        NSMutableDictionary *mutableContext = [originalContext mutableCopy];
        [mutableContext removeObjectsForKeys:keys];
        self.presetContexts[tag] = [mutableContext copy];
    }
    pthread_rwlock_unlock(&_contextLock);
}

- (void)_resetContextForTag:(NSString *)tag {
    if (!tag) return;

    pthread_rwlock_wrlock(&_contextLock);
    [self.presetContexts removeObjectForKey:tag];
    pthread_rwlock_unlock(&_contextLock);
}

- (NSDictionary *)_mergedContext:(nullable NSDictionary *)context forTag:(NSString *)tag {
    pthread_rwlock_rdlock(&_contextLock);
    NSDictionary *globalContext = [self.globalContexts copy];
    NSDictionary *existingContext = [self.presetContexts[tag] copy] ?: @{};
    pthread_rwlock_unlock(&_contextLock);
    
    NSMutableDictionary *merged = [NSMutableDictionary dictionaryWithDictionary:globalContext];
    [merged addEntriesFromDictionary:existingContext];
    
    if (context) {
        [merged addEntriesFromDictionary:context];
    }
    
    return [merged copy];
}

- (NSDictionary *)_serializedError:(id)error {
    if (!error) {
        return nil;
    }
    
    if ([error isKindOfClass:[NSDictionary class]]) {
        return error;
    }
    
    if ([error isKindOfClass:[NSError class]]) {
        NSError *nsError = (NSError *)error;
        NSString *message = nsError.localizedDescription;
        
        if (!message || message.length == 0) {
            message = nsError.domain;
            if (!message || message.length == 0) {
                message = @"Unknown error";
            }
        }
        
        return @{
            @"code": @(nsError.code),
            @"message": message
        };
    }
    
    if ([error isKindOfClass:[NSString class]]) {
        NSString *errorMessage = (NSString *)error;
        
        if (!errorMessage || errorMessage.length == 0) {
            errorMessage = @"Unknown error";
        }
        
        return @{
            @"code": @(-1),
            @"message": errorMessage
        };
    }
    
    NSString *errorDescription = [error description];
    if (!errorDescription || errorDescription.length == 0) {
        errorDescription = @"Unknown error";
    }
    
    return @{
        @"code": @(-1),
        @"message": errorDescription
    };
}

#pragma mark - App State Tracking

/**
 * 设置应用状态通知监听
 */
- (void)_setupAppStateNotifications {
    NSNotificationCenter *center = [NSNotificationCenter defaultCenter];
    
    // 应用将要进入后台
    [center addObserver:self
               selector:@selector(_applicationDidEnterBackground:)
                   name:UIApplicationDidEnterBackgroundNotification
                 object:nil];
    
    // 应用将要进入前台
    [center addObserver:self
               selector:@selector(_applicationWillEnterForeground:)
                   name:UIApplicationWillEnterForegroundNotification
                 object:nil];
}

/**
 * 应用进入后台
 */
- (void)_applicationDidEnterBackground:(NSNotification *)notification {
    self.isAppInForeground = NO;
    self.isStateInitialized = YES;
}

/**
 * 应用将要进入前台
 */
- (void)_applicationWillEnterForeground:(NSNotification *)notification {
    self.isAppInForeground = YES;
    self.isStateInitialized = YES;
}

/**
 * 检测初始应用状态
 */
- (void)_detectInitialAppState {
    @try {
        UIApplication *application = [UIApplication sharedApplication];
        UIApplicationState currentState = application.applicationState;
        
        switch (currentState) {
            case UIApplicationStateActive:
                // 应用在前台且活跃
                self.isAppInForeground = YES;
                self.isStateInitialized = YES;
                break;
                
            case UIApplicationStateInactive:
                // 应用在前台但非活跃（例如接电话、通知中心下拉等）
                self.isAppInForeground = YES;
                self.isStateInitialized = YES;
                break;
                
            case UIApplicationStateBackground:
                // 应用在后台
                self.isAppInForeground = NO;
                self.isStateInitialized = YES;
                break;
                
            default:
                // 未知状态，使用默认值
                self.isAppInForeground = NO; // 安全起见，默认为后台
                self.isStateInitialized = NO; // 等待通知更新
                break;
        }
        
        NSLog(@"MGOLogger: Initial app state detected - %@", 
              self.isAppInForeground ? @"foreground" : @"background");
        
    } @catch (NSException *exception) {
        // 获取状态失败，使用默认值
        NSLog(@"MGOLogger: Failed to detect initial app state: %@", exception.reason);
        self.isAppInForeground = NO; // 安全起见，默认为后台
        self.isStateInitialized = NO; // 等待通知更新
    }
}

/**
 * 获取当前应用状态字符串
 */
- (NSString *)_getCurrentAppStatus {
    // 如果状态还没初始化，尝试重新检测
    if (!self.isStateInitialized) {
        [self _detectInitialAppState];
    }
    
    return self.isAppInForeground ? @"foreground" : @"background";
}

@end

//
//  MoegoLoggerReactNative.m
//  MoegoLogger
//
//  Created by wang<PERSON> on 2025/5/28.
//
#import "MoegoLoggerReactNative.h"
#import <React/RCTBridgeModule.h>
#import <React/RCTBridge.h>

#import "MGOLogger.h"

@implementation MoegoLoggerReactNative

#pragma mark - React Native

RCT_EXPORT_MODULE()

+ (BOOL)requiresMainQueueSetup {
    return YES;
}

RCT_EXPORT_METHOD(setLoggerEnabled:(BOOL)isEnabled) {
    [MGOLogger setLoggerEnabled:isEnabled];
}

RCT_EXPORT_METHOD(debug:(NSString*)message event:(NSDictionary*)event) {
    [MGOLogger rnLog:message event:event type:MGOLogEventTypeDebug];
}

RCT_EXPORT_METHOD(info:(NSString*)message event:(NSDictionary*)event) {
    [MGOLogger rnLog:message event:event type:MGOLogEventTypeInfo];
}

RCT_EXPORT_METHOD(warn:(NSString*)message event:(NSDictionary*)event) {
    [MGOLogger rnLog:message event:event type:MGOLogEventTypeWarn];
}

RCT_EXPORT_METHOD(error:(NSString*)message event:(NSDictionary*)event) {
    [MGOLogger rnLog:message event:event type:MGOLogEventTypeError];
}

RCT_EXPORT_METHOD(addGlobalContext:(NSDictionary*)context) {
    [MGOLogger addGlobalContext:context];
}

RCT_EXPORT_METHOD(addContext:(NSDictionary*)context forTag:(NSString*)tag) {
    [MGOLogger addContext:context forTag:tag];
}

RCT_EXPORT_METHOD(resetTagContext:(NSString *)tag) {
    [MGOLogger resetContextForTag:tag];
}

@end

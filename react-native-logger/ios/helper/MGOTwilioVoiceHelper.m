//
//  MGOTwilioVoiceHelper.m
//  DatadogCore
//
//  Created by wang<PERSON> on 2025/6/10.
//

#import "MGOTwilioVoiceHelper.h"
#import <AVFoundation/AVFoundation.h>

NSString * const kTwilioVoiceLoggerTag = @"call";

@implementation MGOTwilioVoiceHelper

+ (void)setIncomingContext:(NSDictionary *)payload {
    NSDictionary *context = [self _incomingPushPayloadToDict:payload];
    [MGOLogger addContext:context forTag:kTwilioVoiceLoggerTag];
}

+ (void)resetIncomingContext {
    NSArray *keys = @[@"twi_account_sid", @"twi_call_sid", @"twi_message_id", @"twi_from", @"twi_to",
                      @"customerId", @"clientId"];
    [MGOLogger removeKeys:keys fromContextForTag:kTwilioVoiceLoggerTag];
}

+ (NSString *)dataToHexString:(NSData *)data {
    if (!data || data.length == 0) {
        return @"";
    }
    
    const uint8_t *bytes = data.bytes;
    NSMutableString *hexString = [NSMutableString stringWithCapacity:data.length * 2];
    
    for (NSUInteger i = 0; i < data.length; i++) {
        [hexString appendFormat:@"%02x", bytes[i]];
    }
    
    return hexString;
}


+ (NSDictionary *)_incomingPushPayloadToDict:(NSDictionary *)payload {
    NSMutableDictionary *result = [NSMutableDictionary dictionary];

    // 解析 twi_params 字符串为 key-value 对
    NSString *paramsString = payload[@"twi_params"];
    NSArray *paramsPairs = [paramsString componentsSeparatedByString:@"&"];
    for (NSString *pair in paramsPairs) {
        NSArray *keyValue = [pair componentsSeparatedByString:@"="];
        if (keyValue.count == 2) {
            NSString *key = keyValue[0];
            NSString *value = keyValue[1];
            if (key && value) {
                result[key] = value;
            }
        }
    }

    // 逐个添加特定字段
    if (payload[@"twi_account_sid"]) {
        result[@"twi_account_sid"] = payload[@"twi_account_sid"];
    }
    if (payload[@"twi_call_sid"]) {
        result[@"twi_call_sid"] = payload[@"twi_call_sid"];
    }
    if (payload[@"twi_message_id"]) {
        result[@"twi_message_id"] = payload[@"twi_message_id"];
    }
    if (payload[@"twi_from"]) {
        result[@"twi_from"] = payload[@"twi_from"];
    }
    if (payload[@"twi_to"]) {
        result[@"twi_to"] = payload[@"twi_to"];
    }

    return [result copy];
}

+ (void)sendAudioStatusEvent {
    NSDictionary *info = [self _collectAudioInfo];
    
    MGOLogEvent *event = nil;
    if (info[@"error"]) {
        event = [MGOLogEvent errorWithMessage:@"current audio status"
                                      eventId:@"twilio_voice_current_audio_status"
                                       params:nil
                                        error:info[@"error"]
                                      context:nil
                                          tag:kTwilioVoiceLoggerTag];
    } else {
        event = [MGOLogEvent infoWithMessage:@"current audio status"
                                     eventId:@"twilio_voice_current_audio_status"
                                      params:nil
                                      result:info
                                     context:nil
                                         tag:kTwilioVoiceLoggerTag];
    }
    [MGOLogger logEvent: event];
}

+ (NSDictionary *)_collectAudioInfo {
    AVAudioSession *session = [AVAudioSession sharedInstance];
    NSMutableDictionary *deviceInfo = [NSMutableDictionary dictionary];

    // 检查是否有活跃的输出设备（不主动 setActive）
    if (!session.isOtherAudioPlaying && session.currentRoute.outputs.count == 0) {
        return @{
            @"error": @"AVAudioSession is not active or has no active audio route"
        };
    }

    // 1. 获取输出设备
    AVAudioSessionPortDescription *output = session.currentRoute.outputs.firstObject;
    AVAudioSessionPortDescription *input = session.currentRoute.inputs.firstObject;

    if (output) {
        deviceInfo[@"output"] = @{
            @"portName": output.portName ?: @"",
            @"portType": output.portType ?: @""
        };
    }

    if (input) {
        deviceInfo[@"input"] = @{
            @"portName": input.portName ?: @"",
            @"portType": input.portType ?: @""
        };
    }

    // 2. 判断当前模式（近似 Android 的 AudioManager mode）
    NSString *modeString = @"UNKNOWN";
    NSString *category = session.category;

    if ([category isEqualToString:AVAudioSessionCategoryPlayAndRecord]) {
        if ([output.portType isEqualToString:AVAudioSessionPortBuiltInReceiver] ||
            [output.portType isEqualToString:AVAudioSessionPortBluetoothHFP]) {
            modeString = @"IN_COMMUNICATION";
        } else {
            modeString = @"PLAY_AND_RECORD";
        }
    } else if ([category isEqualToString:AVAudioSessionCategoryPlayback]) {
        modeString = @"PLAYBACK";
    } else if ([category isEqualToString:AVAudioSessionCategoryRecord]) {
        modeString = @"RECORD";
    }

    deviceInfo[@"mode"] = modeString;
    deviceInfo[@"volume"] = @(session.outputVolume); // 媒体音量

    return deviceInfo;
}

@end

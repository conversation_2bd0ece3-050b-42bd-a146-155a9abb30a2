//
//  MGOTwilioVoiceHelper.h
//  DatadogCore
//
//  Created by wang<PERSON> on 2025/6/10.
//

#import <Foundation/Foundation.h>
#import "MGOLogger.h"

NS_ASSUME_NONNULL_BEGIN

#define TwilioVoiceLogInvoke() \
    do { \
        const char *funcName = __PRETTY_FUNCTION__; \
        NSString *msg = [NSString stringWithUTF8String:funcName]; \
        [MGOLogger message:msg params:nil tag:kTwilioVoiceLoggerTag]; \
    } while(0)

FOUNDATION_EXPORT NSString * const kTwilioVoiceLoggerTag;

@interface MGOTwilioVoiceHelper : NSObject

+ (void)setIncomingContext:(NSDictionary *)payload;

+ (void)resetIncomingContext;

+ (NSString *)dataToHexString:(NSData *)data;

+ (void)sendAudioStatusEvent;

@end

NS_ASSUME_NONNULL_END

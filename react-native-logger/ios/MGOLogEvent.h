//
//  MGOLogEvent.h
//  MoegoLogger
//
//  Created by wa<PERSON><PERSON> on 2025/6/10.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, MGOLogEventType) {
    MGOLogEventTypeDebug,
    MGOLogEventTypeInfo,
    MGOLogEventTypeWarn,
    MGOLogEventTypeError
};

@interface MGOLogEvent : NSObject

@property (nonatomic, copy) NSString *message;
@property (nonatomic, copy, nullable) NSString *eventId;
@property (nonatomic, strong, nullable) NSDictionary *params;
@property (nonatomic, strong, nullable) NSObject<NSCopying> *result;
@property (nonatomic, strong, nullable) id error; // NSString or NSError or NSDictionary
@property (nonatomic, strong, nullable) NSDictionary *context;
@property (nonatomic, copy) NSString *tag;
@property (nonatomic, assign) MGOLogEventType type;
@property (nonatomic, assign) NSTimeInterval timestamp; // 时间戳，毫秒

- (instancetype)initWithType:(MGOLogEventType)type
                     message:(NSString *)message
                    eventId:(nullable NSString *)eventId
                     params:(nullable NSDictionary *)params
                     result:(nullable NSObject<NSCopying> *)result
                      error:(nullable id)error
                    context:(nullable NSDictionary *)context
                        tag:(NSString *)tag;

+ (instancetype)debugWithMessage:(NSString *)message
                         eventId:(nullable NSString *)eventId
                          params:(nullable NSDictionary *)params
                          result:(nullable NSObject<NSCopying> *)result
                         context:(nullable NSDictionary *)context
                             tag:(NSString *)tag;

+ (instancetype)infoWithMessage:(NSString *)message
                        eventId:(nullable NSString *)eventId
                         params:(nullable NSDictionary *)params
                         result:(nullable NSObject<NSCopying> *)result
                        context:(nullable NSDictionary *)context
                            tag:(NSString *)tag;

+ (instancetype)warningWithMessage:(NSString *)message
                           eventId:(nullable NSString *)eventId
                            params:(nullable NSDictionary *)params
                            result:(nullable NSObject<NSCopying> *)result
                           context:(nullable NSDictionary *)context
                               tag:(NSString *)tag;

/// Error 类型日志（不带 result）
+ (instancetype)errorWithMessage:(NSString *)message
                         eventId:(nullable NSString *)eventId
                          params:(nullable NSDictionary *)params
                           error:(nullable id)error
                         context:(nullable NSDictionary *)context
                             tag:(NSString *)tag;

+ (instancetype)infoWithMessage:(NSString *)message
                        eventId:(nullable NSString *)eventId
                            tag:(NSString *)tag;

@end    

NS_ASSUME_NONNULL_END

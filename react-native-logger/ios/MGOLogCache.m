//
//  MGOLogCache.m
//  MoegoLogger
//
//  Created by wa<PERSON><PERSON> on 2025/6/12.
//

#import "MGOLogCache.h"
#import <MMKV/MMKV.h>
#import <DatadogSDKReactNative/DdLogs.h>
#import <React/RCTBridgeModule.h>

@interface DdLogs (MoegoLogger)
- (void)debug:(NSString *)message context:(NSDictionary *)context resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject;
- (void)info:(NSString *)message context:(NSDictionary *)context resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject;
- (void)error:(NSString *)message context:(NSDictionary *)context resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject;
- (void)warn:(NSString *)message context:(NSDictionary *)context resolve:(RCTPromiseResolveBlock)resolve reject:(RCTPromiseRejectBlock)reject;
@end

static NSString * const kLogMMKVID = @"moego_log_cache";

@interface MGOLogCache ()

@property (nonatomic, strong) MMKV *logMMKV;
@property (nonatomic, strong) dispatch_queue_t uploadQueue;
@property (nonatomic, strong, nullable) DdLogs *ddLogs;
@property (nonatomic, assign) BOOL isInitialized;
@property (nonatomic, assign) volatile BOOL isUploading;
@property (nonatomic, copy) RCTPromiseResolveBlock emptyResolveBlock;
@property (nonatomic, copy) RCTPromiseRejectBlock emptyRejectBlock;

@end

@implementation MGOLogCache

+ (instancetype)sharedInstance {
    static MGOLogCache *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        [MMKV initializeMMKV:nil];
        _logMMKV = [MMKV mmkvWithID:kLogMMKVID mode:MMKVSingleProcess];
        _uploadQueue = dispatch_queue_create("com.moego.logcache.uploader", DISPATCH_QUEUE_SERIAL);
        dispatch_set_target_queue(_uploadQueue, dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0));
        _isInitialized = NO;
        _isUploading = NO;
        _emptyResolveBlock = ^(id result) {};
        _emptyRejectBlock = ^(NSString *code, NSString *message, NSError *error) {};
    }
    return self;
}

- (void)bindWithDdLog:(nullable DdLogs *)logs {
    self.ddLogs = logs;
    self.isInitialized = (logs != nil);
    if (self.isInitialized) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(10 * NSEC_PER_SEC)), self.uploadQueue, ^{
            [self startUploadTask];
        });
    }
}

- (void)cacheLogEvent:(NSDictionary *)event {
    if (self.isInitialized) {
        dispatch_async(self.uploadQueue, ^{
            [self uploadSingleLogImmediately:event];
        });
    } else {
        [self saveToLogFile:event];
    }
}

- (void)uploadSingleLogImmediately:(NSDictionary *)event {
    if (!self.ddLogs) return;

    NSString *type = event[@"type"];
    NSString *message = event[@"message"];
    if (!type || !message) return;

    NSMutableDictionary *mutableEvent = [event mutableCopy];
    [mutableEvent removeObjectForKey:@"type"];
    [mutableEvent removeObjectForKey:@"message"];
    
    NSDictionary *context = @{@"event": mutableEvent};

    if ([type isEqualToString:@"INFO"]) {
        [self.ddLogs info:message context:context resolve:self.emptyResolveBlock reject:self.emptyRejectBlock];
    } else if ([type isEqualToString:@"WARN"]) {
        [self.ddLogs warn:message context:context resolve:self.emptyResolveBlock reject:self.emptyRejectBlock];
    } else if ([type isEqualToString:@"ERROR"]) {
        [self.ddLogs error:message context:context resolve:self.emptyResolveBlock reject:self.emptyRejectBlock];
    } else if ([type isEqualToString:@"DEBUG"]) {
        [self.ddLogs debug:message context:context resolve:self.emptyResolveBlock reject:self.emptyRejectBlock];
    }
}

- (void)saveToLogFile:(NSDictionary *)event {
    NSString *key = [NSString stringWithFormat:@"%f", [event[@"timestamp"] doubleValue]];
    NSError *error;
    NSData *data = [NSJSONSerialization dataWithJSONObject:event options:0 error:&error];
    if (data && !error) {
        [self.logMMKV setObject:data forKey:key];
    } else {
        NSLog(@"MGOLogCache: Failed to serialize log event for caching: %@", error);
    }
}

- (void)startUploadTask {
    if (self.isUploading) return;
    self.isUploading = YES;

    dispatch_async(self.uploadQueue, ^{
        @try {
            [self uploadAllCachedLogs];
        } @catch (NSException *exception) {
            NSLog(@"MGOLogCache: Upload thread error: %@", exception.reason);
        } @finally {
            self.isUploading = NO;
        }
    });
}

- (void)uploadAllCachedLogs {
    NSArray<NSString *> *keys = [self.logMMKV allKeys];
    if (!keys || keys.count == 0) return;
    
    NSArray<NSString *> *sortedKeys = [keys sortedArrayUsingSelector:@selector(compare:)];
    
    for (NSString *key in sortedKeys) {
        NSData *data = [self.logMMKV getObjectOfClass:[NSData class] forKey:key];
        if (data) {
            NSError *error;
            NSDictionary *eventDict = [NSJSONSerialization JSONObjectWithData:data options:0 error:&error];
            if (eventDict && !error) {
                [self uploadSingleLogImmediately:eventDict];
                [self.logMMKV removeValueForKey:key];
            } else {
                 [self.logMMKV removeValueForKey:key];
            }
        }
    }
}

@end

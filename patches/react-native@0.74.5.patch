diff --git a/Libraries/Text/TextInput/Multiline/RCTMultilineTextInputView.mm b/Libraries/Text/TextInput/Multiline/RCTMultilineTextInputView.mm
index e3a7c6be60eb16fb5d74cce7bf4b91e1dc611f54..f7d062872a94389f685a464789611552c96558ce 100644
--- a/Libraries/Text/TextInput/Multiline/RCTMultilineTextInputView.mm
+++ b/Libraries/Text/TextInput/Multiline/RCTMultilineTextInputView.mm
@@ -22,6 +22,10 @@ - (instancetype)initWithBridge:(RCTBridge *)bridge
     _backedTextInputView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
     _backedTextInputView.textInputDelegate = self;
 
+    if (@available(iOS 17.0, *)) {
+      _backedTextInputView.inlinePredictionType = UITextInlinePredictionTypeNo;
+    }
+
     [self addSubview:_backedTextInputView];
   }
 
diff --git a/ReactCommon/jsi/jsi/JSIDynamic.cpp b/ReactCommon/jsi/jsi/JSIDynamic.cpp
index 5a2d38d9dd39156321587d73c81e0811ba603e79..d3cc5851759ff06955f4f09ec6d2ba4e076bca5f 100644
--- a/ReactCommon/jsi/jsi/JSIDynamic.cpp
+++ b/ReactCommon/jsi/jsi/JSIDynamic.cpp
@@ -140,7 +140,13 @@ void dynamicFromValueShallow(
     }
     stack.emplace_back(&output, std::move(obj));
   } else if (value.isBigInt()) {
-    throw JSError(runtime, "JS BigInts are not convertible to dynamic");
+    // In debug builds, assert to help developers catch BigInt usage
+    DCHECK(false) << "JS BigInts are not convertible to dynamic. "
+                     "Consider converting BigInt to string or number before "
+                     "passing to native code.";
+    // In release builds, gracefully handle by substituting null
+    // (consistent with JSON.stringify behavior for BigInts)
+    output = nullptr;
   } else if (value.isSymbol()) {
     throw JSError(runtime, "JS Symbols are not convertible to dynamic");
   } else {

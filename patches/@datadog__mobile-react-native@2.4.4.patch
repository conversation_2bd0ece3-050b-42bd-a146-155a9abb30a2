diff --git a/android/src/main/kotlin/com/datadog/reactnative/DdSdkReactNativePackage.kt b/android/src/main/kotlin/com/datadog/reactnative/DdSdkReactNativePackage.kt
index 8fcf84a2f2064c63ae0b306b1483244b8143011c..d1f44e63304f1a87411acbf6b50bd627957723ec 100644
--- a/android/src/main/kotlin/com/datadog/reactnative/DdSdkReactNativePackage.kt
+++ b/android/src/main/kotlin/com/datadog/reactnative/DdSdkReactNativePackage.kt
@@ -11,19 +11,21 @@ import com.facebook.react.bridge.NativeModule
 import com.facebook.react.bridge.ReactApplicationContext
 import com.facebook.react.module.model.ReactModuleInfo
 import com.facebook.react.module.model.ReactModuleInfoProvider
+import android.util.Log
 
 /**
  * Package of native dd-sdk-reactnative native modules.
  */
 class DdSdkReactNativePackage : TurboReactPackage() {
     private val sdkWrapper = DatadogSDKWrapper()
+    private var ddLogs: DdLogs? = null
 
     override fun getModule(name: String, reactContext: ReactApplicationContext): NativeModule? {
         return when (name) {
             DdSdkImplementation.NAME -> DdSdk(reactContext, sdkWrapper)
             DdRumImplementation.NAME -> DdRum(reactContext, sdkWrapper)
             DdTraceImplementation.NAME -> DdTrace(reactContext)
-            DdLogsImplementation.NAME -> DdLogs(reactContext, sdkWrapper)
+            DdLogsImplementation.NAME -> getDdLogs(reactContext)
             else -> null
         }
     }
@@ -51,4 +53,19 @@ class DdSdkReactNativePackage : TurboReactPackage() {
             moduleInfos
         }
     }
+
+    private fun getDdLogs(reactContext: ReactApplicationContext): DdLogs {
+        if (ddLogs == null) {
+            val instance = DdLogs(reactContext, sdkWrapper)
+            try {
+                val loggerClass = Class.forName("com.moego.logger.MGOLogger")
+                val bindMethod = loggerClass.getMethod("bindWithDdLog", DdLogs::class.java)
+                bindMethod.invoke(null, instance)
+            } catch (e: Exception) {
+                Log.e("DdSdkReactNativePackage", "Failed to bind MGOLogger", e)
+            }
+            ddLogs = instance
+        }
+        return ddLogs!!
+    }
 }
diff --git a/ios/Sources/DdLogs.mm b/ios/Sources/DdLogs.mm
index 6cc530d8df24167607c6dcd03f3811898248bf4e..8cabbb4642f077585ebab694791a502b5f267180 100644
--- a/ios/Sources/DdLogs.mm
+++ b/ios/Sources/DdLogs.mm
@@ -105,6 +105,11 @@ - (DdLogsImplementation*)ddLogsImplementation
 {
     if (_ddLogsImplementation == nil) {
         _ddLogsImplementation = [[DdLogsImplementation alloc] init];
+        
+        // 绑定logger
+        Class loggerClass = NSClassFromString(@"MGOLogger");
+        SEL selector = NSSelectorFromString(@"bindWithDdLog:");
+        [loggerClass performSelector:selector withObject:self];
     }
     return _ddLogsImplementation;
 }

diff --git a/android/build.gradle b/android/build.gradle
index 4665e8dc108eef37b52da0fa44ba35292e58e15b..4ee0becafdc94f252539f1199be416a1e8272722 100644
--- a/android/build.gradle
+++ b/android/build.gradle
@@ -74,6 +74,8 @@ dependencies {
   implementation "com.google.firebase:firebase-messaging:${versions.firebaseMessaging}"
   implementation "com.twilio:audioswitch:${versions.audioSwitch}"
   implementation 'com.google.android.material:material:1.1.0'
+  implementation project(':expo-notifications')
+  implementation project(':moego_react-native-logger')
 
   constraints {
     implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0") {
diff --git a/android/src/main/java/com/twiliovoicereactnative/AudioSwitchManager.java b/android/src/main/java/com/twiliovoicereactnative/AudioSwitchManager.java
index 3086f10be2345e99e16d8fabcf17fad94e84f8c0..ca4795a719face66e9c3b57c5d90567b4ba4c003 100644
--- a/android/src/main/java/com/twiliovoicereactnative/AudioSwitchManager.java
+++ b/android/src/main/java/com/twiliovoicereactnative/AudioSwitchManager.java
@@ -9,6 +9,7 @@ import java.util.HashMap;
 import java.util.Map;
 import java.util.UUID;
 
+import static com.moego.logger.helper.MGOTwilioVoiceHelperKt.twilioVoiceLogInvoke;
 import static com.twiliovoicereactnative.CommonConstants.AudioDeviceKeyEarpiece;
 import static com.twiliovoicereactnative.CommonConstants.AudioDeviceKeySpeaker;
 import static com.twiliovoicereactnative.CommonConstants.AudioDeviceKeyBluetooth;
@@ -69,6 +70,7 @@ class AudioSwitchManager {
   }
 
   public void start() {
+    twilioVoiceLogInvoke("AudioSwitchManager start");
     audioSwitch.start((devices, selectedDevice) -> {
 
       audioDevices.clear();
@@ -88,6 +90,7 @@ class AudioSwitchManager {
   }
 
   public void stop() {
+    twilioVoiceLogInvoke("AudioSwitchManager stop");
     audioSwitch.stop();
   }
 
diff --git a/android/src/main/java/com/twiliovoicereactnative/CallListenerProxy.java b/android/src/main/java/com/twiliovoicereactnative/CallListenerProxy.java
index c88a2345e5af4e4b9ba7aeaffd05c54e3102f508..99e58d07f8ede59da39ffb2a777cc4d4965cece6 100644
--- a/android/src/main/java/com/twiliovoicereactnative/CallListenerProxy.java
+++ b/android/src/main/java/com/twiliovoicereactnative/CallListenerProxy.java
@@ -6,10 +6,16 @@ import android.util.Pair;
 import androidx.annotation.NonNull;
 import androidx.annotation.Nullable;
 
+import com.facebook.react.bridge.WritableArray;
 import com.facebook.react.bridge.WritableMap;
+import com.moego.logger.MGOLogEvent;
+import com.moego.logger.MGOLogger;
+import com.moego.logger.helper.MGOTwilioVoiceHelper;
 import com.twilio.voice.Call;
 import com.twilio.voice.CallException;
 
+import static com.moego.logger.helper.MGOTwilioVoiceHelperKt.TwilioVoiceLoggerTag;
+import static com.moego.logger.helper.MGOTwilioVoiceHelperKt.twilioVoiceLogInvoke;
 import static com.twiliovoicereactnative.CommonConstants.CallEventConnected;
 import static com.twiliovoicereactnative.CommonConstants.CallEventDisconnected;
 import static com.twiliovoicereactnative.CommonConstants.CallEventReconnected;
@@ -34,6 +40,8 @@ import static com.twiliovoicereactnative.ReactNativeArgumentsSerializer.*;
 import com.twiliovoicereactnative.CallRecordDatabase.CallRecord;
 
 import java.util.Date;
+import java.util.HashMap;
+import java.util.Map;
 import java.util.Objects;
 import java.util.Set;
 import java.util.UUID;
@@ -51,6 +59,7 @@ class CallListenerProxy implements Call.Listener {
   @Override
   public void onConnectFailure(@NonNull Call call, @NonNull CallException callException) {
     debug("onConnectFailure");
+    twilioVoiceLogInvoke("CallListenerProxy onConnectFailure");
 
     // stop sound and routing
     getMediaPlayerManager().stop();
@@ -62,6 +71,13 @@ class CallListenerProxy implements Call.Listener {
     // take down notification
     getVoiceServiceApi().cancelActiveCallNotification(callRecord);
 
+    MGOLogEvent event = MGOLogEvent
+            .errorBuilder("onConnect failure", "twilio_voice_call_connect_failure",
+                    TwilioVoiceLoggerTag)
+            .error(serializeVoiceException(callException))
+            .build();
+    MGOLogger.logEvent(event);
+
     // serialize and notify JS
     sendJSEvent(
       constructJSMap(
@@ -73,6 +89,7 @@ class CallListenerProxy implements Call.Listener {
   @Override
   public void onRinging(@NonNull Call call) {
     debug("onRinging");
+    twilioVoiceLogInvoke("CallListenerProxy onRinging");
 
     // find call record
     CallRecord callRecord = Objects.requireNonNull(getCallRecordDatabase().get(new CallRecord(uuid)));
@@ -94,6 +111,7 @@ class CallListenerProxy implements Call.Listener {
   @Override
   public void onConnected(@NonNull Call call) {
     debug("onConnected");
+    twilioVoiceLogInvoke("CallListenerProxy onConnected");
 
     // find call record
     CallRecord callRecord = Objects.requireNonNull(getCallRecordDatabase().get(new CallRecord(uuid)));
@@ -101,6 +119,13 @@ class CallListenerProxy implements Call.Listener {
     callRecord.setTimestamp(new Date());
     getMediaPlayerManager().stop();
 
+    MGOTwilioVoiceHelper.sendAudioStatusEvent(context);
+
+    MGOLogEvent event = MGOLogEvent
+            .infoBuilder("onConnected success", "twilio_voice_call_connect_success", TwilioVoiceLoggerTag)
+            .build();
+    MGOLogger.logEvent(event);
+
     // notify JS layer
     sendJSEvent(
       constructJSMap(
@@ -111,6 +136,11 @@ class CallListenerProxy implements Call.Listener {
   @Override
   public void onReconnecting(@NonNull Call call, @NonNull CallException callException) {
     debug("onReconnecting");
+    twilioVoiceLogInvoke("CallListenerProxy onReconnecting");
+    MGOLogEvent event = MGOLogEvent
+            .infoBuilder("onReconnecting", "twilio_voice_call_reconnecting", TwilioVoiceLoggerTag)
+            .build();
+    MGOLogger.logEvent(event);
 
     // find & update call record
     CallRecord callRecord = Objects.requireNonNull(getCallRecordDatabase().get(new CallRecord(uuid)));
@@ -126,6 +156,11 @@ class CallListenerProxy implements Call.Listener {
   @Override
   public void onReconnected(@NonNull Call call) {
     debug("onReconnected");
+    twilioVoiceLogInvoke("CallListenerProxy onReconnected");
+    MGOLogEvent event = MGOLogEvent
+            .infoBuilder("onReconnected", "twilio_voice_call_reconnected", TwilioVoiceLoggerTag)
+            .build();
+    MGOLogger.logEvent(event);
 
     // find & update call record
     CallRecord callRecord = Objects.requireNonNull(getCallRecordDatabase().get(new CallRecord(uuid)));
@@ -140,6 +175,8 @@ class CallListenerProxy implements Call.Listener {
   @Override
   public void onDisconnected(@NonNull Call call, @Nullable CallException callException) {
     debug("onDisconnected");
+    twilioVoiceLogInvoke("CallListenerProxy onDisconnected");
+    MGOTwilioVoiceHelper.sendAudioStatusEvent(context);
 
     // find & remove call record
     CallRecord callRecord = Objects.requireNonNull(getCallRecordDatabase().remove(new CallRecord(uuid)));
@@ -150,6 +187,21 @@ class CallListenerProxy implements Call.Listener {
     getAudioSwitchManager().getAudioSwitch().deactivate();
     getVoiceServiceApi().cancelActiveCallNotification(callRecord);
 
+    if (callException == null) {
+      MGOLogEvent event = MGOLogEvent
+              .infoBuilder("onDisconnected success", "twilio_voice_call_disconnect_success",
+                      TwilioVoiceLoggerTag)
+              .build();
+      MGOLogger.logEvent(event);
+    } else {
+      MGOLogEvent event = MGOLogEvent
+              .errorBuilder("onDisconnected failure", "twilio_voice_call_disconnect_failure",
+                      TwilioVoiceLoggerTag)
+              .error(serializeVoiceException(callException))
+              .build();
+      MGOLogger.logEvent(event);
+    }
+
     // notify JS layer
     sendJSEvent(
       constructJSMap(
@@ -164,16 +216,18 @@ class CallListenerProxy implements Call.Listener {
                                            @NonNull Set<Call.CallQualityWarning> previousWarnings) {
     debug("onCallQualityWarningsChanged");
 
-    // find call record
     CallRecord callRecord = Objects.requireNonNull(getCallRecordDatabase().get(new CallRecord(uuid)));
 
-    // notify JS layer
+    WritableArray currentArray = serializeCallQualityWarnings(currentWarnings);
+    WritableArray previousArray = serializeCallQualityWarnings(previousWarnings);
+
+    // Notify JS
     sendJSEvent(
-      constructJSMap(
-        new Pair<>(VoiceEventType, CallEventQualityWarningsChanged),
-        new Pair<>(JS_EVENT_KEY_CALL_INFO, serializeCall(callRecord)),
-        new Pair<>(CallEventCurrentWarnings, serializeCallQualityWarnings(currentWarnings)),
-        new Pair<>(CallEventPreviousWarnings, serializeCallQualityWarnings(previousWarnings))));
+            constructJSMap(
+                    new Pair<>(VoiceEventType, CallEventQualityWarningsChanged),
+                    new Pair<>(JS_EVENT_KEY_CALL_INFO, serializeCall(callRecord)),
+                    new Pair<>(CallEventCurrentWarnings, currentArray),
+                    new Pair<>(CallEventPreviousWarnings, previousArray)));
   }
 
   private void sendJSEvent(@NonNull WritableMap event) {
diff --git a/android/src/main/java/com/twiliovoicereactnative/CallMessageListenerProxy.java b/android/src/main/java/com/twiliovoicereactnative/CallMessageListenerProxy.java
index c4df165f73e72b12c785ce12af6774528ef692a1..9ebafdf3f803f8c4bc86509033f5239bb6e139ad 100644
--- a/android/src/main/java/com/twiliovoicereactnative/CallMessageListenerProxy.java
+++ b/android/src/main/java/com/twiliovoicereactnative/CallMessageListenerProxy.java
@@ -1,5 +1,6 @@
 package com.twiliovoicereactnative;
 
+import static com.moego.logger.helper.MGOTwilioVoiceHelperKt.twilioVoiceLogInvoke;
 import static com.twiliovoicereactnative.CommonConstants.CallEventMessageFailure;
 import static com.twiliovoicereactnative.CommonConstants.CallEventMessageReceived;
 import static com.twiliovoicereactnative.CommonConstants.CallEventMessageSent;
@@ -41,6 +42,7 @@ public class CallMessageListenerProxy implements Call.CallMessageListener {
   @Override
   public void onMessageFailure(String callSid, String voiceEventSID, VoiceException voiceException) {
     logger.debug("onMessageFailure");
+    twilioVoiceLogInvoke("CallMessageListenerProxy onMessageFailure");
 
     // notify JS layer
     sendJSEvent(
@@ -55,6 +57,7 @@ public class CallMessageListenerProxy implements Call.CallMessageListener {
   @Override
   public void onMessageSent(String callSid, String voiceEventSID) {
     logger.debug("onMessageSent");
+    twilioVoiceLogInvoke("CallMessageListenerProxy onMessageSent");
 
     // notify JS layer
     sendJSEvent(
@@ -67,6 +70,7 @@ public class CallMessageListenerProxy implements Call.CallMessageListener {
   @Override
   public void onMessageReceived(String callSid, CallMessage callMessage) {
     logger.debug("onMessageReceived");
+    twilioVoiceLogInvoke("CallMessageListenerProxy onMessageReceived");
 
     //final call record
     final CallRecord callRecord =
diff --git a/android/src/main/java/com/twiliovoicereactnative/JsInitializationStatus.java b/android/src/main/java/com/twiliovoicereactnative/JsInitializationStatus.java
new file mode 100644
index 0000000000000000000000000000000000000000..8e4d516743425399e214046b5986052bd4f18324
--- /dev/null
+++ b/android/src/main/java/com/twiliovoicereactnative/JsInitializationStatus.java
@@ -0,0 +1,48 @@
+package com.twiliovoicereactnative;
+
+import android.util.Log;
+import java.util.ArrayList;
+import java.util.List;
+
+public class JsInitializationStatus {
+    private static boolean isInitialized = false;
+    private static final List<JsInitializationListener> listeners = new ArrayList<>();
+
+    public interface JsInitializationListener {
+        void onInitialized();
+    }
+
+    public static synchronized boolean isInitialized() {
+        return isInitialized;
+    }
+
+    public static synchronized void setInitialized(boolean initialized) {
+        isInitialized = initialized;
+        if (initialized) {
+            notifyListeners();
+        }
+    }
+
+    public static synchronized void addListener(JsInitializationListener listener) {
+        if (isInitialized) {
+            listener.onInitialized();
+        } else {
+            listeners.add(listener);
+        }
+    }
+
+    public static synchronized void removeListener(JsInitializationListener listener) {
+        listeners.remove(listener);
+    }
+
+    private static void notifyListeners() {
+        for (JsInitializationListener listener : new ArrayList<>(listeners)) {
+            try {
+                listener.onInitialized();
+            } catch (Exception e) {
+                Log.e("JsInitializationStatus", "Error notifying listener", e);
+            }
+        }
+        listeners.clear();
+    }
+}
\ No newline at end of file
diff --git a/android/src/main/java/com/twiliovoicereactnative/MediaPlayerManager.java b/android/src/main/java/com/twiliovoicereactnative/MediaPlayerManager.java
index 68813977a24262321c4fd465e12093dbf8701bdb..ac5232872aa6ba8b55e84ec699acdbdd0c5bcc4e 100644
--- a/android/src/main/java/com/twiliovoicereactnative/MediaPlayerManager.java
+++ b/android/src/main/java/com/twiliovoicereactnative/MediaPlayerManager.java
@@ -1,5 +1,7 @@
 package com.twiliovoicereactnative;
 
+import static com.moego.logger.helper.MGOTwilioVoiceHelperKt.twilioVoiceLogInvoke;
+
 import android.content.Context;
 import android.media.AudioAttributes;
 import android.media.SoundPool;
@@ -36,6 +38,7 @@ class MediaPlayerManager {
   }
 
   public void play(final SoundTable sound) {
+    twilioVoiceLogInvoke("MediaPlayerManager play");
     activeStream = soundPool.play(
       soundMap.get(sound),
       1.f,
@@ -46,6 +49,7 @@ class MediaPlayerManager {
   }
 
   public void stop() {
+    twilioVoiceLogInvoke("MediaPlayerManager stop");
     soundPool.stop(activeStream);
     activeStream = 0;
   }
diff --git a/android/src/main/java/com/twiliovoicereactnative/NotificationUtility.java b/android/src/main/java/com/twiliovoicereactnative/NotificationUtility.java
index a2da776b532359d06d3013dabd188a25d046b830..8bfe9a61e5f0c32d064144d6a44acea12519937e 100644
--- a/android/src/main/java/com/twiliovoicereactnative/NotificationUtility.java
+++ b/android/src/main/java/com/twiliovoicereactnative/NotificationUtility.java
@@ -24,6 +24,7 @@ import androidx.core.app.Person;
 
 import com.twilio.voice.CallInvite;
 
+import static com.moego.logger.helper.MGOTwilioVoiceHelperKt.twilioVoiceLogInvoke;
 import static com.twiliovoicereactnative.Constants.VOICE_CHANNEL_DEFAULT_IMPORTANCE;
 import static com.twiliovoicereactnative.Constants.VOICE_CHANNEL_HIGH_IMPORTANCE;
 import static com.twiliovoicereactnative.Constants.VOICE_CHANNEL_LOW_IMPORTANCE;
@@ -135,6 +136,7 @@ class NotificationUtility {
   public static Notification createIncomingCallNotification(@NonNull Context context,
                                                             @NonNull final CallRecord callRecord,
                                                             @NonNull final String channelImportance) {
+    twilioVoiceLogInvoke("NotificationUtility createIncomingCallNotification");
     final NotificationResource notificationResource = new NotificationResource(
       context,
       NotificationResource.Type.INCOMING,
@@ -179,6 +181,7 @@ class NotificationUtility {
 
   public static Notification createCallAnsweredNotificationWithLowImportance(@NonNull Context context,
                                                                              @NonNull final CallRecord callRecord) {
+    twilioVoiceLogInvoke("NotificationUtility createCallAnsweredNotificationWithLowImportance");
     final NotificationResource notificationResource = new NotificationResource(
       context,
       NotificationResource.Type.ANSWERED,
@@ -216,6 +219,7 @@ class NotificationUtility {
 
   public static Notification createOutgoingCallNotificationWithLowImportance(@NonNull Context context,
                                                                              @NonNull final CallRecord callRecord) {
+    twilioVoiceLogInvoke("NotificationUtility createOutgoingCallNotificationWithLowImportance");
     final NotificationResource notificationResource = new NotificationResource(
       context,
       NotificationResource.Type.OUTGOING,
@@ -252,6 +256,7 @@ class NotificationUtility {
   }
 
   public static void createNotificationChannels(@NonNull Context context) {
+    twilioVoiceLogInvoke("NotificationUtility createNotificationChannels");
     NotificationManagerCompat notificationManager = NotificationManagerCompat.from(context);
     notificationManager.createNotificationChannelGroup(
       new NotificationChannelGroupCompat.Builder(Constants.VOICE_CHANNEL_GROUP)
@@ -268,6 +273,7 @@ class NotificationUtility {
   }
 
   public static void destroyNotificationChannels(@NonNull Context context) {
+    twilioVoiceLogInvoke("NotificationUtility destroyNotificationChannels");
     NotificationManagerCompat notificationManager = NotificationManagerCompat.from(context);
     notificationManager.deleteNotificationChannelGroup(Constants.VOICE_CHANNEL_GROUP);
   }
diff --git a/android/src/main/java/com/twiliovoicereactnative/ReactNativeArgumentsSerializer.java b/android/src/main/java/com/twiliovoicereactnative/ReactNativeArgumentsSerializer.java
index 5259cc7a1e1ce17c00a9423ad6f235c14aca1612..31234b83cc16b9d1d7b1803216e9723fdc9a7230 100644
--- a/android/src/main/java/com/twiliovoicereactnative/ReactNativeArgumentsSerializer.java
+++ b/android/src/main/java/com/twiliovoicereactnative/ReactNativeArgumentsSerializer.java
@@ -55,7 +55,10 @@ import com.twilio.audioswitch.AudioDevice;
 import com.twilio.voice.Call;
 import com.twilio.voice.CallInvite;
 
+import java.util.ArrayList;
 import java.util.Date;
+import java.util.HashMap;
+import java.util.List;
 import java.util.Locale;
 import java.util.Map;
 import java.util.Map.Entry;
@@ -188,6 +191,18 @@ class ReactNativeArgumentsSerializer {
     return null;
   }
 
+  public static Map<String, String> serializeAudioDeviceToMap(@Nullable AudioDevice audioDevice) {
+    if (audioDevice == null) return null;
+
+    String type = audioDevice.getClass().getSimpleName();
+
+    Map<String, String> result = new HashMap<>();
+    result.put(AudioDeviceKeyName, audioDevice.getName());
+    result.put(AudioDeviceKeyType, AudioSwitchManager.AUDIO_DEVICE_TYPE.get(type));
+
+    return result;
+  }
+
   /**
    * Serializes a map of UUIDs to AudioDevices into a list of [key, value] tuples.
    * @param audioDevices A map of UUIDs to AudioDevices
diff --git a/android/src/main/java/com/twiliovoicereactnative/TwilioVoiceReactNativeModule.java b/android/src/main/java/com/twiliovoicereactnative/TwilioVoiceReactNativeModule.java
index 921c71b7ad5d5a06926be18c34159d7d1d99e066..0e32f89e52e50885f94c91d090ab8ebe50031cd0 100644
--- a/android/src/main/java/com/twiliovoicereactnative/TwilioVoiceReactNativeModule.java
+++ b/android/src/main/java/com/twiliovoicereactnative/TwilioVoiceReactNativeModule.java
@@ -13,7 +13,12 @@ import com.facebook.react.bridge.ReadableType;
 import com.facebook.react.bridge.WritableArray;
 import com.facebook.react.bridge.WritableMap;
 import com.facebook.react.module.annotations.ReactModule;
+import com.google.android.gms.tasks.Tasks;
 import com.google.firebase.messaging.FirebaseMessaging;
+import com.google.firebase.installations.FirebaseInstallations;
+import com.moego.logger.MGOLogEvent;
+import com.moego.logger.MGOLogger;
+import com.moego.logger.helper.MGOTwilioVoiceHelper;
 import com.twilio.audioswitch.AudioDevice;
 import com.twilio.voice.Call;
 import com.twilio.voice.CallMessage;
@@ -27,7 +32,10 @@ import com.twilio.voice.Voice;
 import java.util.HashMap;
 import java.util.Map;
 import java.util.UUID;
+import java.util.concurrent.CancellationException;
 
+import static com.moego.logger.helper.MGOTwilioVoiceHelperKt.TwilioVoiceLoggerTag;
+import static com.moego.logger.helper.MGOTwilioVoiceHelperKt.twilioVoiceLogInvoke;
 import static com.twiliovoicereactnative.CommonConstants.ReactNativeVoiceSDK;
 import static com.twiliovoicereactnative.CommonConstants.ReactNativeVoiceSDKVer;
 import static com.twiliovoicereactnative.CommonConstants.VoiceEventType;
@@ -149,6 +157,11 @@ public class TwilioVoiceReactNativeModule extends ReactContextBaseJavaModule {
       @Override
       public void onRegistered(@NonNull String accessToken, @NonNull String fcmToken) {
         logger.log("Successfully registered FCM");
+        MGOLogEvent event = MGOLogEvent
+                .infoBuilder("Register success", "twilio_voice_register_success", TwilioVoiceLoggerTag)
+                .build();
+        MGOLogger.logEvent(event);
+
         sendJSEvent(constructJSMap(new Pair<>(VoiceEventType, VoiceEventRegistered)));
         promise.resolve(null);
       }
@@ -163,6 +176,13 @@ public class TwilioVoiceReactNativeModule extends ReactContextBaseJavaModule {
           registrationException.getMessage());
         logger.error(errorMessage);
 
+        MGOLogEvent event = MGOLogEvent
+                .errorBuilder("Failed to register", "twilio_voice_register_failure",
+                        TwilioVoiceLoggerTag)
+                .error(serializeVoiceException(registrationException))
+                .build();
+        MGOLogger.logEvent(event);
+
         sendJSEvent(constructJSMap(
           new Pair<>(VoiceEventType, VoiceEventError),
           new Pair<>(VoiceErrorKeyError, serializeVoiceException(registrationException))));
@@ -177,6 +197,11 @@ public class TwilioVoiceReactNativeModule extends ReactContextBaseJavaModule {
       @Override
       public void onUnregistered(String accessToken, String fcmToken) {
         logger.log("Successfully unregistered FCM");
+        MGOLogEvent event = MGOLogEvent
+                .infoBuilder("unregister success", "twilio_voice_unregister_success", TwilioVoiceLoggerTag)
+                .build();
+        MGOLogger.logEvent(event);
+
         sendJSEvent(constructJSMap(new Pair<>(VoiceEventType, VoiceEventUnregistered)));
         promise.resolve(null);
       }
@@ -190,6 +215,13 @@ public class TwilioVoiceReactNativeModule extends ReactContextBaseJavaModule {
           registrationException.getMessage());
         logger.error(errorMessage);
 
+        MGOLogEvent event = MGOLogEvent
+                .errorBuilder("Failed to unregister", "twilio_voice_unregister_failure",
+                        TwilioVoiceLoggerTag)
+                .error(serializeVoiceException(registrationException))
+                .build();
+        MGOLogger.logEvent(event);
+
         sendJSEvent(constructJSMap(
           new Pair<>(VoiceEventType, VoiceEventError),
           new Pair<>(VoiceErrorKeyError, serializeVoiceException(registrationException))));
@@ -207,6 +239,7 @@ public class TwilioVoiceReactNativeModule extends ReactContextBaseJavaModule {
     Promise promise
   ) {
     logger.debug(".voice_connect_android()");
+    twilioVoiceLogInvoke("TwilioVoiceReactNativeModule voice_connect_android");
 
     mainHandler.post(() -> {
       logger.debug(".voice_connect_android() > runnable");
@@ -355,6 +388,7 @@ public class TwilioVoiceReactNativeModule extends ReactContextBaseJavaModule {
 
   @ReactMethod
   public void voice_selectAudioDevice(String uuid, Promise promise) {
+    twilioVoiceLogInvoke("TwilioVoiceReactNativeModule voice_selectAudioDevice");
     AudioDevice audioDevice = audioSwitchManager.getAudioDevices().get(uuid);
     if (audioDevice == null) {
       promise.reject(reactContext.getString(R.string.missing_audiodevice_uuid, uuid));
@@ -364,10 +398,14 @@ public class TwilioVoiceReactNativeModule extends ReactContextBaseJavaModule {
     audioSwitchManager.getAudioSwitch().selectDevice(audioDevice);
 
     promise.resolve(null);
+
+    MGOTwilioVoiceHelper.setSelectedDevice(serializeAudioDeviceToMap(audioDevice));
+    MGOTwilioVoiceHelper.sendAudioStatusEvent(reactContext);
   }
 
   @ReactMethod
   public void voice_setIncomingCallContactHandleTemplate(String template, Promise promise) {
+    twilioVoiceLogInvoke("TwilioVoiceReactNativeModule voice_setIncomingCallContactHandleTemplate");
     ConfigurationProperties.setIncomingCallContactHandleTemplate(template);
     promise.resolve(null);
   }
@@ -394,6 +432,7 @@ public class TwilioVoiceReactNativeModule extends ReactContextBaseJavaModule {
   @ReactMethod
   public void call_isMuted(String uuid, Promise promise) {
     logger.debug(".call_isMuted()");
+    twilioVoiceLogInvoke("TwilioVoiceReactNativeModule call_isMuted");
 
     mainHandler.post(() -> {
       logger.debug(".call_isMuted() > runnable");
@@ -409,6 +448,7 @@ public class TwilioVoiceReactNativeModule extends ReactContextBaseJavaModule {
   @ReactMethod
   public void call_isOnHold(String uuid, Promise promise) {
     logger.debug(".call_isOnHold()");
+    twilioVoiceLogInvoke("TwilioVoiceReactNativeModule call_isOnHold");
 
     mainHandler.post(() -> {
       logger.debug(".call_isOnHold() > runnable");
@@ -424,6 +464,7 @@ public class TwilioVoiceReactNativeModule extends ReactContextBaseJavaModule {
   @ReactMethod
   public void call_disconnect(String uuid, Promise promise) {
     logger.debug(".call_disconnect()");
+    twilioVoiceLogInvoke("TwilioVoiceReactNativeModule call_disconnect");
 
     mainHandler.post(() -> {
       logger.debug(".call_disconnect() > runnable");
@@ -441,6 +482,7 @@ public class TwilioVoiceReactNativeModule extends ReactContextBaseJavaModule {
   @ReactMethod
   public void call_hold(String uuid, boolean hold, Promise promise) {
     logger.debug(".call_hold()");
+    twilioVoiceLogInvoke("TwilioVoiceReactNativeModule call_hold");
 
     mainHandler.post(() -> {
       logger.debug(".call_hold() > runnable");
@@ -457,6 +499,7 @@ public class TwilioVoiceReactNativeModule extends ReactContextBaseJavaModule {
   @ReactMethod
   public void call_mute(String uuid, boolean mute, Promise promise) {
     logger.debug(".call_mute()");
+    twilioVoiceLogInvoke("TwilioVoiceReactNativeModule call_mute");
 
     mainHandler.post(() -> {
       logger.debug(".call_mute() > runnable");
@@ -545,41 +588,33 @@ public class TwilioVoiceReactNativeModule extends ReactContextBaseJavaModule {
   @ReactMethod
   public void voice_register(String token, Promise promise) {
     logger.debug(".voice_register()");
+    twilioVoiceLogInvoke("TwilioVoiceReactNativeModule voice_register");
 
     mainHandler.post(() -> {
       logger.debug(".voice_register() > runnable");
 
-      FirebaseMessaging.getInstance().getToken()
-        .addOnCompleteListener(task -> {
-          if (!task.isSuccessful()) {
-            final String warningMsg =
-              reactContext.getString(R.string.fcm_token_registration_fail, task.getException());
-            logger.warning(warningMsg);
-            promise.reject(warningMsg);
-            return;
-          }
-
-          // Get new FCM registration token
-          String fcmToken = task.getResult();
-
-          if (fcmToken == null) {
-            final String warningMsg = reactContext.getString(R.string.fcm_token_null);
-            logger.warning(warningMsg);
-            promise.reject(warningMsg);
-            return;
-          }
-
-          // Log and toast
+      // 封装一个获取token的方法，支持重试
+      getFcmTokenWithRetry(new FcmTokenCallback() {
+        @Override
+        public void onSuccess(String fcmToken) {
           logger.debug("Registering with FCM with token " + fcmToken);
           RegistrationListener registrationListener = createRegistrationListener(promise);
           Voice.register(token, Voice.RegistrationChannel.FCM, fcmToken, registrationListener);
-        });
+        }
+
+        @Override
+        public void onFailure(String errorMsg, Exception e) {
+          logger.warning(errorMsg);
+          promise.reject(errorMsg, e);
+        }
+      });
     });
   }
 
   @ReactMethod
   public void voice_unregister(String token, Promise promise) {
     logger.debug(".voice_unregister()");
+    twilioVoiceLogInvoke("TwilioVoiceReactNativeModule voice_unregister");
 
     mainHandler.post(() -> {
       logger.debug(".voice_unregister() > runnable");
@@ -614,6 +649,7 @@ public class TwilioVoiceReactNativeModule extends ReactContextBaseJavaModule {
 
   @ReactMethod void voice_handleEvent(ReadableMap messageData, Promise promise) {
     logger.debug(".voice_handleEvent()");
+    twilioVoiceLogInvoke("TwilioVoiceReactNativeModule voice_handleEvent");
 
     mainHandler.post(() -> {
       logger.debug(".voice_handleEvent() > runnable");
@@ -650,6 +686,7 @@ public class TwilioVoiceReactNativeModule extends ReactContextBaseJavaModule {
   @ReactMethod
   public void callInvite_accept(String callInviteUuid, ReadableMap options, Promise promise) {
     logger.debug("callInvite_accept uuid" + callInviteUuid);
+    twilioVoiceLogInvoke("TwilioVoiceReactNativeModule callInvite_accept");
 
     mainHandler.post(() -> {
       logger.debug(".callInvite_accept() > runnable");
@@ -674,6 +711,7 @@ public class TwilioVoiceReactNativeModule extends ReactContextBaseJavaModule {
   @ReactMethod
   public void callInvite_reject(String uuid, Promise promise) {
     logger.debug("callInvite_reject uuid" + uuid);
+    twilioVoiceLogInvoke("TwilioVoiceReactNativeModule callInvite_reject");
 
     mainHandler.post(() -> {
       logger.debug(".callInvite_reject() > runnable");
@@ -739,4 +777,46 @@ public class TwilioVoiceReactNativeModule extends ReactContextBaseJavaModule {
   private void sendJSEvent(@NonNull WritableMap event) {
     getJSEventEmitter().sendEvent(ScopeVoice, event);
   }
+
+  private interface FcmTokenCallback {
+    void onSuccess(String fcmToken);
+    void onFailure(String errorMsg, Exception e);
+  }
+
+  // 支持重试的获取token方法
+  private void getFcmTokenWithRetry(final FcmTokenCallback callback) {
+    FirebaseMessaging.getInstance().getToken().addOnCompleteListener(task -> {
+      if (task.isSuccessful() && task.getResult() != null) {
+        callback.onSuccess(task.getResult());
+      } else {
+        Exception initialException = task.getException() != null ? task.getException() : new IllegalStateException("FCM token was null.");
+        logger.warning(initialException.toString());
+
+        // Chain tasks: Delete FID, then get a new token.
+        FirebaseInstallations.getInstance().delete()
+            .continueWithTask(deleteTask -> {
+              if (!deleteTask.isSuccessful()) {
+                Exception cause = deleteTask.getException();
+                if (cause == null) {
+                  cause = new CancellationException("Firebase Installation ID deletion task was cancelled.");
+                }
+                return Tasks.forException(cause);
+              }
+
+              return FirebaseMessaging.getInstance().getToken();
+            })
+            .addOnCompleteListener(retryTask -> {
+              if (retryTask.isSuccessful() && retryTask.getResult() != null) {
+                callback.onSuccess(retryTask.getResult());
+              } else {
+                String finalErrorMsg = "Fetching FCM registration token failed, even after retry.";
+                Exception finalException = retryTask.getException() != null ? retryTask.getException() : new IllegalStateException("FCM token was null after retry.");
+
+                callback.onFailure(finalErrorMsg, finalException);
+              }
+            });
+      }
+    });
+  }
+    
 }
diff --git a/android/src/main/java/com/twiliovoicereactnative/VoiceActivityProxy.java b/android/src/main/java/com/twiliovoicereactnative/VoiceActivityProxy.java
index afb7cadfe106fdc1123257ce04352ded5b13e14f..674c375d93055cca5946bac976ff25bad8c5939d 100644
--- a/android/src/main/java/com/twiliovoicereactnative/VoiceActivityProxy.java
+++ b/android/src/main/java/com/twiliovoicereactnative/VoiceActivityProxy.java
@@ -1,7 +1,15 @@
 package com.twiliovoicereactnative;
 
+import static com.moego.logger.helper.MGOTwilioVoiceHelperKt.TwilioVoiceLoggerTag;
+import static com.moego.logger.helper.MGOTwilioVoiceHelperKt.twilioVoiceLogInvoke;
+
+import java.util.HashMap;
 import java.util.List;
+import java.util.Map;
 import java.util.Vector;
+import java.util.Arrays;
+import java.util.HashSet;
+import java.util.Set;
 
 import android.Manifest;
 import android.app.Activity;
@@ -16,6 +24,8 @@ import androidx.annotation.NonNull;
 import androidx.core.app.ActivityCompat;
 import androidx.core.content.ContextCompat;
 
+import com.moego.logger.MGOLogger;
+
 public class VoiceActivityProxy {
   private static final SDKLog logger = new SDKLog(VoiceActivityProxy.class);
   private static final int PERMISSION_REQUEST_CODE = 101;
@@ -34,13 +44,14 @@ public class VoiceActivityProxy {
   }
   public void onCreate(Bundle ignoredSavedInstanceState) {
     logger.debug("onCreate(): invoked");
+    twilioVoiceLogInvoke("VoiceActivityProxy onCreate");
     // Ensure the microphone permission is enabled
     if (!checkPermissions()) {
       requestPermissions();
     }
     // These flags ensure that the activity can be launched when the screen is locked.
     Window window = context.getWindow();
-    window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
+    window.clearFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
       | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
       | WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
     // handle any incoming intents
@@ -48,12 +59,15 @@ public class VoiceActivityProxy {
   }
   public void onDestroy() {
     logger.debug("onDestroy(): invoked");
+    twilioVoiceLogInvoke("VoiceActivityProxy onDestroy");
   }
   public void onNewIntent(Intent intent) {
     logger.debug("onNewIntent(...): invoked");
+    twilioVoiceLogInvoke("VoiceActivityProxy onNewIntent");
     handleIntent(intent);
   }
   private void requestPermissions() {
+    twilioVoiceLogInvoke("VoiceActivityProxy requestPermissions");
     List<String> permissionsRequestList = new Vector<>();
     for (final String permission: VoiceActivityProxy.permissionList) {
       permissionsRequestList.add(permission);
@@ -66,7 +80,9 @@ public class VoiceActivityProxy {
       permissionsRequestList.toArray(new String[0]),
       PERMISSION_REQUEST_CODE);
   }
+
   private boolean checkPermissions() {
+    twilioVoiceLogInvoke("VoiceActivityProxy checkPermissions");
     for (String permission: VoiceActivityProxy.permissionList) {
       if (PackageManager.PERMISSION_GRANTED !=
         ContextCompat.checkSelfPermission(context, permission)) {
@@ -76,12 +92,49 @@ public class VoiceActivityProxy {
     return true;
   }
   private void handleIntent(Intent intent) {
+    if (intent == null) {
+      logger.debug("handleIntent: Received null intent. Will not start VoiceService.");
+      return;
+    }
     String action = intent.getAction();
-    if ((null != action) && (!action.equals(Constants.ACTION_PUSH_APP_TO_FOREGROUND))) {
+
+    HashMap<String, Object> params = new HashMap<>();
+    if (action != null) {
+      params.put("action", action);
+    }
+    MGOLogger.message("VoiceActivityProxy handleIntent", params, TwilioVoiceLoggerTag);
+
+    // Define allowed actions (whitelist)
+    Set<String> allowedActions = new HashSet<>(Arrays.asList(
+      Constants.ACTION_ACCEPT_CALL,
+      Constants.ACTION_REJECT_CALL,
+      Constants.ACTION_CANCEL_ACTIVE_CALL_NOTIFICATION,
+      Constants.ACTION_INCOMING_CALL,
+      Constants.ACTION_CANCEL_CALL,
+      Constants.ACTION_CALL_DISCONNECT,
+      Constants.ACTION_RAISE_OUTGOING_CALL_NOTIFICATION,
+      Constants.ACTION_FOREGROUND_AND_DEPRIORITIZE_INCOMING_CALL_NOTIFICATION,
+      Constants.ACTION_PUSH_APP_TO_FOREGROUND
+    ));
+    if (!allowedActions.contains(action)) {
+        logger.debug("handleIntent: Received unknown or disallowed action: " + action + ". Will not start VoiceService.");
+        return;
+    }
+    if (!action.equals(Constants.ACTION_PUSH_APP_TO_FOREGROUND)) {
       Intent copiedIntent = new Intent(intent);
       copiedIntent.setClass(context.getApplicationContext(), VoiceService.class);
       copiedIntent.setFlags(0);
-      context.getApplicationContext().startService(copiedIntent);
+
+      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && 
+          (action.equals(Constants.ACTION_RAISE_OUTGOING_CALL_NOTIFICATION) || 
+           action.equals(Constants.ACTION_ACCEPT_CALL))) {
+        // 针对 Android 12 及以上的来电和前台服务请求使用 startForegroundService
+        twilioVoiceLogInvoke("onNewIntent startForegroundService");
+        context.getApplicationContext().startForegroundService(copiedIntent);
+      } else {
+        twilioVoiceLogInvoke("onNewIntent startService");
+        context.getApplicationContext().startService(copiedIntent);
+      }
     }
   }
 
diff --git a/android/src/main/java/com/twiliovoicereactnative/VoiceApplicationProxy.java b/android/src/main/java/com/twiliovoicereactnative/VoiceApplicationProxy.java
index be2aae0e9feaf3fb42049ed06b425b9ca27f9b10..67166e48fb388488c1b0f6df201358d526e35aad 100644
--- a/android/src/main/java/com/twiliovoicereactnative/VoiceApplicationProxy.java
+++ b/android/src/main/java/com/twiliovoicereactnative/VoiceApplicationProxy.java
@@ -1,5 +1,6 @@
 package com.twiliovoicereactnative;
 
+import static com.moego.logger.helper.MGOTwilioVoiceHelperKt.twilioVoiceLogInvoke;
 import static com.twiliovoicereactnative.CallRecordDatabase.CallRecord;
 
 import java.util.List;
@@ -12,7 +13,7 @@ import android.content.Intent;
 import android.content.ServiceConnection;
 import android.os.IBinder;
 
-import com.facebook.react.ReactNativeHost;
+import com.facebook.react.defaults.DefaultReactNativeHost;
 import com.facebook.react.ReactPackage;
 
 public class VoiceApplicationProxy {
@@ -27,15 +28,22 @@ public class VoiceApplicationProxy {
   private final ServiceConnection voiceServiceObserver = new ServiceConnection() {
     @Override
     public void onServiceConnected(ComponentName name, IBinder service) {
-      if (name.getClassName().equals(VoiceService.class.getName()))
-        voiceServiceApi = (VoiceService.VoiceServiceAPI)service;
+      twilioVoiceLogInvoke("VoiceApplicationProxy onServiceConnected");
+
+      try {
+        if (name.getClassName().equals(VoiceService.class.getName()))
+          voiceServiceApi = (VoiceService.VoiceServiceAPI)service;
+      } catch (Exception e) {
+        logger.error("Error binding to VoiceService: " + e.getMessage());
+      }
     }
     @Override
     public void onServiceDisconnected(ComponentName name) {
+      twilioVoiceLogInvoke("VoiceApplicationProxy onServiceDisconnected");
       voiceServiceApi = null;
     }
   };
-  public abstract static class VoiceReactNativeHost extends ReactNativeHost {
+  public abstract static class VoiceReactNativeHost extends DefaultReactNativeHost {
     public VoiceReactNativeHost(Application application) {
       super(application);
     }
@@ -64,6 +72,7 @@ public class VoiceApplicationProxy {
   }
   public void onCreate() {
     logger.debug("onCreate(..) invoked");
+    twilioVoiceLogInvoke("VoiceApplicationProxy onCreate");
     // construct JS event engine
     jsEventEmitter = new JSEventEmitter();
     // construct notification channels
@@ -80,6 +89,7 @@ public class VoiceApplicationProxy {
   }
   public void onTerminate() {
     logger.debug("onTerminate(..) invoked");
+    twilioVoiceLogInvoke("VoiceApplicationProxy onTerminate");
     // shutdown notificaiton channels
     NotificationUtility.destroyNotificationChannels(context);
     // shutdown audioswitch & media manager
diff --git a/android/src/main/java/com/twiliovoicereactnative/VoiceFirebaseMessagingService.java b/android/src/main/java/com/twiliovoicereactnative/VoiceFirebaseMessagingService.java
index ae9060e376d9758472ed0dbfd43b271bdeab371e..a129186a6befbbf3f2194e4cd21fcb4c4c84da5b 100644
--- a/android/src/main/java/com/twiliovoicereactnative/VoiceFirebaseMessagingService.java
+++ b/android/src/main/java/com/twiliovoicereactnative/VoiceFirebaseMessagingService.java
@@ -1,10 +1,15 @@
 package com.twiliovoicereactnative;
 
+import com.moego.logger.MGOLogEvent;
+import com.moego.logger.MGOLogger;
+import com.moego.logger.helper.MGOTwilioVoiceHelper;
+
+import static com.moego.logger.helper.MGOTwilioVoiceHelperKt.TwilioVoiceLoggerTag;
+import static com.moego.logger.helper.MGOTwilioVoiceHelperKt.twilioVoiceLogInvoke;
 import static com.twiliovoicereactnative.VoiceApplicationProxy.getCallRecordDatabase;
 import static com.twiliovoicereactnative.VoiceApplicationProxy.getVoiceServiceApi;
 
 import com.twiliovoicereactnative.CallRecordDatabase.CallRecord;
-
 import android.os.PowerManager;
 import androidx.annotation.NonNull;
 import androidx.annotation.Nullable;
@@ -17,16 +22,37 @@ import com.twilio.voice.CancelledCallInvite;
 import com.twilio.voice.MessageListener;
 import com.twilio.voice.Voice;
 
+import expo.modules.notifications.service.delegates.FirebaseMessagingDelegate;
+
 import java.util.Objects;
 import java.util.UUID;
 
 public class VoiceFirebaseMessagingService extends FirebaseMessagingService {
   private static final SDKLog logger = new SDKLog(VoiceFirebaseMessagingService.class);
 
+  private volatile FirebaseMessagingDelegate firebaseMessagingDelegate;
+
+  protected FirebaseMessagingDelegate getFirebaseMessagingDelegate() {
+    if(firebaseMessagingDelegate == null) {
+      synchronized (this) {
+        if (firebaseMessagingDelegate == null) {
+          firebaseMessagingDelegate = new FirebaseMessagingDelegate(this);
+        }
+      }
+    }
+    return firebaseMessagingDelegate;
+  }
+
   public static class MessageHandler implements MessageListener  {
     @Override
     public void onCallInvite(@NonNull CallInvite callInvite) {
       logger.log(String.format("onCallInvite %s", callInvite.getCallSid()));
+      twilioVoiceLogInvoke("VoiceFirebaseMessagingService onCallInvite");
+
+      MGOLogEvent event = MGOLogEvent
+              .infoBuilder("onMessageReceived", "twilio_voice_call_invite_received", TwilioVoiceLoggerTag)
+              .build();
+      MGOLogger.logEvent(event);
 
       final CallRecord callRecord = new CallRecord(UUID.randomUUID(), callInvite);
 
@@ -38,6 +64,7 @@ public class VoiceFirebaseMessagingService extends FirebaseMessagingService {
     public void onCancelledCallInvite(@NonNull CancelledCallInvite cancelledCallInvite,
                                       @Nullable CallException callException) {
       logger.log(String.format("onCancelledCallInvite %s", cancelledCallInvite.getCallSid()));
+      twilioVoiceLogInvoke("VoiceFirebaseMessagingService onCancelledCallInvite");
 
       CallRecord callRecord = Objects.requireNonNull(
         getCallRecordDatabase().remove(new CallRecord(cancelledCallInvite.getCallSid())));
@@ -51,6 +78,14 @@ public class VoiceFirebaseMessagingService extends FirebaseMessagingService {
   @Override
   public void onNewToken(@NonNull String token) {
     logger.log("Refreshed FCM token: " + token);
+    twilioVoiceLogInvoke("VoiceFirebaseMessagingService onNewToken");
+    getFirebaseMessagingDelegate().onNewToken(token);
+  }
+
+  @Override
+  public void onDeletedMessages() {
+    twilioVoiceLogInvoke("VoiceFirebaseMessagingService onDeletedMessages");
+    getFirebaseMessagingDelegate().onDeletedMessages();
   }
 
   /**
@@ -64,6 +99,14 @@ public class VoiceFirebaseMessagingService extends FirebaseMessagingService {
     logger.debug("Bundle data: " + remoteMessage.getData());
     logger.debug("From: " + remoteMessage.getFrom());
 
+    MGOTwilioVoiceHelper.setIncomingContext(remoteMessage.getData());
+    twilioVoiceLogInvoke("VoiceFirebaseMessagingService onMessageReceived");
+
+    MGOLogEvent event = MGOLogEvent
+            .infoBuilder("onMessageReceived", "twilio_voice_incoming_push_received", TwilioVoiceLoggerTag)
+            .build();
+    MGOLogger.logEvent(event);
+
     PowerManager pm = (PowerManager)getSystemService(POWER_SERVICE);
     boolean isScreenOn = pm.isInteractive(); // check if screen is on
     if (!isScreenOn) {
@@ -80,9 +123,13 @@ public class VoiceFirebaseMessagingService extends FirebaseMessagingService {
         remoteMessage.getData(),
         new MessageHandler(),
         new CallMessageListenerProxy())) {
-        logger.error("The message was not a valid Twilio Voice SDK payload: " +
+        logger.debug("The message was resolved by expo notification " +
           remoteMessage.getData());
+        getFirebaseMessagingDelegate().onMessageReceived(remoteMessage);
       }
     }
+
+    super.onMessageReceived(remoteMessage); // 如果你有父类逻辑需要调用
   }
+
 }
diff --git a/android/src/main/java/com/twiliovoicereactnative/VoiceService.java b/android/src/main/java/com/twiliovoicereactnative/VoiceService.java
index 3eff434bf45479eaacaa69b4aeb8ae400d0dec61..c291242f617cb3e9d2d7cc67b86adeedd827eeba 100644
--- a/android/src/main/java/com/twiliovoicereactnative/VoiceService.java
+++ b/android/src/main/java/com/twiliovoicereactnative/VoiceService.java
@@ -1,6 +1,7 @@
 package com.twiliovoicereactnative;
 
-
+import static com.moego.logger.helper.MGOTwilioVoiceHelperKt.TwilioVoiceLoggerTag;
+import static com.moego.logger.helper.MGOTwilioVoiceHelperKt.twilioVoiceLogInvoke;
 import static com.twiliovoicereactnative.CommonConstants.CallInviteEventKeyCallSid;
 import static com.twiliovoicereactnative.CommonConstants.CallInviteEventKeyType;
 import static com.twiliovoicereactnative.CommonConstants.CallInviteEventTypeValueAccepted;
@@ -53,6 +54,9 @@ import androidx.core.app.ActivityCompat;
 import androidx.core.app.ServiceCompat;
 
 import com.facebook.react.bridge.WritableMap;
+import com.moego.logger.MGOLogEvent;
+import com.moego.logger.MGOLogger;
+import com.moego.logger.helper.MGOTwilioVoiceHelper;
 import com.twilio.voice.AcceptOptions;
 import com.twilio.voice.Call;
 import com.twilio.voice.ConnectOptions;
@@ -63,36 +67,47 @@ import java.util.UUID;
 
 public class VoiceService extends Service {
   private static final SDKLog logger = new SDKLog(VoiceService.class);
+
   public class VoiceServiceAPI extends Binder {
     public Call connect(@NonNull ConnectOptions cxnOptions,
-                        @NonNull Call.Listener listener) {
+        @NonNull Call.Listener listener) {
       logger.debug("connect");
+      twilioVoiceLogInvoke("VoiceService connect");
       return Voice.connect(VoiceService.this, cxnOptions, listener);
     }
+
     public void disconnect(final CallRecordDatabase.CallRecord callRecord) {
       VoiceService.this.disconnect(callRecord);
     }
+
     public void incomingCall(final CallRecordDatabase.CallRecord callRecord) {
       VoiceService.this.incomingCall(callRecord);
     }
+
     public void acceptCall(final CallRecordDatabase.CallRecord callRecord) {
       VoiceService.this.acceptCall(callRecord);
     }
+
     public void rejectCall(final CallRecordDatabase.CallRecord callRecord) {
       VoiceService.this.rejectCall(callRecord);
     }
+
     public void cancelCall(final CallRecordDatabase.CallRecord callRecord) {
       VoiceService.this.cancelCall(callRecord);
     }
+
     public void raiseOutgoingCallNotification(final CallRecordDatabase.CallRecord callRecord) {
       VoiceService.this.raiseOutgoingCallNotification(callRecord);
     }
+
     public void cancelActiveCallNotification(final CallRecordDatabase.CallRecord callRecord) {
       VoiceService.this.cancelActiveCallNotification(callRecord);
     }
+
     public void foregroundAndDeprioritizeIncomingCallNotification(final CallRecordDatabase.CallRecord callRecord) {
       VoiceService.this.foregroundAndDeprioritizeIncomingCallNotification(callRecord);
     }
+
     public Context getServiceContext() {
       return VoiceService.this;
     }
@@ -100,39 +115,60 @@ public class VoiceService extends Service {
 
   @Override
   public int onStartCommand(Intent intent, int flags, int startId) {
-    // apparently the system can recreate the service without sending it an intent so protect
+    if (intent != null) {
+      twilioVoiceLogInvoke("VoiceService onStartCommand with intent: " + intent.getAction());
+    } else {
+      twilioVoiceLogInvoke("VoiceService onStartCommand with null intent");
+    }
+    // apparently the system can recreate the service without sending it an intent
+    // so protect
     // against that case (GH-430).
     if (null != intent) {
-      switch (Objects.requireNonNull(intent.getAction())) {
+      String action = intent.getAction();
+
+      UUID uuid = getMessageUUID(intent);
+      CallRecordDatabase.CallRecord record = getCallRecord(uuid);
+      if (record == null && !Objects.equals(action, ACTION_PUSH_APP_TO_FOREGROUND)) {
+        logger.warning("Missing UUID for action: " + action + ", ignoring");
+        MGOLogEvent event = MGOLogEvent
+                .errorBuilder("Missing UUID for action: " + action+ ", ignore call",
+                        "twilio_voice_incoming_ignore",
+                        TwilioVoiceLoggerTag)
+                .build();
+        MGOLogger.logEvent(event);
+        return START_NOT_STICKY;
+      }
+
+      logger.debug("VoiceService onStartCommand with intent: " + intent.getAction());
+      switch (Objects.requireNonNull(action)) {
         case ACTION_INCOMING_CALL:
-          incomingCall(getCallRecord(Objects.requireNonNull(getMessageUUID(intent))));
+          incomingCall(record);
           break;
         case ACTION_ACCEPT_CALL:
           try {
-            acceptCall(getCallRecord(Objects.requireNonNull(getMessageUUID(intent))));
+            acceptCall(record);
           } catch (SecurityException e) {
             sendPermissionsError();
             logger.warning(e, "Cannot accept call, lacking necessary permissions");
           }
           break;
         case ACTION_REJECT_CALL:
-          rejectCall(getCallRecord(Objects.requireNonNull(getMessageUUID(intent))));
+          rejectCall(record);
           break;
         case ACTION_CANCEL_CALL:
-          cancelCall(getCallRecord(Objects.requireNonNull(getMessageUUID(intent))));
+          cancelCall(record);
           break;
         case ACTION_CALL_DISCONNECT:
-          disconnect(getCallRecord(Objects.requireNonNull(getMessageUUID(intent))));
+          disconnect(record);
           break;
         case ACTION_RAISE_OUTGOING_CALL_NOTIFICATION:
-          raiseOutgoingCallNotification(getCallRecord(Objects.requireNonNull(getMessageUUID(intent))));
+          raiseOutgoingCallNotification(record);
           break;
         case ACTION_CANCEL_ACTIVE_CALL_NOTIFICATION:
-          cancelActiveCallNotification(getCallRecord(Objects.requireNonNull(getMessageUUID(intent))));
+          cancelActiveCallNotification(record);
           break;
         case ACTION_FOREGROUND_AND_DEPRIORITIZE_INCOMING_CALL_NOTIFICATION:
-          foregroundAndDeprioritizeIncomingCallNotification(
-            getCallRecord(Objects.requireNonNull(getMessageUUID(intent))));
+          foregroundAndDeprioritizeIncomingCallNotification(record);
           break;
         case ACTION_PUSH_APP_TO_FOREGROUND:
           logger.warning("VoiceService received foreground request, ignoring");
@@ -147,36 +183,51 @@ public class VoiceService extends Service {
 
   @Override
   public IBinder onBind(Intent intent) {
+    twilioVoiceLogInvoke("VoiceService onBind");
     return new VoiceServiceAPI();
   }
+
   public static Intent constructMessage(@NonNull Context context,
-                                        @NonNull final String action,
-                                        @NonNull final Class<?> target,
-                                        @NonNull final UUID uuid) {
+      @NonNull final String action,
+      @NonNull final Class<?> target,
+      @NonNull final UUID uuid) {
     Intent intent = new Intent(context.getApplicationContext(), target);
     intent.setAction(action);
     intent.putExtra(Constants.MSG_KEY_UUID, uuid);
     return intent;
   }
+
   private void disconnect(final CallRecordDatabase.CallRecord callRecord) {
     logger.debug("disconnect");
+    twilioVoiceLogInvoke("VoiceService disconnect");
     if (null != callRecord) {
       Objects.requireNonNull(callRecord.getVoiceCall()).disconnect();
     } else {
       logger.warning("No call record found");
     }
+
+    MGOTwilioVoiceHelper.resetIncomingContext();
   }
+
   private void incomingCall(final CallRecordDatabase.CallRecord callRecord) {
     logger.debug("incomingCall: " + callRecord.getUuid());
+    twilioVoiceLogInvoke("VoiceService incomingCall");
 
     // verify that mic permissions have been granted and if not, throw a error
     if ((Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) &&
-      ActivityCompat.checkSelfPermission(VoiceService.this,
-        Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
+        ActivityCompat.checkSelfPermission(VoiceService.this,
+            Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
 
       // report to js layer lack of permissions issue
       sendPermissionsError();
 
+      MGOLogEvent event = MGOLogEvent
+              .errorBuilder("Incoming call cannot be handled, microphone permission not granted ",
+                      "twilio_voice_incoming_ignore",
+                      TwilioVoiceLoggerTag)
+              .build();
+      MGOLogger.logEvent(event);
+
       // report an error to logger
       logger.warning("WARNING: Incoming call cannot be handled, microphone permission not granted");
       return;
@@ -185,12 +236,16 @@ public class VoiceService extends Service {
     // put up notification
     callRecord.setNotificationId(NotificationUtility.createNotificationIdentifier());
     Notification notification = NotificationUtility.createIncomingCallNotification(
-      VoiceService.this,
-      callRecord,
-      VOICE_CHANNEL_HIGH_IMPORTANCE);
+            VoiceService.this,
+            callRecord,
+            VOICE_CHANNEL_HIGH_IMPORTANCE);
     createOrReplaceNotification(callRecord.getNotificationId(), notification);
 
-
+    MGOLogEvent event = MGOLogEvent
+            .infoBuilder("show incomingCall notification", "twilio_voice_show_incoming_call_success",
+                    TwilioVoiceLoggerTag)
+            .build();
+    MGOLogger.logEvent(event);
 
     // play ringer sound
     VoiceApplicationProxy.getAudioSwitchManager().getAudioSwitch().activate();
@@ -198,17 +253,19 @@ public class VoiceService extends Service {
 
     // trigger JS layer
     sendJSEvent(
-      ScopeVoice,
-      constructJSMap(
-        new Pair<>(VoiceEventType, VoiceEventTypeValueIncomingCallInvite),
-        new Pair<>(JS_EVENT_KEY_CALL_INVITE_INFO, serializeCallInvite(callRecord))));
-  }
+            ScopeVoice,
+            constructJSMap(
+                    new Pair<>(VoiceEventType, VoiceEventTypeValueIncomingCallInvite),
+                    new Pair<>(JS_EVENT_KEY_CALL_INVITE_INFO, serializeCallInvite(callRecord))));
+  };
+
   private void acceptCall(final CallRecordDatabase.CallRecord callRecord) {
     logger.debug("acceptCall: " + callRecord.getUuid());
+    twilioVoiceLogInvoke("VoiceService acceptCall");
 
     // verify that mic permissions have been granted and if not, throw a error
     if (ActivityCompat.checkSelfPermission(VoiceService.this,
-      Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
+        Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
       // cancel incoming call notification
       removeNotification(callRecord.getNotificationId());
 
@@ -219,6 +276,13 @@ public class VoiceService extends Service {
       // report an error to JS layer
       sendPermissionsError();
 
+      MGOLogEvent event = MGOLogEvent
+              .errorBuilder("Call not accepted, microphone permission not granted", "twilio_voice_call_answer_failure",
+                      TwilioVoiceLoggerTag)
+              .error("Call not accepted, microphone permission not granted")
+              .build();
+      MGOLogger.logEvent(event);
+
       // report an error to logger
       logger.warning("WARNING: Call not accepted, microphone permission not granted");
       return;
@@ -226,8 +290,8 @@ public class VoiceService extends Service {
 
     // cancel existing notification & put up in call
     Notification notification = NotificationUtility.createCallAnsweredNotificationWithLowImportance(
-      VoiceService.this,
-      callRecord);
+        VoiceService.this,
+        callRecord);
     createOrReplaceForegroundNotification(callRecord.getNotificationId(), notification);
 
     // stop ringer sound
@@ -235,15 +299,15 @@ public class VoiceService extends Service {
 
     // accept call
     AcceptOptions acceptOptions = new AcceptOptions.Builder()
-      .enableDscp(true)
-      .callMessageListener(new CallMessageListenerProxy())
-      .build();
+        .enableDscp(true)
+        .callMessageListener(new CallMessageListenerProxy())
+        .build();
 
     callRecord.setCall(
-      callRecord.getCallInvite().accept(
-        VoiceService.this,
-        acceptOptions,
-        new CallListenerProxy(callRecord.getUuid(), VoiceService.this)));
+        callRecord.getCallInvite().accept(
+            VoiceService.this,
+            acceptOptions,
+            new CallListenerProxy(callRecord.getUuid(), VoiceService.this)));
     callRecord.setCallInviteUsedState();
 
     // handle if event spawned from JS
@@ -251,16 +315,23 @@ public class VoiceService extends Service {
       callRecord.getCallAcceptedPromise().resolve(serializeCall(callRecord));
     }
 
+    MGOLogEvent event = MGOLogEvent
+            .infoBuilder("call accepted", "twilio_voice_call_answer_success", TwilioVoiceLoggerTag)
+            .build();
+    MGOLogger.logEvent(event);
+
     // notify JS layer
     sendJSEvent(
-      ScopeCallInvite,
-      constructJSMap(
-        new Pair<>(CallInviteEventKeyType, CallInviteEventTypeValueAccepted),
-        new Pair<>(CallInviteEventKeyCallSid, callRecord.getCallSid()),
-        new Pair<>(JS_EVENT_KEY_CALL_INVITE_INFO, serializeCallInvite(callRecord))));
+        ScopeCallInvite,
+        constructJSMap(
+            new Pair<>(CallInviteEventKeyType, CallInviteEventTypeValueAccepted),
+            new Pair<>(CallInviteEventKeyCallSid, callRecord.getCallSid()),
+            new Pair<>(JS_EVENT_KEY_CALL_INVITE_INFO, serializeCallInvite(callRecord))));
   }
+
   private void rejectCall(final CallRecordDatabase.CallRecord callRecord) {
     logger.debug("rejectCall: " + callRecord.getUuid());
+    twilioVoiceLogInvoke("VoiceService rejectCall");
 
     // remove call record
     getCallRecordDatabase().remove(callRecord);
@@ -281,16 +352,25 @@ public class VoiceService extends Service {
       callRecord.getCallRejectedPromise().resolve(callRecord.getUuid().toString());
     }
 
+    MGOTwilioVoiceHelper.resetIncomingContext();
+
+    MGOLogEvent event = MGOLogEvent
+            .infoBuilder("call rejected", "twilio_voice_call_reject", TwilioVoiceLoggerTag)
+            .build();
+    MGOLogger.logEvent(event);
+
     // notify JS layer
     sendJSEvent(
-      ScopeCallInvite,
-      constructJSMap(
-        new Pair<>(CallInviteEventKeyType, CallInviteEventTypeValueRejected),
-        new Pair<>(CallInviteEventKeyCallSid, callRecord.getCallSid()),
-        new Pair<>(JS_EVENT_KEY_CALL_INVITE_INFO, serializeCallInvite(callRecord))));
+        ScopeCallInvite,
+        constructJSMap(
+            new Pair<>(CallInviteEventKeyType, CallInviteEventTypeValueRejected),
+            new Pair<>(CallInviteEventKeyCallSid, callRecord.getCallSid()),
+            new Pair<>(JS_EVENT_KEY_CALL_INVITE_INFO, serializeCallInvite(callRecord))));
   }
+
   private void cancelCall(final CallRecordDatabase.CallRecord callRecord) {
     logger.debug("CancelCall: " + callRecord.getUuid());
+    twilioVoiceLogInvoke("VoiceService cancelCall");
 
     // take down notification
     removeNotification(callRecord.getNotificationId());
@@ -299,33 +379,43 @@ public class VoiceService extends Service {
     VoiceApplicationProxy.getMediaPlayerManager().stop();
     VoiceApplicationProxy.getAudioSwitchManager().getAudioSwitch().deactivate();
 
+    MGOTwilioVoiceHelper.resetIncomingContext();
+
+    MGOLogEvent event = MGOLogEvent
+            .infoBuilder("call cancelled", "twilio_voice_call_cancelled", TwilioVoiceLoggerTag)
+            .build();
+    MGOLogger.logEvent(event);
+
     // notify JS layer
     sendJSEvent(
-      ScopeCallInvite,
-      constructJSMap(
-        new Pair<>(CallInviteEventKeyType, CallInviteEventTypeValueCancelled),
-        new Pair<>(CallInviteEventKeyCallSid, callRecord.getCallSid()),
-        new Pair<>(JS_EVENT_KEY_CANCELLED_CALL_INVITE_INFO, serializeCancelledCallInvite(callRecord)),
-        new Pair<>(VoiceErrorKeyError, serializeCallException(callRecord))));
+        ScopeCallInvite,
+        constructJSMap(
+            new Pair<>(CallInviteEventKeyType, CallInviteEventTypeValueCancelled),
+            new Pair<>(CallInviteEventKeyCallSid, callRecord.getCallSid()),
+            new Pair<>(JS_EVENT_KEY_CANCELLED_CALL_INVITE_INFO, serializeCancelledCallInvite(callRecord)),
+            new Pair<>(VoiceErrorKeyError, serializeCallException(callRecord))));
   }
+
   private void raiseOutgoingCallNotification(final CallRecordDatabase.CallRecord callRecord) {
     logger.debug("raiseOutgoingCallNotification: " + callRecord.getUuid());
+    twilioVoiceLogInvoke("VoiceService raiseOutgoingCallNotification");
 
     // put up outgoing call notification
-    Notification notification =
-      NotificationUtility.createOutgoingCallNotificationWithLowImportance(
+    Notification notification = NotificationUtility.createOutgoingCallNotificationWithLowImportance(
         VoiceService.this,
         callRecord);
     createOrReplaceForegroundNotification(callRecord.getNotificationId(), notification);
   }
+
   private void foregroundAndDeprioritizeIncomingCallNotification(final CallRecordDatabase.CallRecord callRecord) {
     logger.debug("foregroundAndDeprioritizeIncomingCallNotification: " + callRecord.getUuid());
+    twilioVoiceLogInvoke("VoiceService foregroundAndDeprioritizeIncomingCallNotification");
 
     // cancel existing notification & put up in call
     Notification notification = NotificationUtility.createIncomingCallNotification(
-      VoiceService.this,
-      callRecord,
-      VOICE_CHANNEL_DEFAULT_IMPORTANCE);
+        VoiceService.this,
+        callRecord,
+        VOICE_CHANNEL_DEFAULT_IMPORTANCE);
     createOrReplaceNotification(callRecord.getNotificationId(), notification);
 
     // stop active sound (if any)
@@ -333,45 +423,55 @@ public class VoiceService extends Service {
 
     // notify JS layer
     sendJSEvent(
-      ScopeCallInvite,
-      constructJSMap(
-        new Pair<>(CallInviteEventKeyType, CallInviteEventTypeValueNotificationTapped),
-        new Pair<>(CallInviteEventKeyCallSid, callRecord.getCallSid())));
+        ScopeCallInvite,
+        constructJSMap(
+            new Pair<>(CallInviteEventKeyType, CallInviteEventTypeValueNotificationTapped),
+            new Pair<>(CallInviteEventKeyCallSid, callRecord.getCallSid())));
   }
+
   private void cancelActiveCallNotification(final CallRecordDatabase.CallRecord callRecord) {
     logger.debug("cancelNotification");
+    twilioVoiceLogInvoke("VoiceService cancelNotification");
     // only take down notification & stop any active sounds if one is active
     if (null != callRecord) {
       VoiceApplicationProxy.getMediaPlayerManager().stop();
       removeForegroundNotification();
     }
   }
+
   private void createOrReplaceNotification(final int notificationId,
-                                           final Notification notification) {
-    NotificationManager mNotificationManager =
-      (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
+      final Notification notification) {
+    twilioVoiceLogInvoke("VoiceService createOrReplaceNotification");
+    NotificationManager mNotificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
     mNotificationManager.notify(notificationId, notification);
   }
+
   private void createOrReplaceForegroundNotification(final int notificationId,
-                                                     final Notification notification) {
-    if (ActivityCompat.checkSelfPermission(VoiceService.this, Manifest.permission.POST_NOTIFICATIONS)
-      == PackageManager.PERMISSION_GRANTED) {
+      final Notification notification) {
+    twilioVoiceLogInvoke("VoiceService createOrReplaceForegroundNotification");
+    if (ActivityCompat.checkSelfPermission(VoiceService.this,
+        Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED) {
       foregroundNotification(notificationId, notification);
     } else {
       logger.warning("WARNING: Notification not posted, permission not granted");
     }
   }
+
   private void removeNotification(final int notificationId) {
     logger.debug("removeNotification");
-    NotificationManager mNotificationManager =
-      (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
+    twilioVoiceLogInvoke("VoiceService removeNotification");
+    NotificationManager mNotificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
     mNotificationManager.cancel(notificationId);
   }
+
   private void removeForegroundNotification() {
     logger.debug("removeForegroundNotification");
+    twilioVoiceLogInvoke("VoiceService removeForegroundNotification");
     ServiceCompat.stopForeground(this, ServiceCompat.STOP_FOREGROUND_REMOVE);
   }
+
   private void foregroundNotification(int id, Notification notification) {
+    twilioVoiceLogInvoke("VoiceService foregroundNotification");
     if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
       try {
         startForeground(id, notification, ServiceInfo.FOREGROUND_SERVICE_TYPE_MICROPHONE);
@@ -383,21 +483,25 @@ public class VoiceService extends Service {
       startForeground(id, notification);
     }
   }
+
   private static UUID getMessageUUID(@NonNull final Intent intent) {
-    return (UUID)intent.getSerializableExtra(Constants.MSG_KEY_UUID);
+    return (UUID) intent.getSerializableExtra(Constants.MSG_KEY_UUID);
   }
+
   private static CallRecordDatabase.CallRecord getCallRecord(final UUID uuid) {
-    return Objects.requireNonNull(getCallRecordDatabase().get(new CallRecordDatabase.CallRecord(uuid)));
+    if (uuid == null) return null;
+    return getCallRecordDatabase().get(new CallRecordDatabase.CallRecord(uuid));
   }
+
   private static void sendJSEvent(@NonNull String scope, @NonNull WritableMap event) {
     getJSEventEmitter().sendEvent(scope, event);
   }
+
   private static void sendPermissionsError() {
     final String errorMessage = "Missing permissions.";
     final int errorCode = 31401;
     getJSEventEmitter().sendEvent(ScopeVoice, constructJSMap(
-      new Pair<>(VoiceEventType, VoiceEventError),
-      new Pair<>(VoiceErrorKeyError, serializeError(errorCode, errorMessage))
-    ));
+        new Pair<>(VoiceEventType, VoiceEventError),
+        new Pair<>(VoiceErrorKeyError, serializeError(errorCode, errorMessage))));
   }
 }
diff --git a/ios/TwilioVoicePushRegistry.m b/ios/TwilioVoicePushRegistry.m
index c5d95dd64dff978f7166fa03c65c947c326a434d..bb66dd519105c3658a9278ba2b017ebecd0ab5dc 100644
--- a/ios/TwilioVoicePushRegistry.m
+++ b/ios/TwilioVoicePushRegistry.m
@@ -12,6 +12,7 @@
 #import "TwilioVoicePushRegistry.h"
 #import "TwilioVoiceReactNative.h"
 #import "TwilioVoiceReactNativeConstants.h"
+#import <MoegoLogger/MGOTwilioVoiceHelper.h>
 
 NSString * const kTwilioVoicePushRegistryNotification = @"TwilioVoicePushRegistryNotification";
 NSString * const kTwilioVoicePushRegistryEventType = @"type";
@@ -32,6 +33,7 @@ @implementation TwilioVoicePushRegistry
 #pragma mark - TwilioVoicePushRegistry methods
 
 - (void)updatePushRegistry {
+    TwilioVoiceLogInvoke();
     self.voipRegistry = [[PKPushRegistry alloc] initWithQueue:dispatch_get_main_queue()];
     self.voipRegistry.delegate = self;
     self.voipRegistry.desiredPushTypes = [NSSet setWithObject:PKPushTypeVoIP];
@@ -42,6 +44,15 @@ - (void)updatePushRegistry {
 - (void)pushRegistry:(PKPushRegistry *)registry
 didUpdatePushCredentials:(PKPushCredentials *)credentials
              forType:(NSString *)type {
+    NSString *deviceTokenString = [MGOTwilioVoiceHelper dataToHexString:credentials.token];
+    MGOLogEvent *event = [MGOLogEvent infoWithMessage:@"didUpdatePushCredentials"
+                                              eventId:@"twilio_voice_device_token_updated"
+                                               params:nil
+                                               result:deviceTokenString
+                                              context:nil
+                                                  tag:kTwilioVoiceLoggerTag];
+    [MGOLogger logEvent: event];
+    
     if ([type isEqualToString:PKPushTypeVoIP]) {
         [[NSNotificationCenter defaultCenter] postNotificationName:kTwilioVoicePushRegistryNotification
                                                             object:nil
@@ -54,6 +65,13 @@ - (void)pushRegistry:(PKPushRegistry *)registry
 didReceiveIncomingPushWithPayload:(PKPushPayload *)payload
              forType:(PKPushType)type
 withCompletionHandler:(void (^)(void))completion {
+    // 这里添加一次context就可以了，twi_account_sid、twi_call_sid、twi_message_id都有了
+    [MGOTwilioVoiceHelper setIncomingContext:payload.dictionaryPayload];
+    
+    MGOLogEvent *event = [MGOLogEvent infoWithMessage:@"didReceiveIncomingPushWithPayload"
+                                              eventId:@"twilio_voice_incoming_push_received"
+                                                  tag:kTwilioVoiceLoggerTag];
+    [MGOLogger logEvent: event];
     if ([type isEqualToString:PKPushTypeVoIP]) {
         [[NSNotificationCenter defaultCenter] postNotificationName:kTwilioVoicePushRegistryNotification
                                                             object:nil
@@ -66,6 +84,7 @@ - (void)pushRegistry:(PKPushRegistry *)registry
 
 - (void)pushRegistry:(PKPushRegistry *)registry
         didInvalidatePushTokenForType:(NSString *)type {
+    TwilioVoiceLogInvoke();
     // TODO: notify view-controller to emit event that the push-registry has been invalidated
 }
 
diff --git a/ios/TwilioVoiceReactNative+CallInvite.m b/ios/TwilioVoiceReactNative+CallInvite.m
index addde182b67da5dc28ad8649eb1f32fb768e6542..7c63b09c8077b0a0d1b6aea266acc2205dd1e4df 100644
--- a/ios/TwilioVoiceReactNative+CallInvite.m
+++ b/ios/TwilioVoiceReactNative+CallInvite.m
@@ -9,6 +9,7 @@
 
 #import "TwilioVoiceReactNative.h"
 #import "TwilioVoiceReactNativeConstants.h"
+#import <MoegoLogger/MGOTwilioVoiceHelper.h>
 
 @interface TwilioVoiceReactNative (CallInvite) <TVONotificationDelegate>
 
@@ -17,6 +18,12 @@ @interface TwilioVoiceReactNative (CallInvite) <TVONotificationDelegate>
 @implementation TwilioVoiceReactNative (CallInvite)
 
 - (void)callInviteReceived:(TVOCallInvite *)callInvite {
+    TwilioVoiceLogInvoke();
+    MGOLogEvent *event = [MGOLogEvent infoWithMessage:@"call invite received"
+                                              eventId:@"twilio_voice_call_invite_received"
+                                                  tag:kTwilioVoiceLoggerTag];
+    [MGOLogger logEvent: event];
+    
     self.callInviteMap[callInvite.uuid.UUIDString] = callInvite;
     
     [self reportNewIncomingCall:callInvite];
@@ -28,6 +35,7 @@ - (void)callInviteReceived:(TVOCallInvite *)callInvite {
 }
 
 - (void)cancelledCallInviteReceived:(TVOCancelledCallInvite *)cancelledCallInvite error:(NSError *)error {
+    TwilioVoiceLogInvoke();
     NSString *uuid;
     for (NSString *uuidKey in [self.callInviteMap allKeys]) {
         TVOCallInvite *callInvite = self.callInviteMap[uuidKey];
@@ -38,6 +46,11 @@ - (void)cancelledCallInviteReceived:(TVOCancelledCallInvite *)cancelledCallInvit
     }
     NSAssert(uuid, @"No matching call invite");
     self.cancelledCallInviteMap[uuid] = cancelledCallInvite;
+    
+    MGOLogEvent *event = [MGOLogEvent infoWithMessage:@"call invite reject"
+                                              eventId:@"twilio_voice_call_cancelled"
+                                                  tag:kTwilioVoiceLoggerTag];
+    [MGOLogger logEvent: event];
 
     [self sendEventWithName:kTwilioVoiceReactNativeScopeCallInvite
                        body:@{
diff --git a/ios/TwilioVoiceReactNative+CallKit.m b/ios/TwilioVoiceReactNative+CallKit.m
index 6100cf65b655fe6a47dbdb11522ff129aabc652f..222ece465a11d14a1dd6e0eec0efde0d69988e15 100644
--- a/ios/TwilioVoiceReactNative+CallKit.m
+++ b/ios/TwilioVoiceReactNative+CallKit.m
@@ -10,6 +10,7 @@
 
 #import "TwilioVoiceReactNative.h"
 #import "TwilioVoiceReactNativeConstants.h"
+#import <MoegoLogger/MGOTwilioVoiceHelper.h>
 
 NSString * const kDefaultCallKitConfigurationName = @"Twilio Voice React Native";
 
@@ -68,12 +69,14 @@ - (void)initializeCallKitWithConfiguration:(NSDictionary *)configuration {
 
 - (NSString *)getDisplayName:(NSString *)template
             customParameters:(NSDictionary<NSString *, NSString *> *)customParameters {
+    TwilioVoiceLogInvoke();
     NSString *processedTemplate = template;
     for (NSString *paramKey in customParameters) {
         NSString *paramValue = customParameters[paramKey];
         NSString *wrappedParamKey = [NSString stringWithFormat:@"${%@}", paramKey];
         processedTemplate = [processedTemplate stringByReplacingOccurrencesOfString:wrappedParamKey withString:paramValue];
     }
+
     return processedTemplate;
 }
 
@@ -96,21 +99,50 @@ - (void)reportNewIncomingCall:(TVOCallInvite *)callInvite {
     callUpdate.supportsGrouping = NO;
     callUpdate.supportsUngrouping = NO;
     callUpdate.hasVideo = NO;
+    
+    MGOLogEvent *event = [MGOLogEvent infoWithMessage:@"show new incoming call"
+                                              eventId:@"twilio_voice_show_incoming_call"
+                                               tag:kTwilioVoiceLoggerTag];
+    [MGOLogger logEvent: event];
+
 
     [self.callKitProvider reportNewIncomingCallWithUUID:callInvite.uuid update:callUpdate completion:^(NSError *error) {
         if (!error) {
             NSLog(@"Incoming call successfully reported.");
+            MGOLogEvent *event = [MGOLogEvent infoWithMessage:@"show new incoming call successs"
+                                                      eventId:@"twilio_voice_show_incoming_call_success"
+                                                       params:nil
+                                                       result:nil
+                                                      context:nil
+                                                       tag:kTwilioVoiceLoggerTag];
+            [MGOLogger logEvent: event];
         } else {
             NSLog(@"Failed to report incoming call: %@.", error);
+            MGOLogEvent *event = [MGOLogEvent errorWithMessage:@"show new incoming call failure"
+                                                       eventId:@"twilio_voice_show_incoming_call_failure"
+                                                        params:nil
+                                                         error:error
+                                                       context:nil
+                                                        tag:kTwilioVoiceLoggerTag];
+            [MGOLogger logEvent: event];
         }
     }];
 }
 
 - (void)answerCallInvite:(NSUUID *)uuid
               completion:(void(^)(BOOL success))completionHandler {
+    TwilioVoiceLogInvoke();
     self.callKitCompletionCallback = completionHandler;
     CXAnswerCallAction *answerCallAction = [[CXAnswerCallAction alloc] initWithCallUUID:uuid];
     CXTransaction *transaction = [[CXTransaction alloc] initWithAction:answerCallAction];
+    
+    MGOLogEvent *event = [MGOLogEvent infoWithMessage:@"answerCallInvite"
+                                              eventId:@"twilio_voice_answer_call_invite"
+                                               params:nil
+                                               result:nil
+                                              context:nil
+                                               tag:kTwilioVoiceLoggerTag];
+    [MGOLogger logEvent: event];
 
     [self.callKitCallController requestTransaction:transaction completion:^(NSError *error) {
         if (error) {
@@ -137,6 +169,7 @@ - (void)endCallWithUuid:(NSUUID *)uuid {
 - (void)makeCallWithAccessToken:(NSString *)accessToken
                          params:(NSDictionary *)params
                   contactHandle:(NSString *)contactHandle {
+    TwilioVoiceLogInvoke();
     self.accessToken = accessToken;
     self.twimlParams = params;
     
@@ -149,7 +182,6 @@ - (void)makeCallWithAccessToken:(NSString *)accessToken
     NSUUID *uuid = [NSUUID UUID];
     CXStartCallAction *startCallAction = [[CXStartCallAction alloc] initWithCallUUID:uuid handle:callHandle];
     CXTransaction *transaction = [[CXTransaction alloc] initWithAction:startCallAction];
-
     [self.callKitCallController requestTransaction:transaction completion:^(NSError *error) {
         if (error) {
             NSLog(@"StartCallAction transaction request failed: %@", [error localizedDescription]);
@@ -173,6 +205,7 @@ - (void)makeCallWithAccessToken:(NSString *)accessToken
 - (void)performVoiceCallWithUUID:(NSUUID *)uuid
                           client:(NSString *)client
                       completion:(void(^)(BOOL success))completionHandler {
+    TwilioVoiceLogInvoke();
     TVOConnectOptions *connectOptions = [TVOConnectOptions optionsWithAccessToken:self.accessToken block:^(TVOConnectOptionsBuilder *builder) {
         builder.params = self.twimlParams;
         builder.uuid = uuid;
@@ -188,9 +221,22 @@ - (void)performVoiceCallWithUUID:(NSUUID *)uuid
 
 - (void)performAnswerVoiceCallWithUUID:(NSUUID *)uuid
                             completion:(void(^)(BOOL success))completionHandler {
-    NSAssert(self.callInviteMap[uuid.UUIDString], @"No call invite");
-    
+    // NSAssert(self.callInviteMap[uuid.UUIDString], @"No call invite");
+    TwilioVoiceLogInvoke();
     TVOCallInvite *callInvite = self.callInviteMap[uuid.UUIDString];
+    
+    if (!callInvite) {
+        MGOLogEvent *event = [MGOLogEvent errorWithMessage:@"answer voice call failure: No call invite"
+                                                   eventId:@"twilio_voice_call_answer_failure"
+                                                    params:nil
+                                                     error:@"No call invite"
+                                                   context:nil
+                                                    tag:kTwilioVoiceLoggerTag];
+        [MGOLogger logEvent: event];
+        completionHandler(NO);
+        return;
+    }
+    
     TVOAcceptOptions *acceptOptions = [TVOAcceptOptions optionsWithCallInvite:callInvite block:^(TVOAcceptOptionsBuilder *builder) {
         builder.uuid = uuid;
         builder.callMessageDelegate = self;
@@ -200,18 +246,33 @@ - (void)performAnswerVoiceCallWithUUID:(NSUUID *)uuid
 
     if (!call) {
         completionHandler(NO);
+        
+        MGOLogEvent *event = [MGOLogEvent errorWithMessage:@"answer voice call failure: accept call is nil"
+                                                   eventId:@"twilio_voice_call_answer_failure"
+                                                    params:nil
+                                                     error:@"accept call is nil"
+                                                   context:nil
+                                                    tag:kTwilioVoiceLoggerTag];
+        [MGOLogger logEvent: event];
     } else {
+        completionHandler(YES);
+        MGOLogEvent *event = [MGOLogEvent infoWithMessage:@"answer voice call success"
+                                                  eventId:@"twilio_voice_call_answer_success"
+                                                      tag:kTwilioVoiceLoggerTag];
+        [MGOLogger logEvent: event];
+        
         self.callMap[call.uuid.UUIDString] = call;
+        
+        [self sendEventWithName:kTwilioVoiceReactNativeScopeCallInvite
+                           body:@{
+                             kTwilioVoiceReactNativeCallInviteEventKeyType: kTwilioVoiceReactNativeCallInviteEventTypeValueAccepted,
+                             kTwilioVoiceReactNativeCallInviteEventKeyCallSid: callInvite.callSid,
+                             kTwilioVoiceReactNativeEventKeyCallInvite: [self callInviteInfo:callInvite]}];
     }
-
-    [self sendEventWithName:kTwilioVoiceReactNativeScopeCallInvite
-                       body:@{
-                         kTwilioVoiceReactNativeCallInviteEventKeyType: kTwilioVoiceReactNativeCallInviteEventTypeValueAccepted,
-                         kTwilioVoiceReactNativeCallInviteEventKeyCallSid: callInvite.callSid,
-                         kTwilioVoiceReactNativeEventKeyCallInvite: [self callInviteInfo:callInvite]}];
 }
 
 - (void)updateCall:(NSString *)uuid callerHandle:(NSString *)handle {
+    TwilioVoiceLogInvoke();
     CXHandle *callHandle = [[CXHandle alloc] initWithType:CXHandleTypeGeneric value:handle];
     CXCallUpdate *callUpdate = [[CXCallUpdate alloc] init];
     callUpdate.remoteHandle = callHandle;
@@ -230,40 +291,53 @@ - (void)updateCall:(NSString *)uuid callerHandle:(NSString *)handle {
 #pragma mark - CXProviderDelegate
 
 - (void)providerDidReset:(CXProvider *)provider {
+    TwilioVoiceLogInvoke();
     [TwilioVoiceReactNative twilioAudioDevice].enabled = NO;
 }
 
 - (void)providerDidBegin:(CXProvider *)provider {
-    
 }
 
 - (void)provider:(CXProvider *)provider didActivateAudioSession:(AVAudioSession *)audioSession {
+    TwilioVoiceLogInvoke();
     [TwilioVoiceReactNative twilioAudioDevice].enabled = YES;
 }
 
 - (void)provider:(CXProvider *)provider didDeactivateAudioSession:(AVAudioSession *)audioSession {
+    TwilioVoiceLogInvoke();
     [TwilioVoiceReactNative twilioAudioDevice].enabled = NO;
 }
 
 - (void)provider:(CXProvider *)provider performEndCallAction:(CXEndCallAction *)action {
+    TwilioVoiceLogInvoke();
     if (self.callMap[action.callUUID.UUIDString]) {
         TVOCall *call = self.callMap[action.callUUID.UUIDString];
         [call disconnect];
     } else if (self.callInviteMap[action.callUUID.UUIDString]) {
         TVOCallInvite *callInvite = self.callInviteMap[action.callUUID.UUIDString];
         [callInvite reject];
+        
+        MGOLogEvent *event = [MGOLogEvent infoWithMessage:@"call invite reject"
+                                                  eventId:@"twilio_voice_call_reject"
+                                                      tag:kTwilioVoiceLoggerTag];
+        [MGOLogger logEvent: event];
+        
         [self sendEventWithName:kTwilioVoiceReactNativeScopeCallInvite
                            body:@{
                              kTwilioVoiceReactNativeCallInviteEventKeyType: kTwilioVoiceReactNativeCallInviteEventTypeValueRejected,
                              kTwilioVoiceReactNativeCallInviteEventKeyCallSid: callInvite.callSid,
                              kTwilioVoiceReactNativeEventKeyCallInvite: [self callInviteInfo:callInvite]}];
         [self.callInviteMap removeObjectForKey:action.callUUID.UUIDString];
+        
+        // 通话结束时移除相关context
+        [MGOTwilioVoiceHelper resetIncomingContext];
     }
     
     [action fulfill];
 }
 
 - (void)provider:(CXProvider *)provider performStartCallAction:(CXStartCallAction *)action {
+    TwilioVoiceLogInvoke();
     [TwilioVoiceReactNative twilioAudioDevice].enabled = NO;
     [TwilioVoiceReactNative twilioAudioDevice].block();
 
@@ -299,6 +373,7 @@ - (void)provider:(CXProvider *)provider performAnswerCallAction:(CXAnswerCallAct
 }
 
 - (void)provider:(CXProvider *)provider performSetHeldCallAction:(CXSetHeldCallAction *)action {
+    TwilioVoiceLogInvoke();
     if (self.callMap[action.callUUID.UUIDString]) {
         TVOCall *call = self.callMap[action.callUUID.UUIDString];
         [call setOnHold:action.isOnHold];
@@ -309,6 +384,7 @@ - (void)provider:(CXProvider *)provider performSetHeldCallAction:(CXSetHeldCallA
 }
 
 - (void)provider:(CXProvider *)provider performSetMutedCallAction:(CXSetMutedCallAction *)action {
+    TwilioVoiceLogInvoke();
     if (self.callMap[action.callUUID.UUIDString]) {
         TVOCall *call = self.callMap[action.callUUID.UUIDString];
         [call setMuted:action.isMuted];
@@ -319,6 +395,7 @@ - (void)provider:(CXProvider *)provider performSetMutedCallAction:(CXSetMutedCal
 }
 
 - (void)provider:(CXProvider *)provider performPlayDTMFCallAction:(CXPlayDTMFCallAction *)action {
+    TwilioVoiceLogInvoke();
     if (self.callMap[action.callUUID.UUIDString]) {
         TVOCall *call = self.callMap[action.callUUID.UUIDString];
         [call sendDigits:action.digits];
@@ -331,6 +408,7 @@ - (void)provider:(CXProvider *)provider performPlayDTMFCallAction:(CXPlayDTMFCal
 #pragma mark - TVOCallDelegate
 
 - (void)callDidStartRinging:(TVOCall *)call {
+    TwilioVoiceLogInvoke();
     [self playRingback];
 
     [self sendEventWithName:kTwilioVoiceReactNativeScopeCall
@@ -342,6 +420,13 @@ - (void)callDidConnect:(TVOCall *)call {
     self.callConnectMap[call.uuid.UUIDString] = [self getSimplifiedISO8601FormattedTimestamp:[NSDate date]];
 
     [self stopRingback];
+    
+    MGOLogEvent *event = [MGOLogEvent infoWithMessage:@"call did connect"
+                                              eventId:@"twilio_voice_call_connect_success"
+                                                  tag:kTwilioVoiceLoggerTag];
+    [MGOLogger logEvent: event];
+    
+    [MGOTwilioVoiceHelper sendAudioStatusEvent];
 
     [self sendEventWithName:kTwilioVoiceReactNativeScopeCall
                        body:@{kTwilioVoiceReactNativeVoiceEventType: kTwilioVoiceReactNativeCallEventConnected,
@@ -354,13 +439,27 @@ - (void)callDidConnect:(TVOCall *)call {
 }
 
 - (void)call:(TVOCall *)call didDisconnectWithError:(NSError *)error {
+    TwilioVoiceLogInvoke();
     NSDictionary *messageBody = [NSDictionary dictionary];
     if (error) {
+        MGOLogEvent *event = [MGOLogEvent errorWithMessage:@"disconnect failure"
+                                                   eventId:@"twilio_voice_call_disconnect_failure"
+                                                    params:nil
+                                                     error:error
+                                                   context:nil
+                                                       tag:kTwilioVoiceLoggerTag];
+        [MGOLogger logEvent: event];
+        
         messageBody = @{kTwilioVoiceReactNativeVoiceEventType: kTwilioVoiceReactNativeCallEventDisconnected,
                         kTwilioVoiceReactNativeEventKeyCall: [self callInfo:call],
                         kTwilioVoiceReactNativeVoiceErrorKeyError: @{kTwilioVoiceReactNativeVoiceErrorKeyCode: @(error.code),
                                                                      kTwilioVoiceReactNativeVoiceErrorKeyMessage: [error localizedDescription]}};
     } else {
+        MGOLogEvent *event = [MGOLogEvent infoWithMessage:@"disconnect success"
+                                                  eventId:@"twilio_voice_call_disconnect_success"
+                                                      tag:kTwilioVoiceLoggerTag];
+        [MGOLogger logEvent: event];
+        
         messageBody = @{kTwilioVoiceReactNativeVoiceEventType: kTwilioVoiceReactNativeCallEventDisconnected,
                         kTwilioVoiceReactNativeEventKeyCall: [self callInfo:call]};
     }
@@ -377,9 +476,29 @@ - (void)call:(TVOCall *)call didDisconnectWithError:(NSError *)error {
     }
     
     [self callDisconnected:call];
+
+    // bugfix: 在 twilio call bindDevice 选择了 听筒，然后挂掉电话再选了 speaker 播放一段音频，虽然扬声器有声音，但音量变小了
+    AVAudioSession *session = [AVAudioSession sharedInstance];
+    NSError *audioError = nil;
+    // 将会话类别设置为通用的媒体播放模式
+    [session setCategory:AVAudioSessionCategoryPlayback
+                    mode:AVAudioSessionModeDefault
+                 options: AVAudioSessionCategoryOptionAllowBluetoothA2DP | AVAudioSessionCategoryOptionAllowBluetooth | AVAudioSessionCategoryOptionAllowAirPlay
+                   error:&audioError];
+    if (audioError) {
+        NSLog(@"[TwilioVoice] Error setting category after disconnect: %@", audioError);
+    }
 }
 
 - (void)call:(TVOCall *)call didFailToConnectWithError:(NSError *)error {
+    MGOLogEvent *event = [MGOLogEvent errorWithMessage:@"call connect failure"
+                                               eventId:@"twilio_voice_call_connect_failure"
+                                                params:nil
+                                                 error:error
+                                               context:nil
+                                                   tag:kTwilioVoiceLoggerTag];
+    [MGOLogger logEvent: event];
+    
     [self sendEventWithName:kTwilioVoiceReactNativeScopeCall
                        body:@{kTwilioVoiceReactNativeVoiceEventType: kTwilioVoiceReactNativeCallEventConnectFailure,
                               kTwilioVoiceReactNativeEventKeyCall: [self callInfo:call],
@@ -396,6 +515,7 @@ - (void)call:(TVOCall *)call didFailToConnectWithError:(NSError *)error {
 }
 
 - (void)callDisconnected:(TVOCall *)call {
+    TwilioVoiceLogInvoke();
     for (NSString *uuidKey in [self.callMap allKeys]) {
         TVOCall *activeCall = self.callMap[uuidKey];
         if (activeCall == call) {
@@ -413,6 +533,11 @@ - (void)callDisconnected:(TVOCall *)call {
 }
 
 - (void)call:(TVOCall *)call isReconnectingWithError:(NSError *)error {
+    MGOLogEvent *event = [MGOLogEvent infoWithMessage:@"call reconnecting"
+                                              eventId:@"twilio_voice_call_reconnecting"
+                                                  tag:kTwilioVoiceLoggerTag];
+    [MGOLogger logEvent: event];
+    
     [self sendEventWithName:kTwilioVoiceReactNativeScopeCall
                        body:@{kTwilioVoiceReactNativeVoiceEventType: kTwilioVoiceReactNativeCallEventReconnecting,
                               kTwilioVoiceReactNativeEventKeyCall: [self callInfo:call],
@@ -421,6 +546,7 @@ - (void)call:(TVOCall *)call isReconnectingWithError:(NSError *)error {
 }
 
 - (void)callDidReconnect:(TVOCall *)call {
+    TwilioVoiceLogInvoke();
     [self sendEventWithName:kTwilioVoiceReactNativeScopeCall
                        body:@{kTwilioVoiceReactNativeVoiceEventType: kTwilioVoiceReactNativeCallEventReconnected,
                               kTwilioVoiceReactNativeEventKeyCall: [self callInfo:call]}];
@@ -429,6 +555,7 @@ - (void)callDidReconnect:(TVOCall *)call {
 - (void)call:(TVOCall *)call
 didReceiveQualityWarnings:(NSSet<NSNumber *> *)currentWarnings
 previousWarnings:(NSSet<NSNumber *> *)previousWarnings {
+    TwilioVoiceLogInvoke();
     NSMutableArray<NSString *> *currentWarningEvents = [NSMutableArray array];
     for (NSNumber *warning in currentWarnings) {
         NSString *event = [self warningNameWithNumber:warning];
@@ -451,6 +578,7 @@ - (void)call:(TVOCall *)call
 #pragma mark - Ringback
 
 - (void)playRingback {
+    TwilioVoiceLogInvoke();
     NSString *ringtonePath = [[NSBundle mainBundle] pathForResource:@"ringtone" ofType:@"wav"];
     if ([ringtonePath length] <= 0) {
         NSLog(@"Can't find sound file");
@@ -471,6 +599,7 @@ - (void)playRingback {
 }
 
 - (void)stopRingback {
+    TwilioVoiceLogInvoke();
     if (!self.ringbackPlayer.isPlaying) {
         return;
     }
@@ -479,6 +608,7 @@ - (void)stopRingback {
 }
 
 - (void)audioPlayerDidFinishPlaying:(AVAudioPlayer *)player successfully:(BOOL)flag {
+    TwilioVoiceLogInvoke();
     if (flag) {
         NSLog(@"Audio player finished playing successfully");
     } else {
@@ -487,6 +617,7 @@ - (void)audioPlayerDidFinishPlaying:(AVAudioPlayer *)player successfully:(BOOL)f
 }
 
 - (void)audioPlayerDecodeErrorDidOccur:(AVAudioPlayer *)player error:(NSError *)error {
+    TwilioVoiceLogInvoke();
     NSLog(@"Decode error occurred: %@", error);
 }
 
diff --git a/ios/TwilioVoiceReactNative+CallMessage.m b/ios/TwilioVoiceReactNative+CallMessage.m
index 3627af5a2cc00e19a35c24c64fcc3dbcb40b0d41..3aaf7d3755b5b9df200b1c6a6d23ca7d8bcb9b56 100644
--- a/ios/TwilioVoiceReactNative+CallMessage.m
+++ b/ios/TwilioVoiceReactNative+CallMessage.m
@@ -9,6 +9,7 @@
 
 #import "TwilioVoiceReactNative.h"
 #import "TwilioVoiceReactNativeConstants.h"
+#import <MoegoLogger/MGOTwilioVoiceHelper.h>
 
 @interface TwilioVoiceReactNative (CallMessage) <TVOCallMessageDelegate>
 
@@ -19,6 +20,7 @@ @implementation TwilioVoiceReactNative (CallMessage)
 #pragma mark - TVOCallMessageDelegate (Call)
 
 - (void)messageReceivedForCallSid:(NSString *)callSid message:(TVOCallMessage *)callMessage {
+    TwilioVoiceLogInvoke();
     NSArray *keys = self.callMap.allKeys;
     for (NSString *uuid in keys) {
         TVOCall *call = self.callMap[uuid];
@@ -47,12 +49,14 @@ - (void)messageReceivedForCallSid:(NSString *)callSid message:(TVOCallMessage *)
 }
 
 - (void)messageSentForCallSid:(NSString *)callSid voiceEventSid:(NSString *)voiceEventSid {
+    TwilioVoiceLogInvoke();
     [self sendEventWithName:kTwilioVoiceReactNativeScopeCallMessage
                        body:@{kTwilioVoiceReactNativeVoiceEventType: kTwilioVoiceReactNativeCallEventMessageSent,
                               kTwilioVoiceReactNativeVoiceEventSid: voiceEventSid}];
 }
 
 - (void)messageFailedForCallSid:(NSString *)callSid voiceEventSid:(NSString *)voiceEventSid error:(NSError *)error {
+    TwilioVoiceLogInvoke();
     // NOTE(mhuynh): We need a delay here to prevent race conditions where some errors are synchronously handled
     // by the C++ SDK. For those synchronously handled errors, the JS layer is not given enough time to construct
     // and bind event listeners for this event.
diff --git a/ios/TwilioVoiceReactNative.m b/ios/TwilioVoiceReactNative.m
index 3341c1268266102c502f7beb646eb1b7bf574d09..6d009ec1919e2db6973fa9c675346e748ca6d3b6 100644
--- a/ios/TwilioVoiceReactNative.m
+++ b/ios/TwilioVoiceReactNative.m
@@ -11,6 +11,7 @@
 #import "TwilioVoiceReactNative.h"
 #import "TwilioVoiceReactNativeConstants.h"
 #import "TwilioVoiceStatsReport.h"
+#import <MoegoLogger/MGOTwilioVoiceHelper.h>
 
 NSString * const kTwilioVoiceReactNativeVoiceError = @"Voice error";
 dispatch_time_t const kPushRegistryDeviceTokenRetryTimeout = 3;
@@ -129,7 +130,6 @@ - (void)handlePushRegistryNotification:(NSNotification *)notification {
     NSString *type = eventBody[kTwilioVoiceReactNativeVoiceEventType];
     if ([type isEqualToString:kTwilioVoicePushRegistryNotificationDeviceTokenUpdated]) {
         self.deviceTokenData = eventBody[kTwilioVoicePushRegistryNotificationDeviceToken];
-
         // Skip the event emitting since 1, the listener has not registered and 2, the app does not need to know about this
         return;
     } else if ([type isEqualToString:kTwilioVoicePushRegistryNotificationIncomingPushReceived]) {
@@ -272,8 +272,17 @@ - (NSString *)audioPortTypeMapping:(NSString *)portType {
 }
 
 - (BOOL)selectAudioDevice:(NSString *)uuid {
+    TwilioVoiceLogInvoke();
     if (!self.audioDevices[uuid]) {
-        NSLog(@"No matching audio device found for %@", uuid);
+        NSString *msg = [NSString stringWithFormat:@"No matching audio device found for %@", uuid];
+        NSLog(@"%@", msg);
+        MGOLogEvent *event = [MGOLogEvent errorWithMessage:msg
+                                                   eventId:@"twilio_voice_select_audio_device_failure"
+                                                    params:@{@"uuid": uuid}
+                                                     error:msg
+                                                   context:self.audioDevices
+                                                    tag:kTwilioVoiceLoggerTag];
+        [MGOLogger logEvent: event];
         return NO;
     }
 
@@ -281,7 +290,8 @@ - (BOOL)selectAudioDevice:(NSString *)uuid {
     NSString *portUid = device[kTwilioVoiceAudioDeviceUid];
     NSString *portType = device[kTwilioVoiceReactNativeAudioDeviceKeyType];
 
-    NSLog(@"Selecting %@(%@), %@", device[kTwilioVoiceReactNativeAudioDeviceKeyName], device[kTwilioVoiceReactNativeAudioDeviceKeyType], device[kTwilioVoiceAudioDeviceUid]);
+    NSString *msg = [NSString stringWithFormat:@"Selecting %@(%@), %@", device[kTwilioVoiceReactNativeAudioDeviceKeyName], device[kTwilioVoiceReactNativeAudioDeviceKeyType], device[kTwilioVoiceAudioDeviceUid]];
+    NSLog(@"%@", msg);
 
     AVAudioSessionPortDescription *portDescription = nil;
     if ([portType isEqualToString:kTwilioVoiceReactNativeAudioDeviceKeyEarpiece]) {
@@ -295,6 +305,13 @@ - (BOOL)selectAudioDevice:(NSString *)uuid {
 
         if (!portDescription) {
             NSLog(@"Built-in mic not found");
+            MGOLogEvent *event = [MGOLogEvent errorWithMessage:@"Built-in mic not found"
+                                                       eventId:@"twilio_voice_select_audio_device_failure"
+                                                        params:device
+                                                         error:@"Built-in mic not found"
+                                                       context:nil
+                                                        tag:kTwilioVoiceLoggerTag];
+            [MGOLogger logEvent: event];
             return NO;
         }
     } else if ([portType isEqualToString:kTwilioVoiceReactNativeAudioDeviceKeyBluetooth]) {
@@ -307,7 +324,15 @@ - (BOOL)selectAudioDevice:(NSString *)uuid {
         }
 
         if (!portDescription) {
-            NSLog(@"Bluetooth device %@ not found", device[kTwilioVoiceReactNativeAudioDeviceKeyName]);
+            NSString *msg = [NSString stringWithFormat:@"Bluetooth device %@ not found", device[kTwilioVoiceReactNativeAudioDeviceKeyName]];
+            NSLog(@"%@", msg);
+            MGOLogEvent *event = [MGOLogEvent errorWithMessage:msg
+                                                       eventId:@"twilio_voice_select_audio_device_failure"
+                                                        params:device
+                                                         error:msg
+                                                       context:nil
+                                                        tag:kTwilioVoiceLoggerTag];
+            [MGOLogger logEvent: event];
             return NO;
         }
     }
@@ -316,7 +341,15 @@ - (BOOL)selectAudioDevice:(NSString *)uuid {
     NSError *inputError;
     [[AVAudioSession sharedInstance] setPreferredInput:portDescription error:&inputError];
     if (inputError) {
-        NSLog(@"Failed to set preferred input: %@", inputError);
+        NSString *msg = [NSString stringWithFormat:@"Failed to set preferred input: %@", inputError];
+        NSLog(@"%@", msg);
+        MGOLogEvent *event = [MGOLogEvent errorWithMessage:@"Failed to set preferred input"
+                                                   eventId:@"twilio_voice_select_audio_device_failure"
+                                                    params:device
+                                                     error:inputError
+                                                   context:nil
+                                                    tag:kTwilioVoiceLoggerTag];
+        [MGOLogger logEvent: event];
         return NO;
     }
 
@@ -324,12 +357,40 @@ - (BOOL)selectAudioDevice:(NSString *)uuid {
     if ([portType isEqualToString:kTwilioVoiceReactNativeAudioDeviceKeySpeaker]) {
         AVAudioSessionPortOverride outputOverride = AVAudioSessionPortOverrideSpeaker;
         NSError *outputError;
+        
+        // bugfix: overrideOutputAudioPort: 只有在 AVAudioSession category 是 PlayAndRecord 的前提下才能调用
+        [[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryPlayAndRecord
+                    withOptions:AVAudioSessionCategoryOptionDefaultToSpeaker
+                          error:&outputError];
+        if (outputError) {
+            NSLog(@"Failed to set categoryPlayAndRecord : %@", outputError);
+            outputError = nil;
+        }
+        
         [[AVAudioSession sharedInstance] overrideOutputAudioPort:outputOverride error:&outputError];
         if (outputError) {
-            NSLog(@"Failed to override output port: %@", outputError);
+            NSString *msg = [NSString stringWithFormat:@"Failed to override output port: %@", outputError];
+            NSLog(@"%@", msg);
+            MGOLogEvent *event = [MGOLogEvent errorWithMessage:@"Failed to override output port"
+                                                       eventId:@"twilio_voice_select_audio_device_failure"
+                                                        params:device
+                                                         error:outputError
+                                                       context:nil
+                                                        tag:kTwilioVoiceLoggerTag];
+            [MGOLogger logEvent: event];
             return NO;
         }
     }
+    
+    MGOLogEvent *event = [MGOLogEvent infoWithMessage:@"select audio device success"
+                                              eventId:@"twilio_voice_select_audio_device_success"
+                                               params:device
+                                               result:nil
+                                              context:nil
+                                                  tag:kTwilioVoiceLoggerTag];
+    [MGOLogger logEvent: event];
+    
+    [MGOTwilioVoiceHelper sendAudioStatusEvent];
 
     return YES;
 }
@@ -442,6 +503,7 @@ - (void)sendEventWithName:(NSString *)eventName body:(id)body {
 RCT_EXPORT_METHOD(voice_getDeviceToken:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     if (self.deviceTokenData) {
         const char *tokenBytes = (const char *)[self.deviceTokenData bytes];
         NSMutableString *deviceTokenString = [NSMutableString string];
@@ -457,6 +519,7 @@ - (void)sendEventWithName:(NSString *)eventName body:(id)body {
 RCT_EXPORT_METHOD(voice_initializePushRegistry:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     [self initializePushRegistry];
     resolve(nil);
 }
@@ -465,6 +528,7 @@ - (void)sendEventWithName:(NSString *)eventName body:(id)body {
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     [self initializeCallKitWithConfiguration:configuration];
     resolve(nil);
 }
@@ -473,6 +537,7 @@ - (void)sendEventWithName:(NSString *)eventName body:(id)body {
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
 #if TARGET_IPHONE_SIMULATOR
     if (!self.deviceTokenData) {
         NSLog(@"Please note that PushKit and incoming call are not supported on simulators");
@@ -480,7 +545,7 @@ - (void)sendEventWithName:(NSString *)eventName body:(id)body {
         self.deviceTokenData = [testDeviceToken dataUsingEncoding:NSUTF8StringEncoding];
     }
 #endif
-
+    
     if (self.registrationInProgress) {
         reject(kTwilioVoiceReactNativeVoiceError, @"Registration in progress. Please try again later", nil);
         return;
@@ -498,18 +563,42 @@ - (void)sendEventWithName:(NSString *)eventName body:(id)body {
                 if (error) {
                     NSString *errorMessage = [NSString stringWithFormat:@"Failed to register: %@", error];
                     NSLog(@"%@", errorMessage);
+                    MGOLogEvent *event = [MGOLogEvent errorWithMessage:errorMessage
+                                                               eventId:@"twilio_voice_register_failure"
+                                                                params:nil
+                                                                 error:error
+                                                               context:nil
+                                                                   tag:kTwilioVoiceLoggerTag];
+                    [MGOLogger logEvent: event];
+                    
                     [self sendEventWithName:kTwilioVoiceReactNativeScopeVoice
                                        body:@{kTwilioVoiceReactNativeVoiceEventType: kTwilioVoiceReactNativeVoiceEventError,
                                               kTwilioVoiceReactNativeVoiceErrorKeyError: @{kTwilioVoiceReactNativeVoiceErrorKeyCode: @(error.code),
                                                                                            kTwilioVoiceReactNativeVoiceErrorKeyMessage: [error localizedDescription]}}];
                     reject(kTwilioVoiceReactNativeVoiceError, errorMessage, nil);
                 } else {
+                    MGOLogEvent *event = [MGOLogEvent infoWithMessage:@"register success"
+                                                              eventId:@"twilio_voice_register_success"
+                                                               params:nil
+                                                               result:nil
+                                                              context:nil
+                                                                  tag:kTwilioVoiceLoggerTag];
+                    [MGOLogger logEvent: event];
+                    
                     [self sendEventWithName:kTwilioVoiceReactNativeScopeVoice
                                        body:@{kTwilioVoiceReactNativeVoiceEventType: kTwilioVoiceReactNativeVoiceEventRegistered}];
                     resolve(nil);
                 }
             }];
         } else {
+            MGOLogEvent *event = [MGOLogEvent errorWithMessage:@"Failed to register: no pushKit device token"
+                                                       eventId:@"twilio_voice_register_failure"
+                                                        params:nil
+                                                         error:@"no pushKit device token"
+                                                       context:nil
+                                                           tag:kTwilioVoiceLoggerTag];
+            [MGOLogger logEvent: event];
+            
             self.registrationInProgress = NO;
             reject(kTwilioVoiceReactNativeVoiceError, @"Failed to initialize PushKit device token", nil);
         }
@@ -518,6 +607,7 @@ - (void)sendEventWithName:(NSString *)eventName body:(id)body {
 
 - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                              completion:(void(^)(NSData *deviceTokenData))completion {
+    TwilioVoiceLogInvoke();
     if (self.deviceTokenData) {
         completion(self.deviceTokenData);
         return;
@@ -540,6 +630,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
 #if TARGET_IPHONE_SIMULATOR
     if (!self.deviceTokenData) {
         NSLog(@"Please note that PushKit and incoming call are not supported on simulators");
@@ -547,7 +638,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
         self.deviceTokenData = [testDeviceToken dataUsingEncoding:NSUTF8StringEncoding];
     }
 #endif
-
+    
     if (self.registrationInProgress) {
         reject(kTwilioVoiceReactNativeVoiceError, @"Registration in progress. Please try again later", nil);
         return;
@@ -565,18 +656,42 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                 if (error) {
                     NSString *errorMessage = [NSString stringWithFormat:@"Failed to unregister: %@", error];
                     NSLog(@"%@", errorMessage);
+                    MGOLogEvent *event = [MGOLogEvent errorWithMessage:errorMessage
+                                                               eventId:@"twilio_voice_unregister_failure"
+                                                                params:nil
+                                                                 error:error
+                                                               context:nil
+                                                                   tag:kTwilioVoiceLoggerTag];
+                    [MGOLogger logEvent: event];
+                    
                     [self sendEventWithName:kTwilioVoiceReactNativeScopeVoice
                                        body:@{kTwilioVoiceReactNativeVoiceEventType: kTwilioVoiceReactNativeVoiceEventError,
                                               kTwilioVoiceReactNativeVoiceErrorKeyError: @{kTwilioVoiceReactNativeVoiceErrorKeyCode: @(error.code),
                                                                                            kTwilioVoiceReactNativeVoiceErrorKeyMessage: [error localizedDescription]}}];
                     reject(kTwilioVoiceReactNativeVoiceError, errorMessage, nil);
                 } else {
+                    MGOLogEvent *event = [MGOLogEvent infoWithMessage:@"unregister success"
+                                                              eventId:@"twilio_voice_unregister_success"
+                                                               params:nil
+                                                               result:nil
+                                                              context:nil
+                                                                  tag:kTwilioVoiceLoggerTag];
+                    [MGOLogger logEvent: event];
+                    
                     [self sendEventWithName:kTwilioVoiceReactNativeScopeVoice
                                        body:@{kTwilioVoiceReactNativeVoiceEventType: kTwilioVoiceReactNativeVoiceEventUnregistered}];
                     resolve(nil);
                 }
             }];
         } else {
+            MGOLogEvent *event = [MGOLogEvent errorWithMessage:@"Failed to unregister: no pushKit device token"
+                                                       eventId:@"twilio_voice_unregister_failure"
+                                                        params:nil
+                                                         error:@"no pushKit device token"
+                                                       context:nil
+                                                           tag:kTwilioVoiceLoggerTag];
+            [MGOLogger logEvent: event];
+            
             self.registrationInProgress = NO;
             reject(kTwilioVoiceReactNativeVoiceError, @"Failed to initialize PushKit device token", nil);
         }
@@ -589,6 +704,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     [self makeCallWithAccessToken:accessToken params:params contactHandle:contactHandle];
     self.callPromiseResolver = resolve;
 }
@@ -596,6 +712,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
 RCT_EXPORT_METHOD(voice_getCalls:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     NSMutableArray *callInfoArray = [NSMutableArray array];
     for (NSString *uuid in [self.callMap allKeys]) {
         TVOCall *call = self.callMap[uuid];
@@ -607,6 +724,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
 RCT_EXPORT_METHOD(voice_getCallInvites:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     NSMutableArray *callInviteInfoArray = [NSMutableArray array];
     for (NSString *uuid in [self.callInviteMap allKeys]) {
         TVOCallInvite *callInvite = self.callInviteMap[uuid];
@@ -618,6 +736,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
 RCT_EXPORT_METHOD(voice_getAudioDevices:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     NSMutableArray *nativeAudioDeviceInfos = [NSMutableArray array];
     for (NSString *key in [self.audioDevices allKeys]) {
         [nativeAudioDeviceInfos addObject:self.audioDevices[key]];
@@ -630,6 +749,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     if ([self selectAudioDevice:uuid]) {
         resolve(nil);
     } else {
@@ -640,6 +760,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
 RCT_EXPORT_METHOD(voice_showNativeAvRoutePicker:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     TVRNAVRoutePickerView *routePicker = [[TVRNAVRoutePickerView alloc] initWithFrame:CGRectZero];
 
     UIWindow *window = [UIApplication sharedApplication].windows[0];
@@ -665,6 +786,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     if (self.callMap[uuid]) {
         [self endCallWithUuid:[[NSUUID alloc] initWithUUIDString:uuid]];
         resolve(nil);
@@ -677,6 +799,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     TVOCall *call = self.callMap[uuid];
     NSString *state = @"";
     if (call) {
@@ -690,6 +813,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     TVOCall *call = self.callMap[uuid];
     NSString *callSid = (call && call.state != TVOCallStateConnecting)? call.sid : @"";
 
@@ -700,6 +824,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     TVOCall *call = self.callMap[uuid];
     NSString *from = (call && [call.from length] > 0)? call.from : @"";
 
@@ -710,6 +835,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     TVOCall *call = self.callMap[uuid];
     NSString *to = (call && [call.to length] > 0)? call.to : @"";
 
@@ -721,6 +847,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     TVOCall *call = self.callMap[uuid];
     if (call) {
         [call setOnHold:onHold];
@@ -734,6 +861,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     TVOCall *call = self.callMap[uuid];
     if (call) {
         resolve(@(call.isOnHold));
@@ -747,6 +875,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     TVOCall *call = self.callMap[uuid];
     if (call) {
         [call setMuted:muted];
@@ -760,6 +889,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     TVOCall *call = self.callMap[uuid];
     if (call) {
         resolve(@(call.isMuted));
@@ -773,6 +903,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     TVOCall *call = self.callMap[uuid];
     if (call) {
         [call sendDigits:digits];
@@ -788,6 +919,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     TVOCall *call = self.callMap[uuid];
     if (call) {
         [call postFeedback:[self scoreFromString:score] issue:[self issueFromString:issue]];
@@ -801,6 +933,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     TVOCall *call = self.callMap[uuid];
     if (call) {
         [call getStatsWithBlock:^(NSArray<TVOStatsReport *> *statsReports) {
@@ -820,6 +953,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     TVOCallInvite *callInvite = self.callInviteMap[uuid];
     TVOCall *call = self.callMap[uuid];
     if (call) {
@@ -829,6 +963,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
             builder.contentType = contentType;
         }];
         NSString *voiceEventSid = [call sendMessage:callMessage];
+        
         resolve(voiceEventSid);
     } else if (callInvite) {
         TVOCallMessage *callMessage = [TVOCallMessage messageWithContent:content
@@ -850,6 +985,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     [self answerCallInvite:[[NSUUID alloc] initWithUUIDString:callInviteUuid]
                 completion:^(BOOL success) {
         if (success) {
@@ -875,6 +1011,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     [self endCallWithUuid:[[NSUUID alloc] initWithUUIDString:callInviteUuid]];
     resolve(nil);
 }
@@ -883,6 +1020,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     resolve(@(YES));
 }
 
@@ -890,6 +1028,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     if (self.callInviteMap[callInviteUuid]) {
         TVOCallInvite *callInvite = self.callInviteMap[callInviteUuid];
         resolve(callInvite.callSid);
@@ -902,6 +1041,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     if (self.callInviteMap[callInviteUuid]) {
         TVOCallInvite *callInvite = self.callInviteMap[callInviteUuid];
         resolve(callInvite.from);
@@ -914,6 +1054,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     if (self.callInviteMap[callInviteUuid]) {
         TVOCallInvite *callInvite = self.callInviteMap[callInviteUuid];
         resolve(callInvite.to);
@@ -927,6 +1068,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     if (self.callInviteMap[callInviteUuid]) {
         [self updateCall:callInviteUuid callerHandle:handle];
         resolve(nil);
@@ -939,6 +1081,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     if (self.cancelledCallInviteMap[cancelledCallInviteUuid]) {
         TVOCancelledCallInvite *cancelledCallInvite = self.cancelledCallInviteMap[cancelledCallInviteUuid];
         resolve(cancelledCallInvite.callSid);
@@ -951,6 +1094,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     if (self.cancelledCallInviteMap[cancelledCallInviteUuid]) {
         TVOCancelledCallInvite *cancelledCallInvite = self.cancelledCallInviteMap[cancelledCallInviteUuid];
         resolve(cancelledCallInvite.from);
@@ -963,6 +1107,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     if (self.cancelledCallInviteMap[cancelledCallInviteUuid]) {
         TVOCancelledCallInvite *cancelledCallInvite = self.cancelledCallInviteMap[cancelledCallInviteUuid];
         resolve(cancelledCallInvite.to);
@@ -976,6 +1121,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
 RCT_EXPORT_METHOD(util_generateId:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     resolve([NSUUID UUID].UUIDString);
 }
 
@@ -983,6 +1129,7 @@ - (void)asyncPushRegistryInitialization:(dispatch_time_t)timeout
                   resolver:(RCTPromiseResolveBlock)resolve
                   rejecter:(RCTPromiseRejectBlock)reject)
 {
+    TwilioVoiceLogInvoke();
     self.incomingCallContactHandleTemplate = template;
     resolve(NULL);
 }
diff --git a/twilio-voice-react-native.podspec b/twilio-voice-react-native.podspec
index 3b1aee529dc924d6dbd444914f80d5233d1bbca0..15637216f35fba2f229c5d88a35af5b5961610a5 100644
--- a/twilio-voice-react-native.podspec
+++ b/twilio-voice-react-native.podspec
@@ -17,6 +17,7 @@ Pod::Spec.new do |s|
 
   s.dependency "React-Core"
   s.dependency "TwilioVoice", "6.12.1"
+  s.dependency 'MoegoLogger'
   s.xcconfig  =  { 'VALID_ARCHS' => 'arm64 x86_64' }
   s.pod_target_xcconfig   = { 'VALID_ARCHS[sdk=iphoneos*]' => 'arm64', 'VALID_ARCHS[sdk=iphonesimulator*]' => 'arm64 x86_64' }
 end

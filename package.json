{"name": "moego-mobile", "version": "1.0.0", "main": "index.js", "scripts": {"start": "BRANCH_NAME=$(git branch --show-current) CI_ACTION=LOCALLY_DEV expo start", "android": "BRANCH_NAME=$(git branch --show-current) CI_ACTION=LOCALLY_DEV expo run:android", "ios": "BRANCH_NAME=$(git branch --show-current) CI_ACTION=LOCALLY_DEV expo run:ios", "preinstall": "npx only-allow pnpm", "test": "jest", "test:coverage": "jest --coverage --watchAll", "emulator-android": "./scripts/start_android_emulator.sh", "icon": "node ./scripts/build_icons.js --basedir assets/images --prefix Icon --svg --preview --open", "icon:nps": "node ./scripts/build_icons.js --basedir assets/images/nps --prefix IconNPS --svg --preview --open", "styles": "node ./scripts/tasks.js styles", "generate": "node ./scripts/tasks.js generate", "generate:paths": "node ./scripts/tasks.js generate-paths", "transform": "node ./scripts/tasks.js transform", "prepare": "husky install", "check:types": "tsc --noEmit", "lint-push": "bash ./ci/lint.sh all && git push -u origin $(git rev-parse --abbrev-ref HEAD)", "openapi": "openapi2dts --outputRest src/types/openApi/", "replace-env": "tsx ./scripts/replace_env.ts", "generate:bundle": "tsx ./scripts/generate_bundle.ts", "native:bump-version": "tsx ./scripts/bump_version.ts", "native:preassemble": "tsx ./scripts/preassemble.ts", "prettier": "prettier --write \"./src/**/*.{md,ts,tsx,js,jsx,css,less,scss,html,json,yaml}\""}, "lint-staged": {"**/*.{md,ts,tsx,js,jsx,css,less,scss,html,json,yaml}": "prettier --write", "*.{ts,tsx,js,jsx}": "pnpm eslint --cache --fix --quiet"}, "dependencies": {"@ant-design/react-native": "5.0.5", "@datadog/mobile-react-native": "^2.4.4", "@datadog/mobile-react-navigation": "^2.8.1", "@expo/react-native-action-sheet": "^3.12.0", "@googlemaps/google-maps-services-js": "3.3.3", "@gorhom/portal": "^1.0.14", "@growthbook/growthbook-react": "^1.0.1", "@intercom/intercom-react-native": "^7.2.1", "@mapbox/polyline": "^1.1.1", "@moego/api-web": "1.***********.1", "@moego/bff-openapi": "^0.0.63", "@moego/finance-assets": "1.0.15", "@moego/finance-business-app-kit": "1.0.15", "@moego/finance-plugins": "1.0.15", "@moego/finance-utils": "1.0.15", "@moego/http-client": "0.137.0", "@moego/icons-react-native": "^1.4.0", "@moego/react-native-logger": "workspace:*", "@moego/react-native-traceroute": "workspace:*", "@moego/tools": "^0.136.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-camera-roll/camera-roll": "^6.0.2", "@react-native-clipboard/clipboard": "^1.14.1", "@react-native-community/netinfo": "^11.3.1", "@react-native-community/segmented-control": "^2.2.2", "@react-native-community/slider": "^4.5.2", "@react-native-firebase/analytics": "^20.4.0", "@react-native-firebase/app": "^20.4.0", "@react-native-picker/picker": "2.4.10", "@react-navigation/drawer": "^6.6.6", "@react-navigation/elements": "^1.3.21", "@react-navigation/native": "^6.1.9", "@react-navigation/routers": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@sentry/react-native": "5.33.1", "@stripe/stripe-react-native": "0.37.2", "@stripe/stripe-terminal-react-native": "0.0.1-beta.21", "@twilio/voice-react-native-sdk": "1.6.1", "@types/language-tags": "^1.0.4", "@types/sprintf-js": "^1.1.4", "@wuba/react-native-echarts": "^1.3.1", "ahooks": "^3.8.1", "async-mutex": "^0.4.0", "axios": "^1.7.2", "big.js": "^6.2.1", "buffer": "^6.0.3", "chroma-js": "^3.1.1", "country-data-list": "^1.2.1", "date-holidays": "^3.21.1", "dayjs": "^1.11.13", "echarts": "^5.5.0", "emoji-regex": "^10.3.0", "expo": "~51.0.39", "expo-application": "~5.9.1", "expo-av": "~14.0.7", "expo-blur": "~13.0.2", "expo-clipboard": "~6.0.3", "expo-constants": "~16.0.2", "expo-contacts": "~13.0.5", "expo-dev-client": "~4.0.26", "expo-device": "~6.0.2", "expo-file-system": "~17.0.1", "expo-font": "~12.0.10", "expo-haptics": "~13.0.1", "expo-image-manipulator": "~12.0.5", "expo-image-picker": "~15.0.7", "expo-linear-gradient": "~13.0.2", "expo-localization": "~15.0.3", "expo-location": "~17.0.1", "expo-mail-composer": "~13.0.1", "expo-media-library": "~16.0.5", "expo-notifications": "~0.28.19", "expo-screen-orientation": "~7.0.5", "expo-secure-store": "~13.0.2", "expo-splash-screen": "~0.27.6", "expo-status-bar": "~1.12.1", "expo-system-ui": "^3.0.7", "expo-task-manager": "~11.8.2", "expo-updates": "~0.25.26", "expo-video-thumbnails": "~8.0.0", "expo-web-browser": "~13.0.3", "geolib": "^3.3.3", "hex-to-rgba": "^2.0.1", "install": "^0.13.0", "javascript-time-ago": "^2.3.10", "js-base64": "^3.7.7", "language-tags": "^1.0.9", "languagedetect": "^2.0.0", "lodash": "^4.17.21", "monofile-utilities": "^5.0.0", "numeral": "^2.0.6", "ordinal": "^1.0.3", "posthog-react-native": "^2.8.1", "react": "18.2.0", "react-hook-form": "^7.53.0", "react-native": "0.74.5", "react-native-actionsheet": "^2.4.2", "react-native-background-timer": "^2.4.1", "react-native-bundle-splitter": "^3.0.1", "react-native-chart-kit": "^6.12.0", "react-native-countdown-circle-timer": "^2.5.4", "react-native-date-picker": "^4.3.3", "react-native-dns-lookup": "^1.0.6", "react-native-draggable": "^3.3.0", "react-native-draggable-flatlist": "^4.0.1", "react-native-emoji-selector": "^0.2.0", "react-native-flash-message": "^0.4.2", "react-native-gesture-handler": "~2.16.2", "react-native-hyperlink": "^0.0.22", "react-native-image-crop-picker": "0.41.6", "react-native-image-zoom-viewer": "^3.0.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-map-clustering": "^3.4.2", "react-native-map-link": "2.11.3", "react-native-maps": "1.14.0", "react-native-mime-types": "^2.4.0", "react-native-progress": "^5.0.1", "react-native-qrcode-styled": "^0.2.1", "react-native-qrcode-svg": "^6.2.0", "react-native-reanimated": "~3.13.0", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "4.10.5", "react-native-screens": "~3.31.1", "react-native-send-intent": "^1.3.0", "react-native-square-in-app-payments": "1.7.6", "react-native-square-reader-sdk": "^1.4.3", "react-native-svg": "15.2.0", "react-native-swipe-list-view": "^3.2.9", "react-native-swiper": "^1.6.0", "react-native-uuid": "^2.0.1", "react-native-view-shot": "3.8.0", "react-native-web": "~0.19.12", "react-native-webview": "13.8.6", "react-redux": "^7.2.6", "redux": "^4.1.2", "redux-batched-actions": "^0.5.0", "redux-devtools-extension": "^2.13.9", "redux-thunk": "^2.4.0", "set-cookie-parser": "^2.6.0", "sprintf-js": "^1.1.3", "stripe": "^8.191.0", "stripe-client": "^1.1.5", "timezones.json": "^1.5.3", "use-deep-compare": "^1.1.0", "validator": "^13.7.0"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/plugin-transform-logical-assignment-operators": "^7.23.3", "@commitlint/cli": "^17.7.0", "@commitlint/config-angular": "^17.7.0", "@expo/metro-config": "~0.18.11", "@moego/eslint-plugin-moego-fe": "^0.205.0", "@moego/openapi2dts": "^1.12.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.4.0", "@types/big.js": "^6.1.6", "@types/chroma-js": "^2.4.4", "@types/echarts": "^4.9.22", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.8", "@types/lodash": "^4.14.177", "@types/mime": "^3.0.4", "@types/numeral": "^2.0.2", "@types/react": "~18.2.79", "@types/react-native-actionsheet": "^2.4.6", "@types/react-native-square-in-app-payments": "1.4.4", "@types/set-cookie-parser": "^2.4.7", "babel-plugin-react-anonymous-display-name": "^0.1.0", "babel-plugin-transform-remove-console": "^6.9.4", "dpdm-fast": "^1.0.3", "eslint": "^9.22.0", "expo-build-properties": "~0.12.5", "fs-extra": "^11.1.1", "git-branch-is": "^4.0.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-expo": "~51.0.4", "lint-staged": "15.2.8", "mime": "^3.0.0", "nativewind": "^2.0.11", "prettier": "^3.0.0", "prettier-plugin-organize-imports": "^3.2.2", "react-native-svg-transformer": "^1.3.0", "svgo": "^3.0.5", "tailwindcss": "3.3.2", "tsx": "^4.7.2", "typescript": "5.8.3", "yargs": "^17.7.2"}, "private": true, "pnpm": {"patchedDependencies": {"react-native-swiper@1.6.0": "patches/<EMAIL>", "react-native-map-link@2.11.3": "patches/<EMAIL>", "react-native-square-reader-sdk@1.4.3": "patches/<EMAIL>", "expo-updates@0.25.26": "patches/<EMAIL>", "react-native@0.74.5": "patches/<EMAIL>", "expo-notifications@0.28.19": "patches/<EMAIL>", "react-native-actionsheet@2.4.2": "patches/<EMAIL>", "react-native-reanimated@3.13.0": "patches/<EMAIL>", "react-native-image-crop-picker@0.41.6": "patches/<EMAIL>", "expo@51.0.39": "patches/<EMAIL>", "@datadog/mobile-react-native@2.4.4": "patches/@<EMAIL>", "expo-modules-core@1.12.26": "patches/<EMAIL>", "react-native-screens@3.31.1": "patches/<EMAIL>", "@twilio/voice-react-native-sdk@1.6.1": "patches/@<EMAIL>"}, "overrides": {"zrender": "^5.6.0"}}, "engines": {"node": ">=16.0.0", "pnpm": "^8.4.0"}, "packageManager": "pnpm@8.15.7+sha256.50783dd0fa303852de2dd1557cd4b9f07cb5b018154a6e76d0f40635d6cee019"}
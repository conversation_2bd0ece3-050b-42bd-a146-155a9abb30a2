{"$schema": "https://moego.s3.us-west-2.amazonaws.com/ops/github-actions/ci-json-schema.json", "service_name": "moego-business-web-v2", "grey_url": "https://[grey]-go.[env].moego.dev", "slack": ["#platform-fe-alert", "#fe-infra"], "language": {"type": "node", "version": "18"}, "install": {"commands": ["pnpm i"], "cache_dir": "node_modules"}, "lint": {"commands": ["bash ci/lint.sh"]}, "build": {"commands": ["bash ci/build.sh"], "build_image": [{"dockerfile": "ci/Dockerfile", "context": "."}], "upload_cdn": [{"from": "build/static", "to": "/w3/go/v2/static", "max_age": 2592000}, {"from": "build", "to": "/w3/go/v2", "exclude": "static/**", "max_age": 0}]}, "deploy": {"type": "service"}, "tia": {"job_name": "BrowserStack Test (BWeb)", "artifact_name": "test-impact-analysis-data-bweb"}}
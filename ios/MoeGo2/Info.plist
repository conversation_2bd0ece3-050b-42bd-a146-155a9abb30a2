<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>MoeGo 2</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
	<key>CFBundleShortVersionString</key>
	<string>2.16.2</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>commoementmoegobusiness</string>
				<string>com.moement.moego.business</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.165878578571-gg182jk3638nqf6ftm5i8lms0su3pv05</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>exp+moego-business-2</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>130</string>
	<key>GMSApiKey</key>
	<string>AIzaSyDOLelnu83PZ2Oey1GO0qmcj6xIJMf7AzY</string>
	<key>INIntentsSupported</key>
	<array>
		<string>Intent</string>
	</array>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationCategoryType</key>
	<string></string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>moego-smart-schedule</string>
		<string>comgooglemaps</string>
		<string>waze</string>
		<string>square-commerce-v1</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>MoeGo would like access to Bluetooth in order to connect your device to compatible
			hardware.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>MoeGo would like access to Bluetooth in order to connect your device to compatible
			hardware.</string>
	<key>NSCameraUsageDescription</key>
	<string>To take photo for profile image for accounts, clients, and pets.</string>
	<key>NSContactsUsageDescription</key>
	<string>To create pet owner profiles from mobile contacts.</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>This app uses the local WiFi network to connect to supported card readers.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>To find clients near your current location.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>To find clients near your current location.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>MoeGo would like to access your location information in order to provide you with
			location related services. You can change location access permissions at any time in the
			application settings.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Enable MoeGo 2 access to voice call for real-time audio communication.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Save photo in message.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>To upload photo for profile image for accounts, clients, pets.</string>
	<key>UIAppFonts</key>
	<array>
		<string>ProximaNova-Regular.otf</string>
		<string>ProximaNova-Semibold.otf</string>
		<string>ProximaNova-Bold.otf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>bluetooth-central</string>
		<string>voip</string>
		<string>fetch</string>
		<string>location</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>SplashScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<false/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDefault</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>

//
//  NSDictionary+SafeInit.m
//  MoeGo2
//
//  Created on 2025-08-08
//  Copyright © 2025 MoeGo. All rights reserved.
//

#import "NSDictionary+SafeInit.h"
#import <objc/runtime.h>

@implementation NSDictionary (SafeInit)

+ (void)load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        [self swizzleDictionaryMethods];
    });
}

+ (void)swizzleDictionaryMethods {
    Class cls = NSClassFromString(@"__NSPlaceholderDictionary");
    SEL originalSelector = @selector(initWithObjects:forKeys:count:);
    SEL swizzledSelector = @selector(safe_initWithObjects:forKeys:count:);

    Method originalMethod = class_getInstanceMethod(cls, originalSelector);
    Method swizzledMethod = class_getInstanceMethod(cls, swizzledSelector);

    method_exchangeImplementations(originalMethod, swizzledMethod);
}

- (instancetype)safe_initWithObjects:(const id [])objects forKeys:(const id<NSCopying> [])keys count:(NSUInteger)cnt {
    NSUInteger newCount = 0;
    id newObjects[cnt];
    id<NSCopying> newKeys[cnt];

    for (NSUInteger i = 0; i < cnt; i++) {
        id key = keys[i];
        id obj = objects[i];

        if (key == nil || obj == nil) {
            NSLog(@"❌ NSDictionary nil protection: key[%lu]=%@, obj[%lu]=%@", (unsigned long)i, key, (unsigned long)i, obj);
            continue;
        }

        newKeys[newCount] = key;
        newObjects[newCount] = obj;
        newCount++;
    }

    return [self safe_initWithObjects:newObjects forKeys:newKeys count:newCount];
}

@end

PODS:
  - boost (1.83.0)
  - DatadogCore (2.18.0):
    - DatadogInternal (= 2.18.0)
  - DatadogCrashReporting (2.18.0):
    - DatadogInternal (= 2.18.0)
    - P<PERSON>rashReporter (~> 1.11.2)
  - DatadogInternal (2.18.0)
  - DatadogLogs (2.18.0):
    - DatadogInternal (= 2.18.0)
  - DatadogRUM (2.18.0):
    - DatadogInternal (= 2.18.0)
  - DatadogSDKReactNative (2.4.4):
    - DatadogCore (~> 2.18.0)
    - DatadogCrashReporting (~> 2.18.0)
    - DatadogLogs (~> 2.18.0)
    - DatadogRUM (~> 2.18.0)
    - DatadogTrace (~> 2.18.0)
    - DatadogWebViewTracking (~> 2.18.0)
    - React-Core
  - DatadogTrace (2.18.0):
    - DatadogInternal (= 2.18.0)
    - OpenTelemetrySwiftApi (= 1.6.0)
  - DatadogWebViewTracking (2.18.0):
    - DatadogInternal (= 2.18.0)
  - DoubleConversion (1.1.6)
  - EASClient (0.12.0):
    - ExpoModulesCore
  - EXApplication (5.9.1):
    - ExpoModulesCore
  - EXAV (14.0.7):
    - ExpoModulesCore
    - ReactCommon/turbomodule/core
  - EXConstants (16.0.2):
    - ExpoModulesCore
  - EXImageLoader (4.7.0):
    - ExpoModulesCore
    - React-Core
  - EXJSONUtils (0.13.1)
  - EXLocation (17.0.1):
    - ExpoModulesCore
  - EXManifests (0.14.3):
    - ExpoModulesCore
  - EXNotifications (0.28.19):
    - ExpoModulesCore
  - Expo (51.0.39):
    - ExpoModulesCore
  - expo-dev-client (4.0.27):
    - EXManifests
    - expo-dev-launcher
    - expo-dev-menu
    - expo-dev-menu-interface
    - EXUpdatesInterface
  - expo-dev-launcher (4.0.27):
    - DoubleConversion
    - EXManifests
    - expo-dev-launcher/Main (= 4.0.27)
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-launcher/Main (4.0.27):
    - DoubleConversion
    - EXManifests
    - expo-dev-launcher/Unsafe
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-launcher/Unsafe (4.0.27):
    - DoubleConversion
    - EXManifests
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu (5.0.21):
    - DoubleConversion
    - expo-dev-menu/Main (= 5.0.21)
    - expo-dev-menu/ReactNativeCompatibles (= 5.0.21)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu-interface (1.8.3)
  - expo-dev-menu/Main (5.0.21):
    - DoubleConversion
    - EXManifests
    - expo-dev-menu-interface
    - expo-dev-menu/Vendored
    - ExpoModulesCore
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu/ReactNativeCompatibles (5.0.21):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu/SafeAreaView (5.0.21):
    - DoubleConversion
    - ExpoModulesCore
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu/Vendored (5.0.21):
    - DoubleConversion
    - expo-dev-menu/SafeAreaView
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - ExpoAsset (10.0.10):
    - ExpoModulesCore
  - ExpoBlur (13.0.2):
    - ExpoModulesCore
  - ExpoClipboard (6.0.3):
    - ExpoModulesCore
  - ExpoContacts (13.0.5):
    - ExpoModulesCore
  - ExpoDevice (6.0.2):
    - ExpoModulesCore
  - ExpoFileSystem (17.0.1):
    - ExpoModulesCore
  - ExpoFont (12.0.10):
    - ExpoModulesCore
  - ExpoHaptics (13.0.1):
    - ExpoModulesCore
  - ExpoImageManipulator (12.0.5):
    - EXImageLoader
    - ExpoModulesCore
    - SDWebImageWebPCoder
  - ExpoImagePicker (15.0.7):
    - ExpoModulesCore
  - ExpoKeepAwake (13.0.2):
    - ExpoModulesCore
  - ExpoLinearGradient (13.0.2):
    - ExpoModulesCore
  - ExpoLocalization (15.0.3):
    - ExpoModulesCore
  - ExpoMailComposer (13.0.1):
    - ExpoModulesCore
  - ExpoMediaLibrary (16.0.5):
    - ExpoModulesCore
    - React-Core
  - ExpoModulesCore (1.12.26):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - ExpoScreenOrientation (7.0.5):
    - DoubleConversion
    - ExpoModulesCore
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - ExpoSecureStore (13.0.2):
    - ExpoModulesCore
  - ExpoSystemUI (3.0.7):
    - ExpoModulesCore
  - ExpoVideoThumbnails (8.0.0):
    - ExpoModulesCore
  - ExpoWebBrowser (13.0.3):
    - ExpoModulesCore
  - EXSplashScreen (0.27.6):
    - DoubleConversion
    - ExpoModulesCore
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - EXStructuredHeaders (3.8.0)
  - EXTaskManager (11.8.2):
    - ExpoModulesCore
    - UMAppLoader
  - EXUpdates (0.25.26):
    - DoubleConversion
    - EASClient
    - EXManifests
    - ExpoModulesCore
    - EXStructuredHeaders
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - ReachabilitySwift
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - "sqlite3 (~> 3.45.3+1)"
    - Yoga
  - EXUpdatesInterface (0.16.2):
    - ExpoModulesCore
  - FBLazyVector (0.74.5)
  - Firebase/Analytics (10.29.0):
    - Firebase/Core
  - Firebase/Core (10.29.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.29.0)
  - Firebase/CoreOnly (10.29.0):
    - FirebaseCore (= 10.29.0)
  - FirebaseAnalytics (10.29.0):
    - FirebaseAnalytics/AdIdSupport (= 10.29.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.29.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.29.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseCore (10.29.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - fmt (9.1.0)
  - glog (0.3.5)
  - Google-Maps-iOS-Utils (4.2.2):
    - Google-Maps-iOS-Utils/Clustering (= 4.2.2)
    - Google-Maps-iOS-Utils/Geometry (= 4.2.2)
    - Google-Maps-iOS-Utils/GeometryUtils (= 4.2.2)
    - Google-Maps-iOS-Utils/Heatmap (= 4.2.2)
    - Google-Maps-iOS-Utils/QuadTree (= 4.2.2)
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Clustering (4.2.2):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Geometry (4.2.2):
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/GeometryUtils (4.2.2):
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Heatmap (4.2.2):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/QuadTree (4.2.2):
    - GoogleMaps (~> 7.3)
  - GoogleAnalytics (3.23.0):
    - GoogleUtilities/UserDefaults (~> 7.11)
  - GoogleAppMeasurement (10.29.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.29.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.29.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.29.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.29.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleMaps (7.4.0):
    - GoogleMaps/Maps (= 7.4.0)
  - GoogleMaps/Base (7.4.0)
  - GoogleMaps/Maps (7.4.0):
    - GoogleMaps/Base
  - GoogleTagManager (7.4.6):
    - FirebaseAnalytics (< 12.0, >= 10.0)
    - GoogleAnalytics (~> 3.23)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.74.5):
    - hermes-engine/Pre-built (= 0.74.5)
  - hermes-engine/Pre-built (0.74.5)
  - Intercom (17.2.2)
  - intercom-react-native (7.2.1):
    - Intercom (~> 17.2.2)
    - React-Core
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - MMKV (1.3.14):
    - MMKVCore (~> 1.3.14)
  - MMKVCore (1.3.14)
  - MoegoLogger (0.1.0):
    - DatadogSDKReactNative
    - MMKV (~> 1.3.4)
  - MoegoTraceroute (0.1.0):
    - React-Core
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - OpenTelemetrySwiftApi (1.6.0)
  - PLCrashReporter (1.11.2)
  - PromisesObjC (2.4.0)
  - RCT-Folly (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Default (= 2024.01.01.00)
  - RCT-Folly/Default (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
  - RCT-Folly/Fabric (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
  - RCTDeprecation (0.74.5)
  - RCTRequired (0.74.5)
  - RCTTypeSafety (0.74.5):
    - FBLazyVector (= 0.74.5)
    - RCTRequired (= 0.74.5)
    - React-Core (= 0.74.5)
  - ReachabilitySwift (5.2.4)
  - React (0.74.5):
    - React-Core (= 0.74.5)
    - React-Core/DevSupport (= 0.74.5)
    - React-Core/RCTWebSocket (= 0.74.5)
    - React-RCTActionSheet (= 0.74.5)
    - React-RCTAnimation (= 0.74.5)
    - React-RCTBlob (= 0.74.5)
    - React-RCTImage (= 0.74.5)
    - React-RCTLinking (= 0.74.5)
    - React-RCTNetwork (= 0.74.5)
    - React-RCTSettings (= 0.74.5)
    - React-RCTText (= 0.74.5)
    - React-RCTVibration (= 0.74.5)
  - React-callinvoker (0.74.5)
  - React-Codegen (0.74.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.74.5)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/CoreModulesHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/Default (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/DevSupport (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.74.5)
    - React-Core/RCTWebSocket (= 0.74.5)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTBlobHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTImageHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTTextHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTWebSocket (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.74.5)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-CoreModules (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety (= 0.74.5)
    - React-Codegen
    - React-Core/CoreModulesHeaders (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.74.5)
    - ReactCommon
    - SocketRocket (= 0.7.0)
  - React-cxxreact (0.74.5):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.74.5)
    - React-debug (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-jsinspector
    - React-logger (= 0.74.5)
    - React-perflogger (= 0.74.5)
    - React-runtimeexecutor (= 0.74.5)
  - React-debug (0.74.5)
  - React-Fabric (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.74.5)
    - React-Fabric/attributedstring (= 0.74.5)
    - React-Fabric/componentregistry (= 0.74.5)
    - React-Fabric/componentregistrynative (= 0.74.5)
    - React-Fabric/components (= 0.74.5)
    - React-Fabric/core (= 0.74.5)
    - React-Fabric/imagemanager (= 0.74.5)
    - React-Fabric/leakchecker (= 0.74.5)
    - React-Fabric/mounting (= 0.74.5)
    - React-Fabric/scheduler (= 0.74.5)
    - React-Fabric/telemetry (= 0.74.5)
    - React-Fabric/templateprocessor (= 0.74.5)
    - React-Fabric/textlayoutmanager (= 0.74.5)
    - React-Fabric/uimanager (= 0.74.5)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/inputaccessory (= 0.74.5)
    - React-Fabric/components/legacyviewmanagerinterop (= 0.74.5)
    - React-Fabric/components/modal (= 0.74.5)
    - React-Fabric/components/rncore (= 0.74.5)
    - React-Fabric/components/root (= 0.74.5)
    - React-Fabric/components/safeareaview (= 0.74.5)
    - React-Fabric/components/scrollview (= 0.74.5)
    - React-Fabric/components/text (= 0.74.5)
    - React-Fabric/components/textinput (= 0.74.5)
    - React-Fabric/components/unimplementedview (= 0.74.5)
    - React-Fabric/components/view (= 0.74.5)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/inputaccessory (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/modal (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/rncore (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/safeareaview (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/text (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/textinput (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/unimplementedview (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/textlayoutmanager (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricImage (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired (= 0.74.5)
    - RCTTypeSafety (= 0.74.5)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.74.5)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-featureflags (0.74.5)
  - React-graphics (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-Core/Default (= 0.74.5)
    - React-utils
  - React-hermes (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-cxxreact (= 0.74.5)
    - React-jsi
    - React-jsiexecutor (= 0.74.5)
    - React-jsinspector
    - React-perflogger (= 0.74.5)
    - React-runtimeexecutor
  - React-ImageManager (0.74.5):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.74.5):
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-debug
    - React-jsi
    - React-Mapbuffer
  - React-jsi (0.74.5):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
  - React-jsiexecutor (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-cxxreact (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-jsinspector
    - React-perflogger (= 0.74.5)
  - React-jsinspector (0.74.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-featureflags
    - React-jsi
    - React-runtimeexecutor (= 0.74.5)
  - React-jsitracing (0.74.5):
    - React-jsi
  - React-logger (0.74.5):
    - glog
  - React-Mapbuffer (0.74.5):
    - glog
    - React-debug
  - react-native-background-timer (2.4.1):
    - React-Core
  - react-native-cameraroll (6.0.2):
    - React-Core
  - react-native-date-picker (4.4.2):
    - React-Core
  - react-native-google-maps (1.14.0):
    - Google-Maps-iOS-Utils (= 4.2.2)
    - GoogleMaps (= 7.4.0)
    - React-Core
  - react-native-maps (1.14.0):
    - React-Core
  - react-native-netinfo (11.3.1):
    - React-Core
  - react-native-render-html (6.3.4):
    - React-Core
  - react-native-safe-area-context (4.10.5):
    - React-Core
  - react-native-segmented-control (2.2.2):
    - React-Core
  - react-native-slider (4.5.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-view-shot (3.8.0):
    - React-Core
  - react-native-webview (13.8.6):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-nativeconfig (0.74.5)
  - React-NativeModulesApple (0.74.5):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.74.5)
  - React-RCTActionSheet (0.74.5):
    - React-Core/RCTActionSheetHeaders (= 0.74.5)
  - React-RCTAnimation (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTAppDelegate (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-CoreModules
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - ReactCommon
  - React-RCTBlob (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-Codegen
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-nativeconfig
    - React-RCTImage
    - React-RCTText
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.74.5):
    - React-Codegen
    - React-Core/RCTLinkingHeaders (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-NativeModulesApple
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.74.5)
  - React-RCTNetwork (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTSettings (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTText (0.74.5):
    - React-Core/RCTTextHeaders (= 0.74.5)
    - Yoga
  - React-RCTVibration (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - React-Codegen
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-rendererdebug (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - RCT-Folly (= 2024.01.01.00)
    - React-debug
  - React-rncore (0.74.5)
  - React-RuntimeApple (0.74.5):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-utils
  - React-RuntimeCore (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
  - React-runtimeexecutor (0.74.5):
    - React-jsi (= 0.74.5)
  - React-RuntimeHermes (0.74.5):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsitracing
    - React-nativeconfig
    - React-RuntimeCore
    - React-utils
  - React-runtimescheduler (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-debug
    - React-jsi (= 0.74.5)
  - ReactCommon (0.74.5):
    - ReactCommon/turbomodule (= 0.74.5)
  - ReactCommon/turbomodule (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.74.5)
    - React-cxxreact (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-logger (= 0.74.5)
    - React-perflogger (= 0.74.5)
    - ReactCommon/turbomodule/bridging (= 0.74.5)
    - ReactCommon/turbomodule/core (= 0.74.5)
  - ReactCommon/turbomodule/bridging (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.74.5)
    - React-cxxreact (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-logger (= 0.74.5)
    - React-perflogger (= 0.74.5)
  - ReactCommon/turbomodule/core (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.74.5)
    - React-cxxreact (= 0.74.5)
    - React-debug (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-logger (= 0.74.5)
    - React-perflogger (= 0.74.5)
    - React-utils (= 0.74.5)
  - RNCAsyncStorage (1.23.1):
    - React-Core
  - RNCClipboard (1.14.2):
    - React-Core
  - RNCPicker (2.4.10):
    - React-Core
  - RNDnsLookup (1.0.6):
    - React
  - RNFBAnalytics (20.5.0):
    - Firebase/Analytics (= 10.29.0)
    - React-Core
    - RNFBApp
  - RNFBApp (20.5.0):
    - Firebase/CoreOnly (= 10.29.0)
    - React-Core
  - RNGestureHandler (2.16.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNImageCropPicker (0.41.6):
    - React-Core
    - React-RCTImage
    - RNImageCropPicker/QBImagePickerController (= 0.41.6)
    - TOCropViewController (~> 2.7.4)
  - RNImageCropPicker/QBImagePickerController (0.41.6):
    - React-Core
    - React-RCTImage
    - TOCropViewController (~> 2.7.4)
  - RNReaderSDK (1.1.4):
    - React
  - RNReanimated (3.13.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.31.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNSentry (5.33.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Sentry/HybridSDK (= 8.36.0)
    - Yoga
  - RNSquareInAppPayments (1.6.3):
    - React
    - SquareBuyerVerificationSDK (= 1.6.3)
    - SquareInAppPaymentsSDK (= 1.6.3)
  - RNSquarePos (1.0.13):
    - React
    - SquarePointOfSaleSDK
  - RNSVG (15.2.0):
    - React-Core
  - SDWebImage/Core (5.20.0)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - Sentry/HybridSDK (8.36.0)
  - SocketRocket (0.7.0)
  - "sqlite3 (3.45.3+1)":
    - "sqlite3/common (= 3.45.3+1)"
  - "sqlite3/common (3.45.3+1)"
  - SquareBuyerVerificationSDK (1.6.3)
  - SquareInAppPaymentsSDK (1.6.3)
  - SquarePointOfSaleSDK (3.5.4)
  - Stripe (23.26.0):
    - StripeApplePay (= 23.26.0)
    - StripeCore (= 23.26.0)
    - StripePayments (= 23.26.0)
    - StripePaymentsUI (= 23.26.0)
    - StripeUICore (= 23.26.0)
  - stripe-react-native (0.37.2):
    - React-Core
    - Stripe (~> 23.26.0)
    - StripeApplePay (~> 23.26.0)
    - StripeFinancialConnections (~> 23.26.0)
    - StripePayments (~> 23.26.0)
    - StripePaymentSheet (~> 23.26.0)
    - StripePaymentsUI (~> 23.26.0)
  - stripe-terminal-react-native (0.0.1-beta.21):
    - React-Core
    - StripeTerminal (~> 3.8.3)
  - StripeApplePay (23.26.0):
    - StripeCore (= 23.26.0)
  - StripeCore (23.26.0)
  - StripeFinancialConnections (23.26.0):
    - StripeCore (= 23.26.0)
    - StripeUICore (= 23.26.0)
  - StripePayments (23.26.0):
    - StripeCore (= 23.26.0)
    - StripePayments/Stripe3DS2 (= 23.26.0)
  - StripePayments/Stripe3DS2 (23.26.0):
    - StripeCore (= 23.26.0)
  - StripePaymentSheet (23.26.0):
    - StripeApplePay (= 23.26.0)
    - StripeCore (= 23.26.0)
    - StripePayments (= 23.26.0)
    - StripePaymentsUI (= 23.26.0)
  - StripePaymentsUI (23.26.0):
    - StripeCore (= 23.26.0)
    - StripePayments (= 23.26.0)
    - StripeUICore (= 23.26.0)
  - StripeTerminal (3.8.3)
  - StripeUICore (23.26.0):
    - StripeCore (= 23.26.0)
  - TOCropViewController (2.7.4)
  - twilio-voice-react-native (1.6.1):
    - MoegoLogger
    - React-Core
    - TwilioVoice (= 6.12.1)
  - TwilioVoice (6.12.1)
  - UMAppLoader (4.6.0)
  - Yoga (0.0.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - "DatadogSDKReactNative (from `../node_modules/@datadog/mobile-react-native`)"
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EASClient (from `../node_modules/expo-eas-client/ios`)
  - EXApplication (from `../node_modules/expo-application/ios`)
  - EXAV (from `../node_modules/expo-av/ios`)
  - EXConstants (from `../node_modules/expo-constants/ios`)
  - EXImageLoader (from `../node_modules/expo-image-loader/ios`)
  - EXJSONUtils (from `../node_modules/expo-json-utils/ios`)
  - EXLocation (from `../node_modules/expo-location/ios`)
  - EXManifests (from `../node_modules/expo-manifests/ios`)
  - EXNotifications (from `../node_modules/expo-notifications/ios`)
  - Expo (from `../node_modules/expo`)
  - expo-dev-client (from `../node_modules/expo-dev-client/ios`)
  - expo-dev-launcher (from `../node_modules/expo-dev-launcher`)
  - expo-dev-menu (from `../node_modules/expo-dev-menu`)
  - expo-dev-menu-interface (from `../node_modules/expo-dev-menu-interface/ios`)
  - ExpoAsset (from `../node_modules/expo-asset/ios`)
  - ExpoBlur (from `../node_modules/expo-blur/ios`)
  - ExpoClipboard (from `../node_modules/expo-clipboard/ios`)
  - ExpoContacts (from `../node_modules/expo-contacts/ios`)
  - ExpoDevice (from `../node_modules/expo-device/ios`)
  - ExpoFileSystem (from `../node_modules/expo-file-system/ios`)
  - ExpoFont (from `../node_modules/expo-font/ios`)
  - ExpoHaptics (from `../node_modules/expo-haptics/ios`)
  - ExpoImageManipulator (from `../node_modules/expo-image-manipulator/ios`)
  - ExpoImagePicker (from `../node_modules/expo-image-picker/ios`)
  - ExpoKeepAwake (from `../node_modules/expo-keep-awake/ios`)
  - ExpoLinearGradient (from `../node_modules/expo-linear-gradient/ios`)
  - ExpoLocalization (from `../node_modules/expo-localization/ios`)
  - ExpoMailComposer (from `../node_modules/expo-mail-composer/ios`)
  - ExpoMediaLibrary (from `../node_modules/expo-media-library/ios`)
  - ExpoModulesCore (from `../node_modules/expo-modules-core`)
  - ExpoScreenOrientation (from `../node_modules/expo-screen-orientation/ios`)
  - ExpoSecureStore (from `../node_modules/expo-secure-store/ios`)
  - ExpoSystemUI (from `../node_modules/expo-system-ui/ios`)
  - ExpoVideoThumbnails (from `../node_modules/expo-video-thumbnails/ios`)
  - ExpoWebBrowser (from `../node_modules/expo-web-browser/ios`)
  - EXSplashScreen (from `../node_modules/expo-splash-screen/ios`)
  - EXStructuredHeaders (from `../node_modules/expo-structured-headers/ios`)
  - EXTaskManager (from `../node_modules/expo-task-manager/ios`)
  - EXUpdates (from `../node_modules/expo-updates/ios`)
  - EXUpdatesInterface (from `../node_modules/expo-updates-interface/ios`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - fmt (from `../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleTagManager
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - "intercom-react-native (from `../node_modules/@intercom/intercom-react-native`)"
  - "MoegoLogger (from `../node_modules/@moego/react-native-logger`)"
  - "MoegoTraceroute (from `../node_modules/@moego/react-native-traceroute`)"
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - react-native-background-timer (from `../node_modules/react-native-background-timer`)
  - "react-native-cameraroll (from `../node_modules/@react-native-camera-roll/camera-roll`)"
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - react-native-google-maps (from `../node_modules/react-native-maps`)
  - react-native-maps (from `../node_modules/react-native-maps`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-render-html (from `../node_modules/react-native-render-html`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-segmented-control (from `../node_modules/@react-native-community/segmented-control`)"
  - "react-native-slider (from `../node_modules/@react-native-community/slider`)"
  - react-native-view-shot (from `../node_modules/react-native-view-shot`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - RNDnsLookup (from `../node_modules/react-native-dns-lookup`)
  - "RNFBAnalytics (from `../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNImageCropPicker (from `../node_modules/react-native-image-crop-picker`)
  - RNReaderSDK (from `../node_modules/react-native-square-reader-sdk`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - "RNSentry (from `../node_modules/@sentry/react-native`)"
  - RNSquareInAppPayments (from `../node_modules/react-native-square-in-app-payments`)
  - RNSquarePos (from `./RNSquarePos`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - "stripe-react-native (from `../node_modules/@stripe/stripe-react-native`)"
  - "stripe-terminal-react-native (from `../node_modules/@stripe/stripe-terminal-react-native`)"
  - "twilio-voice-react-native (from `../node_modules/@twilio/voice-react-native-sdk`)"
  - UMAppLoader (from `../node_modules/unimodules-app-loader/ios`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - DatadogCore
    - DatadogCrashReporting
    - DatadogInternal
    - DatadogLogs
    - DatadogRUM
    - DatadogTrace
    - DatadogWebViewTracking
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - Google-Maps-iOS-Utils
    - GoogleAnalytics
    - GoogleAppMeasurement
    - GoogleMaps
    - GoogleTagManager
    - GoogleUtilities
    - Intercom
    - libwebp
    - MMKV
    - MMKVCore
    - nanopb
    - OpenTelemetrySwiftApi
    - PLCrashReporter
    - PromisesObjC
    - ReachabilitySwift
    - SDWebImage
    - SDWebImageWebPCoder
    - Sentry
    - SocketRocket
    - sqlite3
    - SquareBuyerVerificationSDK
    - SquareInAppPaymentsSDK
    - SquarePointOfSaleSDK
    - Stripe
    - StripeApplePay
    - StripeCore
    - StripeFinancialConnections
    - StripePayments
    - StripePaymentSheet
    - StripePaymentsUI
    - StripeTerminal
    - StripeUICore
    - TOCropViewController
    - TwilioVoice

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DatadogSDKReactNative:
    :path: "../node_modules/@datadog/mobile-react-native"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EASClient:
    :path: "../node_modules/expo-eas-client/ios"
  EXApplication:
    :path: "../node_modules/expo-application/ios"
  EXAV:
    :path: "../node_modules/expo-av/ios"
  EXConstants:
    :path: "../node_modules/expo-constants/ios"
  EXImageLoader:
    :path: "../node_modules/expo-image-loader/ios"
  EXJSONUtils:
    :path: "../node_modules/expo-json-utils/ios"
  EXLocation:
    :path: "../node_modules/expo-location/ios"
  EXManifests:
    :path: "../node_modules/expo-manifests/ios"
  EXNotifications:
    :path: "../node_modules/expo-notifications/ios"
  Expo:
    :path: "../node_modules/expo"
  expo-dev-client:
    :path: "../node_modules/expo-dev-client/ios"
  expo-dev-launcher:
    :path: "../node_modules/expo-dev-launcher"
  expo-dev-menu:
    :path: "../node_modules/expo-dev-menu"
  expo-dev-menu-interface:
    :path: "../node_modules/expo-dev-menu-interface/ios"
  ExpoAsset:
    :path: "../node_modules/expo-asset/ios"
  ExpoBlur:
    :path: "../node_modules/expo-blur/ios"
  ExpoClipboard:
    :path: "../node_modules/expo-clipboard/ios"
  ExpoContacts:
    :path: "../node_modules/expo-contacts/ios"
  ExpoDevice:
    :path: "../node_modules/expo-device/ios"
  ExpoFileSystem:
    :path: "../node_modules/expo-file-system/ios"
  ExpoFont:
    :path: "../node_modules/expo-font/ios"
  ExpoHaptics:
    :path: "../node_modules/expo-haptics/ios"
  ExpoImageManipulator:
    :path: "../node_modules/expo-image-manipulator/ios"
  ExpoImagePicker:
    :path: "../node_modules/expo-image-picker/ios"
  ExpoKeepAwake:
    :path: "../node_modules/expo-keep-awake/ios"
  ExpoLinearGradient:
    :path: "../node_modules/expo-linear-gradient/ios"
  ExpoLocalization:
    :path: "../node_modules/expo-localization/ios"
  ExpoMailComposer:
    :path: "../node_modules/expo-mail-composer/ios"
  ExpoMediaLibrary:
    :path: "../node_modules/expo-media-library/ios"
  ExpoModulesCore:
    :path: "../node_modules/expo-modules-core"
  ExpoScreenOrientation:
    :path: "../node_modules/expo-screen-orientation/ios"
  ExpoSecureStore:
    :path: "../node_modules/expo-secure-store/ios"
  ExpoSystemUI:
    :path: "../node_modules/expo-system-ui/ios"
  ExpoVideoThumbnails:
    :path: "../node_modules/expo-video-thumbnails/ios"
  ExpoWebBrowser:
    :path: "../node_modules/expo-web-browser/ios"
  EXSplashScreen:
    :path: "../node_modules/expo-splash-screen/ios"
  EXStructuredHeaders:
    :path: "../node_modules/expo-structured-headers/ios"
  EXTaskManager:
    :path: "../node_modules/expo-task-manager/ios"
  EXUpdates:
    :path: "../node_modules/expo-updates/ios"
  EXUpdatesInterface:
    :path: "../node_modules/expo-updates-interface/ios"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-06-28-RNv0.74.3-7bda0c267e76d11b68a585f84cfdd65000babf85
  intercom-react-native:
    :path: "../node_modules/@intercom/intercom-react-native"
  MoegoLogger:
    :path: "../node_modules/@moego/react-native-logger"
  MoegoTraceroute:
    :path: "../node_modules/@moego/react-native-traceroute"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  react-native-background-timer:
    :path: "../node_modules/react-native-background-timer"
  react-native-cameraroll:
    :path: "../node_modules/@react-native-camera-roll/camera-roll"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-google-maps:
    :path: "../node_modules/react-native-maps"
  react-native-maps:
    :path: "../node_modules/react-native-maps"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-render-html:
    :path: "../node_modules/react-native-render-html"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-segmented-control:
    :path: "../node_modules/@react-native-community/segmented-control"
  react-native-slider:
    :path: "../node_modules/@react-native-community/slider"
  react-native-view-shot:
    :path: "../node_modules/react-native-view-shot"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNDnsLookup:
    :path: "../node_modules/react-native-dns-lookup"
  RNFBAnalytics:
    :path: "../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNImageCropPicker:
    :path: "../node_modules/react-native-image-crop-picker"
  RNReaderSDK:
    :path: "../node_modules/react-native-square-reader-sdk"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSentry:
    :path: "../node_modules/@sentry/react-native"
  RNSquareInAppPayments:
    :path: "../node_modules/react-native-square-in-app-payments"
  RNSquarePos:
    :path: "./RNSquarePos"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  stripe-react-native:
    :path: "../node_modules/@stripe/stripe-react-native"
  stripe-terminal-react-native:
    :path: "../node_modules/@stripe/stripe-terminal-react-native"
  twilio-voice-react-native:
    :path: "../node_modules/@twilio/voice-react-native-sdk"
  UMAppLoader:
    :path: "../node_modules/unimodules-app-loader/ios"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: d3f49c53809116a5d38da093a8aa78bf551aed09
  DatadogCore: 24f3bf4daa04949ef98b706ec4d4cb3cfde5443c
  DatadogCrashReporting: 1a16f982c569f0d7058779a4540d4506002c89c6
  DatadogInternal: 4719225e5e3a5550a52196930cecdf2a589820b2
  DatadogLogs: 0365989aebbf3d0c7cb7346cf2bbb932c0eacc01
  DatadogRUM: 8461273acc87ddfe4a8bbc6e6672ae9542471f86
  DatadogSDKReactNative: 1bdbebae33aa0b9dd4e9054a6a9bb09d8ca875c8
  DatadogTrace: cc8ae210c274aa40658da83c9cc15de85bde405a
  DatadogWebViewTracking: eeb8ea3fa13983752d2cc4dc0994664bf8388e15
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  EASClient: 1509a9a6b48b932ec61667644634daf2562983b8
  EXApplication: c08200c34daca7af7fd76ac4b9d606077410e8ad
  EXAV: afa491e598334bbbb92a92a2f4dd33d7149ad37f
  EXConstants: 409690fbfd5afea964e5e9d6c4eb2c2b59222c59
  EXImageLoader: ab589d67d6c5f2c33572afea9917304418566334
  EXJSONUtils: 30c17fd9cc364d722c0946a550dfbf1be92ef6a4
  EXLocation: 43e9b582ca63a23c6f0a18d8cbe2145b3a388b55
  EXManifests: c1fab4c3237675e7b0299ea8df0bcb14baca4f42
  EXNotifications: 85496c9fab09d759d0e4ff594bca078ab817c40c
  Expo: 8c995afb875c15bf8439af0b20bcb9ed8f90d0bd
  expo-dev-client: 85deba11af998ea86e62093b17fce56aa2c6f26b
  expo-dev-launcher: fe4f2c0a0aa627449eeaec5f9f11e04090f97c40
  expo-dev-menu: 5b14897ecce3a8cf9e9cf9109344c2c192a3766a
  expo-dev-menu-interface: be32c09f1e03833050f0ee290dcc86b3ad0e73e4
  ExpoAsset: 323700f291684f110fb55f0d4022a3362ea9f875
  ExpoBlur: fa53f874e7b208bc3756d1bf07903c12e790beb1
  ExpoClipboard: 23d203f5d4843699fbc45be1cc4fe1fbd811a6fa
  ExpoContacts: 67cc93caa3e5023dd245f9e0ef26856aaa005c3e
  ExpoDevice: fc94f0e42ecdfd897e7590f2874fc64dfa7e9b1c
  ExpoFileSystem: 80bfe850b1f9922c16905822ecbf97acd711dc51
  ExpoFont: 00756e6c796d8f7ee8d211e29c8b619e75cbf238
  ExpoHaptics: 5a3a88971af384255baf2504f38b41189cec6984
  ExpoImageManipulator: aea99205c66043a00a0af90e345395637b9902fa
  ExpoImagePicker: 12a420923383ae38dccb069847218f27a3b87816
  ExpoKeepAwake: 3b8815d9dd1d419ee474df004021c69fdd316d08
  ExpoLinearGradient: 8cec4a09426d8934c433e83cb36262d72c667fce
  ExpoLocalization: f04eeec2e35bed01ab61c72ee1768ec04d093d01
  ExpoMailComposer: e6da16de4682fba919051b2e680f804af00341f3
  ExpoMediaLibrary: 81573bcbd50cbd0a3ef57216c93593157d32b558
  ExpoModulesCore: 1914927fe4a693215b1ed57eb934c15641d4aef7
  ExpoScreenOrientation: 5d6a977177dc2f904cc072b51a0d37d0983c0d6a
  ExpoSecureStore: 060cebcb956b80ddae09821610ac1aa9e1ac74cd
  ExpoSystemUI: d4f065a016cae6721b324eb659cdee4d4cf0cb26
  ExpoVideoThumbnails: 40c737b2507a7470b14e9452e286513beb9a7d77
  ExpoWebBrowser: 7595ccac6938eb65b076385fd23d035db9ecdc8e
  EXSplashScreen: 20d60cb03f05e50c23745bf2dc2573b9b2eb42ec
  EXStructuredHeaders: cb8d1f698e144f4c5547b4c4963e1552f5d2b457
  EXTaskManager: 9c3520305c3aa1b4a12a7c6d1e3f85f2779c06e9
  EXUpdates: 5aaa8436127d20302bf0a94bc543c13fb588d694
  EXUpdatesInterface: 996527fd7d1a5d271eb523258d603f8f92038f24
  FBLazyVector: ac12dc084d1c8ec4cc4d7b3cf1b0ebda6dab85af
  Firebase: cec914dab6fd7b1bd8ab56ea07ce4e03dd251c2d
  FirebaseAnalytics: 23717de130b779aa506e757edb9713d24b6ffeda
  FirebaseCore: 30e9c1cbe3d38f5f5e75f48bfcea87d7c358ec16
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  fmt: 4c2741a687cc09f0634a2e2c72a838b99f1ff120
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  Google-Maps-iOS-Utils: f77eab4c4326d7e6a277f8e23a0232402731913a
  GoogleAnalytics: 9e360c5cfffa536ff64dc383bc468577b7ba5c5d
  GoogleAppMeasurement: f9de05ee17401e3355f68e8fc8b5064d429f5918
  GoogleMaps: 032f676450ba0779bd8ce16840690915f84e57ac
  GoogleTagManager: 9ef072dc46718db4b2ef5b801a32a6946efbaafb
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  hermes-engine: 8c1577f3fdb849cbe7729c2e7b5abc4b845e88f8
  Intercom: 9676d5bde35c84f32c43cb66e88d5f6f43c4cdc0
  intercom-react-native: e436fd53a282e253531ff243b55f3a5f880d702b
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  MMKV: 7b5df6a8bf785c6705cc490c541b9d8a957c4a64
  MMKVCore: 3f40b896e9ab522452df9df3ce983471aa2449ba
  MoegoLogger: 835470bd04b5e9b06ce873341e893bae77373584
  MoegoTraceroute: f1c8857ea320237ba2f0f4929656d10f4cd602f7
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  OpenTelemetrySwiftApi: 657da8071c2908caecce11548e006f779924ff9c
  PLCrashReporter: 499c53b0104f95c302d94fd723ebb03c56d9bac8
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: 02617c592a293bd6d418e0a88ff4ee1f88329b47
  RCTDeprecation: 3afceddffa65aee666dafd6f0116f1d975db1584
  RCTRequired: ec1239bc9d8bf63e10fb92bd8b26171a9258e0c1
  RCTTypeSafety: f5ecbc86c5c5fa163c05acb7a1c5012e15b5f994
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  React: fc9fa7258eff606f44d58c5b233a82dc9cf09018
  React-callinvoker: e3fab14d69607fb7e8e3a57e5a415aed863d3599
  React-Codegen: bd1a15f54af401efee5f439aa6fd420b10550125
  React-Core: 3a5fd9e781cecf87803e5b091496a606a3df774a
  React-CoreModules: cbf4707dafab8f9f826ac0c63a07d0bf5d01e256
  React-cxxreact: 7b188556271e3c7fdf22a04819f6a6225045b9dd
  React-debug: 2d6f912c0c4c91a9fde617d8425088af7315c10b
  React-Fabric: 47ff62e0c7f017606585327f6016190625295c5e
  React-FabricImage: 823627aa521b4ecc896334f0dbf2bc8376edbf1e
  React-featureflags: 2a4555681de0d4b683d98d7e9fd7bdf9e9ce1aa2
  React-graphics: edbd2a6c018b2e2d541ab8cb886cc31babf14646
  React-hermes: a7054fbcbda3957e3c5eaad06ef9bf79998d535a
  React-ImageManager: 314824c4bb6f152699724dc9eb3ce544b87048bd
  React-jserrorhandler: fffe10523886a352161ef492af2063651721c8ee
  React-jsi: f3ce1dd2e950b6ad12b65ea3ef89168f1b94c584
  React-jsiexecutor: b4df3a27973d82f9abf3c4bd0f88e042cda25f16
  React-jsinspector: 2ea90b8e53970a1fea1449fb8e6419e21ca79867
  React-jsitracing: c83efb63c8e9e1dff72a3c56e88ae1c530a87795
  React-logger: 257858bd55f3a4e1bc0cf07ddc8fb9faba6f8c7c
  React-Mapbuffer: dce508662b995ffefd29e278a16b78217039d43d
  react-native-background-timer: 17ea5e06803401a379ebf1f20505b793ac44d0fe
  react-native-cameraroll: 2a5be165d8799664f658569496f1e08178dc3eab
  react-native-date-picker: 312c387d2ff873c66c5e4cf78ff6827fa91644e2
  react-native-google-maps: 94df984b1647973baffe17c0b7b52828d6b69d94
  react-native-maps: cbf2f03bfeebfd7ec45966b066db13a075fd2af3
  react-native-netinfo: bdb108d340cdb41875c9ced535977cac6d2ff321
  react-native-render-html: 984dfe2294163d04bf5fe25d7c9f122e60e05ebe
  react-native-safe-area-context: a240ad4b683349e48b1d51fed1611138d1bdad97
  react-native-segmented-control: 65df6cd0619b780b3843d574a72d4c7cec396097
  react-native-slider: d303826dbd07814ebba94464ea1e41d59b610116
  react-native-view-shot: 6b7ed61d77d88580fed10954d45fad0eb2d47688
  react-native-webview: 5951c8e0d457322e39c580778dd8505daf3f0327
  React-nativeconfig: f326487bc61eba3f0e328da6efb2711533dcac46
  React-NativeModulesApple: d89733f5baed8b9249ca5a8e497d63c550097312
  React-perflogger: ed4e0c65781521e0424f2e5e40b40cc7879d737e
  React-RCTActionSheet: 49d53ff03bb5688ca4606c55859053a0cd129ea5
  React-RCTAnimation: 07b4923885c52c397c4ec103924bf6e53b42c73e
  React-RCTAppDelegate: 316e295076734baf9bdf1bfac7d92ab647aed930
  React-RCTBlob: 85c57b0d5e667ff8a472163ba3af0628171a64bb
  React-RCTFabric: 62695e345da7c451b05a131f0c6ba80367dbd5c3
  React-RCTImage: b965c85bec820e2a9c154b1fb00a2ecdd59a9c92
  React-RCTLinking: 75f04a5f27c26c4e73a39c50df470820d219df79
  React-RCTNetwork: c1a9143f4d5778efc92da40d83969d03912ccc24
  React-RCTSettings: c6800f91c0ecd48868cd5db754b0b0a7f5ffe039
  React-RCTText: b923e24f9b7250bc4f7ab154c4168ad9f8d8fc9d
  React-RCTVibration: 08c4f0c917c435b3619386c25a94ee5d64c250f0
  React-rendererdebug: fac75dc155e1202cfc187485a6e4f6e842fcc5c7
  React-rncore: 12dc32f08f195e573e9d969a348b976a3d057bbc
  React-RuntimeApple: 5c7591dd19de1c7fefe8e61cf934d8f8f9fc0409
  React-RuntimeCore: ec3c8be706ca2e4607eb8c675d32512352501f9e
  React-runtimeexecutor: 0e688aefc14c6bc8601f4968d8d01c3fb6446844
  React-RuntimeHermes: df243bd7c8d4ba3bd237ce6ded22031e02d37908
  React-runtimescheduler: db7189185a2e5912b0d17194302e501f801a381e
  React-utils: 3f1fcffc14893afb9a7e5b7c736353873cc5fc95
  ReactCommon: f79ae672224dc1e6c2d932062176883c98eebd57
  RNCAsyncStorage: 826b603ae9c0f88b5ac4e956801f755109fa4d5c
  RNCClipboard: 5e503962f0719ace8f7fdfe9c60282b526305c85
  RNCPicker: 0bc2f0a29abcca7b7ed44a2d036aac9ab6d25700
  RNDnsLookup: 1afbfef1d1f9f9ba427b4afdd81c12c4790de3ff
  RNFBAnalytics: 6ffcca3c60f402775f59d23402287e14de97616b
  RNFBApp: 5f87753a8d8b37d229adf85cd0ff37709ffdf008
  RNGestureHandler: 20a4307fd21cbff339abfcfa68192f3f0a6a518b
  RNImageCropPicker: 8e39c01f205e00d739c31e682f068aac315587bf
  RNReaderSDK: c432129543aa152968c6221f68a63aabb03786fe
  RNReanimated: 308bcd09703f16e26367dfd0c2bba43fc296aa47
  RNScreens: 30249f9331c3b00ae7cb7922e11f58b3ed369c07
  RNSentry: d2f4b8dc672fb4e26b50b99e006e52d547e36b3f
  RNSquareInAppPayments: 69e4c6d6be92bc7f94cca947fc91264dc8f8343b
  RNSquarePos: be658b908063280f05922bbb0152ae86c6314634
  RNSVG: 43b64ed39c14ce830d840903774154ca0c1f27ec
  SDWebImage: 73c6079366fea25fa4bb9640d5fb58f0893facd8
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  Sentry: f8374b5415bc38dfb5645941b3ae31230fbeae57
  SocketRocket: abac6f5de4d4d62d24e11868d7a2f427e0ef940d
  sqlite3: 02d1f07eaaa01f80a1c16b4b31dfcbb3345ee01a
  SquareBuyerVerificationSDK: d325f473a724cfa5684a837296a307152ca52854
  SquareInAppPaymentsSDK: 4d7f1e984fee5d8d7dbd44a67661ca1aedee7f76
  SquarePointOfSaleSDK: 4cdf7e5f2f900234248db44aea4648bba3bec179
  Stripe: e6f816be5a573f7ef45b82d9d843130154fd0269
  stripe-react-native: f1af0235d838495e0c209da6da321eb6f74b70b8
  stripe-terminal-react-native: ba328640bd353ccbe94002de4a96c263b67bb95f
  StripeApplePay: d6ef43f2bf834463e1dcaaf4a61c681aec04990e
  StripeCore: c38075c1b083ee36d6f867d4133f78ecf623d6dc
  StripeFinancialConnections: 346c6200ffac9bd3fe80bfe7e17795125f9b7379
  StripePayments: ade58825c7d75d78a212c5db37e23b05387fb6ba
  StripePaymentSheet: b7cab74c10a5a4b225f948e225dda427a6899e08
  StripePaymentsUI: 1f0cbac9145149fbbb87e27660af0d3be91aae98
  StripeTerminal: 1982b8395d3a45d887a6db0ff75aa3df8be58dcb
  StripeUICore: d6803286f1e2b80c62c320b304503c87bf65de3c
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  twilio-voice-react-native: b2b188083b434b8da58ce9e78c1f4774bb30ca1e
  TwilioVoice: fdf3e90f84023b42d64c204ffbd03c490c86e0cf
  UMAppLoader: f17a5ee8e85b536ace0fc254b447a37ed198d57e
  Yoga: 33622183a85805e12703cd618b2c16bfd18bfffb

PODFILE CHECKSUM: 8c2c63683213bc37eea5e6130ea0e1bd70e93dbf

COCOAPODS: 1.15.2

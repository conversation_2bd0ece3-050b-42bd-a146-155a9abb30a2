import { type ConfigContext } from '@expo/config';
/**
 * 由于 b app 非 managed workflow，需要在构建之前利用脚本对一些配置进行修改。
 * 脚本需要支持以下功能
 * 1. 修改 NATIVE_VERSION，同时在 build.gradle 和 Info.plist 中修改对应的版本号
 * 2. 修改 BUILD_NUMBER，同时在 build.gradle 和 Info.plist 中修改对应
 * 3. 生产环境：修改 PROD_RUNTIME_NUMBER，同时在 android/resource/string 和 Expo.plist 中修改对应
 * 4. 测试环境：修改 RUNTIME_VERSION 为 TEST，同时在 android/resource/string 和 Expo.plist 中修改对应
 * 5. 测试环境：配置 intercom/square key，最好注入环境变量
 * 6. 测试环境：配置 expo-updates url，最好注入环境变量
 * 7. 生产环境：关闭 iOS 注册逻辑，最好注入环境变量
 * 其中 4-6 是测试包， 7 是生产包构建前置条件，需要在构建之前完成，构建后再进行恢复
 * --- DO NOT MODIFY THIS CONTENT ---
 */
const NATIVE_VERSION = '2.16.2';
const BUILD_NUMBER = 130;
const PROD_RUNTIME_NUMBER = 32;
const DEFAULT_CI_ACTION = 'LOCALLY_PROD_BUILD';
const DISABLE_IOS_REGISTRATION = false;
const DEFAULT_BRANCH_NAME = 'production';
const UPDATES_CHECK_AUTOMATICALLY = 'ALWAYS';
// --- DO NOT MODIFY THIS CONTENT---
const PROD_RUNTIME_VERSION = `production-v${PROD_RUNTIME_NUMBER}`;
const CI_ACTION =
  (process.env.CI_ACTION as 'DEPLOY' | 'BUILD' | 'LOCALLY_TEST_BUILD' | 'LOCALLY_PROD_BUILD' | 'LOCALLY_DEV') ??
  DEFAULT_CI_ACTION;
const CI_BUILD_ID = process.env.BUILD_ID ?? '';
// 本地默认是在 production 分支构建
const BRANCH_NAME = process.env.BRANCH_NAME ?? DEFAULT_BRANCH_NAME;

const IS_ONLINE_BRANCH = BRANCH_NAME === 'online';
const IS_NEXT_BRANCH = BRANCH_NAME === 'next';
const IS_PRODUCTION_BRANCH = BRANCH_NAME === 'production';

const IS_DEPLOY = CI_ACTION === 'DEPLOY';
const LOCALLY_PROD_BUILD = CI_ACTION === 'LOCALLY_PROD_BUILD';
// 本地 production 分支打 prod 包或者 online 分支 CI deploy 发热更包，都是 prod 环境。
const IS_PROD = (IS_PRODUCTION_BRANCH && LOCALLY_PROD_BUILD) || (IS_ONLINE_BRANCH && IS_DEPLOY) || IS_NEXT_BRANCH;
const BUILD_ENV = CI_ACTION === 'LOCALLY_DEV' ? 'DEV' : IS_PROD ? 'PROD' : 'TEST';
const RUNTIME_VERSION = IS_PROD ? PROD_RUNTIME_VERSION : 'TEST';
const EXPO_UPDATES_URL = IS_PROD
  ? 'https://expo-updates.moego.pet/api/manifest'
  : 'https://expo-updates.t2.moego.dev/api/manifest';

export { BUILD_ENV, PROD_RUNTIME_NUMBER, RUNTIME_VERSION };

export default (_ctx: ConfigContext) => {
  return {
    name: 'MoeGo 2',
    owner: 'moement',
    slug: 'moego-business-2',
    version: NATIVE_VERSION,
    orientation: 'portrait',
    icon: './assets/icon.png',
    userInterfaceStyle: 'light',
    splash: {
      image: './assets/splash.png',
      resizeMode: 'contain',
      backgroundColor: '#ffffff',
    },
    assetBundlePatterns: ['**/*'],
    ios: {
      supportsTablet: true,
      bundleIdentifier: 'com.moement.moego.business',
      buildNumber: String(BUILD_NUMBER),
      infoPlist: {
        LSApplicationQueriesSchemes: ['moego-smart-schedule', 'comgooglemaps', 'waze', 'square-commerce-v1'],
        NSLocationAlwaysAndWhenInUseUsageDescription: 'To find clients near your current location.',
        NSLocationAlwaysUsageDescription: 'To find clients near your current location.',
        NSLocationWhenInUseUsageDescription: 'To find clients near your current location.',
        NSCameraUsageDescription: 'To take photo for profile image for accounts, clients, and pets.',
        NSContactsUsageDescription: 'To create pet owner profiles from mobile contacts.',
        NSPhotoLibraryUsageDescription: 'To upload photo for profile image for accounts, clients, pets.',
        NSPhotoLibraryAddUsageDescription: 'Save photo in message.',
        NSMicrophoneUsageDescription: 'To take photo for profile image for accounts, clients, and pets.',
      },
      googleServicesFile: './GoogleService-Info.plist',
      config: {
        googleMapsApiKey: 'AIzaSyDOLelnu83PZ2Oey1GO0qmcj6xIJMf7AzY',
      },
    },
    android: {
      package: 'com.moement.moego.business',
      googleServicesFile: './google-services.json',
      permissions: [
        'CAMERA',
        'WRITE_EXTERNAL_STORAGE',
        'CAMERA_ROLL',
        'ACCESS_COARSE_LOCATION',
        'ACCESS_FINE_LOCATION',
        'READ_CONTACTS',
        'VIBRATE',
        'ACCESS_BACKGROUND_LOCATION',
        'USE_FULL_SCREEN_INTENT',
      ],
      versionCode: BUILD_NUMBER,
      config: {
        googleMaps: {
          apiKey: 'AIzaSyAmUHw_EPnvcw2N0NM574N1sHazZMcP4Fg',
        },
      },
    },
    plugins: [
      [
        'expo-build-properties',
        {
          android: { compileSdkVersion: 34, targetSdkVersion: 34, minSdkVersion: 26 },
          ios: {
            useFrameworks: 'static',
          },
        },
      ],
      ['expo-font'],
      [
        'expo-notifications',
        {
          icon: './assets/notification-icon.png',
          color: '#f96b18',
          sounds: ['./assets/moego.wav'],
        },
      ],
      'expo-localization',
      [
        'expo-secure-store',
        {
          configureAndroidBackup: true,
          faceIDPermission: 'Allow $(PRODUCT_NAME) to access your Face ID biometric data.',
        },
      ],
      [
        '@sentry/react-native/expo',
        {
          url: 'https://sentry.io/',
          project: 'business-mobile',
          organization: 'moego-ey',
        },
      ],
      '@react-native-firebase/app',
      // [
      //   'config-plugin-react-native-intercom',
      //   {
      //     iosApiKey: 'ios_sdk-0a32dacbbea1f90edf9b01acead2ed9afde68f89',
      //     androidApiKey: 'android_sdk-f3e399e7548e930ec9813f5bb31abbb9a8198680',
      //     appId: 'oh5g31xm',
      //   },
      // ],
      [
        '@stripe/stripe-react-native',
        {
          merchantIdentifier: '',
          enableGooglePay: false,
        },
      ],
      [
        '@stripe/stripe-terminal-react-native',
        {
          bluetoothBackgroundMode: true,
          locationWhenInUsePermission:
            'MoeGo would like to access your location information in order to provide you with location related services. You can change location access permissions at any time in the application settings.',
          bluetoothPeripheralPermission:
            'MoeGo would like access to Bluetooth in order to connect your device to compatible hardware.',
          bluetoothAlwaysUsagePermission:
            'MoeGo would like access to Bluetooth in order to connect your device to compatible hardware.',
        },
      ],
      './react-native-maps-plugin',
    ],
    platforms: ['ios', 'android'],
    extra: {
      eas: {
        projectId: '1f42d26e-c8b2-4e3e-aff0-8b4465f5b199',
      },
      CI_BUILD_ID,
      BRANCH_NAME,
      BUILD_ENV,
      RUNTIME_NUMBER: PROD_RUNTIME_NUMBER,
      DISABLE_IOS_REGISTRATION,
    },
    runtimeVersion: RUNTIME_VERSION,
    updates: {
      url: EXPO_UPDATES_URL,
      fallbackToCacheTimeout: 0,
      checkAutomatically: UPDATES_CHECK_AUTOMATICALLY,
      requestHeaders: {
        'moego-app-name': 'moego-business',
      },
    },
    // hooks: {
    //   postExport: [
    //     CI_ACTION === 'LOCALLY_DEV'
    //       ? {}
    //       : {
    //           // build 阶段默认 release 只能为 `${bundleIdentifier}@${version}+${buildNumber}`，无法修改。
    //           file: 'sentry-expo/upload-sourcemaps',
    //           config: {
    //             organization: 'moego',
    //             project: 'moego-business-mobile-v2',
    //             url: 'https://sentry.moego.pet/',
    //           },
    //         },
    //   ],
    // },
  };
};

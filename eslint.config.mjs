import pluginMoego from '@moego/eslint-plugin-moego-fe';
import { globalIgnores } from 'eslint/config';

/** @type {import('eslint').Linter.Config[]} */
export default [
  globalIgnores([
    '*.config.*',
    '*.spec.ts',
    '*.test.ts',
    '*.test.tsx',
    'src/types/openApi/',
    'jest.coverage.js',
    'react-native-maps-plugin.js',
  ]),
  { files: ['src/**/*.{js,mjs,cjs,ts,jsx,tsx}'] },
  {
    plugins: {
      'moego-fe': pluginMoego,
    },
  },
  ...pluginMoego.configs.recommended['react-native'],
  {
    languageOptions: {
      globals: {
        es2022: true,
      },
    },
    settings: {
      react: {
        version: '18.2.0',
      },
    },
    rules: {
      'moego-fe/disable-export-default': [
        'error',
        {
          exclude: ['src/modules/**/*', 'eslint.config.mjs'],
        },
      ],
      'moego-fe/disable-index': [
        'error',
        {
          exclude: ['index.js', 'react-native-traceroute/**/*'],
        },
      ],
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          args: 'all',
          argsIgnorePattern: '^_',
          caughtErrors: 'all',
          caughtErrorsIgnorePattern: '^_',
          destructuredArrayIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          ignoreRestSiblings: true,
        },
      ],
      'sonarjs/no-unused-vars': 'off',
      '@typescript-eslint/consistent-type-imports': 'off',
      'prefer-const': 'off',
    },
  },
];
